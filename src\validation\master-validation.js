const { query } = require('express');
const { Joi } = require('express-validation');

const issuecategoryTypeListValidation = {
  body: Joi.object({
    master_type: Joi.string().required(),
    customer_type: Joi.string().required()
  }),
};


const getGovtSchemeValidation = {
  query: Joi.object({
    page: Joi.number().integer().min(1).allow(null, '').default(null).messages({
      'number.base': 'Page must be a number',
      'number.min': 'Page must be greater than 0',
    }),
    limit: Joi.number().integer().min(1).allow(null, '').default(null).messages({
      'number.base': 'Limit must be a number',
      'number.min': 'Limit must be greater than 0',
    }),
    search: Joi.string().max(200).allow(null, '').messages({
      'string.max': 'Search query should not exceed 200 characters',
    }),
    orderBy: Joi.string().valid('asc', 'desc').allow(null, '').messages({
      'string.valid': 'OrderBy must be either "asc" or "desc"',
    }),
    orderName: Joi.string().allow(null, '').messages({
      'string.base': 'OrderName must be a string',
    }),
  }),
};

const getProofMasterValidation = {
  query: Joi.object({
    page: Joi.number()
      .integer()
      .min(1)
      .allow(null, '')
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.min': 'Page must be greater than 0',
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .allow(null, '')
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.min': 'Limit must be greater than 0',
      }),
    search: Joi.string()
      .max(200)
      .allow(null, '')
      .messages({
        'string.max': 'Search query should not exceed 200 characters',
      }),
    orderBy: Joi.string()
      .valid('asc', 'desc')
      .allow(null, '')
      .messages({
        'string.valid': 'OrderBy must be either "asc" or "desc"',
      }),
    orderName: Joi.string()
      .allow(null, '')
      .messages({
        'string.base': 'OrderName must be a string',
      }),
      proof_type: Joi.string()
      .valid('proof_of_ownership', 'photo_proof', 'proof_of_rented')
      .messages({
        'string.valid': 'proof_type must be "proof_of_ownership" or "photo_proof" or "proof_of_rented"',
      }),
  }),
};
const getResonMasterValidation = {
  body: Joi.object({
    master_type: Joi.string().required(),
  }),
};
const getServiceRateValidation = {
  body: Joi.object({
    service_type_slug: Joi.string().max(40),
  }),
};
module.exports = {
  issuecategoryTypeListValidation,
  getGovtSchemeValidation,
  getProofMasterValidation,
  getResonMasterValidation,
  getServiceRateValidation
};
