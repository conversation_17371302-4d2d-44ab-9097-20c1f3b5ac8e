const { Joi } = require('express-validation');
const { PHONE_NO_INVALID, PINCODE_INVALID } = require('../constant/messages');
const {
  MOBILE_REGEX,
  ALPHASPACE_REGEX,
  PINCODE_REGEX,
} = require('../constant/regex');

const graphDataValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    duration: Joi.string()
      .valid('6_months', '1_year', '2_year', '3_year')
      .required(),
  }).required(),
};

const currentBillValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().required(),
    customer_meter_id: Joi.number().optional(),
    Date: Joi.date().iso().required(),
  }).required(),
};
const notificationValidation = {
  query: Joi.object({
    customer_connection_id: Joi.number().required(),
  }).required(),
};

const viewNotificationValidation = {
  body: Joi.object({
    alert_request_id: Joi.number().required(),
  }).required(),
};

module.exports = {
  graphDataValidation,
  currentBillValidation,
  notificationValidation,
  viewNotificationValidation
};
