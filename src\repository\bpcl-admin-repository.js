const { dbModels, sequelize } = require("../database/connection");
const { fn, col, literal, Op, Sequelize, where } = require("sequelize");
const SASTokenHelper = require("../helpers/azureSASSDKHelper");
const mst_admin = require("../model/mst_admin");
const tpiRepository = require("./tpi-repository");

const getChannelPartnersCount = async (body) => {
  try {
    const result = await dbModels.mst_geographical_areas.findOne({
      where: {
        geographical_area_id: body.geographical_area_id,
      },
      attributes: [
        [
          Sequelize.literal(
            `
        (SELECT COUNT(mst_admin.admin_id)
        FROM mst_admin
        JOIN mst_persona_role 
        ON mst_admin.persona_role_id = mst_persona_role.persona_role_id
        JOIN trx_admin_mapped_areas 
        ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id
        JOIN mst_areas 
        ON trx_admin_mapped_areas.area_id = mst_areas.area_id
         and mst_areas.area_id in (` +
            body.area_id +
            `)
        WHERE trx_admin_mapped_areas.area_id = mst_areas.area_id
        AND mst_persona_role.role_name = 'TPI'
        AND mst_admin.user_role = 'sales'
        AND mst_areas.geographical_area_id = mst_geographical_areas.geographical_area_id)
      `
          ),
          "tpiSalesCount",
        ],
        [
          Sequelize.literal(
            `
        (SELECT COUNT(mst_admin.admin_id)
        FROM mst_admin
        JOIN mst_persona_role 
        ON mst_admin.persona_role_id = mst_persona_role.persona_role_id
        JOIN trx_admin_mapped_areas 
        ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id
        JOIN mst_areas 
        ON trx_admin_mapped_areas.area_id = mst_areas.area_id
         and mst_areas.area_id in (` +
            body.area_id +
            `)
        WHERE trx_admin_mapped_areas.area_id = mst_areas.area_id
        AND mst_persona_role.role_name = 'TPI'
        AND mst_admin.user_role = 'engineer'
        AND mst_areas.geographical_area_id = mst_geographical_areas.geographical_area_id)
      `
          ),
          "tpiEngineerCount",
        ],
        [
          Sequelize.literal(
            `
        (SELECT COUNT(mst_admin.admin_id)
        FROM mst_admin
        JOIN mst_persona_role 
        ON mst_admin.persona_role_id = mst_persona_role.persona_role_id
        JOIN trx_admin_mapped_areas 
        ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id
        JOIN mst_areas 
        ON trx_admin_mapped_areas.area_id = mst_areas.area_id
         and mst_areas.area_id in (` +
            body.area_id +
            `)
        WHERE mst_persona_role.role_name = 'LMC'
        AND mst_admin.user_role = 'contractor'
        AND mst_areas.geographical_area_id = mst_geographical_areas.geographical_area_id)
      `
          ),
          "lmcContractorCount",
        ],
        [
          Sequelize.literal(
            `
        (SELECT COUNT(mst_admin.admin_id)
        FROM mst_admin
        JOIN mst_persona_role 
        ON mst_admin.persona_role_id = mst_persona_role.persona_role_id
        JOIN trx_admin_mapped_areas 
        ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id
        JOIN mst_areas 
        ON trx_admin_mapped_areas.area_id = mst_areas.area_id
         and mst_areas.area_id in (` +
            body.area_id +
            `)
        WHERE mst_persona_role.role_name = 'LMC'
        AND mst_admin.user_role = 'supervisor'
        AND mst_areas.geographical_area_id = mst_geographical_areas.geographical_area_id)
      `
          ),
          "lmcSupervisorCount",
        ],
        [
          Sequelize.literal(
            `
        (SELECT COUNT(mst_admin.admin_id)
        FROM mst_admin
        JOIN mst_persona_role 
        ON mst_admin.persona_role_id = mst_persona_role.persona_role_id
        JOIN trx_admin_mapped_areas 
        ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id
        JOIN mst_areas 
        ON trx_admin_mapped_areas.area_id = mst_areas.area_id
         and mst_areas.area_id in (` +
            body.area_id +
            `)
        WHERE mst_persona_role.role_name = 'DMA'
        AND mst_admin.user_role = 'contractor'
        AND mst_areas.geographical_area_id = mst_geographical_areas.geographical_area_id)
      `
          ),
          "dmaContractorCount",
        ],
        [
          Sequelize.literal(
            `
        (SELECT COUNT(mst_admin.admin_id)
        FROM mst_admin
        JOIN mst_persona_role 
        ON mst_admin.persona_role_id = mst_persona_role.persona_role_id
        JOIN trx_admin_mapped_areas 
        ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id
        JOIN mst_areas 
        ON trx_admin_mapped_areas.area_id = mst_areas.area_id
         and mst_areas.area_id in (` +
            body.area_id +
            `)
        WHERE mst_persona_role.role_name = 'DMA'
        AND mst_admin.user_role = 'supervisor'
        AND mst_areas.geographical_area_id = mst_geographical_areas.geographical_area_id)
      `
          ),
          "dmaSupervisorCount",
        ],
        [
          Sequelize.literal(
            `
        (SELECT COUNT(mst_admin.admin_id)
        FROM mst_admin
        JOIN mst_persona_role 
        ON mst_admin.persona_role_id = mst_persona_role.persona_role_id
        JOIN trx_admin_mapped_areas 
        ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id
        JOIN mst_areas 
        ON trx_admin_mapped_areas.area_id = mst_areas.area_id
         and mst_areas.area_id in (` +
            body.area_id +
            `)
        WHERE mst_persona_role.role_name = 'FO'
        AND mst_admin.user_role = 'contractor'
        AND mst_areas.geographical_area_id = mst_geographical_areas.geographical_area_id)
      `
          ),
          "foContractorCount",
        ],
        [
          Sequelize.literal(
            `
        (SELECT COUNT(mst_admin.admin_id)
        FROM mst_admin
        JOIN mst_persona_role 
        ON mst_admin.persona_role_id = mst_persona_role.persona_role_id
        JOIN trx_admin_mapped_areas 
        ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id
        JOIN mst_areas 
        ON trx_admin_mapped_areas.area_id = mst_areas.area_id
         and mst_areas.area_id in (` +
            body.area_id +
            `)
        WHERE mst_persona_role.role_name = 'FO'
        AND mst_admin.user_role = 'supervisor'
        AND mst_areas.geographical_area_id = mst_geographical_areas.geographical_area_id)
      `
          ),
          "foSupervisorCount",
        ],
      ],
      raw: true,
    });

    return {
      dma: {
        total_contractors: result.dmaContractorCount,
        total_supervisors: result.dmaSupervisorCount,
        total_team_members:
          result.dmaContractorCount + result.dmaSupervisorCount,
      },
      lmc: {
        total_contractors: result.lmcContractorCount,
        total_supervisors: result.lmcSupervisorCount,
        total_team_members:
          result.lmcContractorCount + result.lmcSupervisorCount,
      },
      fo: {
        total_contractors: result.foContractorCount,
        total_supervisors: result.foSupervisorCount,
        total_team_members: result.foContractorCount + result.foSupervisorCount,
      },
      tpi: {
        total_sales: result.tpiSalesCount,
        total_engineers: result.tpiEngineerCount,
        total_team_members: result.tpiSalesCount + result.tpiEngineerCount,
      },
      total_channel_partners:
        result.dmaContractorCount +
        result.lmcContractorCount +
        result.foContractorCount +
        result.tpiSalesCount +
        result.tpiEngineerCount,
      total_team_members:
        result.dmaContractorCount +
        result.dmaSupervisorCount +
        result.lmcContractorCount +
        result.lmcSupervisorCount +
        result.foContractorCount +
        result.foSupervisorCount +
        result.tpiSalesCount +
        result.tpiEngineerCount,
    };
  } catch (error) {
    console.error("Error in getChannelPartnersCount:", error);
    throw error;
  }
};
const contractorOnboardingRepo = async (
  persona_role_id,
  user_roles,
  contractorTypeForDb,
  req
) => {
  const transaction = await sequelize.transaction();
  try {
    const {
      ga_id,
      contractor_type,
      mobile,
      alt_mobile,
      email,
      first_name,
      middle_name,
      last_name,
      agency,
      area_id,
      document_type,
    } = req.body;

    const file = req.file;

    // Create Contractor
    const createContractor = await dbModels.mst_admin.create(
      {
        persona_role_id,
        user_role: user_roles,
        first_name,
        middle_name,
        last_name,
        mobile,
        alternate_mobile: alt_mobile,
        email,
        is_active: false,
        created_by: req.user.admin_id || 0,
      },
      { transaction }
    );

    // Mapped GA Area
    await dbModels.trx_admin_mapped_ga_areas.create(
      {
        admin_id: createContractor.admin_id,
        ga_area_id: ga_id,
        created_by: req.user.admin_id || 0,
      },
      { transaction }
    );

    // Mapped Area
    const areaIds = Array.isArray(area_id) ? area_id : [area_id];
    const areaMappings = areaIds.map((area_id) => ({
      amga_id: 0,
      admin_id: createContractor.admin_id,
      area_id,
      is_active: true,
      created_by: req.user.admin_id || 0,
      updated_by: 0,
    }));
    const insertIntomappedAreaTable =
      await dbModels.trx_admin_mapped_areas.bulkCreate(areaMappings, {
        transaction,
        returning: false,
      });

    // Onboard Approval
    await dbModels.trx_admin_onboard_approve.create(
      {
        contractor_id: createContractor.admin_id,
        contractor_type: contractorTypeForDb,
        approve_status: "pending",
        is_active: true,
        created_by: req.user.admin_id || 0,
      },
      { transaction }
    );

    // Agency logic
    let getAgency = await dbModels.mst_agencies.findOne({
      where: { agency_name: agency },
      transaction,
    });

    if (!getAgency) {
      getAgency = await dbModels.mst_agencies.create(
        {
          agency_name: agency,
          created_by: req.user.admin_id || 0,
        },
        { transaction }
      );
    }
    const folderName =
      "admin/adm_" +
      createContractor.admin_id +
      "/ContractorOnboarding/documents";
    const permission = "racwd";
    const sasToken = await SASTokenHelper.generateSASToken(permission);
    const fileUpload = await SASTokenHelper.azureSingleFileUpload(
      req,
      {},
      sasToken,
      folderName
    );

    // Upload supporting document if file is provided
    if (fileUpload && fileUpload.filename && fileUpload.error == null) {
      await dbModels.trx_admin_contractor_other_details.create(
        {
          admin_id: createContractor.admin_id,
          ga_area_id: parseInt(ga_id),
          agency_id: getAgency.agency_id,
          document_type: document_type,
          file_name: fileUpload.filename,
          file_path: folderName,
          file_ext: fileUpload.filename.split(".").pop(),
          created_by: req.user.admin_id || 0,
        },
        { transaction }
      );
    }

    // All good — commit the transaction
    await transaction.commit();

    return {
      user: createContractor,
      //area: insertIntoAdminMappedGaAreaTable,
      mappedArea: insertIntomappedAreaTable,
      message: "Contractor onboarded successfully",
    };
  } catch (error) {
    await transaction.rollback(); // Rollback on error
    console.error("Error in contractorOnboardingRepo:", error);
    throw error;
  }
};

const getAreaListRepo = async (req) => {
  try {
    const { ga_id } = req.body;

    const result = dbModels.mst_areas.findAll({
      where: {
        geographical_area_id: ga_id,
      },
    });
    return result;
  } catch (error) {
    console.error("Error in getChannelPartnersCount:", error);
    throw error;
  }
};

const contractorPendingApprovalRepo = async (req) => {
  try {
    const { ga_id, type } = req.body || req.query;
    const user = req.user?.admin_id;

    const queryOptions = {
      where: {
        is_active: true,
        approve_status: type,
        ...(type === "pending"
          ? { created_by: { [Op.ne]: user } }
          : { updated_by: user }),
      },
      include: [
        {
          model: dbModels.mst_admin,
          as: "contractor",
          attributes: [
            "admin_id",
            "first_name",
            "last_name",
            "mobile",
            "user_role",
            ...(type !== "pending" ? ["updated_by", "updated_at"] : []),
          ],
          include: [
            {
              model: dbModels.trx_admin_contractor_other_details,
              as: "trx_admin_contractor_other_details",
              where: {
                ga_area_id: ga_id,
                is_active: true,
              },
              attributes: [
                "document_type",
                "file_name",
                "file_path",
                "file_ext",
              ],
              include: [
                {
                  model: dbModels.mst_agencies,
                  as: "agency",
                  attributes: ["agency_name"],
                },
              ],
            },
            {
              model: dbModels.trx_admin_mapped_areas,
              as: "trx_admin_mapped_areas",
              where: { is_active: true },
              required: false,
              attributes: ["admin_mapped_area_id", "area_id"],
              include: [
                {
                  model: dbModels.mst_areas,
                  as: "area",
                  attributes: ["area_name"],
                  required: false,
                },
              ],
            },
          ],
        },
        {
          model: dbModels.mst_admin,
          as: "onboardedBy",
          attributes: ["first_name", "last_name"],
        },
      ],
      attributes: ["contractor_type", "created_at"],
      logging: console.log,
    };

    const result =
      await dbModels.trx_admin_onboard_approve.findAll(queryOptions);
    return result;
  } catch (error) {
    console.error("Error in contractorPendingApprovalRepo:", error);
    throw error; // Re-throw the error for the caller to handle
  }
};

const acceptRepo = async (gaId, contractorId, password, user) => {
  const transaction = await sequelize.transaction();
  try {
    const updatedById = user?.admin_id; // Assuming user object has admin_id
    const approvedById = updatedById;
    console.log("password in repo---->", password);
    // Update trx_admin_onboard_approve table
    const userdata = await dbModels.trx_admin_onboard_approve.update(
      {
        approve_status: "approved",
        approved_by: user,
        updated_by: user,
        updated_at: new Date(),
      },
      {
        where: {
          contractor_id: contractorId,
        },
        transaction,
      }
    );

    // Update mst_admin table
    await dbModels.mst_admin.update(
      {
        is_active: 1,
        password: password,
        updated_by: user,
        updated_at: new Date(),
      },
      {
        where: {
          admin_id: contractorId,
        },
        transaction,
      }
    );

    // Commit transaction
    await transaction.commit();
    return userdata;
  } catch (error) {
    await transaction.rollback();
    console.error("Error in acceptRepo:", error);
    throw error;
  }
};

const rejectRepo = async (contractorId, remark, user) => {
  try {
    console.log("remark--->", remark);
    const userdata = await dbModels.trx_admin_onboard_approve.update(
      {
        approve_status: "rejected",
        remark: remark,
        updated_by: user,
        updated_at: new Date(),
      },
      {
        where: {
          contractor_id: contractorId,
        },
      }
    );
    return userdata;
  } catch (error) {
    console.error("Error in getChannelPartnersCount:", error);
    throw error;
  }
};
const checkTPISaleEngineerRepo = async (
  geographicalAreaId,
  areaIds,
  user_roles
) => {
  try {
    const result = await dbModels.mst_admin.findAll({
      logging: true,
      where: {
        persona_role_id: 2,
        user_role: user_roles,
        is_active: 1,
      },
      attributes: ["admin_id", "first_name", "last_name", "email", "mobile"],
      include: [
        {
          model: dbModels.trx_admin_mapped_areas,
          as: "trx_admin_mapped_areas",
          where: {
            is_active: 1,
            area_id: { [Op.in]: areaIds },
          },
          required: true,
          attributes: ["area_id"],
          include: [
            {
              model: dbModels.mst_areas,
              as: "area",
              required: true,
              attributes: ["area_name", "area_id"],
              include: [
                {
                  model: dbModels.mst_geographical_areas,
                  as: "geographical_area",
                  required: true,
                  where: {
                    geographical_area_id: geographicalAreaId, // <-- Pass your dynamic geographical_area_id here
                  },
                  attributes: ["geographical_area_id", "ga_area_name"],
                },
              ],
            },
          ],
        },
      ],
      distinct: true,
    });

    return result;
  } catch (error) {
    console.error("Error in getChannelPartnersCount:", error);
    throw error;
  }
};
const getContractorDetails = async (adminId) => {
  try {
    const result = await dbModels.mst_admin.findOne({
      where: {
        admin_id: adminId,
        // is_active: true,
      },
      attributes: [
        "admin_id",
        "first_name",
        "last_name",
        "email",
        "mobile",
        "created_by",
      ],
      include: [
        {
          model: dbModels.mst_persona_role,
          as: "persona_role", // 👈 make sure this alias matches your association
          attributes: ["role_name"],
          required: false,
        },
        {
          model: dbModels.trx_admin_mapped_areas,
          as: "trx_admin_mapped_areas",
          required: false,
          where: { is_active: true },
          attributes: ["admin_mapped_area_id", "area_id"],
          include: [
            {
              model: dbModels.mst_areas,
              as: "area",
              required: false,
              attributes: ["area_name"],
              include: [
                {
                  model: dbModels.mst_geographical_areas,
                  as: "geographical_area",
                  required: false,
                  attributes: ["geographical_area_id", "ga_area_name"],
                },
              ],
            },
          ],
        },
      ],
    });

    return result;
  } catch (error) {
    console.error("Error in getChannelPartnersCount:", error);
    throw error;
  }
};

const getSubAdminDetails = async (id) => {
  try {
    const result = dbModels.mst_admin.findOne({
      where: {
        admin_id: id,
      },
      attributes: ["admin_id", "first_name", "last_name", "email", "mobile"],
    });
    return result;
  } catch (error) {
    console.error("Error in getChannelPartnersCount:", error);
    throw error;
  }
};

const subAdminOnboardingRepo = async (payload, password, req) => {
  const transaction = await sequelize.transaction();
  try {
    const {
      ga_id,
      sub_admin_type,
      title,
      emp_name,
      bpcl_id,
      position_id,
      mobile,
      alt_mobile,
      email,
      period_from,
      period_to,
    } = payload;

    const result = await dbModels.mst_admin.create(
      {
        persona_role_id: 5,
        user_role: "bpcl-sub-admin",
        prefix: title,
        first_name: emp_name,
        last_name: "",
        mobile,
        alternate_mobile: alt_mobile,
        email,
        bpcl_id,
        position_id,
        from_date: period_from,
        to_date: period_to,
        password: password,
        created_by: req.user.admin_id || 0,
      },
      { transaction }
    );

    if (result?.admin_id && sub_admin_type) {
      await dbModels.trx_bpcl_admin_role_mapping.create(
        {
          bpcl_admin_id: result.admin_id,
          bpcl_admin_role_id: sub_admin_type,
          created_by: req.user.admin_id || 0,
        },
        { transaction }
      );
    }

    await dbModels.trx_bpcl_admin_mapped_ga_areas.create(
      {
        bpcl_admin_id: result.admin_id,
        ga_area_id: ga_id,
        created_by: req.user.admin_id || 0,
      },
      { transaction }
    );

    await transaction.commit();
    if (result.password) {
      delete result.password;
    }
    return result;
  } catch (error) {
    await transaction.rollback();
    console.error("Error in subAdminOnboardingRepo:", error);
    throw error;
  }
};

const getGaAreawithAreaIdRepo = async (id) => {
  try {
    const result = dbModels.mst_geographical_areas.findAll({
      where: {
        geographical_area_id: id,
      },
      attributes: ["geographical_area_id", "region_name", "ga_area_name"],
    });
    return result;
  } catch (error) {
    console.error("Error in getChannelPartnersCount:", error);
    throw error;
  }
};

const getSubAdminTypeListRepo = async () => {
  try {
    const result = dbModels.mst_bpcl_admin_roles.findAll({
      where: {
        bpcl_admin_role_type: "ga",
      },
    });
    return result;
  } catch (error) {
    console.error("Error in getSubAdminTypeListRepo:", error);
    throw error;
  }
};

const checkMobileAndEmailExistInGARepo = async (mobile, email, gaAreaId) => {
  try {
    const mobileAdmin = await dbModels.mst_admin.findOne({
      where: { mobile },
      include: [
        {
          model: dbModels.trx_bpcl_admin_mapped_ga_areas,
          as: "trx_bpcl_admin_mapped_ga_areas",
          attributes: ["ga_area_id"],
        },
      ],
    });

    const emailAdmin = await dbModels.mst_admin.findOne({
      where: { email },
      include: [
        {
          model: dbModels.trx_bpcl_admin_mapped_ga_areas,
          as: "trx_bpcl_admin_mapped_ga_areas",
          attributes: ["ga_area_id"],
        },
      ],
    });

    const checkGaMatch = (adminRecord) => {
      if (!adminRecord || !adminRecord.trx_bpcl_admin_mapped_ga_areas)
        return false;
      return adminRecord.trx_bpcl_admin_mapped_ga_areas.some(
        (area) => area.ga_area_id === gaAreaId
      );
    };

    console.log("mobileAdmin", mobileAdmin);
    console.log("emailAdmin", emailAdmin);
    return {
      mobile: {
        data: mobileAdmin,
        exists: !!mobileAdmin,
        sameGa: checkGaMatch(mobileAdmin),
      },
      email: {
        data: emailAdmin,
        exists: !!emailAdmin,
        sameGa: checkGaMatch(emailAdmin),
      },
    };
  } catch (error) {
    console.error("Error in checkMobileAndEmailExistInGARepo:", error);
    throw error;
  }
};
const getGeographicalAreaListRepo = async (user) => {
  try {
    const mobileAdmin = await dbModels.trx_bpcl_admin_mapped_ga_areas.findAll({
      where: { bpcl_admin_id: user },
      attributes: ["bamga_id", "bpcl_admin_id", "ga_area_id"],
      include: [
        {
          model: dbModels.mst_geographical_areas,
          as: "ga_area",
          attributes: ["geographical_area_id", "ga_area_name"],
        },
      ],
    });

    return mobileAdmin;
  } catch (error) {
    console.error("Error in getGeographicalAreaListRepo:", error);
    throw error;
  }
};

const getTpiSalesByAreaWiseList = async (
  ga_area_id,
  page = 1,
  limit = 10,
  area_id = [],
  search = null
) => {
  try {
    const offset = (page - 1) * limit;
    const whereObj = { ga_area_id };
    let whereText = "geographical_area_id = :ga_area_id";
    if (area_id.length > 0) {
      whereObj.area_id = area_id;
      whereText += " and mst_areas.area_id in (:area_id)";
    }
    if (search) {
      whereObj.search = `%${search}%`;
      whereText +=
        " and (mst_admin.first_name like :search or mst_admin.last_name like :search or mst_areas.area_name like :search)";
    }
    const tpiStatusQuery = `(
		SELECT count(tpi_id)
		FROM trx_customer_connection_tpi_mapping AS tcctm
		JOIN trx_customer_connection AS tcc ON tcctm.customer_connection_id = tcc.customer_connection_id
		JOIN mst_available_pngareas AS map ON tcc.available_pngareas_id = map.available_pngareas_id
		JOIN mst_pincodes AS mp ON mp.pincode_id = map.pincode_id
		JOIN mst_areas AS ma ON ma.area_id = mp.area_id
		WHERE tcctm.tpi_id = mst_admin.admin_id
			AND tcctm.is_latest = 1
			AND ma.area_id = mst_areas.area_id
      :whereTpiStatus
		)`;
    const sqlQuery = `SELECT mst_areas.area_name
  ,mst_areas.area_id
	,mst_agencies.agency_name
	,mst_admin.admin_id
	,mst_admin.first_name
	,mst_admin.last_name
	,mst_admin.mobile
	,mst_admin.email
	,${tpiStatusQuery.replace(":whereTpiStatus", "")} AS totalAssigned
	,${tpiStatusQuery.replace(":whereTpiStatus", "AND tcctm.tpi_status = 'pending'")} AS totalPending
	,${tpiStatusQuery.replace(":whereTpiStatus", "AND tcctm.tpi_status = 'rework'")} AS totalRework
	,${tpiStatusQuery.replace(":whereTpiStatus", "AND tcctm.tpi_status = 'pending-so'")} as totalApproved
	,${tpiStatusQuery.replace(":whereTpiStatus", "AND tcctm.tpi_status = 'rejected'")} AS totalRejected
FROM mst_areas
JOIN trx_admin_mapped_areas AS tama ON tama.area_id = mst_areas.area_id
JOIN mst_admin ON mst_admin.admin_id = tama.admin_id
	AND mst_admin.user_role = 'sales'
LEFT JOIN trx_admin_contractor_other_details AS tacod ON tacod.admin_id = mst_admin.admin_id
LEFT JOIN mst_agencies ON mst_agencies.agency_id = tacod.agency_id
WHERE ${whereText} order by mst_areas.area_id OFFSET :offset ROWS
FETCH NEXT :limit ROWS ONLY;`;
    const records = await sequelize.query(sqlQuery, {
      type: Sequelize.QueryTypes.SELECT,
      replacements: { ...whereObj, offset, limit },
    });
    const countSqlQuery = `SELECT count(mst_areas.area_id) as count
    FROM mst_areas
  JOIN trx_admin_mapped_areas AS tama ON tama.area_id = mst_areas.area_id
  JOIN mst_admin ON mst_admin.admin_id = tama.admin_id
    AND mst_admin.user_role = 'sales'
  LEFT JOIN trx_admin_contractor_other_details AS tacod ON tacod.admin_id = mst_admin.admin_id
  LEFT JOIN mst_agencies ON mst_agencies.agency_id = tacod.agency_id
  WHERE ${whereText}`;
    const countData = await sequelize.query(countSqlQuery, {
      type: Sequelize.QueryTypes.SELECT,
      replacements: whereObj,
    });
    return { records, count: countData[0].count };
  } catch (error) {
    console.error("Repo Error in getTpiSalesByAreaWiseList", error);
    throw error;
  }
};

const getTpiEngineerByAreaWiseList = async (
  ga_area_id,
  page = 1,
  limit = 10,
  area_id = [],
  search = null
) => {
  try {
    const offset = (page - 1) * limit;
    const whereObj = { ga_area_id };
    let whereText = "geographical_area_id = :ga_area_id";
    if (area_id.length > 0) {
      whereObj.area_id = area_id;
      whereText += " and mst_areas.area_id in (:area_id)";
    }
    if (search) {
      whereObj.search = `%${search}%`;
      whereText +=
        " and (mst_admin.first_name like :search or mst_admin.last_name like :search or mst_areas.area_name like :search)";
    }
    const tpiStatusQuery = `(
		SELECT count(tmlcs.tpi_engineer_id)
		FROM trx_mapped_lmc_connection_stages AS tmlcs
		JOIN trx_lmc_connection_mapping AS tlcm ON tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id
		JOIN trx_customer_connection AS tcc ON tlcm.customer_connection_id = tcc.customer_connection_id
		JOIN mst_available_pngareas AS map ON tcc.available_pngareas_id = map.available_pngareas_id
		JOIN mst_pincodes AS mp ON mp.pincode_id = map.pincode_id
		JOIN mst_areas AS ma ON ma.area_id = mp.area_id
		WHERE tmlcs.tpi_engineer_id = mst_admin.admin_id
			AND tmlcs.is_latest = 1
			AND ma.area_id = mst_areas.area_id
			AND tmlcs.lmc_stage = :lmc_stage
			AND tmlcs.tpi_status = :tpi_status
		)`;
    const sqlQuery = `SELECT mst_areas.area_name
  ,mst_areas.area_id
	,mst_agencies.agency_name
	,mst_admin.admin_id
	,mst_admin.first_name
	,mst_admin.last_name
	,mst_admin.mobile
	,mst_admin.email
	,${tpiStatusQuery.replace(":lmc_stage", "'feasibility-check'").replace(":tpi_status", "'pending'")} AS feasibilityTotalPendingApproval
	,${tpiStatusQuery.replace(":lmc_stage", "'feasibility-check'").replace(":tpi_status", "'resubmit'")} AS feasibilityTotalResubmit
	,${tpiStatusQuery.replace(":lmc_stage", "'feasibility-check'").replace(":tpi_status", "'completed'")} AS feasibilityTotalApproval
	,${tpiStatusQuery.replace(":lmc_stage", "'feasibility-check'").replace(":tpi_status", "'rejected'")} as feasibilityTotalRejected
  ,${tpiStatusQuery.replace(":lmc_stage", "'installation'").replace(":tpi_status", "'pending'")} AS installationTotalPendingApproval
	,${tpiStatusQuery.replace(":lmc_stage", "'installation'").replace(":tpi_status", "'resubmit'")} AS installationTotalResubmit
	,${tpiStatusQuery.replace(":lmc_stage", "'installation'").replace(":tpi_status", "'pending-eo'")} AS installationTotalApproval
	,${tpiStatusQuery.replace(":lmc_stage", "'installation'").replace(":tpi_status", "'rejected'")} as installationTotalRejected

FROM mst_areas
JOIN trx_admin_mapped_areas AS tama ON tama.area_id = mst_areas.area_id
JOIN mst_admin ON mst_admin.admin_id = tama.admin_id
	AND mst_admin.user_role = 'engineer'
LEFT JOIN trx_admin_contractor_other_details AS tacod ON tacod.admin_id = mst_admin.admin_id
LEFT JOIN mst_agencies ON mst_agencies.agency_id = tacod.agency_id
WHERE ${whereText} order by mst_areas.area_id OFFSET :offset ROWS
FETCH NEXT :limit ROWS ONLY;`;
    const records = await sequelize.query(sqlQuery, {
      type: Sequelize.QueryTypes.SELECT,
      replacements: { ...whereObj, offset, limit },
    });
    const countSqlQuery = `SELECT count(mst_areas.area_id) as count
    FROM mst_areas
  JOIN trx_admin_mapped_areas AS tama ON tama.area_id = mst_areas.area_id
  JOIN mst_admin ON mst_admin.admin_id = tama.admin_id
    AND mst_admin.user_role = 'engineer'
  LEFT JOIN trx_admin_contractor_other_details AS tacod ON tacod.admin_id = mst_admin.admin_id
  LEFT JOIN mst_agencies ON mst_agencies.agency_id = tacod.agency_id
  WHERE ${whereText}`;
    const countData = await sequelize.query(countSqlQuery, {
      type: Sequelize.QueryTypes.SELECT,
      replacements: whereObj,
    });
    return { records, count: countData[0].count };
  } catch (error) {
    console.error("Repo Error in getTpiEngineerByAreaWiseList", error);
  }
};
const userExistenceCheckForContractorOnboardingInSameGa = async (
  mobile,
  email,
  gaAreaId
) => {
  try {
    const existingUser = await dbModels.mst_admin.findOne({
      where: {
        [Op.or]: [{ email: email }, { mobile: mobile }],
      },
      attributes: [
        "admin_id",
        "first_name",
        "last_name",
        "user_role",
        "email",
        "mobile",
      ],
      include: [
        {
          model: dbModels.trx_admin_mapped_areas,
          as: "trx_admin_mapped_areas",
          required: false,
          attributes: ["admin_mapped_area_id"],
          include: [
            {
              model: dbModels.mst_areas,
              as: "area",
              required: false,
              attributes: ["area_id", "area_name"],
              include: [
                {
                  model: dbModels.mst_geographical_areas,
                  as: "geographical_area",
                  required: false,
                  attributes: ["geographical_area_id", "ga_area_name"],
                },
              ],
            },
          ],
        },
      ],
    });

    return existingUser;
  } catch (error) {
    console.error(
      "Error in userExistenceCheckForContractorOnboardingInSameGa:",
      error
    );
    throw error;
  }
};

const updateAreaForContractorRepo = async (admin_id, req) => {
  try {
    const area_id = req.body.area_id;
    const areaIds = Array.isArray(area_id) ? area_id : [area_id];
    const areaMappings = areaIds.map((area_id) => ({
      amga_id: 0,
      admin_id: admin_id,
      area_id,
      is_active: true,
      created_by: req.user.admin_id || 0,
      updated_by: 0,
    }));
    const insertIntomappedAreaTable =
      await dbModels.trx_admin_mapped_areas.bulkCreate(areaMappings, {
        //transaction,
        returning: false,
      });
    return insertIntomappedAreaTable;
  } catch (error) {
    console.error("Error in getChannelPartnersCount:", error);
    throw error;
  }
};
const fetchAreaNameAndGeographicalAreaRepo = async (area_id) => {
  try {
    const areaIds = Array.isArray(area_id) ? area_id : [area_id];
    const areas = await dbModels.mst_areas.findAll({
      where: {
        area_id: areaIds, // Sequelize automatically converts this to IN
      },
      attributes: ["area_id", "area_name"],
      include: [
        {
          model: dbModels.mst_geographical_areas,
          as: "geographical_area",
          attributes: ["ga_area_name"],
        },
      ],
    });
    return areas;
  } catch (error) {
    console.error("Error in fetchAreaNameAndGeographicalAreaRepo:", error);
    throw error;
  }
};

const detailsByPersonaRoleOLD = async (personaRoleId) => {
  try {
    return await mst_admin.findAll({
      where: {
        persona_role_id: personaRoleId,
      },
      attributes: ["admin_id", "user_role", "first_name", "last_name"],
      include: [
        // Contractor Onboard Flow
        {
          model: trx_admin_onboard_approve,
          as: "contractorOnboard",
          required: false,
          where: { is_active: 1 },
          attributes: [
            "created_by",
            "approved_by",
            "approve_status",
            "updated_by",
          ],
          include: [
            {
              model: mst_admin,
              as: "contractorOnboardedBy",
              attributes: ["first_name", "last_name"],
            },
            {
              model: mst_admin,
              as: "contractorApprovedBy",
              attributes: ["first_name", "last_name"],
            },
            {
              model: mst_admin,
              as: "contractorUpdatedBy",
              attributes: ["first_name", "last_name"],
            },
          ],
        },

        // Supervisor Onboard Flow
        {
          model: trx_tpi_onboard_approve,
          as: "supervisorOnboard",
          required: false,
          where: { is_active: 1 },
          attributes: ["created_by", "tpi_id", "approve_status", "updated_by"],
          include: [
            {
              model: mst_admin,
              as: "supervisorOnboardedBy",
              attributes: ["first_name", "last_name"],
            },
            {
              model: mst_admin,
              as: "supervisorApprovedBy",
              attributes: ["first_name", "last_name"],
            },
            {
              model: mst_admin,
              as: "supervisorUpdatedBy",
              attributes: ["first_name", "last_name"],
            },
          ],
        },

        // Agency Details
        {
          model: trx_admin_contractor_other_details,
          as: "contractorDetails",
          required: false,
          where: { is_active: 1 },
          include: [
            {
              model: mst_agencies,
              as: "contractorAgency",
              attributes: ["agency_name"],
            },
          ],
        },

        // Mapped Areas
        {
          model: trx_admin_mapped_areas,
          as: "mappedAreas",
          required: false,
          where: { is_active: 1 },
          include: [
            {
              model: mst_areas,
              as: "mappedArea",
              attributes: ["area_name"],
            },
          ],
        },
      ],
    });
  } catch (error) {
    console.error("Error in detailsByPersonaRole:", error);
    throw error;
  }
};

const detailsByPersonaRole = async (personaRoleId, geographicalAreaId = null) => {
  const admins = await dbModels.mst_admin.findAll({
    where: {
      persona_role_id: personaRoleId,
    },
    attributes: ["admin_id", "user_role", "first_name", "last_name"],
    include: [
      {
        model: dbModels.trx_admin_onboard_approve,
        as: "contractorOnboard",
        required: false,
        where: { is_active: 1 },
        attributes: [
          "created_by",
          "approved_by",
          "approve_status",
          "updated_by",
          "updated_at",
        ],
        include: [
          {
            model: dbModels.mst_admin,
            as: "contractorOnboardedBy",
            attributes: ["first_name", "last_name", "user_role"],
          },
          {
            model: dbModels.mst_admin,
            as: "contractorApprovedBy",
            attributes: ["first_name", "last_name", "user_role"],
          },
          {
            model: dbModels.mst_admin,
            as: "contractorUpdatedBy",
            attributes: ["first_name", "last_name", "user_role"],
          },
        ],
      },
      {
        model: dbModels.trx_tpi_onboard_approve,
        as: "supervisorOnboard",
        required: false,
        where: { is_active: 1 },
        attributes: ["created_by", "tpi_id", "approve_status", "updated_by"],
        include: [
          {
            model: dbModels.mst_admin,
            as: "supervisorOnboardedBy",
            attributes: ["first_name", "last_name", "user_role"],
          },
          {
            model: dbModels.mst_admin,
            as: "supervisorApprovedBy",
            attributes: ["first_name", "last_name", "user_role"],
          },
          {
            model: dbModels.mst_admin,
            as: "supervisorUpdatedBy",
            attributes: ["first_name", "last_name", "user_role"],
          },
        ],
      },
      {
        model: dbModels.trx_admin_contractor_other_details,
        as: "contractorDetails",
        required: false,
        where: { is_active: 1 },
        include: [
          {
            model: dbModels.mst_agencies,
            as: "contractorAgency",
            attributes: ["agency_name"],
          },
        ],
      },
      {
        model: dbModels.trx_admin_mapped_areas,
        as: "mappedAreas",
        required: false,
        where: {
          is_active: 1,
        },
        include: [
          {
            model: dbModels.mst_areas,
            as: "mappedArea",
            required: !!geographicalAreaId,
            attributes: ["area_name", "geographical_area_id"],
            where: {
              ...(geographicalAreaId && { geographical_area_id: geographicalAreaId }),
            },
          },
        ],
      },
    ],
  });

  const adminRecord = await dbModels.mst_admin.findOne({
    where: { persona_role_id: personaRoleId },
  });
  const admin_id = adminRecord.admin_id;

  const mappingData = await dbModels.trx_bpcl_admin_role_mapping.findOne({
    where: { bpcl_admin_id: admin_id },
    attributes: ["bpcl_admin_role_id"],
  });
  const bpcl_admin_role_id = mappingData?.bpcl_admin_role_id || null;

  let subAdminRole = null;
  if (bpcl_admin_role_id) {
    subAdminRole = await dbModels.mst_bpcl_admin_roles.findOne({
      where: { bpcl_admin_role_id },
      attributes: ["role_name"],
    });
  }

  const filteredAdmins = geographicalAreaId
    ? admins.filter((admin) =>
      admin.mappedAreas?.some((area) =>
        area.mappedArea?.geographical_area_id === geographicalAreaId
      )
    )
    : admins;

  const formatted = filteredAdmins.map((admin) => {
    const isContractor = admin.user_role === "contractor";
    const isSupervisor = admin.user_role === "supervisor";

    const onboard = isContractor
      ? admin.contractorOnboard
      : admin.supervisorOnboard;

    const onboardedBy = isContractor
      ? onboard?.contractorOnboardedBy
      : onboard?.supervisorOnboardedBy;

    const approvedBy = isContractor
      ? onboard?.contractorApprovedBy
      : onboard?.supervisorApprovedBy;

    const updatedBy = isContractor
      ? onboard?.contractorUpdatedBy
      : onboard?.supervisorUpdatedBy;

    const agencyName =
      admin.contractorDetails?.contractorAgency?.agency_name || "";

    const areasAssigned =
      admin.mappedAreas
        ?.map((area) => area.mappedArea?.area_name)
        .filter(Boolean)
        .join(", ") || "";

    return {
      admin_id: admin.admin_id,
      type: admin.user_role,
      DMA: `${admin.first_name} ${admin.last_name}`,
      onboarded_by_id: onboard?.created_by || null,
      onboarded_by: onboardedBy
        ? `${onboardedBy.first_name} ${onboardedBy.last_name}`
        : null,
      onboarded_by_role_name: onboardedBy?.user_role || null,
      onboarded_by_subadmin_role_name: subAdminRole?.role_name || null,
      approved_by_id: isContractor
        ? onboard?.approved_by
        : onboard?.tpi_id || null,
      approved_by: approvedBy
        ? `${approvedBy.first_name} ${approvedBy.last_name}`
        : null,
      approved_by_role_name: approvedBy?.user_role || null,
      approved_by_subadmin_role_name: subAdminRole?.role_name || null,
      status: onboard?.approve_status || null,
      updated_at: onboard?.updated_at
        ? new Date(onboard.updated_at).toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "long",
          year: "numeric",
        })
        : null,
      agency_assigned: agencyName,
      last_updated_by_id: onboard?.updated_by || null,
      last_updated_by: updatedBy
        ? `${updatedBy.first_name} ${updatedBy.last_name}`
        : null,
      areas_assigned: areasAssigned,
    };
  });

  return formatted;
};

const getContractorManagementTPIRepo = async (
  geographical_area_id,
  user_role
) => {
  try {
    const query = `
      SELECT 
        ma1.admin_id, 
        ma1.email, 
        ma1.mobile, 
        ma2.email AS onboarded_by, 
        ma2.user_role as creator_role,
        tao.approved_by, 
        tao.approve_status, 
        tao.updated_at,  
        mag.agency_name as agency_name, 
        STRING_AGG(mar.area_name, ',') AS area_names, 
        MAX(tama.updated_by) AS updated_by,
        ma3.user_role AS approved_by_role_name,
        rm1.bpcl_admin_role_id AS approved_by_role_id,
        br1.role_name AS approved_by_subadmin_role_name,
        rm2.bpcl_admin_role_id AS onboarded_by_role_id,
        br2.role_name AS onboarded_by_subadmin_role_name
   

      FROM mst_admin ma1
      LEFT JOIN mst_admin ma2 ON ma1.created_by = ma2.admin_id
      LEFT JOIN trx_admin_onboard_approve tao ON ma1.admin_id = tao.contractor_id
      LEFT JOIN mst_admin ma3 ON tao.approved_by = ma3.admin_id
      
      LEFT JOIN trx_bpcl_admin_role_mapping rm1 ON ma3.admin_id = rm1.bpcl_admin_id
      LEFT JOIN mst_bpcl_admin_roles br1 ON rm1.bpcl_admin_role_id = br1.bpcl_admin_role_id 
      LEFT JOIN trx_bpcl_admin_role_mapping rm2 ON ma2.admin_id = rm2.bpcl_admin_id
      LEFT JOIN mst_bpcl_admin_roles br2 ON rm2.bpcl_admin_role_id = br2.bpcl_admin_role_id

      LEFT JOIN trx_admin_contractor_other_details agm ON ma1.admin_id = agm.admin_id
      LEFT JOIN mst_agencies mag ON mag.agency_id = agm.agency_id
      LEFT JOIN trx_admin_mapped_areas tama ON ma1.admin_id = tama.admin_id
      LEFT JOIN mst_areas mar ON tama.area_id = mar.area_id
      WHERE 
        ma1.user_role = :user_role
        AND mar.geographical_area_id = :geographical_area_id
      GROUP BY 
        ma1.admin_id, 
        ma1.email, 
        ma1.mobile, 
        ma2.email, 
        ma2.user_role,
        tao.approved_by, 
        tao.approve_status, 
        tao.updated_at,
        mag.agency_name,
        ma3.user_role,
        rm1.bpcl_admin_role_id,
        br1.role_name,
        rm2.bpcl_admin_role_id,
        br2.role_name   
    `;

    const results = await sequelize.query(query, {
      replacements: {
        user_role,
        geographical_area_id
      },
      type: sequelize.QueryTypes.SELECT,
    });

    return results.map((row) => ({
      tpi_details: {
        admin_id: row.admin_id,
        email: row.email,
        mobile: row.mobile,
        areas: row.area_names ? row.area_names.split(",") : [],
      },
      onboarded_by: {
        email: row.onboarded_by,
        role: row.creator_role,
        onboarded_by_subadmin_role_name: row.onboarded_by_subadmin_role_name || null,
      },
      agency_details: {
        agency_name: row.agency_name,
      },
      status: {
        approved_by: row.approved_by,
        approve_status: row.approve_status,
        updated_at: row.updated_at,
        updated_by: row.updated_by,
        approved_by_role_name: row.approved_by_role_name,
        approved_by_subadmin_role_name: row.approved_by_subadmin_role_name || null,
      },
    }));
  } catch (error) {
    console.error("Error in getContractorManagementTPIRepo:", error);
    throw error;
  }
};

const getContractorManagementAreaUpdate = async (admin_id, area_id, user) => {
  const transaction = await sequelize.transaction();
  try {
    // Update trx_admin_onboard_approve table
    area_id.forEach(async (element) => {
      await dbModels.trx_admin_mapped_areas.create({
        admin_id: admin_id,
        area_id: element,
        updated_by: user,
        updated_at: new Date(),
      });
    });

    // Commit transaction
    await transaction.commit();
    return true;
  } catch (error) {
    await transaction.rollback();
    console.error("Error in bpcalAdminRepo:", error);
    throw error;
  }
};

const getAreaForAPI = async (area_id, persona_role, user_role) => {
  const getArea = await sequelize.query(
    `
    SELECT mst_admin.admin_id, mst_admin.first_name, mst_admin.last_name 
 FROM dbo.trx_admin_mapped_areas AS trx_admin_mapped_areas INNER JOIN 
   dbo.mst_admin AS mst_admin  
    ON trx_admin_mapped_areas.admin_id = mst_admin.admin_id
    AND trx_admin_mapped_areas.is_active = 1 AND trx_admin_mapped_areas.area_id = ` +
    area_id +
    `
	   
        WHERE 
		mst_admin.persona_role_id = ` +
    persona_role +
    ` AND 
		mst_admin.user_role = '` +
    user_role +
    `' AND 
		mst_admin.is_active = 1 ;
  `,
    {
      type: Sequelize.QueryTypes.SELECT,
    }
  );

  return getArea;
};

const getProfileData = async (user) => {
  return await dbModels.mst_admin.findOne({
    where: {
      admin_id: user,
      is_active: 1,
    },
    attributes: [
      "admin_id",
      "email",
      "mobile",
      "alternate_mobile",
      "first_name",
      "last_name",
      "city",
      "state",
      "bpcl_id",
      "position_id",
    ],
    include: [
      {
        model: dbModels.mst_admin,
        as: "createdBy",
        attributes: ["first_name", "last_name"],
        required: false,
        on: Sequelize.literal("mst_admin.created_by = createdBy.admin_id"),
      },
      {
        model: dbModels.trx_admin_contractor_other_details,
        as: "trx_admin_contractor_other_details",
        attributes: ["agency_id"],
        required: false,
        include: [
          {
            model: dbModels.mst_agencies,
            as: "agency",
            attributes: ["agency_name"],
            required: false,
            //               on: Sequelize.literal('tpi.tpi_id = supervisorApprovedBy.admin_id')
          },
        ],
      },
      {
        model: dbModels.trx_tpi_onboard_approve,
        as: "tpi",
        attributes: ["tpi_id", "supervisor_id"],
        required: false,
        include: [
          {
            model: dbModels.mst_admin,
            as: "supervisorApprovedBy",
            attributes: ["first_name", "last_name"],
            required: false,
            //               on: Sequelize.literal('tpi.tpi_id = supervisorApprovedBy.admin_id')
          },
        ],
      },
      {
        model: dbModels.trx_admin_mapped_areas,
        as: "mappedAreas",
        attributes: ["area_id"],
        required: false,
        include: [
          {
            model: dbModels.mst_areas,
            as: "mappedArea",
            attributes: ["area_name"],
            required: false,
            include: [
              {
                model: dbModels.mst_geographical_areas,
                as: "geographical_area",
                attributes: ["ga_area_name"],
                required: false,
              },
            ],
          },
        ],
      },
    ],
  });
};

const customerApprovalListRepo = async (ga_id, status) => {
  try {
    const internalStatuses =
      status === "closed"
        ? ["application-rejected-so", "application-approved-so"]
        : [status];

    const result = await dbModels.trx_customer_connection.findAll({
      attributes: [
        "customer_connection_id",
        "bp_id",
        "first_name",
        "last_name",
        "internal_status",
        "created_by_type",
        ["created_at", "created_on"],
        ["updated_at", "updated_on"],
      ],
      where: {
        internal_status: {
          [Op.in]: internalStatuses,
        },
      },
      include: [
        {
          model: dbModels.mst_available_pngareas,
          as: "available_pngarea",
          attributes: ["pincode"],
          include: [
            {
              model: dbModels.mst_pincodes,
              as: "pincodeAreasAlias",
              where: {
                geographical_area_id: ga_id,
              },
              attributes: [],
            },
          ],
        },
        // {
        //   model: dbModels.trx_help_with_registration,
        //   as: 'trx_help_with_registrations',
        //   attributes: ['customer_type'],
        //   required: false
        // },
        {
          model: dbModels.trx_customer_connection_tpi_mapping,
          as: "trx_customer_connection_tpi_mappings",
          where: {
            is_latest: true,
          },
          required: false,
          attributes: [
            ["updated_at", "submitted_on"],
            ["remarks", "remarks"],
          ],
          include: [
            {
              model: dbModels.mst_admin,
              as: "tpi",
              attributes: [
                ["admin_id", "admin_id"],
                ["first_name", "tpi_first_name"],
                ["last_name", "tpi_last_name"],
                ["mobile", "tpi_mobile"],
              ],
              required: false,
            },
          ],
        },
        {
          model: dbModels.mst_admin,
          as: "reviewer",
          attributes: [
            ["admin_id", "reviewer_id"],
            ["first_name", "reviewer_first_name"],
            ["last_name", "reviewer_last_name"],
          ],
          required: false,
        },
      ],
    });

    return result;
  } catch (error) {
    console.error("Error in customerApprovalListRepo:", error);
    throw error;
  }
};
const customerPersonalDetailsRepo = async (customer_connection_id) => {
  try {
    const customerDetails = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id },
      include: [
        {
          model: dbModels.trx_customer_connection_address,
          as: "trx_customer_connection_addresses",
          required: false,
        },
        {
          model: dbModels.trx_customer_proof_documents,
          as: "trx_customer_proof_documents",
          required: false,
        },
        {
          model: dbModels.mst_govt_scheme,
          attributes: ["govt_scheme_id", "scheme_name"],
          as: "govt_scheme",
          required: false,
        },
      ],
      raw: false,
      nest: true,
    });

    if (!customerDetails) {
      throw new Error("Customer application details not found");
    }

    return customerDetails;
  } catch (error) {
    console.error("Error in customerPersonalDetailsRepo:", error);
    throw error;
  }
};

const customerApprovalListEoRepo = async (ga_id, status) => {
  try {
    const tpi_status =
      status === "closed" ? ["completed-eo", "rejected-eo"] : [status];

    const result = await dbModels.trx_customer_connection.findAll({
      attributes: [
        "customer_connection_id",
        "bp_id",
        "first_name",
        "last_name",
        "created_by_type",
        ["created_at", "created_on"],
        ["updated_at", "updated_on"],
      ],
      include: [
        {
          model: dbModels.mst_available_pngareas,
          as: "available_pngarea",
          attributes: ["pincode"],
          include: [
            {
              model: dbModels.mst_pincodes,
              as: "pincodeAreasAlias",
              where: {
                geographical_area_id: ga_id,
              },
              attributes: [],
            },
          ],
        },
        // {
        //   model: dbModels.trx_help_with_registration,
        //   as: 'trx_help_with_registrations',
        //   attributes: ['customer_type'],
        //   required: false
        // },
        {
          model: dbModels.trx_customer_connection_tpi_mapping,
          as: "trx_customer_connection_tpi_mappings",
          where: {
            is_latest: true,
          },
          required: false,
          attributes: [
            ["updated_at", "submitted_on"],
            ["remarks", "remarks"],
          ],
          include: [
            {
              model: dbModels.mst_admin,
              as: "tpi",
              attributes: [
                ["admin_id", "admin_id"],
                ["first_name", "tpi_first_name"],
                ["last_name", "tpi_last_name"],
                ["mobile", "tpi_mobile"],
              ],
              required: false,
            },
          ],
        },
        {
          model: dbModels.trx_lmc_connection_mapping,
          as: "trx_lmc_connection_mappings",
          required: true,
          include: [
            {
              model: dbModels.trx_mapped_lmc_connection_stages,
              as: "trx_mapped_lmc_connection_stages",
              required: true,
              where: {
                // is_latest: true,
                lmc_stage: 'installation',
                tpi_status: {
                  [Op.in]: tpi_status,
                },
              },
              attributes: ["mapped_lmc_connection_stage_id", "tpi_status"],
            },
          ],
        },
        {
          model: dbModels.mst_admin,
          as: "reviewer",
          attributes: [
            ['admin_id', 'reviewer_id'],
            ['first_name', 'reviewer_first_name'],
            ['last_name', 'reviewer_last_name'],
            ['user_role', 'reviewer_role_name']
          ],
          required: false,
        },
      ],
    });

    return result;
  } catch (error) {
    console.error("Error in customerApprovalListEoRepo:", error);
    throw error;
  }
};

const customerApprovalListManualGmrRepo = async (ga_id, status) => {
  try {
    const tpi_status =
      status === "closed" ? ["completed-jmr-eo", "rejected-jmr-eo"] : [status];

    const result = await dbModels.trx_customer_connection.findAll({
      attributes: [
        "customer_connection_id",
        "bp_id",
        "first_name",
        "last_name",
        "created_by_type",
        ["created_at", "created_on"],
        "updated_by",
        ["updated_at", "updated_on"],
      ],
      include: [
        {
          model: dbModels.mst_available_pngareas,
          as: "available_pngarea",
          attributes: ["pincode"],
          required: true, // ensure filtering applies to the result set
          include: [
            {
              model: dbModels.mst_pincodes,
              as: "pincodeAreasAlias",
              where: {
                geographical_area_id: ga_id,
              },
              attributes: [],
              required: true, // necessary to filter customers
            },
          ],
        },
        // {
        //   model: dbModels.trx_help_with_registration,
        //   as: 'trx_help_with_registrations',
        //   attributes: ['customer_type'],
        //   required: false,
        // },
        {
          model: dbModels.trx_customer_connection_tpi_mapping,
          as: "trx_customer_connection_tpi_mappings",
          where: {
            is_latest: true,
          },
          required: false,
          attributes: [
            ["updated_at", "submitted_on"],
            ["remarks", "remarks"],
          ],
          include: [
            {
              model: dbModels.mst_admin,
              as: "tpi",
              attributes: [
                ["first_name", "tpi_first_name"],
                ["last_name", "tpi_last_name"],
                ["mobile", "tpi_mobile"],
              ],
              required: false,
            },
          ],
        },
        {
          model: dbModels.trx_customer_meter_readings,
          as: "trx_customer_meter_readings",
          attributes: [["customer_connection_id", "customer_connection_id"]],
          required: true, // ensure filtering applies to result set
          include: [
            {
              model: dbModels.trx_tpi_manual_meter_approval,
              as: "trx_tpi_manual_meter_approvals",
              where: {
                tpi_status: {
                  [Op.in]: tpi_status,
                },
                is_latest: true,
              },
              attributes: [
                ["tmma_id", "tmma_id"],
                ["tpi_status", "tpi_status"],
              ],
              required: true, // must match this to include the customer
            },
          ],
        },
        {
          model: dbModels.mst_admin,
          as: "reviewer",
          attributes: [
            ['admin_id', 'reviewer_id'],
            ['first_name', 'reviewer_first_name'],
            ['last_name', 'reviewer_last_name'],
            ['user_role', 'reviewer_role_name']
          ],
          required: false
        }
      ],
    });
    return result;
  } catch (error) {
    console.error("Error in customerApprovalListManualGmrRepo:", error);
    throw error;
  }
};

//LMC installation details

const getStageDetails = async (mapped_lmc_connection_stage_id) => {
  return dbModels.trx_mapped_lmc_connection_stages.findOne({
    where: { mapped_lmc_connection_stage_id },
    raw: true,
    include: [
      {
        model: dbModels.trx_lmc_connection_mapping,
        as: "lmc_connection_mapping",
        attributes: ["customer_connection_id"],
      },
    ],
  });
};

const getInstallationDetails = async (mapped_lmc_connection_stage_id) => {
  return dbModels.trx_mapped_lmc_installation_details.findOne({
    where: { mapped_lmc_connection_stage_id },
    raw: true,
  });
};

const getUploadedDocuments = async (mapped_lmc_connection_stage_id) => {
  return dbModels.trx_lmc_pipeline_documents.findAll({
    where: { mapped_lmc_connection_stage_id },
    raw: true,
  });
};

const getMeterDetails = async (customer_connection_id) => {
  return await dbModels.trx_customer_meter.findOne({
    where: {
      customer_connection_id: customer_connection_id,
    },
  });
};

const getLatestFeasibilityCommissioning = async (
  mapped_lmc_connection_stage_id
) => {
  return dbModels.trx_mapped_lmc_feasibility_commission_details.findOne({
    where: {
      mapped_lmc_connection_stage_id,
      is_latest: 1,
    },
    order: [["created_at", "DESC"]],
    raw: true,
  });
};

const getJmrSubmissionDetails = async (tmma_id) => {
  try {
    const result = await dbModels.trx_tpi_manual_meter_approval.findOne({
      attributes: [
        "tmma_id",
        "meter_reading_file",
        "meter_reading",
        "tpi_status",
        "created_at",
      ],
      include: [
        {
          model: dbModels.trx_customer_meter_readings,
          as: "customer_meter_reading",
          attributes: [
            "customer_meter_reading_id",
            "meter_reading_file",
            "created_at",
          ],
          include: [
            {
              model: dbModels.trx_customer_meter,
              as: "customer_meter",
              attributes: ["meter_type", "meter_no"],
            },
            {
              model: dbModels.trx_customer_connection,
              as: "customer_connection",
              attributes: ["first_name", "last_name", "mobile", "bp_id"],
            },
            {
              model: dbModels.mst_admin,
              as: "submitted_admin",
              attributes: ["first_name", "last_name", "mobile"],
            },
          ],
        },
      ],
      where: {
        tmma_id: tmma_id,
      },
      raw: true,
      nest: true,
    });

    if (!result) {
      return null;
    }

    const existingRecord = await dbModels.trx_tpi_manual_meter_approval.findOne({
      where: { tmma_id },
    });
    const customer_meter_reading_id = existingRecord.customer_meter_reading_id;

    const meterReading = await dbModels.trx_customer_meter_readings.findOne({
      where: { customer_meter_reading_id },
      attributes: ["customer_connection_id"],
    });
    const customer_connection_id = meterReading.customer_connection_id;

    const mapping = await dbModels.trx_lmc_connection_mapping.findOne({
      where: { customer_connection_id },
      attributes: ["lmc_connection_mapping_id"],
    });
    const lmc_connection_mapping_id = mapping.lmc_connection_mapping_id;

    const existingStage = await dbModels.trx_mapped_lmc_connection_stages.findOne({
      where: {
        lmc_connection_mapping_id,
        lmc_stage: "installation",
      },
      attributes: ["mapped_lmc_connection_stage_id"],
    });
    const mapped_lmc_connection_stage_id = existingStage.mapped_lmc_connection_stage_id;

    const existingConversion = await dbModels.trx_mapped_lmc_feasibility_commission_details.findOne({
      where: { mapped_lmc_connection_stage_id },
      attributes: ["ready_for_conversion"],
    });

    // Transform the result into desired format
    const formattedResult = {
      submission_id: result.tmma_id,
      customer: {
        name: `${result.customer_meter_reading?.customer_connection?.first_name || ""} ${result.customer_meter_reading?.customer_connection?.last_name || ""}`.trim(),
        mobile: result.customer_meter_reading?.customer_connection?.mobile,
        bp_id: result.customer_meter_reading?.customer_connection?.bp_id,
      },
      lmc_supervisor: {
        name: `${result.customer_meter_reading?.submitted_admin?.first_name || ""} ${result.customer_meter_reading?.submitted_admin?.last_name || ""}`.trim(),
        mobile: result.customer_meter_reading?.submitted_admin?.mobile,
      },
      meter_details: {
        type: result.customer_meter_reading?.customer_meter?.meter_type,
        number: result.customer_meter_reading?.customer_meter?.meter_no,
        reading: result.meter_reading,
        jmr_file: result.customer_meter_reading?.meter_reading_file,
        approval_file: result.meter_reading_file,
      },
      requested_on: result.created_at, // Changed from result.customer_meter_reading?.created_at to result.created_at
      status: result.tpi_status,
      ready_for_conversion: existingConversion?.ready_for_conversion || null,
    };

    return formattedResult;
  } catch (error) {
    console.error("Error in getJmrSubmissionDetails:", error);
    throw error;
  }
};

const soStatusUpdate = async ({
  customer_connection_id,
  status,
  internal_status,
  reason,
  updated_by,
  actionKey,
}) => {
  try {
    // Fetch the existing customer connection record
    const existingRecord = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id },
    });

    if (!existingRecord) {
      throw new Error("Customer connection not found.");
    }

    const previous_status = existingRecord.status;
    const previous_internal_status = existingRecord.internal_status;

    let reason_id = null;

    if (actionKey?.toLowerCase() === "reject") {
      const reasonRecord = await dbModels.mst_masters.findOne({
        where: { master_value: "document-mismatch" },
      });

      if (!reasonRecord) {
        throw new Error("Reason 'document-mismatch' not found in mst_masters.");
      }
      reason_id = reasonRecord.master_id;
    }

    if (actionKey?.toLowerCase() === "approved") {
      const reasonRecord = await dbModels.mst_masters.findOne({
        where: { master_value: "Team-visit-schedule-is-in-progress" },
      });

      if (!reasonRecord) {
        throw new Error(
          "Reason 'Team-visit-schedule-is-in-progress' not found in mst_masters."
        );
      }
      reason_id = reasonRecord.master_id;
    }

    // if (actionKey?.toLowerCase() === 'resubmission') {
    //   const reasonRecord = await dbModels.mst_masters.findOne({
    //     where: { master_value: 'update-registration-form' },
    //   });

    //   if (!reasonRecord) {
    //     throw new Error("Reason 'update-registration-form' not found in mst_masters.");
    //   }
    //   reason_id = reasonRecord.master_id;
    // }

    // Refetch updated customer connection
    const updatedRecord = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id },
    });

    // // Fetch the previous status history (2nd latest) and update currently_assigned_to_id
    // const previousStatus = await dbModels.trx_customer_connection_status_history.findOne({
    //   where: { customer_connection_id },
    //   order: [['createdAt', 'DESC']],
    //   offset: 1,
    // });

    // if (previousStatus) {
    //   await dbModels.trx_customer_connection.update(
    //     {
    //       currently_assigned_to_id: previousStatus.created_by,
    //     },
    //     {
    //       where: { customer_connection_id },
    //     }
    //   );
    // } else {
    //   console.warn(`No previous status history found for customer_connection_id: ${customer_connection_id}`);
    // }

    // Build update data object
    const connectionUpdateData = {
      status,
      internal_status,
      updated_by,
      reason_id,
    };

    let assignType, createdByType, bpclSubadminStatus, tpiStatusToUpdate;

    let newBPID = null;
    let newCANO = null;

    switch (actionKey?.toLowerCase()) {
      case "approved":
        assignType = "LMC";
        createdByType = "bpcl-sub-admin-so";
        bpclSubadminStatus = "approved";
        tpiStatusToUpdate = "approved-so";
        currentlyAssignedToId = 0;

        // Generate BP ID and CA NO
        const getLatestConnection = await dbModels.trx_customer_connection.findOne({
          attributes: ["bp_id", "ca_no"],
          where: {
            bp_id: {
              [Op.ne]: "",
            },
          },
          order: [["bp_id", "DESC"]],
          raw: true,
        });

        if (!getLatestConnection) {

          newBPID = "100000000001";
          newCANO = "1000000001";
        } else {
          newBPID = (BigInt(getLatestConnection.bp_id) + 1n).toString();
          newCANO = (BigInt(getLatestConnection.ca_no) + 1n).toString();
        }

        connectionUpdateData.bp_id = newBPID;
        connectionUpdateData.ca_no = newCANO;

        break;
      case "reject":
        assignType = "TPI";
        createdByType = "bpcl-sub-admin-so";
        bpclSubadminStatus = "rejected";
        tpiStatusToUpdate = "rejected-so";
        currentlyAssignedToId = updated_by;
        break;
      case "resubmission":
        assignType = "TPI";
        createdByType = "bpcl-sub-admin-so";
        bpclSubadminStatus = "rework";
        tpiStatusToUpdate = "rework-so";
        currentlyAssignedToId = updated_by;
        break;

      default:
        throw new Error("Invalid action key provided.");
    }

    const [updatedCount] = await dbModels.trx_customer_connection.update(connectionUpdateData, {
      where: { customer_connection_id },
    });

    if (updatedCount === 0) {
      return null;
    }

    // Fetch latest TPI mapping record
    const latestTpiMapping =
      await dbModels.trx_customer_connection_tpi_mapping.findOne({
        where: {
          customer_connection_id,
          is_latest: true,
        },
        order: [["sequence_no", "DESC"]],
      });

    const mappingId = latestTpiMapping?.customer_connection_tpi_mapping_id;
    if (!mappingId) {
      throw new Error("Mapping ID not found in TPI mapping table.");
    }

    // Update TPI mapping table
    await dbModels.trx_customer_connection_tpi_mapping.update(
      {
        tpi_status: tpiStatusToUpdate,
        remarks: reason || null,
        updated_by,
        updated_at: new Date(),
      },
      {
        where: {
          customer_connection_id,
          is_latest: true,
        },
      }
    );

    // // Insert into status history table
    // await dbModels.trx_customer_connection_status_history.create({
    //   customer_connection_id,
    //   previous_status,
    //   current_status: status,
    //   previous_internal_status,
    //   current_internal_status: internal_status,
    //   reason_id: null,
    //   description: reason || null,
    //   currently_assigned_to_type: assignType,
    //   currently_assigned_to_id: currentlyAssignedToId,
    //   is_active: true,
    //   created_by: updated_by,
    //   created_by_type: createdByType,
    //   created_at: new Date(),
    //   updated_at: new Date(),
    //   updated_by,
    // });

    // // Approval logic - fetch existing approval
    // const existingApproval = await dbModels.trx_bpcl_subadmin_connection_approval.findOne({
    //   where: {
    //     customer_connection_id,
    //     is_latest: true,
    //   },
    //   order: [['sequence_no', 'DESC']],
    // });

    // const approvalData = {
    //   customer_connection_id,
    //   mapping_id: mappingId,
    //   mapping_type: "customer-application",
    //   bpcl_subadmin_id: updated_by,
    //   bpcl_subadmin_status: bpclSubadminStatus,
    //   remarks: reason || null,
    //   is_latest: true,
    //   is_active: true,
    //   created_at: new Date(),
    //   created_by: updated_by,
    //   updated_at: new Date(),
    //   updated_by,
    // };

    // if (existingApproval) {

    //   // Insert new approval record with incremented sequence number
    //   await dbModels.trx_bpcl_subadmin_connection_approval.create({
    //     ...approvalData,
    //     sequence_no: (existingApproval.sequence_no || 0) + 1,
    //   });
    // } else {
    //   // First time approval insert
    //   await dbModels.trx_bpcl_subadmin_connection_approval.create({
    //     ...approvalData,
    //     sequence_no: 1,
    //   });
    // }
    return updatedRecord;
  } catch (error) {
    console.error("Error in soStatusUpdate repo:", error);
    throw error;
  }
};

const eoStatusUpdate = async ({
  customer_connection_id,
  updated_by,
  customerConnection,
  reason,
  actionKey,
}) => {
  const transaction = await sequelize.transaction();
  try {
    const now = new Date();

    let reason_id = null;

    if (actionKey === "reject") {
      const reasonMaster = await dbModels.mst_masters.findOne({
        where: { master_value: "onboarding-not-possible" },
        transaction,
      });

      if (!reasonMaster) {
        throw new Error(
          "Reason 'onboarding-not-possible' not found in mst_masters."
        );
      }

      reason_id = reasonMaster.master_id;
    }

    if (actionKey === "approved") {
      const reasonMaster = await dbModels.mst_masters.findOne({
        where: { master_value: "payment-security-deposit" },
        transaction,
      });

      if (!reasonMaster) {
        throw new Error(
          "Reason 'payment-security-deposit' not found in mst_masters."
        );
      }

      reason_id = reasonMaster.master_id;
    }

    // Get mapping ID
    const mapping = await dbModels.trx_lmc_connection_mapping.findOne({
      where: { customer_connection_id },
      attributes: ["lmc_connection_mapping_id"],
      transaction,
    });

    if (!mapping) throw new Error("No LMC connection mapping found.");

    const lmc_connection_mapping_id = mapping.lmc_connection_mapping_id;

    // Get existing stage (installation)
    const existingStage =
      await dbModels.trx_mapped_lmc_connection_stages.findOne({
        where: {
          lmc_connection_mapping_id,
          lmc_stage: "installation",
        },
        transaction,
      });

    if (!existingStage) throw new Error("Installation stage not found.");

    let stageUpdateData = {
      visit_status: "completed",
      updated_by,
      updated_at: now,
      reason,
    };

    let connectionUpdateData = {
      updated_by,
    };

    let insertCommissioningStage = false;

    let assignType, createdByType, bpclSubadminStatus, currentlyAssignedToId;


    switch (actionKey?.toLowerCase()) {
      case 'approved':
        stageUpdateData.tpi_status = 'completed-eo';
        connectionUpdateData.status = 'onboarding-approved';
        connectionUpdateData.internal_status = 'onboarding-approved';
        assignType = 'LMC';
        createdByType = 'bpcl-sub-admin-eo';
        bpclSubadminStatus = 'approved';
        insertCommissioningStage = true;
        currentlyAssignedToId = updated_by;


        break;

      case "reject":
        stageUpdateData.tpi_status = "rejected-eo";
        connectionUpdateData.status = "application-rejected";
        connectionUpdateData.internal_status = "onboarding-rejected";
        assignType = "TPI";
        createdByType = "bpcl-sub-admin-eo";
        bpclSubadminStatus = "rejected";
        currentlyAssignedToId = updated_by;
        break;

      case "resubmission":
        stageUpdateData.tpi_status = "resubmit-eo";
        connectionUpdateData.status = "onboarding-inprogress";
        connectionUpdateData.internal_status = "onboarding-inprogress";
        assignType = "TPI";
        createdByType = "bpcl-sub-admin-eo";
        bpclSubadminStatus = "rework";
        currentlyAssignedToId = updated_by;
        break;

      default:
        throw new Error("Invalid actionKey provided.");
    }

    // const previousStatus = await dbModels.trx_customer_connection_status_history.findOne({
    //   where: { customer_connection_id },
    //   order: [['createdAt', 'DESC']],
    //   offset: 1,
    //   transaction,
    // });

    // if (previousStatus) {
    //   await dbModels.trx_customer_connection.update(
    //     {
    //       currently_assigned_to_id: previousStatus.created_by,
    //     },
    //     {
    //       where: { customer_connection_id },
    //       transaction,
    //     }
    //   );
    // }

    // Update installation stage
    await dbModels.trx_mapped_lmc_connection_stages.update(
      {
        ...stageUpdateData,
        // is_latest: false,
      },
      {
        where: {
          lmc_connection_mapping_id,
          lmc_stage: 'installation',
        },
        transaction,
      }
    );

    // Insert commissioning stage only if approved AND not already existing
    let insertedLmcStage = null;
    if (insertCommissioningStage) {
      const existingCommissioning =
        await dbModels.trx_mapped_lmc_connection_stages.findOne({
          where: {
            lmc_connection_mapping_id,
          },
          transaction,
        });

      if (!insertedLmcStage) {
        insertedLmcStage =
          await dbModels.trx_mapped_lmc_connection_stages.create(
            {
              lmc_connection_mapping_id,
              form_no: existingStage.form_no,
              service_order_no: existingStage.service_order_no,
              service_request_no: existingStage.service_request_no,
              lmc_stage: "commissioning",
              visit_status: "not-scheduled",
              tpi_status: "no-submission",
              is_latest: true,
              is_active: true,
              created_by: updated_by,
              updated_by,
              reason,
            },
            { transaction }
          );
      }
      await tpiRepository.createPaymentEntry(
        customer_connection_id,
        updated_by
      );
    }

    // Update customer connection
    await dbModels.trx_customer_connection.update(
      {
        ...customerConnection,
        ...connectionUpdateData,
        reason,
        reason_id,
      },
      {
        where: { customer_connection_id },
        transaction,
      }
    );

    const updatedCustomer = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id },
      transaction,
    });

    // Status History
    const assignedUser = await dbModels.mst_admin.findOne({
      where: { first_name: assignType === "LMC" ? "LMC User" : "TPI User" },
    });

    const assignedToId = assignedUser?.id || null;

    // await dbModels.trx_customer_connection_status_history.create({
    //   customer_connection_id,
    //   previous_status: updatedCustomer.status,
    //   current_status: updatedCustomer.status,
    //   previous_internal_status: updatedCustomer.internal_status,
    //   current_internal_status: updatedCustomer.internal_status,
    //   reason_id: null,
    //   description: reason || null,
    //   currently_assigned_to_type: assignType,
    //   currently_assigned_to_id: currentlyAssignedToId,
    //   is_active: true,
    //   created_by: updated_by,
    //   created_by_type: createdByType,
    //   created_at: now,
    //   updated_at: now,
    //   updated_by,
    // }, { transaction });

    // // Approval Tracking
    // const approvalMappingId = insertedLmcStage?.lmc_connection_mapping_id || existingStage.lmc_connection_mapping_id;

    // const existingApproval = await dbModels.trx_bpcl_subadmin_connection_approval.findOne({
    //   where: {
    //     customer_connection_id,
    //     is_latest: true,
    //   },
    //   order: [['sequence_no', 'DESC']],
    //   transaction,
    // });

    // const baseApprovalData = {
    //   customer_connection_id,
    //   mapping_id: approvalMappingId,
    //   mapping_type: 'lmc-installation',
    //   bpcl_subadmin_id: updated_by,
    //   bpcl_subadmin_status: bpclSubadminStatus,
    //   remarks: reason || null,
    //   is_latest: true,
    //   is_active: true,
    //   created_at: now,
    //   updated_at: now,
    //   created_by: updated_by,
    //   updated_by: updated_by,
    // };

    // if (existingApproval) {

    //   await dbModels.trx_bpcl_subadmin_connection_approval.create({
    //     ...baseApprovalData,
    //     sequence_no: (existingApproval.sequence_no || 0) + 1,
    //   }, { transaction });
    // } else {
    //   await dbModels.trx_bpcl_subadmin_connection_approval.create({
    //     ...baseApprovalData,
    //     sequence_no: 1,
    //   }, { transaction });
    // }

    await transaction.commit();

    return {
      updatedCustomer,
      message: `Installation ${actionKey} processed successfully.`,
    };
  } catch (error) {
    await transaction.rollback();
    console.error("Error in eoStatusUpdate repo:", error);
    throw error;
  }
};

const eoJmrUpdate = async ({
  tmma_id,
  tpi_status,
  reason,
  updated_by,
  actionKey,
}) => {
  const transaction = await sequelize.transaction();

  try {
    // Fetch the existing TPI approval record
    const existingRecord = await dbModels.trx_tpi_manual_meter_approval.findOne(
      {
        where: { tmma_id },
        transaction,
      }
    );

    if (!existingRecord) {
      throw new Error("Record not found for given tmma_id");
    }

    const customer_meter_reading_id = existingRecord.customer_meter_reading_id;
    const previous_status = existingRecord.tpi_status;

    // Fetch the customer_connection_id from the meter reading
    const meterReading = await dbModels.trx_customer_meter_readings.findOne({
      where: { customer_meter_reading_id },
      attributes: ["customer_connection_id"],
      transaction,
    });

    if (!meterReading) {
      throw new Error(
        "Meter reading not found for given customer_meter_reading_id"
      );
    }

    const customer_connection_id = meterReading.customer_connection_id;

    // Get mapping ID
    const mapping = await dbModels.trx_lmc_connection_mapping.findOne({
      where: { customer_connection_id },
      attributes: ["lmc_connection_mapping_id"],
      transaction,
    });

    if (!mapping) throw new Error("No LMC connection mapping found.");

    const lmc_connection_mapping_id = mapping.lmc_connection_mapping_id;

    // Get existing stage (installation)
    const existingStage =
      await dbModels.trx_mapped_lmc_connection_stages.findOne({
        where: {
          lmc_connection_mapping_id,
          lmc_stage: "commissioning",
        },
        transaction,
      });

    if (!existingStage) throw new Error("Installation stage not found.")

    // Update TPI Manual Meter Approval
    const [updatedCount] = await dbModels.trx_tpi_manual_meter_approval.update(
      {
        tpi_status,
        updated_by,
        reason,
      },
      {
        where: { tmma_id },
        transaction,
      }
    );

    if (updatedCount === 0) {
      await transaction.rollback();
      return null;
    }

    const updatedRecord = await dbModels.trx_tpi_manual_meter_approval.findOne({
      where: { tmma_id },
      transaction,
    });

    // Determine metadata based on action
    let assignType = "TPI";
    let createdByType = "TPI"; //bpcl-sub-admin-eo-jmr
    let bpclSubadminStatus;
    let tpiStatusToUpdate = tpi_status;
    let connectionStatus;
    let reason_id = null;

    switch (actionKey?.toLowerCase()) {
      case "approved":
        bpclSubadminStatus = "approved";
        currentlyAssignedToId = 0;
        connectionStatus = "active-connection";

        // Update the tpi_status
        await existingStage.update(
          {
            tpi_status: "completed-eo",
            updated_at: new Date(),
          },
          { transaction }
        );
        break;
      case "reject":
        bpclSubadminStatus = "rejected";
        currentlyAssignedToId = updated_by;
        connectionStatus = "application-rejected";
        // Added: Fetch reason_id for rejection
        const reasonMaster = await dbModels.mst_masters.findOne({
          where: { master_value: "installation-not-possible" },
          transaction,
        });

        if (!reasonMaster) {
          throw new Error(
            "Reason 'installation-not-possible' not found in mst_masters."
          );
        }
        reason_id = reasonMaster.master_id;

        // Update the tpi_status
        await existingStage.update(
          {
            tpi_status: "rejected-eo",
            updated_at: new Date(), // Optional: if you are tracking timestamps
          },
          { transaction }
        );
        break;
      case "resubmission":
        bpclSubadminStatus = "rework";
        currentlyAssignedToId = updated_by;
        connectionStatus = "installation-inprogress";
        break;
      default:
        throw new Error("Invalid action key value");
    }

    // Update connection status
    if (connectionStatus) {
      const updateData = {
        status: connectionStatus,
        // reason_id,
      };

      if (actionKey === "reject") {
        updateData.internal_status = "installation-rejected";
        updateData.reason_id = reason_id;
      }

      await dbModels.trx_customer_connection.update(updateData, {
        where: { customer_connection_id },
        transaction,
      });
    }

    // const previousStatus = await dbModels.trx_customer_connection_status_history.findOne({
    //   where: { customer_connection_id },
    //   order: [['createdAt', 'DESC']],
    //   offset: 1,
    //   transaction,
    // });

    // if (previousStatus) {
    //   await dbModels.trx_customer_connection.update(
    //     {
    //       currently_assigned_to_id: previousStatus.created_by,
    //     },
    //     {
    //       where: { customer_connection_id },
    //       transaction,
    //     }
    //   );
    // }
    // const existingApproval = await dbModels.trx_bpcl_subadmin_connection_approval.findOne({
    //   where: {
    //     customer_connection_id,
    //     is_latest: true,
    //   },
    //   order: [['sequence_no', 'DESC']],
    //   transaction,
    // });

    // const baseApprovalData = {
    //   customer_connection_id,
    //   mapping_id: tmma_id,
    //   mapping_type: 'jmr-manual',
    //   bpcl_subadmin_id: updated_by,
    //   bpcl_subadmin_status: bpclSubadminStatus,
    //   remarks: reason || null,
    //   is_latest: true,
    //   is_active: true,
    //   created_at: new Date(),
    //   created_by: updated_by,
    //   updated_at: new Date(),
    //   updated_by,
    // };

    // if (existingApproval) {

    //   await dbModels.trx_bpcl_subadmin_connection_approval.create(
    //     {
    //       ...baseApprovalData,
    //       sequence_no: (existingApproval.sequence_no || 0) + 1,
    //     },
    //     { transaction }
    //   );
    // } else {
    //   await dbModels.trx_bpcl_subadmin_connection_approval.create(
    //     {
    //       ...baseApprovalData,
    //       sequence_no: 1,
    //     },
    //     { transaction }
    //   );
    // }

    // // Insert into Status History
    // await dbModels.trx_customer_connection_status_history.create(
    //   {
    //     customer_connection_id,
    //     previous_status,
    //     current_status: tpiStatusToUpdate,
    //     previous_internal_status: previous_status,
    //     current_internal_status: tpiStatusToUpdate,
    //     reason_id: null,
    //     description: reason || null,
    //     currently_assigned_to_type: assignType,
    //     currently_assigned_to_id: currentlyAssignedToId,
    //     is_active: true,
    //     created_by: updated_by,
    //     created_by_type: createdByType,
    //     created_at: new Date(),
    //     updated_at: new Date(),
    //     updated_by,
    //   },
    //   { transaction }
    // );

    await transaction.commit();
    return updatedRecord;
  } catch (error) {
    await transaction.rollback();
    console.error("Error in eoJmrUpdate repo:", error);
    throw error;
  }
};

const getSoCommentDetails = async (customer_connection_id) => {
  try {
    const approvalEntries =
      await dbModels.trx_bpcl_subadmin_connection_approval.findAll({
        where: {
          customer_connection_id,
          mapping_type: "customer-application",
        },
        include: [
          {
            model: dbModels.mst_admin,
            as: "bpcl_subadmin",
            attributes: ["first_name", "last_name", "user_role"],
          },
        ],
        attributes: [
          "updated_by",
          "remarks",
          "updated_at",
          "bpcl_subadmin_status",
          "sequence_no",
        ],
        order: [["sequence_no", "ASC"]],
      });

    const result = approvalEntries.map((entry) => {
      const fullName = entry.bpcl_subadmin
        ? `${entry.bpcl_subadmin.first_name || ""} ${entry.bpcl_subadmin.last_name || ""}`.trim()
        : null;

      const formattedDate =
        entry.updated_at instanceof Date
          ? entry.updated_at.toISOString().split("T")[0]
          : entry.updated_at
            ? new Date(entry.updated_at).toISOString().split("T")[0]
            : null;

      return {
        role: entry.bpcl_subadmin?.user_role || null,
        given_by: fullName,
        reason: entry.remarks,
        status: entry.bpcl_subadmin_status || null,
        date: formattedDate,
      };
    });

    return result;
  } catch (error) {
    console.error("Error in getSoCommentDetails repo:", error);
    throw error;
  }
};

const getEoCommentDetails = async (customer_connection_id) => {
  try {
    const approvalEntries =
      await dbModels.trx_bpcl_subadmin_connection_approval.findAll({
        where: {
          customer_connection_id,
          mapping_type: "lmc-installation",
        },
        include: [
          {
            model: dbModels.mst_admin,
            as: "bpcl_subadmin",
            attributes: ["first_name", "last_name", "user_role"],
          },
        ],
        attributes: [
          "updated_by",
          "remarks",
          "updated_at",
          "bpcl_subadmin_status",
          "sequence_no",
        ],
        order: [["sequence_no", "ASC"]],
      });

    const result = approvalEntries.map((entry) => {
      const fullName = entry.bpcl_subadmin
        ? `${entry.bpcl_subadmin.first_name || ""} ${entry.bpcl_subadmin.last_name || ""}`.trim()
        : null;

      const formattedDate =
        entry.updated_at instanceof Date
          ? entry.updated_at.toISOString().split("T")[0]
          : entry.updated_at
            ? new Date(entry.updated_at).toISOString().split("T")[0]
            : null;

      return {
        role: entry.bpcl_subadmin?.user_role || null,
        given_by: fullName,
        reason: entry.remarks,
        status: entry.bpcl_subadmin_status || null,
        date: formattedDate,
      };
    });

    return result;
  } catch (error) {
    console.error("Error in getEoCommentDetails repo:", error);
    throw error;
  }
};

const getEoJmrCommentDetails = async (customer_connection_id) => {
  try {
    const approvalEntries =
      await dbModels.trx_bpcl_subadmin_connection_approval.findAll({
        where: {
          customer_connection_id,
          mapping_type: "jmr-manual",
        },
        include: [
          {
            model: dbModels.mst_admin,
            as: "bpcl_subadmin",
            attributes: ["first_name", "last_name", "user_role"],
          },
        ],
        attributes: [
          "updated_by",
          "remarks",
          "updated_at",
          "bpcl_subadmin_status",
          "sequence_no",
        ],
        order: [["sequence_no", "ASC"]],
      });

    const result = approvalEntries.map((entry) => {
      const fullName = entry.bpcl_subadmin
        ? `${entry.bpcl_subadmin.first_name || ""} ${entry.bpcl_subadmin.last_name || ""}`.trim()
        : null;

      const formattedDate =
        entry.updated_at instanceof Date
          ? entry.updated_at.toISOString().split("T")[0]
          : entry.updated_at
            ? new Date(entry.updated_at).toISOString().split("T")[0]
            : null;

      return {
        role: entry.bpcl_subadmin?.user_role || null,
        given_by: fullName,
        reason: entry.remarks,
        status: entry.bpcl_subadmin_status || null,
        date: formattedDate,
      };
    });

    return result;
  } catch (error) {
    console.error("Error in getEoJmrCommentDetails repo:", error);
    throw error;
  }
};

const getBpclAdminGeographicalAreas = async (req) => {
  try {
    const adminId = req.user?.admin_id;
    const mappedAreas = await dbModels.trx_bpcl_admin_mapped_ga_areas.findAll({
      attributes: ['ga_area_id'],
      where: { bpcl_admin_id: adminId },
    });

    const gaAreaIds = mappedAreas.map(area => area.ga_area_id);

    if (gaAreaIds.length === 0) {
      return [];
    }
    const geographicalAreas = await dbModels.mst_geographical_areas.findAll({
      attributes: [
        'geographical_area_id',
        'region_name',
        'state_id',
        'city_id',
        'ga_area_name',
        'is_active',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
      ],
      where: {
        geographical_area_id: gaAreaIds,
      },
      order: [['region_name', 'ASC']],
    });

    return geographicalAreas;

  } catch (error) {
    console.error('Error in bpclAdminRepository.getBpclAdminGeographicalAreas:', error);
    throw error;
  }
};

module.exports = {
  getChannelPartnersCount,
  contractorOnboardingRepo,
  getAreaListRepo,
  contractorPendingApprovalRepo,
  acceptRepo,
  rejectRepo,
  checkTPISaleEngineerRepo,
  getContractorDetails,
  getSubAdminDetails,
  subAdminOnboardingRepo,
  getGaAreawithAreaIdRepo,
  getSubAdminTypeListRepo,
  checkMobileAndEmailExistInGARepo,
  getGeographicalAreaListRepo,
  getTpiSalesByAreaWiseList,
  getTpiEngineerByAreaWiseList,
  detailsByPersonaRole,
  userExistenceCheckForContractorOnboardingInSameGa,
  updateAreaForContractorRepo,
  fetchAreaNameAndGeographicalAreaRepo,
  getContractorManagementTPIRepo,
  getContractorManagementAreaUpdate,
  getAreaForAPI,
  soStatusUpdate,
  eoStatusUpdate,
  eoJmrUpdate,
  getProfileData,
  customerApprovalListRepo,
  customerPersonalDetailsRepo,
  customerApprovalListEoRepo,
  customerApprovalListManualGmrRepo,
  getStageDetails,
  getInstallationDetails,
  getUploadedDocuments,
  getMeterDetails,
  getLatestFeasibilityCommissioning,
  getJmrSubmissionDetails,
  getSoCommentDetails,
  getEoCommentDetails,
  getEoJmrCommentDetails,
  getBpclAdminGeographicalAreas,
};
