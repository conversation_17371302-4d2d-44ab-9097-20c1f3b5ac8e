const FoSupervisorRepository = require("../repository/fo-supervisor-repository");
const redisHelper = require("../helpers/redisHelper");
const message = require("../utils/constants");
const { formatReading, paginateQuery, formatAddress, formatDate } = require("../utils/commonFn");
const SASTokenHelper = require("../helpers/azureSASSDKHelper");


const getMROListing = async (req) => {
  try {
    const { page, limit, offset } = paginateQuery(req);
    const search = req.body.search || "";
    const geographical_area_id = req.body.geographical_area_id || null;

    let area_ids = []
    if (req?.body?.area_ids) {
      area_ids = req?.body?.area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(Boolean);
    }

    const { count, rows } = await FoSupervisorRepository.getMROListing(
      req.user.admin_id,
      search,
      limit,
      offset,
      geographical_area_id,
      area_ids
    );

    const formatted = rows.map((item) => ({
      mro_ticket: item.mro_ticket,
      total_customers: item.total_customers,
      assigned_date: item.assigned_date
        ? new Date(item.assigned_date).toLocaleDateString("en-GB", {
            day: "numeric",
            month: "short",
            year: "numeric",
          })
        : null,
      scheduled_mr_date: item.scheduled_mr_date
        ? new Date(item.scheduled_mr_date).toLocaleDateString("en-GB", {
            day: "numeric",
            month: "short",
            year: "numeric",
          })
        : null,
    }));

    return {
      success: true,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
      totalCount: count,
      data: formatted,
    };
  } catch (error) {
    console.error("Service error in getMROListing:", error);
    throw {
      success: false,
      statusCode: 500,
      message: "Failed to fetch MRO listing.",
      error: error.message,
    };
  }
};


const getMROTicketDetails = async (payload) => {
  try {
    const mroRecords = await FoSupervisorRepository.getMROTicketDetails(payload?.mro_ticket);
    // Format date as "dd MMM yyyy"
    const formatDate = (date) => {
      if (!date) return "-";
      return new Date(date).toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });
    };

    // Format address
    const formatAddress = (item) => {
      return [
        item["trx_meter_reading_units.customer_connection.trx_customer_connection_addresses.connection_address"],
        item["trx_meter_reading_units.customer_connection.trx_customer_connection_addresses.connection_city"],
        item["trx_meter_reading_units.customer_connection.trx_customer_connection_addresses.connection_district"],
        item["trx_meter_reading_units.customer_connection.trx_customer_connection_addresses.connection_state"],
        item["trx_meter_reading_units.customer_connection.trx_customer_connection_addresses.connection_pincode"],
      ]
        .filter(Boolean)
        .join(", ");
    };

    // Format a single record
    const formatMROTicketDetailsRaw = (item) => ({
      customer_connection_id: item["trx_meter_reading_units.customer_connection.customer_connection_id"],
      customer_name: `${item["trx_meter_reading_units.customer_connection.first_name"] || ""} ${item["trx_meter_reading_units.customer_connection.last_name"] || ""}`.trim(),
      bp_id: item["trx_meter_reading_units.customer_connection.bp_id"] || "",
      ca_no: item["trx_meter_reading_units.customer_connection.ca_no"] || "",
      email: item["trx_meter_reading_units.customer_connection.email"] || "",
      mobile_number: item["trx_meter_reading_units.customer_connection.mobile"] || "xxxxxxxxxx",
      meter_number: item["trx_meter_reading_units.customer_connection.trx_customer_meters.meter_no"] || "",
      meter_type: item["trx_meter_reading_units.customer_connection.trx_customer_meters.meter_type"] || "",
      mro_status: item["trx_meter_reading_units.mru_status"] || item.mro_status,
      mro_id: item.mro_id,
      mru_id: item["trx_meter_reading_units.mru_id"],
      scheduled_mr_date: formatDate(item["trx_meter_reading_units.scheduled_mr_date"]),
      equipment_no: item["trx_meter_reading_units.equipment_no"] || "-",
      address: formatAddress(item),
      last_meter_reading: item["trx_meter_reading_units.trx_customer_meter_readings.created_at"]
        ? formatDate(item["trx_meter_reading_units.trx_customer_meter_readings.created_at"])
        : "-",
      current_reading: item["trx_meter_reading_units.trx_customer_meter_readings.current_reading"] ?? "-",
      uploaded_by: item["trx_meter_reading_units.trx_customer_meter_readings.declared_by"] || "-",
      meter_reading_details: item["trx_meter_reading_units.trx_customer_meter_readings.current_reading"]
        ? "View"
        : "Update",
    });

    // Map all records
    const formattedRecords = mroRecords.map(formatMROTicketDetailsRaw);

    return formattedRecords;
  } catch (error) {
    console.error("Error in getMROTicketDetailsService:", error);
    throw new Error("Error occurred while fetching MRO ticket details.");
  }
};


const getDetailedMROCustomerInfo = async (payload) => {
  try {
    const customer = await FoSupervisorRepository.getDetailedMROCustomerInfo(
      payload?.customer_connection_id
    );

    console.log(customer,"customer")
    if (!customer) return null;

    const meterReadingFile =
      customer["meter_reading_units.trx_customer_meter_readings.meter_reading_file"];

    const upload = !!customer["meter_reading_units.trx_customer_meter_readings.current_reading"];
    const meterReadingFileName = meterReadingFile?.split("/").pop() || "";
    const meterReadingFilePath = meterReadingFile
      ? meterReadingFile.substring(0, meterReadingFile.lastIndexOf("/"))
      : "";

    const formatted = {
      meter_reading_file: meterReadingFile,
      meter_reading_file_name: meterReadingFileName,
      meter_reading_file_path: meterReadingFilePath,
      meter_reading_id:
        customer["meter_reading_units.trx_customer_meter_readings.customer_meter_reading_id"],
      meter_reading: customer["meter_reading_units.trx_customer_meter_readings.current_reading"],
      bp_id: customer.bp_id || "N/A",
      ca_no: customer.ca_no || "N/A",
      customer_name: `${customer.first_name} ${customer.last_name}`.trim(),
      address: formatAddress({
        permanent_address: customer["trx_customer_connection_addresses.permanent_address"],
        connection_address: customer["trx_customer_connection_addresses.connection_address"],
        connection_pincode: customer["trx_customer_connection_addresses.connection_pincode"],
        connection_city: customer["trx_customer_connection_addresses.connection_city"],
        connection_state: customer["trx_customer_connection_addresses.connection_state"],
        connection_district: customer["trx_customer_connection_addresses.connection_district"],
      }),
      mro_ticket: customer["meter_reading_units.mro.mro_ticket"] || "-",
      mru_id: customer["meter_reading_units.mru_id"], // assuming this exists
      remarks: upload ? customer["meter_reading_units.remarks"] : "",
      mro_id: customer["meter_reading_units.mro.mro_id"] || "-",
      scheduled_mr_date: formatDate(customer["meter_reading_units.scheduled_mr_date"]),
      meter_no: customer["trx_customer_meters.meter_no"],
      equipment_no: customer["meter_reading_units.equipment_no"] || "-",
      meter_reading_type:
        customer["meter_reading_units.trx_customer_meter_readings.declared_by"] || "Customer",
    };

    return formatted;
  } catch (error) {
    console.error("Service Error - getDetailedMROCustomerInfo:", error);
    throw error;
  }
};


const getGeographicalAreas = async (supervisorId) => {
  const newReading =
    await FoSupervisorRepository.getGeographicalAreas(supervisorId);

  return newReading;
};
const getFOMetricsGA = async (req) => {
  const adminId = req?.user?.admin_id;
  const geographicalAreaId = req?.body?.geographical_area_id || null;

  const {
    totalAssignedGAs,
    totalAssignedAreas,
    mroSummary,
  } = await FoSupervisorRepository.getFOMetricsGA(adminId, geographicalAreaId);

  const mroStats = mroSummary || {
    total_mros: 0,
    total_completed: 0,
    total_pending: 0,
  };

  return {
    total_assigned_gas: totalAssignedGAs || 0,
    total_assigned_areas: totalAssignedAreas || 0,
    total_mros: parseInt(mroStats.total_mros, 10) || 0,
    completed_mros: parseInt(mroStats.total_completed, 10) || 0,
    pending_mros: parseInt(mroStats.total_pending, 10) || 0,
  };
};


const saveMeterReading = async (req,supervisorId) => {
  try {
    const { current_reading, mro_id, remarks ,meter_no ,mru_id} = req.body;

    if (!current_reading || !mro_id) {
      return {
        success: false,
        message: "Missing required data (reading, connection ID, or MRO ID)."
      };
    }

    const mro = await FoSupervisorRepository.getMROById(mru_id);
    if (!mro?.cycle_start_date || !mro?.cycle_end_date) {
      return { success: false, message: "Invalid MRO or missing cycle dates." };
    }

    const customerMeter = await FoSupervisorRepository.getCustomerMeter(meter_no);
    if (!customerMeter) {
      return { success: false, message: "No active meter found for customer." };
    }

    let cycle = await FoSupervisorRepository.getCycle(mro.cycle_start_date, mro.cycle_end_date);
    if (!cycle) {
      cycle = await FoSupervisorRepository.createCycle(mro.cycle_start_date, mro.cycle_end_date);
    }

    const lastReading = await FoSupervisorRepository.getLastReading(customerMeter.customer_meter_id);
    const previousReading = lastReading?.current_reading || 0;
    const currentReading = parseFloat(current_reading);
    const readingUnits = currentReading - previousReading;
    let filePath = "customer/cus_" + customerMeter.customer_connection_id + "/fo_supervisor/meterreading";
    const permission = 'racwd';
    const sasToken = await SASTokenHelper.generateSASToken(permission);
    const imageUploaded = await SASTokenHelper.azureSingleFileUpload(req, {}, sasToken, filePath);
    filePath = filePath + "/" + imageUploaded.filename;
    const readingData = {
      customer_connection_id: customerMeter.customer_connection_id,
      customer_meter_id: customerMeter.customer_meter_id,
      previous_reading: previousReading,
      current_reading: currentReading,
      reading_units: readingUnits,
      meter_reading_file: filePath || null,
      meter_reading_status: "completed",
      declared_by: "agent",
      created_by: supervisorId || 0,
      created_at: new Date(),
      cycle_id: cycle.cycle_id,
      mru_id,
      due_date: mro.cycle_end_date,
      reason: remarks || null,
      submitted_by: supervisorId || null,
    };

    let result;
    const existingReading = await FoSupervisorRepository.getReadingForCycle(
      customerMeter.customer_meter_id,
      cycle.cycle_id
    );

    if (existingReading) {
      await FoSupervisorRepository.updateReading(existingReading.customer_meter_reading_id, readingData);
      const updated = await FoSupervisorRepository.getReadingById(existingReading.customer_meter_reading_id);

      result = {
        ...updated.dataValues,
        previous_reading: formatReading(updated.previous_reading),
        current_reading: formatReading(updated.current_reading),
        uploadStatus: "details_updated",
      };
    } else {
      const newReading = await FoSupervisorRepository.createReading(readingData);
          // req.files = imageUploaded;
      result = {
        ...newReading.dataValues,
        previous_reading: formatReading(newReading.previous_reading),
        current_reading: formatReading(newReading.current_reading),
        uploadStatus: "details_submitted",
      };
    }
    await FoSupervisorRepository.updateMROStatusAndRemark(
      mru_id,
      'completed',
      remarks || 'Meter reading successfully uploaded'
    );
    
    return {
      success: true,
      message: "Meter reading processed successfully.",
      data: result,
    };
  } catch (error) {
    console.error("Service Error:", error);
    return {
      success: false,
      message: "Error in service while saving meter reading.",
      error: error.message,
    };
  }
};

module.exports = {
  getMROListing,
  getMROTicketDetails,
  getDetailedMROCustomerInfo,
  saveMeterReading,
  getGeographicalAreas,
  getFOMetricsGA,
};
