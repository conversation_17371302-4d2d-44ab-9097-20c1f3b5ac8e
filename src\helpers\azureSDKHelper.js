const path = require('path');
const { BlobServiceClient } = require('@azure/storage-blob');

const azureSingleFileUpload = async (req) => {
  let result = {
    blob_url: null,
    error: null,
  };
  try {
    const containerName = process.env.CONTAINER_NAME;
    const connectionString = process.env.CONNECTION_STRING;
    const blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);
    //  containerClient.createIfNotExists();
    const fileObj = req.file;
    const blobName = path.basename(fileObj.path);
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    // blockBlobClient.uploadFile(fileObj.path);
    const blobHTTPHeaders = {
      blobContentType: 'image/png',
    };
    blockBlobClient.uploadFile(fileObj.path, {
      blobHTTPHeaders,
    });
    result.blob_url = blockBlobClient.url;
    console.log("Inside Helper>>>>>>>>>", blockBlobClient.url);
    return result;
  } catch (err) {
    console.error('Error uploading file:', err.message);
    result.error = err.message;
    return result;
  }
};

const deleteBlob = async (file) => {
  // Retrieve the connection string from environment variables
  const connectionString = process.env.CONNECTION_STRING;

  // Create a BlobServiceClient
  const blobServiceClient =
    BlobServiceClient.fromConnectionString(connectionString);

  // Get a reference to a container
  const containerName = process.env.CONTAINER_NAME;
  const containerClient = blobServiceClient.getContainerClient(containerName);

  // Get a reference to a blob
  const blobName = file;
  const blockBlobClient = containerClient.getBlockBlobClient(blobName);
  try {
    await blockBlobClient.getProperties();
    await blockBlobClient.delete();
    return true;
  } catch (error) {
    if (error.statusCode === 404) {
      return true;
    } else {
      return true;
    }
  }
};

module.exports = { azureSingleFileUpload, deleteBlob };
