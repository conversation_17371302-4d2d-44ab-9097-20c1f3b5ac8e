const customerProfileRepository = require("../repository/customer-profile-repository");
const redisHelper = require('../helpers/redisHelper');
// const otpHelper = require("../helpers/smsEmailHelper");
const otpService = require("./otp-service");
const generateKey = (connectionId, number) => {
  return `${connectionId}_${number}`;
};

const addUpdateEmailService = async (payload) => {
  if (payload.type == "next") {
    let result = await customerProfileRepository.addUpdateCheckEmail(payload);
    
    if (result == "no") {
      const otp = Math.floor(100000 + Math.random() * 900000);
      payload.subject = "Email Verification";
      payload.otp = otp;
      payload.template = `OTP for your request ${otp}`;
      const otpResult = await otpService.sendOtp(
        payload
      );
      console.log("OTP Response:", otpResult);
      return {
        status: result,
        message: "OTP generated and sent successfully",
        otpResponse: otpResult,
      };
    }
    return {
      status: result,
      message: "email already exist",
    };
  } else {
    let result = await customerProfileRepository.addUpdateCheckEmail(payload);
    return {
      status: result,
      message: "email updated",
    };
  }
};

const numberAddUpdate = async (payload) => {
  const result = await customerProfileRepository.addUpdateNumber(payload);
  return result ;
};

const addUpdateAltNumberService = async (payload) => {
  if (payload.type == "next") {
    let result =
      await customerProfileRepository.addUpdateCheckAltNumber(payload);
    if (result == "no") {
      const otp = Math.floor(100000 + Math.random() * 900000);
      payload.subject = "Number Verification";
      payload.otp = otp;
      payload.template = `OTP for your request ${otp}`;
      payload.number = payload.alt_number;
      const otpResult = await otpService.sendOtp(
        payload
      );
      
      console.log("OTP Response:", otpResult);
      return {
        status: result,
        message: "OTP generated and sent successfully",
        otpResponse: otpResult,
      };
    }
    return {
      status: result,
      message: "alt_number already exist",
    };
  } else {
    let result =
      await customerProfileRepository.addUpdateCheckAltNumber(payload);
    return {
      status: result,
      message: "email updated",
    };
  }
};

module.exports = {
  addUpdateEmailService,
  numberAddUpdate,
  addUpdateAltNumberService,
};
