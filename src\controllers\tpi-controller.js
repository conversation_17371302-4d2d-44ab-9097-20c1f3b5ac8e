const { sendSuccess, sendError } = require("../helpers/responseHelper");
const tpiService = require("../services/tpi-service");

exports.tpiCasesSummery = async (req, res) => {
  try {
    const result = await tpiService.getTPICasesSummery(req.query);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.tpiApplicationStatus = async (req, res) => {
  try {
    const result = await tpiService.tpiApplicationStatus(req.query);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.tpiStatusCount = async (req, res) => {
  try {
    const result = await tpiService.getStatusCount(req);
    //const result = "route working"
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

// exports.feasibilityInstallationCustomerDetail = async (req, res) => {
//   try {
//     const result = await tpiService.feasibilityInstallationCustomerDetailService(req.body);
//     //const result = "route working"
//     sendSuccess(req, res, result, 'Fetch Data successfully.');
//   } catch (error) {
//     sendError(req, res, error, error.message);
//   }
// };
exports.customerStatusHistory = async (req, res) => {
  try {
    req.body.tpi_id = req.user.admin_id;
    const response = await tpiService.customerStatusHistory(req.body);
    sendSuccess(req, res, response, 'Customer Status History data retrieved successfully');
  } catch (error) {
    console.error('Error in customerStatusHistory:', error);
    sendError(req, res, null, error.message || 'Failed to retrieve customer status history data');
  }
};

exports.tpiCustomeReview = async (req, res) => {
  try {
    const result = await tpiService.tpiCustomeReview(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.lmcWorkApprovalStatusUpdate = async (req, res) => {
  try {
    req.body.tpi_id = req.user.admin_id;
    const result = await tpiService.lmcWorkApprovalStatusUpdate(req.body);
    sendSuccess(req, res, result, "Updated Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.customerStatusUpdate = async (req, res) => {
  try {
    // Add tpi_id from token to the request body
    const params = {
      ...req.body,
      tpi_id: req.user.admin_id // Assuming the token contains user info
    };
    
    const result = await tpiService.customerStatusUpdate(params);
    sendSuccess(req, res, result, "Status updated successfully");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.lmcStatusDataList = async (req, res) => {
  try {
    req.body.tpi_id = req.user.admin_id;
    const response = await tpiService.lmcStatusDataList(req.body);
    sendSuccess(req, res, response, 'Get TPI LMC Approved Rejected Pending Data successfully');
  } catch (error) {
    console.error('Error in lmcStatusDataList:', error);
    sendError(req, res, null, error.message || 'Failed to retrieve TPI LMC Approved Rejected Pending Data List');
  }
};

exports.lmcStatusUpdate = async (req, res) => {
  try {
    req.body.tpi_id = req.user.admin_id;
    const result = await tpiService.lmcStatusUpdate(req.body);
    sendSuccess(req, res, result, "LMC Status Data Updated Successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.onboardStatusCount = async (req, res) => {
  try {
    req.body.tpi_id = req.user.admin_id;
    const result = await tpiService.onboardStatusCount(req.body);
    //const result = "route working"
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getCustomerApplicationDetails = async (req, res) => {
  try {
    const result = await tpiService.getCustomerApplicationDetailsService(req.body);
    sendSuccess(req, res, result, "Customer application details fetched successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getPendingOnboardingList = async (req, res) => {
  try {
    const result = await tpiService.getPendingOnboardingList(req.body);
    sendSuccess(req, res, result, "Pending onboarding list fetched successfully");
  } catch (error) {
    console.error('Error in getPendingOnboardingList:', error);
    sendError(req, res, error, error.message);
  }
};

exports.handleOnboardingApproval = async (req, res) => {
  try {
    req.body.admin_id = req.user.admin_id; // Add admin_id who is approving/rejecting
    const result = await tpiService.handleOnboardingApproval(req.body);
    sendSuccess(req, res, result, "Onboarding status updated successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getCustomerApplicationStatusCount = async (req, res) => {
  try {
    const result = await tpiService.getCustomerApplicationStatusCount();
    sendSuccess(req, res, result, "Application status counts fetched successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getJmrApprovalList = async (req, res) => {
  try {
    const result = await tpiService.getJmrApprovalList(req.body);
    sendSuccess(req, res, result, "JMR approval list fetched successfully");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getJmrApprovalStatusCount = async (req, res) => {
  try {
    const result = await tpiService.getJmrApprovalStatusCount(req);
    sendSuccess(req, res, result, "JMR approval status counts fetched successfully");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getJmrSubmissionDetails = async (req, res) => {
  try {
    if (!req.body.tmma_id) {
      return sendError(req, res, {}, 'tmma_id is required');
    }
    const result = await tpiService.getJmrSubmissionDetails(req.body.tmma_id);
    sendSuccess(req, res, result, "JMR submission details fetched successfully");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.updateJmrApprovalStatus = async (req, res) => {
  try {
    req.body.tpi_id = req.user.admin_id;
    const result = await tpiService.updateJmrApprovalStatus(req.body);
    sendSuccess(req, res, result, "JMR approval status updated successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getTpiCategories = async (req, res) => {
  try {
    const result = await tpiService.getTpiCategories(req.body);
    sendSuccess(req, res, result, "Categories fetched successfully");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.updateGaApproval = async (req, res) => {
  try {
    req.body.tpi_id = req.user.admin_id;
    const result = await tpiService.updateGaApproval(req.body);
    sendSuccess(req, res, result, "GA approval status updated successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getCommentDetailsTpi = async (req, res) => {
  try {
    const data = await tpiService.getCommentDetailsTpi(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in Comment Detail controller:", error);
    sendError(req, res, error, error.message);
  }
};

exports.getFeasibilityDetailsTpi = async (req, res) => {
  try {
    const result = await tpiService.getFeasibilityFormDetailsTpi(req);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getInstallationFormDataTpi = async (req, res) => {
  try {
    const payload = req.body;

    const result = await tpiService.getInstallationFormDataTpi(payload?.lmc_connection_mapping_id);

    sendSuccess(req, res, result, "Details saved successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};