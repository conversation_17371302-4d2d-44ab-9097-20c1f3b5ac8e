const lmcContractorRepository = require("../repository/lmc-contractor-repository");
const redisHelper = require('../helpers/redisHelper');
const message = require("../utils/constants");
const otpHelper = require("../helpers/smsEmailHelper");
const { mailConfig } = require("../../config");

const getLmcCustomerAssignments = async (params,contractorId) => {
  try {
    let area_ids = [];
    if (params?.area_ids) {
      area_ids = params.area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(Boolean);
    }

    
    if (!area_ids || area_ids.length === 0 ) {
      return [];
    }
    
    const result = lmcContractorRepository.getLmcCustomerAssignments(params,area_ids,contractorId);
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
 
}
const getSupervisorsTracking = async (req) => {
  try {
    const contractorId = req?.user?.admin_id;
    const area_ids = req?.query?.area_ids
      ? req.query.area_ids.split(',').map(id => parseInt(id.trim(), 10)).filter(Boolean)
      : [];

      if (!area_ids || area_ids.length === 0 ) {
        return [];
      }

    const supervisors = await lmcContractorRepository.getSupervisorsTracking(area_ids, contractorId);
    const result = { lmcSupervisors: [] };

    supervisors.forEach((supervisor) => {
      const { admin_id, first_name, last_name, trx_lmc_connection_mappings = [] } = supervisor;

      const supervisorEntry = {
        lmcSupervisorName: `${first_name} ${last_name}`,
        lmcId: admin_id,
        counts: {
          totalCustomers: 0,
          feasibilityStage: 0,
          installationStage: 0,
          commissioningStage: 0,
          rejectedTickets: 0,
          successfulActivations: 0
        }
      };

      trx_lmc_connection_mappings.forEach(mapping => {
        mapping?.trx_mapped_lmc_connection_stages?.forEach(stage => {
          const status = stage?.tpi_status;
      
          // Skip rejected or completed-eo stages from stage count
          const isRejectedOrCompletedEO = status === "rejected" || status === "rejected-eo" || status === "completed-eo";
      
          if (!isRejectedOrCompletedEO) {
            supervisorEntry.counts.totalCustomers++;
      
            switch (stage?.lmc_stage) {
              case "feasibility-check":
                supervisorEntry.counts.feasibilityStage++;
                break;
              case "installation":
                supervisorEntry.counts.installationStage++;
                break;
              case "commissioning":
                supervisorEntry.counts.commissioningStage++;
                break;
            }
          }
      
          // Still count rejected and completed for their own metrics
          if (status === "rejected" || status === "rejected-eo") {
            supervisorEntry.counts.rejectedTickets++;
          }
      
          if (status === "completed-eo") {
            supervisorEntry.counts.successfulActivations++;
          }
        });
      });
      result.lmcSupervisors.push(supervisorEntry);
    });

    return result;
  } catch (error) {
    console.error("Error in getSupervisorsTracking:", error);
    throw error;
  }
};

const getMyLMCSupervisors = async (req) => {
  try {
    const contractorId = req?.user?.admin_id;
    const customer_connection_id = req?.query?.customer_connection_id;
    let area_ids = [];

    // If customer_connection_id is provided, fetch area IDs from DB
    if (customer_connection_id) {
      const areaData = await lmcContractorRepository.getAreaIdFromCustomerConnection(customer_connection_id);

      const fetchedAreaIds = areaData?.['available_pngarea.pincodeAreasAlias.area_id'];
      if (Array.isArray(fetchedAreaIds)) {
        area_ids = [...fetchedAreaIds];
      } else if (fetchedAreaIds) {
        area_ids = [fetchedAreaIds];
      }
    }

    // If query param area_ids is present, override area_ids from above
    if (req?.query?.area_ids) {
      area_ids = req.query.area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(Boolean);
    }

    if (!area_ids || area_ids.length === 0 ) {
      return [];
    }

    const supervisors = await lmcContractorRepository.getMyLMCSupervisors(
      contractorId,
      req?.query?.is_active,
      area_ids,
    );

    const result = supervisors.map(supervisor => ({
      supervisor_id: supervisor.admin_id,
      name: `${supervisor.first_name} ${supervisor.last_name}`,
      mobile: supervisor.mobile,
      email: supervisor.email,
      onboarded_on: formatDate(supervisor.created_at),
      inactive_from: formatDate(supervisor.updated_at)
    }));

    return result;

  } catch (error) {
    console.error("Error in getMyLMCSupervisors:", error);
    throw error;
  }
};

const getAllLMCSupervisors = async (req) => {
  try {
    let area_ids = [];
    const contractorId = req?.user?.admin_id;

    if (req?.query?.area_ids) {
      area_ids = req.query.area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(Boolean);
    }

    if (!area_ids || area_ids.length === 0 ) {
      return [];
    }

    const supervisors = await lmcContractorRepository.getAllLMCSupervisors(
      req?.query?.approve_status,
      area_ids,
      contractorId
    );

    const mappedSupervisors = supervisors.map((supervisor) => {
      const data = supervisor.dataValues || supervisor; 
      const tpiApprove = data.trx_tpi_onboard_approves?.[0]?.dataValues || {};
      
      return {
        supervisor_id: data.admin_id,
        name: `${data.first_name} ${data.last_name}`,
        mobile: data.mobile,
        email: data.email,
        remark: tpiApprove.remark || null,
        onboarded_on: formatDate(data.created_at),
        rejected_date: formatDate(data.updated_at),
      };
    });

    return mappedSupervisors;

  } catch (error) {
    console.error("Error in getAllLMCSupervisors:", error);
    throw error;
  }
};
const formatDate = (date) => {
  if (!date) return null;
  return new Date(date).toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  });
};
const getDashboardMetricsCounts = async (req) => {
  try {
    const contractorId = req?.user?.admin_id;

    const area_ids = req?.query?.area_ids
      ? req.query.area_ids.split(',').map(id => parseInt(id.trim(), 10)).filter(Boolean)
      : [];

      if (!area_ids || area_ids.length === 0 ) {
        return [];
      }
    // Step 1: Get all mapped supervisors and connection data
    const supervisors = await lmcContractorRepository.getSupervisorsTracking(area_ids, contractorId);

    // Step 2: Get geographical areas mapped to this contractor
    const geographicalAreas = await lmcContractorRepository.getGeoAreaByUser(contractorId,area_ids);

    // Flatten total unique areas
    const areaSet = new Set();
    geographicalAreas.forEach(ga => {
      ga?.mst_areas?.forEach(area => {
        areaSet.add(area.area_id);
      });
    });

    const overallCounts = {
      totalSupervisors: supervisors.length,
      totalGeographicalAreas: geographicalAreas.length,
      totalAreasCovered: areaSet.size,
      totalCustomers: 0,
      feasibilityStage: 0,
      totalConnectionsInstallation: 0,
      commissioningStage: 0,
      rejectedCustomers: 0,
      activeConnections: 0,
    };

    // Step 3: Loop through supervisor mapping data to populate stage-wise counts
    supervisors.forEach((supervisor) => {
      const mappings = supervisor?.trx_lmc_connection_mappings || [];

      mappings.forEach(mapping => {
        mapping?.trx_mapped_lmc_connection_stages?.forEach(stage => {
          const status = stage?.tpi_status;
          const isRejectedOrCompletedEO = status === "rejected" || status === "rejected-eo" || status === "completed-eo";

          if (!isRejectedOrCompletedEO) {
            overallCounts.totalCustomers++;

            switch (stage?.lmc_stage) {
              case "feasibility-check":
                overallCounts.feasibilityStage++;
                break;
              case "installation":
                overallCounts.totalConnectionsInstallation++;
                break;
              case "commissioning":
                overallCounts.commissioningStage++;
                break;
            }
          }

          if (status === "rejected" || status === "rejected-eo") {
            overallCounts.rejectedCustomers++;
          }

          if (status === "completed-eo") {
            overallCounts.activeConnections++;
          }
        });
      });
    });

    return overallCounts;
  } catch (error) {
    console.error("Error in getLmcOverallCounts:", error);
    throw error;
  }
};

const formatMessageLMCSupervisorRevokeAccess = (name, lmcId, supportContact) => {
  return `
    <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <br/>

      <p>Dear <strong>${name}</strong>,</p>

      <br/>

      <p>
        We would like to inform you that your LMC Supervisor account (ID: <strong>${lmcId}</strong>) 
        has been marked as inactive. As a result, you will no longer be able to access the portal 
        using your credentials.
      </p>

      <br/>

      <p>
        If you require further assistance,  please contact <strong>${supportContact}</strong>
      </p>

      <br/>

      <p>Best regards,</p>
      <p><strong>BPCL Team</strong></p>
    </div>
  `;
};


const revokeLMCSupervisor = async (req, res) => {
  try {
    const supervisorId = req?.body?.supervisorId;
    const remark = req?.body?.remark || ''; // Default to empty remark

    if (!supervisorId) {
      return sendError(req, res, null, "Supervisor ID is required.");
    }

    const supervisorData = await lmcContractorRepository.getLMCSupervisor(supervisorId);

    if (!supervisorData) {
      return sendError(req, res, null, "Supervisor not found.");
    }

    const name = `${supervisorData.first_name} ${supervisorData.last_name}`;
    const email = supervisorData.email;

    const response = await lmcContractorRepository.revokeLMCSupervisor(supervisorId, remark);

    if (response) {
      const emailBody = formatMessageLMCSupervisorRevokeAccess(
        name,
        supervisorId,
        mailConfig.bcc
      );
      await otpHelper.emailOTPNotification(
        email,
        "Your LMC Supervisor Account Has Been Deactivated",
        emailBody
      );
    }

    return response;
  } catch (error) {
    console.error("Error in revokeLMCSupervisor:", error);
    return sendError(req, res, error, "Failed to revoke LMC Supervisor access.");
  }
};



module.exports = {
  getLmcCustomerAssignments,
  getSupervisorsTracking,
  getMyLMCSupervisors,
  getAllLMCSupervisors,
  getDashboardMetricsCounts,
  formatMessageLMCSupervisorRevokeAccess,
  revokeLMCSupervisor
};