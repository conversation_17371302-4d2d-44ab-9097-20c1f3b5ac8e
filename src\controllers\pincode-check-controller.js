const pincodeService = require("../services/pincode-check-service");
const { sendError, sendSuccess } = require("../helpers/responseHelper");
const message = require("../utils/constants");

const checkPincodeAvailability = async (req, res) => {
  try {
    const { pincode } = req.body;
    
    console.log("pincode=", pincode);
    
    // Validate required fields
    // if (!pincode) {
    //   return sendError(req, res, null, "Pincode is required");
    // }

    // Call service to check pincode availability
    const result = await pincodeService.checkPincodeAvailability(pincode);
    
    if (result === "No") {
      sendError(req, res, null, "Unserviceable PIN code");
    } else {
      // sendSuccess(req, res, { pincode, available: "Yes", result }, "Pincode details found");
      sendSuccess(req, res, result, "Pincode details found"); 
    }
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};

module.exports = { checkPincodeAvailability };
