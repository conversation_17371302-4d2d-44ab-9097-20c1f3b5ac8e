const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_lmc_pipeline_documents",
    {
      lmc_pipeline_document_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      mapped_lmc_connection_stage_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "trx_mapped_lmc_connection_stages",
          key: "mapped_lmc_connection_stage_id",
        },
      },
      file_type: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      file_name: {
        type: DataTypes.STRING(60),
        allowNull: true,
      },
      file_path: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      file_ext: {
        type: DataTypes.STRING(5),
        allowNull: true,
      },
      file_status: {
        type: DataTypes.STRING(10),
        allowNull: true,
        defaultValue: "pending",
      },
      verification_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
        defaultValue: "pending",
      },
      is_latest: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_lmc_pipeline_documents",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_lmc_pipeline_document_id",
          unique: true,
          fields: [{ name: "lmc_pipeline_document_id" }],
        },
      ],
    }
  );
};
