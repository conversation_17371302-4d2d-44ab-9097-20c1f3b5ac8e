const express = require("express");
const dbInsertController = require("../../controllers/db-insert-controller");

const dbInsertRoutes = express.Router();

dbInsertRoutes.post("/mro", dbInsertController.createMeterReadingOrder);
dbInsertRoutes.post(
  "/mapped-ga-area",
  dbInsertController.createMappedGAAreaController
);
dbInsertRoutes.post("/admin", dbInsertController.createAdminController);
dbInsertRoutes.post('/meter-reading-units', dbInsertController.createMeterReadingUnitsController);

module.exports = { dbInsertRoutes };
