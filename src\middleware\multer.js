const multer = require("multer");
const fs = require("fs");
const path = require("path");
const { sendError } = require("../helpers/responseHelper");
const {
  PLEASE_UPLOAD_FILE,
  INVALID_FILE_FRMATE,
} = require("../constant/messages");
const { FILE_OPTIONS } = require("../../config");

const crypto = require("crypto");

// Define directory paths
const rootDir = path.resolve(__dirname, "../../"); // Root directory
const publicDir = path.join(rootDir, "public");
const uploadDir = path.join(publicDir, "uploads");
const customersDir = path.join(uploadDir, "customers");

// Ensure the required directories exist
const ensureDirectoryExists = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true }); // Create directory and parent folders if necessary
  }
};

// Ensure the base directories exist at server startup
ensureDirectoryExists(customersDir);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const userId = req.bpclId || "defaultUser"; // Get user ID from request or use default
    const userDir = path.join(customersDir, userId);

    ensureDirectoryExists(userDir); // Ensure user-specific directory exists

    cb(null, userDir); // Specify the directory to save files
  },
  filename: (req, file, cb) => {
    const ext = path.extname(file.originalname);
    const bpcl_id = req.bpclId;
    const {
      proof_type,
      proof_master_id,
      customer_connection_id,
      declared_by,
      cycle_id,
    } = req.body;
    let fileName;
    const randomString = generateRandomString(6); // Generate a random 6-character string
    const timestamp = Date.now();
    // Check if additional fields exist
    if (customer_connection_id && declared_by && cycle_id) {
      fileName = `${bpcl_id}_${customer_connection_id}_${declared_by}_${cycle_id}${ext}`;
    } else if (customer_connection_id && proof_type && proof_master_id) {
      fileName = `${bpcl_id}_${proof_type}_${proof_master_id}${ext}`;
    } else {
      fileName = `bpclpng_${randomString}_${timestamp}${ext}`;
    }

    cb(null, fileName);
  },
});

// Create multer instance with storage options
const upload = multer({
  storage: storage,
  limits: { fileSize: FILE_OPTIONS.MAX_SIZE * 1024 * 1024 }, // 2 MB limit
  fileFilter: function (req, file, cb) {
    checkFileType(file, cb);
  },
});

// Check file type
function checkFileType(file, cb) {
  // Allowed ext
  const filetypes = /pdf|jpg|jpeg|png/;
  // Check ext
  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  // Check mime
  const mimetype = filetypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb({ message: INVALID_FILE_FRMATE });
  }
}

// Middleware to handle single file upload
const generateRandomString = (length = 8) => {
  return crypto.randomBytes(length).toString("hex"); // Generate a random string
};

const uploadSingle = (req, res, next) => {
  upload.single("file")(req, res, (err) => {console.log(">>>", req.file);
    if (err) {
      if (err.code === "LIMIT_FILE_SIZE") {
        return sendError(req, res, [], "File size exceeds 2 MB limit");
      }
      return sendError(req, res, [], err.message);
    }

    if (!req.file) {
      return sendError(req, res, [], PLEASE_UPLOAD_FILE);
    }

    req.file.path = `/public${req.file.path.replace(publicDir, "").replace(/\\/g, "/")}`;

    next();
  });
};

// Middleware to handle multiple file uploads (at least 2 files required)
const uploadMultiple = (req, res, next) => {
  console.log("nnnnnn", req.files);
  
  upload.array("files", 5)(req, res, (err) => {
    if (err) {
      sendError(req, res, [], err.message);
      return;
    }
    if (!req.files) {
      sendError(
        req,
        res,
        [],
        "File upload limit exceeded. Please upload at least 1 files."
      );
      return;
    }

    req.files.forEach((file) => {
      file.path = `/public${file.path.replace(publicDir, "").replace(/\\/g, "/")}`;
    });

    next();
  });
};

module.exports = { uploadSingle, uploadMultiple };
