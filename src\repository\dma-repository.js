const { dbModels } = require("../database/connection");
const { Op, Sequelize } = require("sequelize");

const createDmaSupervisor = async (supervisorData, contractorId, areaInput) => {
  try {
    // 1. Create the supervisor record
    const supervisor = await dbModels.mst_admin.create({
      first_name: supervisorData.first_name,
      last_name: supervisorData.last_name,
      email: supervisorData.email,
      mobile: supervisorData.mobile,
      alternate_mobile: supervisorData.alternate_mobile,
      password: supervisorData.password,
      user_role: "supervisor",
      persona_role_id: supervisorData.persona_role_id,
      bpcl_admin_id: supervisorData.bpclAdminId,
      is_active: false,
      created_by: contractorId,
    });

    // 2. Create approval entry
    await dbModels.trx_tpi_onboard_approve.create({
      supervisor_id: supervisor.admin_id,
      admin_type: "dma",
      approve_status: "pending",
      is_active: true,
      created_by: contractorId,
      created_at: new Date(),
    });

    // 3. Prepare area mappings
    let areaIds = areaInput;
    const areaMappings = areaIds.map((id) => ({
      admin_id: supervisor.admin_id,
      area_id: id,
      is_active: true,
      created_by: contractorId,
      created_at: new Date(),
    }));

    await dbModels.trx_admin_mapped_areas.bulkCreate(areaMappings);

    return supervisor;
  } catch (error) {
    console.error("Error in createDmaSupervisor:", error);
    throw error;
  }
};

const checkExistingDmaUser = async (email, mobile) => {
  try {
    const existingUser = await dbModels.mst_admin.findOne({
      where: {
        [Op.or]: [{ email }, { mobile }],
        is_active: true,
      },
      include: [
        {
          model: dbModels.mst_persona_role,
          as: "persona_role",
          attributes: ["persona_role_id", "role_name"],
        },
      ],
    });
    return existingUser;
  } catch (error) {
    console.error("Error in checkExistingDmaUser:", error);
    throw error;
  }
};

const getDmaSupervisorById = async (supervisorId) => {
  try {
    return await dbModels.mst_admin.findOne({
      where: {
        admin_id: supervisorId,
        user_role: "supervisor",
        is_active: true,
      },
      include: [
        {
          model: dbModels.mst_persona_role,
          as: "persona_role",
          attributes: ["persona_role_id", "role_name"],
        },
        {
          model: dbModels.trx_tpi_onboard_approve,
          as: "trx_tpi_onboard_approves",
          attributes: ["approve_status"],
          where: {
            approve_status: "approved",
          },
          required: true,
        },
      ],
    });
  } catch (error) {
    console.error("Error in getDmaSupervisorById:", error);
    throw error;
  }
};

const getAllSupervisors = async (contractorId,area_id) => {
  try {

    const supervisors = await dbModels.mst_admin.findAll({
      where: {
        created_by:contractorId,  // 471
        persona_role_id: 1,
        user_role: 'supervisor',
      },
      attributes: [
        'admin_id',
        'first_name',
        'last_name',
        'email',
        'mobile',
        'alternate_mobile',
        'persona_role_id',
        'is_active',
        'created_at',
        [
          Sequelize.literal(`
        CASE 
          WHEN mst_admin.is_active = 0 
            THEN CONVERT(VARCHAR, DATEADD(MINUTE, 330, mst_admin.updated_at), 120)
          ELSE '----'
        END
      `),
          'updated_at'
        ],
      ],
      include: [
        {
          model: dbModels.mst_persona_role,
          as: 'persona_role',
          attributes: ['persona_role_id', 'role_name'],
          required: false,
        },
        {
          model: dbModels.trx_tpi_onboard_approve,
          as: 'trx_tpi_onboard_approves', // make sure this alias matches your model
          attributes: ['approve_status'],
          where: {
            approve_status: 'approved',
          },
          required: true,
        },
        {
          model: dbModels.trx_admin_mapped_areas,
          as: 'mappedAreas', // ✅ CORRECTED alias
          where: {
            area_id: {
              [Op.in]: area_id
            },
            is_active: true,
          },
          required: true,
          attributes: [],
        },
      ],
      order: [['created_at', 'DESC']],
    });

    console.log("supervisors",supervisors)


    return supervisors;
  } catch (error) {
    console.error("Error in getAllSupervisors repo:", error);
    throw error;
  }
};

const deleteSupervisors = async (supervisorIds, contractorId) => {
  try {
    const supervisors = await dbModels.mst_admin.findAll({
      where: {
        admin_id: supervisorIds,
        is_active: true,
      },
      attributes: ["admin_id", "created_by"],
    });

    const unauthorized = [];
    const authorized = [];

    supervisors.forEach((supervisor) => {
      if (supervisor.created_by !== contractorId) {
        unauthorized.push(supervisor.admin_id);
      } else {
        authorized.push(supervisor.admin_id);
      }
    });

    if (authorized.length > 0) {
      const currentDate = new Date();
      await dbModels.mst_admin.update(
        {
          is_active: false,
          updated_by: contractorId,
          updated_at: currentDate, // Explicitly set the updated_at
        },
        {
          where: {
            admin_id: authorized,
            created_by: contractorId,
          },
          silent: false, // Ensure timestamps are updated
        }
      );
    }

    return { unauthorized };
  } catch (error) {
    console.error("Error in deleteSupervisors:", error);
    throw error;
  }
};

const getContractorSummary = async (contractorId, areaIds = []) => {
  try {
    // Step 1: Use areaIds directly as valid area IDs
    const validAreaIds = areaIds;

    if (!validAreaIds.length) {
      return {
        dma_supervisor_summary: { total_supervisors: 0 },
        dma_customer_summary: {
          total_not_started: 0,
          total_in_progress: 0,
          total_pending_approval: 0,
          total_rework: 0,
          total_approved: 0,
          total_rejected: 0,
          total_unassigned: 0,
          total_assigned: 0,
          total_dma_assigned: 0,
        },
      };
    }

    // Step 2: Get all supervisors created by this contractor
    const supervisorIds = await dbModels.mst_admin.findAll({
      where: {
        created_by: contractorId,
        persona_role_id: 1,
        user_role: "supervisor",
        is_active: true,
      },
      attributes: ["admin_id"],
      raw: true,
    }).then((supervisors) => supervisors.map((s) => s.admin_id));

    // Step 3: Get customer registration status summary
    const customerSummary = await dbModels.trx_help_with_registration.findAll({
      attributes: [
        "status",
        [Sequelize.fn("COUNT", Sequelize.col("status")), "count"],
      ],
      include: [
        {
          model: dbModels.mst_pincodes,
          as: "pincodes",
          attributes: [],
          required: true,
          where: {
            area_id: { [Sequelize.Op.in]: validAreaIds },
          },
        },
      ],
      group: ["status"],
      raw: true,
    });

    const statusCount = {
      total_not_started: 0,
      total_in_progress: 0,
      total_pending_approval: 0,
      total_rework: 0,
      total_approved: 0,
      total_rejected: 0,
    };

    customerSummary.forEach((row) => {
      switch (row.status) {
        case "not-started":
          statusCount.total_not_started = parseInt(row.count);
          break;
        case "inprogress":
          statusCount.total_in_progress = parseInt(row.count);
          break;
        case "pending-approval":
          statusCount.total_pending_approval = parseInt(row.count);
          break;
        case "rework":
          statusCount.total_rework = parseInt(row.count);
          break;
        case "approved":
          statusCount.total_approved = parseInt(row.count);
          break;
        case "rejected":
          statusCount.total_rejected = parseInt(row.count);
          break;
      }
    });

    // Step 4: Count unassigned customers
    const totalUnassigned = await dbModels.trx_help_with_registration.count({
      include: [
        {
          model: dbModels.trx_dma_help_with_reg_mapping,
          as: "trx_dma_help_with_reg_mappings",
          required: false,
          where: { is_active: true },
        },
        {
          model: dbModels.mst_pincodes,
          as: "pincodes",
          required: true,
          where: { area_id: { [Sequelize.Op.in]: validAreaIds } },
        },
      ],
      where: {
        "$trx_dma_help_with_reg_mappings.dma_help_with_reg_mapping_id$": null,
      },
    });

    // Step 5: Count assigned customers (assigned by supervisor)
    const totalAssigned = await dbModels.trx_help_with_registration.count({
      where: {
        is_active: true,
        customer_type: "assigned",
      },
      include: [
        {
          model: dbModels.trx_dma_help_with_reg_mapping,
          as: "trx_dma_help_with_reg_mappings",
          required: false,
          where: { is_active: true },
        },
        {
          model: dbModels.mst_pincodes,
          as: "pincodes",
          required: true,
          where: { area_id: { [Sequelize.Op.in]: validAreaIds } },
        },
      ],
    });

    // Step 6: Count DMA-assigned (self-registered) customers
    const totalDmaAssigned = await dbModels.trx_help_with_registration.count({
      where: {
        is_active: true,
        customer_type: "self-registered",
      },
      include: [
        {
          model: dbModels.trx_dma_help_with_reg_mapping,
          as: "trx_dma_help_with_reg_mappings",
          required: false,
          where: { is_active: true },
        },
        {
          model: dbModels.mst_pincodes,
          as: "pincodes",
          required: true,
          where: { area_id: { [Sequelize.Op.in]: validAreaIds } },
        },
      ],
    });

    // Step 7: Final response
    return {
      dma_supervisor_summary: {
        total_supervisors: supervisorIds.length,
      },
      dma_customer_summary: {
        ...statusCount,
        total_unassigned: totalUnassigned,
        total_assigned: totalAssigned,
        total_dma_assigned: totalDmaAssigned,
      },
    };
  } catch (error) {
    console.error("Error in getContractorSummary:", error);
    throw error;
  }
};

const getSupervisorPerformance = async (
  contractorId,
  page = 1,
  limit = 10,
  search = '',
  sortBy = 'supervisor_name',
  sortOrder = 'ASC'
) => {
  try {
    const sequelize = dbModels.mst_admin.sequelize;
    const Op = Sequelize.Op;

    // Fetch all approved active supervisors created by this contractor
    let supervisors = await dbModels.mst_admin.findAll({
      where: {
        created_by: contractorId,
        persona_role_id: 1,
        user_role: 'supervisor',
        is_active: true,
      },
      attributes: [
        'admin_id',
        [
          Sequelize.fn(
            'CONCAT',
            Sequelize.col('first_name'),
            ' ',
            Sequelize.col('last_name')
          ),
          'supervisor_name',
        ],
      ],
      include: [
        {
          model: dbModels.trx_tpi_onboard_approve,
          as: 'trx_tpi_onboard_approves',
          attributes: [],
          where: {
            approve_status: 'approved',
          },
          required: true,
        },
      ],
      raw: true,
    });

    // Apply search (name, or numeric supervisor_id match)
    if (search) {
      const lowerSearch = search.toLowerCase();
      supervisors = supervisors.filter((s) =>
        s.supervisor_name.toLowerCase().includes(lowerSearch) ||
        s.admin_id.toString().includes(lowerSearch)
      );
    }

    // Total after filtering
    const totalRecords = supervisors.length;
    const totalPages = Math.ceil(totalRecords / limit);
    const safePage = Math.max(1, Math.min(page, totalPages || 1));
    const offset = (safePage - 1) * limit;

    // Sort
    supervisors.sort((a, b) => {
      const valA = a[sortBy]?.toString().toLowerCase();
      const valB = b[sortBy]?.toString().toLowerCase();

      if (valA < valB) return sortOrder.toUpperCase() === 'ASC' ? -1 : 1;
      if (valA > valB) return sortOrder.toUpperCase() === 'ASC' ? 1 : -1;
      return 0;
    });

    const paginatedSupervisors = supervisors.slice(offset, offset + limit);

    // Fetch performance stats for paginated supervisors
    const results = await Promise.all(
      paginatedSupervisors.map(async (supervisor) => {
        const query = `
          SELECT 
              thr.status,
              thr.customer_type,
              COUNT(*) AS count
          FROM trx_help_with_registration thr
          INNER JOIN trx_dma_help_with_reg_mapping tdm 
              ON thr.help_with_registration_id = tdm.help_with_registration_id
          WHERE tdm.dma_supervisor_assigned_id = :supervisorId
              AND tdm.is_active = 1
          GROUP BY thr.status, thr.customer_type
        `;

        const stats = await sequelize.query(query, {
          replacements: { supervisorId: supervisor.admin_id },
          type: Sequelize.QueryTypes.SELECT,
        });

        return {
          supervisor_id: supervisor.admin_id,
          supervisor_name: supervisor.supervisor_name,
          total_assigned: stats.reduce((sum, r) => sum + parseInt(r.count), 0),
          approved: parseInt(stats.find((r) => r.status === 'approved')?.count || 0),
          inprogress: parseInt(stats.find((r) => r.status === 'inprogress')?.count || 0),
          'pending-approval': parseInt(stats.find((r) => r.status === 'pending-approval')?.count || 0),
          rework: parseInt(stats.find((r) => r.status === 'rework')?.count || 0),
          rejected: parseInt(stats.find((r) => r.status === 'rejected')?.count || 0),
          'assigned-customers': parseInt(stats.find((r) => r.customer_type === 'assigned')?.count || 0),
          'dma-registered': parseInt(stats.find((r) => r.customer_type === 'self-assigned')?.count || 0),
        };
      })
    );

    return {
      supervisors_performance: results,
      totalRecords,
      totalPages,
      currentPage: safePage,
    };
  } catch (error) {
    console.error('Error in getSupervisorPerformance:', error);
    throw error;
  }
};




const assignCustomersToSupervisor = async (
  supervisorId,
  customerIds,
  contractorId
) => {
  try {
    // First verify if supervisor is approved
    const approvedSupervisor = await dbModels.mst_admin.findOne({
      where: {
        admin_id: supervisorId,
        user_role: "supervisor",
        is_active: true,
      },
      include: [
        {
          model: dbModels.trx_tpi_onboard_approve,
          as: "trx_tpi_onboard_approves",
          where: {
            approve_status: "approved",
          },
          required: true,
        },
      ],
    });

    if (!approvedSupervisor) {
      throw new Error("Supervisor not found or not approved by TPI");
    }

    const result = {
      assigned: [],
      unassigned: [],
      alreadyAssigned: [],
      messages: {},
    };

    // Check for existing assignments
    const existingAssignments =
      await dbModels.trx_dma_help_with_reg_mapping.findAll({
        where: {
          help_with_registration_id: customerIds,
          is_active: true,
        },
        include: [
          {
            model: dbModels.mst_admin,
            as: "dma_supervisor_assigned",
            attributes: ["first_name", "last_name"],
          },
        ],
      });

    const alreadyAssignedIds = existingAssignments.map(
      (a) => a.help_with_registration_id
    );
    result.alreadyAssigned = alreadyAssignedIds;

    // Filter out already assigned customers
    const availableCustomerIds = customerIds.filter(
      (id) => !alreadyAssignedIds.includes(id)
    );

    // Verify customers exist
    const existingCustomers = await dbModels.trx_help_with_registration.findAll(
      {
        where: {
          help_with_registration_id: availableCustomerIds,
        },
      }
    );

    const validCustomerIds = existingCustomers.map(
      (c) => c.help_with_registration_id
    );
    result.unassigned = availableCustomerIds.filter(
      (id) => !validCustomerIds.includes(id)
    );

    if (validCustomerIds.length > 0) {
      // Create new assignments
      await dbModels.trx_dma_help_with_reg_mapping.bulkCreate(
        validCustomerIds.map((customerId) => ({
          help_with_registration_id: customerId,
          dma_supervisor_assigned_id: supervisorId,
          hwrm_status: "not-started",
          is_active: true,
        }))
      );

      result.assigned = validCustomerIds;
    }

    return result;
  } catch (error) {
    console.error("Error in assignCustomersToSupervisor:", error);
    throw error;
  }
};

const getCurrentAssignment = async (customerId) => {
  try {
    return await dbModels.trx_dma_help_with_reg_mapping.findOne({
      where: {
        help_with_registration_id: customerId,
        is_active: true,
      },
      include: [
        {
          model: dbModels.mst_admin,
          as: "dma_supervisor_assigned",
          attributes: ["first_name", "last_name", "created_by"],
        },
      ],
    });
  } catch (error) {
    console.error("Error in getCurrentAssignment:", error);
    throw error;
  }
};

const reassignCustomer = async (
  customerId,
  oldSupervisorId,
  newSupervisorId
) => {
  try {
    // First check if new supervisor is same as current supervisor
    if (oldSupervisorId === newSupervisorId) {
      throw new Error("Customer is already assigned to this supervisor");
    }

    // Get sequelize instance
    const sequelize = dbModels.trx_dma_help_with_reg_mapping.sequelize;

    const result = await sequelize.transaction(async (t) => {
      // 1. Deactivate current assignment
      await dbModels.trx_dma_help_with_reg_mapping.update(
        {
          is_active: false,
          updated_at: sequelize.literal("CURRENT_TIMESTAMP"),
        },
        {
          where: {
            help_with_registration_id: customerId,
            dma_supervisor_assigned_id: oldSupervisorId,
            is_active: true,
          },
          transaction: t,
        }
      );

      // 2. Get current status from help_with_registration
      const customer = await dbModels.trx_help_with_registration.findOne({
        where: { help_with_registration_id: customerId },
        attributes: ["status"],
        transaction: t,
      });

      // 3. Create new assignment
      const newAssignment = await dbModels.trx_dma_help_with_reg_mapping.create(
        {
          help_with_registration_id: customerId,
          dma_supervisor_assigned_id: newSupervisorId,
          hwrm_status: customer.status,
          is_active: true,
        },
        { transaction: t }
      );

      return newAssignment;
    });

    // Get updated assignment with supervisor details
    return await getCurrentAssignment(customerId);
  } catch (error) {
    console.error("Error in reassignCustomer:", error);
    throw error;
  }
};

const createDmaHelpWithRegMapping = async (params) => {
  const newAssignment =
    await dbModels.trx_dma_help_with_reg_mapping.create(params);
  return newAssignment;
};

const getAreaListContractor = async (req) => {
  try {
    const { geographical_area_id } = req.body;
    const admin_id = req.user.admin_id;

    const result = await dbModels.trx_admin_mapped_areas.findAll({
      where: {
        admin_id: admin_id,
      },
      include: [
        {
          model: dbModels.mst_areas,
          as: 'area',
          where: {
            geographical_area_id: geographical_area_id,
          },
          attributes: ['area_id', 'geographical_area_id', 'area_name'],
        },
      ],
    });

    // Format the response to include only the required fields
    const formattedData = result.map((item) => ({
      admin_id: item.admin_id,
      area_id: item.area.area_id,
      geographical_area_id: item.area.geographical_area_id,
      area_name: item.area.area_name,
    }));

    return formattedData;
  } catch (error) {
    console.error("Error in getAreaListContractor:", error);
    throw error;
  }
};

const getAllSupervisorsList = async (
  contractorId,
  area_id,
  page,
  limit,
  search = '',
  sortBy = 'created_at',
  sortOrder = 'DESC'
) => {
  try {
    const offset = (page - 1) * limit;

    // First, fetch ALL matching IDs using global search
    const searchCondition = search
      ? {
          [Op.or]: [
            { first_name: { [Op.like]: `%${search}%` } },
            { last_name: { [Op.like]: `%${search}%` } },
            { email: { [Op.like]: `%${search}%` } },
            { mobile: { [Op.like]: `%${search}%` } },
          ],
        }
      : {};

    const baseWhere = {
      created_by: contractorId,
      persona_role_id: 1,
      user_role: 'supervisor',
    };

    // Step 1: Get all matching admin_ids (global search)
    const allMatchingSupervisors = await dbModels.mst_admin.findAll({
      attributes: ['admin_id'],
      where: {
        ...baseWhere,
        ...searchCondition,
      },
      include: [
        {
          model: dbModels.trx_admin_mapped_areas,
          as: 'mappedAreas',
          where: {
            area_id: { [Op.in]: area_id },
            is_active: true,
          },
          required: true,
          attributes: [],
        },
        {
          model: dbModels.trx_tpi_onboard_approve,
          as: 'trx_tpi_onboard_approves',
          required: true,
          attributes: [],
        },
      ],
      raw: true,
    });

    const matchingIds = allMatchingSupervisors.map(item => item.admin_id);
    const totalRecords = matchingIds.length;
    const totalPages = Math.ceil(totalRecords / limit);
    const safePage = page > totalPages ? totalPages : page;
    const safeOffset = (safePage - 1) * limit;

    // Step 2: Paginate those IDs
    const supervisors = await dbModels.mst_admin.findAll({
      where: {
        admin_id: { [Op.in]: matchingIds },
      },
      attributes: [
        'admin_id',
        'first_name',
        'last_name',
        'email',
        'mobile',
        'alternate_mobile',
        'persona_role_id',
        'is_active',
        'created_at',
        [
          Sequelize.literal(`
            CASE 
              WHEN mst_admin.is_active = 0 
                THEN CONVERT(VARCHAR, DATEADD(MINUTE, 330, mst_admin.updated_at), 120)
              ELSE '----'
            END
          `),
          'updated_at',
        ],
      ],
      include: [
        {
          model: dbModels.mst_persona_role,
          as: 'persona_role',
          attributes: ['persona_role_id', 'role_name'],
          required: false,
        },
        {
          model: dbModels.trx_tpi_onboard_approve,
          as: 'trx_tpi_onboard_approves',
          attributes: ['approve_status'],
          required: true,
        },
        {
          model: dbModels.trx_admin_mapped_areas,
          as: 'mappedAreas',
          where: {
            area_id: {
              [Op.in]: area_id,
            },
            is_active: true,
          },
          required: true,
          attributes: [],
        },
      ],
      order: [[sortBy, sortOrder.toUpperCase()]],
      offset: safeOffset,
      limit,
      distinct: true,
    });

    return {
      supervisors,
      totalRecords,
      totalPages,
      currentPage: safePage,
    };
  } catch (error) {
    console.error('Error in getAllSupervisorsList repo:', error);
    throw error;
  }
};

module.exports = {
  createDmaSupervisor,
  checkExistingDmaUser,
  getDmaSupervisorById,
  getAllSupervisors,
  deleteSupervisors,
  getContractorSummary,
  getSupervisorPerformance,
  assignCustomersToSupervisor,
  getCurrentAssignment,
  reassignCustomer,
  createDmaHelpWithRegMapping,
  getAreaListContractor,
  getAllSupervisorsList
};
