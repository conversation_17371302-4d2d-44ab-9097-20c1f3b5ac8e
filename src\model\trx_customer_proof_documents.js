const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_customer_proof_documents', {
    customer_proof_document_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    customer_connection_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer_connection',
        key: 'customer_connection_id'
      }
    },
    file_size: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    customer_connection_address_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer_connection_address',
        key: 'customer_connection_address_id'
      }
    },
    proof_type: {
      type: DataTypes.STRING(30),
      allowNull: true
    },
    proof_master_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_proof_master',
        key: 'proof_master_id'
      }
    },
    file_name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    file_path: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    file_ext: {
      type: DataTypes.STRING(4),
      allowNull: false
    },
    verification_status: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_customer_proof_documents',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_customer_proof_document_id",
        unique: true,
        fields: [
          { name: "customer_proof_document_id" },
        ]
      },
    ]
  });
};
