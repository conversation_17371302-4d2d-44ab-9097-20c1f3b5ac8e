const { Joi } = require('express-validation');
const moment = require('moment');


const getLmcCustomerAssignmentTicketsValidation = {
  query: Joi.object({
    approve_status: Joi.number().integer().optional()
  })
};


const supervisorOnboardingValidation = {
  body: Joi.object({
    approve_status: Joi.string()
      .valid('pending', 'rejected', 'active', 'inactive')
      .required(),
      
    search: Joi.string().allow('').optional(),

    page: Joi.number().integer().min(1).default(1),

    limit: Joi.number().integer().min(1).default(10),

    fromDate: Joi.date().iso().optional(),

    toDate: Joi.date().iso().optional()
  })
};

const createSupervisorValidation = {
  body: Joi.object({
    first_name: Joi.string().trim().min(2).max(50).required(),
    middle_name: Joi.string().trim().min(2).max(50).allow('').optional(),
    last_name: Joi.string().trim().min(2).max(50).required(),
    email: Joi.string().trim().email().required(),
    mobile: Joi.string()
      .trim()
      .pattern(/^[6-9]\d{9}$/)
      .required(),
    area_ids: Joi.string()
      .pattern(/^\d+(,\d+)*$/)
      .optional()
      .messages({
        "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
      })
  }),
};

const revokeSupervisorValidation = {
  body: Joi.object({
    supervisorId: Joi.required().messages({
      "any.required": "Supervisor ID is required."
    })
  })
};
const mySupervisorValidation = {
  body: Joi.object({
    is_active: Joi.boolean()
      .truthy('true', 'false', 1, 0, '1', '0') // Converts these values to boolean
      .optional()
      .messages({
        "boolean.base": "is_active must be either true or false."
      }),
    status: Joi.string(),
    area_ids: Joi.string()
      .pattern(/^\d+(,\d+)*$/)
      .optional()
      .messages({
        "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
      })
  })
};

const geographicalAreasValidation = {
  query: Joi.object().empty(), // Ensures no query parameters are passed
  body: Joi.object().empty()   // Ensures no request body is sent
};

const dashboardMetricsValidation = {
  query: Joi.object({
    geographical_area_id: Joi.number().integer().positive().optional()
      .messages({
        "number.base": "geographical_area_id must be a number.",
        "number.integer": "geographical_area_id must be an integer.",
        "number.positive": "geographical_area_id must be a positive number."
      })
  })
};

const supervisorAssignValidation = {
  body: Joi.object({
    supervisorId: Joi.required().messages({
      "any.required": "Supervisor ID is required."
    }),
    customerConnectionId: Joi.required().messages({
      "any.required": "Supervisor ID is required."
    })
  })
};

const supervisorTicketAssignValidation = {
  query: Joi.object({
    customer_connection_id: Joi.number().integer().positive().required()
  })
};

const mySupervisorMROListingValidation = {
  body: Joi.object({
    supervisorId: Joi.number().integer().positive().required(),
    area_ids: Joi.string()
      .pattern(/^\d+(,\d+)*$/)
      .required()
      .messages({
        "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
      })
  })
};


module.exports = {
  getLmcCustomerAssignmentTicketsValidation,
  supervisorOnboardingValidation,
  createSupervisorValidation,
  revokeSupervisorValidation,
  mySupervisorValidation,
  geographicalAreasValidation,
  dashboardMetricsValidation,
  supervisorAssignValidation,
  supervisorTicketAssignValidation,
  mySupervisorMROListingValidation
};
