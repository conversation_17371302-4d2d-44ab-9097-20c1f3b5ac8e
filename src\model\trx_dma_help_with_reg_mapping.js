const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_dma_help_with_reg_mapping",
    {
      dma_help_with_reg_mapping_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      help_with_registration_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_help_with_registration",
          key: "help_with_registration_id",
        },
      },
      dma_supervisor_assigned_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
      },
      hwrm_status: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: "not-started",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_dma_help_with_reg_mapping",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_dma_help_with_reg_mapping_id",
          unique: true,
          fields: [{ name: "dma_help_with_reg_mapping_id" }],
        },
      ],
    }
  );
};
