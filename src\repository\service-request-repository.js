const { dbModels } = require("../database/connection");
const { PROGRESS_STATUS, NAME_TRANSFER, NAME_TRANSFER_EMAIL_SUBJECT } = require("../utils/constants");
const dashBoardRepository = require('../repository/dash-board-repository');

const insertChangeAddressRequest = async (payload) => {
  try {
    return await dbModels.trx_change_address_request.create(payload);
  } catch (error) {
    console.error(
      "Error inserting insertChangeAddressRequest:",
      error
    );
    throw error;
  }
};
const updateChangeAddressRequest = async (updateData,where) => {
  try {
    return await dbModels.trx_change_address_request.update(updateData,{where});
  } catch (error) {
    console.error(
      "Error inserting insertChangeAddressRequest:",
      error
    );
    throw error;
  }
};
const findCustomerAddressId = async (customerConnectionId) => {
  try {
    const customerAddress =
      await dbModels.trx_customer_connection_address.findOne({
        where: { customer_connection_id: customerConnectionId },
      });

    return customerAddress;
  } catch (error) {
    console.error("Error fetching customer address:", error.message);
    throw error;
  }
};

const extraPoints = async (
  customerConnectionId,
  extraPointTypes,
  customerAddId
) => {
  try {
    const existingRequests = await dbModels.trx_extra_point_request.findAll({
      attributes: [
        "extra_point_request_id",
        "created_at"
      ],
      where: {
        customer_connection_id: customerConnectionId,
        extra_point_type: extraPointTypes,
      },
      order: [["createdAt", "DESC"]],
    });

    if (existingRequests.length > 0) {
      const latestRequest = existingRequests[0];
      const error = new Error("Reported Issue Already Exist");
      error.details = latestRequest;
      throw error;
      // const alertMessage = `Request already exists Request ID: ${latestRequest.extra_point_request_id} Your ${latestRequest.extra_point_type} request was submitted on ${latestRequest.createdAt.toDateString()}. We'll contact you soon at your registered number and email address.`;

      // throw new Error(alertMessage);
    }

    const newRequests = await Promise.all(
      extraPointTypes.map((extraPointType) =>
        dbModels.trx_extra_point_request.create({
          customer_connection_address_id: customerAddId,
          customer_connection_id: customerConnectionId,
          extra_point_type: extraPointType,
        })
      )
    );

    console.log("Extra Point Requests Inserted:", newRequests);
    return newRequests;
  } catch (error) {
    console.error("Error processing extra point requests:", error);
    throw error;
  }
};

const addConnectionRequestService = async (payload) => {
  try {
    return await dbModels.trx_connection_request.create(payload);
  } catch (error) {
    console.error(
      "Error inserting connection Request request:",
      error.message
    );
    throw error;
  }
};
const alterationModificationRequest = async (payload) => {
  try {
    const {customer_connection_id,customer_connection_address_id,area_id,description}=payload;
    // Check if a transfer request already exists for the given connection ID
    const existingRequest = await dbModels.trx_alteration_request.findOne({
      attributes: [
        "alteration_request_id",
        "created_at"
      ],
      where: { customer_connection_id: customer_connection_id },
    });

    if (existingRequest) {
      const error = new Error("Reported Issue Already Exist");
      error.details = existingRequest;
      throw error;
      // throw new Error(
      //   `Request already exists Request ID: ${existingRequest.alteration_request_id} Your Alteration/Modification for Interior Changes request was submitted on ${existingRequest.connection_transfer_date}. We'll contact you soon at your registered number and email address.`
      // );
    }

    // Insert new transfer request if no existing request is found
    const newRequest = await dbModels.trx_alteration_request.create({
      customer_connection_id: customer_connection_id,
      customer_connection_address_id: customer_connection_address_id,
      area_id: area_id,
      description:description,
      alteration_request_status:PROGRESS_STATUS.PENDING,
    });

    return newRequest;
  } catch (error) {
    console.error('Error inserting alteration/modification request:', error.message);
    throw error;
  }
};
const checkConnectionRequestExist = async (where) => {
  console.log("where", where);

  // Check if a transfer request already exists for the given connection ID
  return await dbModels.trx_connection_request.findOne({
    attributes: [
      "connection_request_id",
      "created_at",
    ],
    where,
    include: [
      {
        model: dbModels.mst_masters,
        as: "request_reason",
        attributes: ["master_name"],
      },
    ],
    raw: false, // Ensures Sequelize returns model instances
    nest: true, // Ensures nested objects are properly structured
  });
};
const checkNewCustomerExistOrNotInDb = async (mobile) => {
  try {
    const checkCustomer =
      await dbModels.trx_customer_connection.findOne({
        where: { mobile: mobile },
      });

    return checkCustomer;
  } catch (error) {
    console.error("Error fetching customer:", error.message);
    throw error;
  }
};
const checkChangeAddressRequestExist = async (where) => {
  console.log("where", where);
  return await dbModels.trx_change_address_request.findOne({
    attributes: [
      "change_address_request_id",
      "request_ticket_no",
      "address_request_status",
      "request_status",
      "created_at"
    ],
    where
  });


};

const getPaymentDetials = async () => {
  try {
    const getConnectionServiceId = await dbModels.mst_connection_services.findOne({
      where: { service_slug: "name-transfer" },
    });
    if (!getConnectionServiceId) {
      throw new Error("Connection service not found.");
    }
    const serviceId = getConnectionServiceId.connection_service_id;

    const getPyamentValue =
      await dbModels.mst_service_rates.findOne({
        where: { connection_service_id: serviceId },
      });

    return getPyamentValue;
  } catch (error) {
    console.error("Error fetching payment details:", error.message);
    throw error;
  }
};

const nameTransferSaveToDbRequest = async (payload,connectionServiceId) => {
  try {
    const {
      existing_connection_id,
      new_connection_id,
      name_transfer_reason,
      prefix,
      first_name,
      middle_name,
      last_name,
      mobile,
      alt_mobile,
      created_by,
    } = payload;
    const date = new Date();
    const formattedDate = date.toISOString().slice(0,10).replace(/-/g, ''); 
    const randomDigits = Math.floor(100 + Math.random() * 900);

    // check name transfer request already exists for the given connection ID
    const existingRequest = await dbModels.trx_customer_name_transfer_request.findOne({
      attributes: ["name_transfer_request_id", "created_at"],
      where: { existing_connection_id: existing_connection_id,
        new_connection_id: new_connection_id,
        name_transfer_reason: name_transfer_reason,
        name_transfer_status: PROGRESS_STATUS.PENDING
      },
      raw: true, // Ensures Sequelize returns model instances
      nest: true, // Ensures nested objects are properly structured
    });
    console.log("existingRequest",existingRequest);
    
    if (existingRequest) {
      const error = new Error("Request Already Exist");
      error.details = existingRequest;
      throw error;
      // throw new Error(
      //   `Request already exists Request ID: ${existingRequest.name_transfer_request_id} Your Name Transfer request was submitted on ${existingRequest.createdAt.toDateString()}. We'll contact you soon at your registered number and email address.`
      // );
    }

    // Insert name transfer request 
    const newRequest = await dbModels.trx_customer_name_transfer_request.create({
      existing_connection_id: existing_connection_id,
      new_connection_id: new_connection_id,
      name_transfer_reason: name_transfer_reason,
      prefix:prefix,
      first_name:first_name,
      middle_name:middle_name,
      last_name:last_name,
      mobile:mobile,
      alt_mobile:alt_mobile,
    });
    const rate = await dbModels.mst_service_rates.findOne({
      where: {
        connection_service_id: connectionServiceId,
        is_active: 1
      },
      include: [
        {
          model: dbModels.mst_connection_services,
          as: "connection_service",
          attributes: ["service_name","service_slug"],
        }
      ]
    });

    console.log("rate",rate);
    const subtotal = rate.rates+rate.handling_fees;
    const gstAmount = (subtotal*rate.gst_percentage)/100;
    const totalAmount = subtotal + gstAmount;
    
    if (rate) {
      await dbModels.trx_connection_payments.create({
        customer_connection_id: existing_connection_id,
        section_type: rate.connection_service.service_slug,
        transaction_id:`TXN${formattedDate}${randomDigits}`,
        base_amount: rate.rates,
        gst_type: 'cgst',
        gst_amount:gstAmount,
        total_amount: totalAmount,
        payment_status: 'completed',
        due_date: null,
        payment_method: "online",
      })
    }
    console.log("newRequest=", newRequest)
    if (newRequest.name_transfer_request_id) {
      const insertNotification = {
        customer_connection_id : newRequest.new_connection_id,
        alert_type : 'Pending',
        subject : NAME_TRANSFER_EMAIL_SUBJECT,
        description :  `${first_name} ${last_name} has initiated a name transfer request in your name`,
        is_notify : 0,
        is_active : 1,
        created_at : new Date(),
        created_by : newRequest.existing_connection_id,
        alert_category : NAME_TRANSFER,
        alert_request_id: newRequest.name_transfer_request_id,
      }
      await dashBoardRepository.createNotification(insertNotification); 
    }
      
    return newRequest;
  } catch (error) {
    console.error('Error inserting name tranfer request:', error.message);
    throw error;
  }
};


const transferApprovalStatusUpdate = async (updateData,where) => {
  console.log("updateData=", updateData);
  console.log("where=", where);
  // return false;
  try {
    return await dbModels.trx_customer_name_transfer_request.update(updateData,{where});
  } catch (error) {
    console.error(
      "Error Transfer Approval Status Update:",
      error.message
    );
    throw error;
  }
};


const getSingleNameTransferRequestInfo = async (whereCondition) => {
  try {
    const customerReqInfo =
      await dbModels.trx_customer_name_transfer_request.findOne({
        where: whereCondition,
        attributes: ["existing_connection_id","new_connection_id", "first_name", "last_name"]
      });
    return customerReqInfo;
  } catch (error) {
    console.error("Error get Single Name Transfer Request Info:", error.message);
    throw error;
  }
};

module.exports = {
  insertChangeAddressRequest,
  findCustomerAddressId,
  extraPoints,
  alterationModificationRequest,
  checkNewCustomerExistOrNotInDb,
  addConnectionRequestService,
  checkConnectionRequestExist,
  checkChangeAddressRequestExist,
  getPaymentDetials,
  updateChangeAddressRequest,
  nameTransferSaveToDbRequest,
  transferApprovalStatusUpdate,
  getSingleNameTransferRequestInfo
};
