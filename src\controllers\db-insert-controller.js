const dbInsertService = require("../services/db-insert-service");

async function createMeterReadingOrder(req, res) {
  try {
    const result = await dbInsertService.createMRO(req.body);
    res.status(200).json({ success: true, message: 'MRO created successfully', data: result });
  } catch (error) {
    console.error("Error creating MRO:", error.message);
    res.status(400).json({ success: false, message: error.message });
  }
}

const createMappedGAAreaController = async (req, res) => {
  try {
    const data = req.body;
    const result = await dbInsertService.createMappedGAAreaService(data);
    res.status(200).json({ success: true, message: 'GA Admin created successfully', data: result });
  } catch (error) {
    console.error("Error in createMappedGAAreaController:", error);
    res.status(400).json({ success: false, message: error.message });
  }
};

const createAdminController = async (req, res) => {
  try {
    const adminData = req.body;
    const result = await dbInsertService(adminData);
    res.status(200).json({ success: true, message: 'Admin created successfully', data: result });
  } catch (error) {
    console.error('Error in createAdminController:', error);
    res.status(400).json({ success: false, error: error.message });
  }
};


const createMeterReadingUnitsController = async (req, res) => {
  try {
    const unitList = req.body;

    if (!Array.isArray(unitList)) {
      return res.status(400).json({ message: 'Payload must be an array of meter reading units.' });
    }

    const result = await dbInsertService(unitList);
    res.status(200).json({ message: 'Meter Reading Units created successfully', data: result });
  } catch (error) {
    console.error('Error in createMeterReadingUnitsController:', error);
    res.status(400).json({ success: false, error: error.message });
  }
};

module.exports = {
  createMeterReadingOrder,
  createMappedGAAreaController,
  createAdminController,
  createMeterReadingUnitsController
};
