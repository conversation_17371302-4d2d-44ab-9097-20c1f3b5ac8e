const helpSupportRepository = require("../repository/help-support-repository");
const customerRepository = require("../repository/customer-repository");
const otpHelper = require("../helpers/smsEmailHelper");

const message = require("../utils/constants");
const redisHelper = require("../helpers/redisHelper");
const path = require("path");
const fs = require("fs");
const { error } = require('console');
const {ReportIssueTemplate} = require("../middleware/emailTemplate");
const addIssue = async (params) => {
  try {
    let addDocDetails;
    const redisKey = redisHelper.getIssueSupportDocKey(
      params.customer_connection_id,
    );
    const fileDetails = await redisHelper.getData(`${redisKey}`);
    if(fileDetails){
      // get file ext
      const extname = path.extname(fileDetails.filename);
      addDocDetails= {
        "file_name": fileDetails.filename,
        "file_path": fileDetails.path,
        "file_ext": extname,
      }
    }
    const whereCondition = {
      customer_connection_id : params.customer_connection_id,
      issue_category_id : params.issue_category_id,
      issue_type_id : params.issue_type_id,
    }
    const getExistingReq = await helpSupportRepository.getExitingIssueRequest(whereCondition);
    if (getExistingReq) {
      const error = new Error("Reported Issue Already Exist");
      error.details = getExistingReq;
      throw error;
    } 
    const result = await helpSupportRepository.addIssue(params,addDocDetails);
    if (result) {
    //fetch customer email and phone number
      const customerDetails = await customerRepository.getCustomerDetailsById({customer_connection_id:params.customer_connection_id});   
      console.log("Customer email:", customerDetails.email);
      console.log("Customer mobile:", customerDetails.mobile);
      const payload = {
        customer_connection_id: params.customer_connection_id,
        email: customerDetails.email,
        number: customerDetails.mobile,
        request_id: result.service_request_id,
        subject : "Report Issue Notification"
      };  
      
      if(customerDetails.email && customerDetails.email!== ""){
        //send email
        const emailResult = await otpHelper.emailOTPNotification(
          payload.email,
          ReportIssueTemplate(payload.first_name,payload.email,payload.request_id),
          payload.subject
        );
        console.log("Email Response:", emailResult);
        // return 
        // result;
      }
      if(customerDetails.mobile && customerDetails.mobile!== ""){
        //send sms
        
        const smsResult = await otpHelper.smsOTPNotification(
          payload.number,
          ReportIssueTemplate(payload.first_name,payload.email,payload.request_id)
        );
        console.log("SMS Response:", smsResult);
      }
      
      // Delete from the Redis
      await redisHelper.deleteData(redisKey);
    }
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
 
};
const uploadDocuments = async (detailsData, files) => {
  try {
    console.log("Received files:", files); // Debugging

    const redisKey = redisHelper.getIssueSupportDocKey(
      detailsData.customer_connection_id,
    );
    
    // Fetch File details from Redis
    let getUploadDetails = await redisHelper.getData(`${redisKey}`);

    getUploadDetails=files;

    // Store the updated file details with upload document in Redis
    await redisHelper.setData(redisKey, getUploadDetails, 3600);

    return { status: "success", message: "Files uploaded and stored in Redis.", data: files };
  } catch (error) {
    console.error("Error in uploadDocuments:", error);
    throw new Error("Error processing the uploaded document");
  }
};
const deleteUploadDocument = async (detailsData) => {
  try {
    // Generate Redis Key
    const redisKey = redisHelper.getIssueSupportDocKey(
      detailsData.customer_connection_id,
    );

    console.log(redisKey);

    // Fetch connection details from Redis
    let getDocumentDetails = await redisHelper.getData(redisKey);
    console.log(getDocumentDetails);

    if (!getDocumentDetails) {
      throw new Error("No documents found in Redis.");
    }

  
    // Get file path from Redis data
    const projectRoot = path.resolve(__dirname, "../.."); // Move up to project root
    const filePath = path.join(projectRoot, getDocumentDetails.path);

    //  Delete file from folder
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error(`Error deleting file ${filePath}:`, err);
      } else {
        console.log(`File deleted successfully: ${filePath}`);
      }
    });

   

    // ✅ Delete Redis
    await redisHelper.deleteData(redisKey);

    return {"message":`Document deleted successfully.`};
  } catch (error) {
    console.error("Error in deleteDocument:", error);
    throw error;
  }
};




module.exports = {
  addIssue,
  uploadDocuments,
  deleteUploadDocument,
};