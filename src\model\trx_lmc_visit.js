const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_lmc_visit",
    {
      lmc_visit_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      lmc_id: {
        type: DataTypes.BIGINT,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
      },
      visit_datetime: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      schedule_type: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      visit_status: {
        type: DataTypes.STRING(12),
        allowNull: false,
      },
      pipeline_size: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      extra_charge: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      pipeline_status: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_lmc_visit",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK__trx_lmc___CEC174EF5175F5B9",
          unique: true,
          fields: [{ name: "lmc_visit_id" }],
        },
      ],
    }
  );
};
