const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('mst_available_pngareas', {
    available_pngareas_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    pincode_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_pincodes',
        key: 'pincode_id'
      }
    },
    pincode: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'mst_available_pngareas',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_available_pngareas_id",
        unique: true,
        fields: [
          { name: "available_pngareas_id" },
        ]
      },
    ]
  });
};
