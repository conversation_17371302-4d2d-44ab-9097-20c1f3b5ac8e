// Sequelizer code
const Sequelize = require("sequelize");
const initModels = require("../model/init-models").initModels;
Sequelize.DATE.prototype._stringify = function _stringify(date, options) {
  return this._applyTimezone(date, options).format("YYYY-MM-DD HH:mm:ss.SSS");
};
// sequelize.beforeConnect(async (config) => {
//   if (process.env.NODE_ENV == "local") {
//     config.password = process.env.DATABASE_PASSWORD;
//   } else {
//     // const dbData = await getDbDetails();
//     // console.log("dbDats", dbData);
//     config.password = await getDbPassword();
//   }
// });

const sequelize = new Sequelize(
  process.env.DATABASE_NAME,
  process.env.DATABASE_USER,
  process.env.DATABASE_PASSWORD,
  {
    host: process.env.DATABASE_HOST,
    port: process.env.DATABASE_PORT,
    dialect: "mssql",

    operatorsAliases: false,
    dialectOptions: {
      options: {
        instanceName: process.env.DATABASE_INSTANCE,
        // encrypt: true,
        trustedConnection: true,
        useUTC: true,
      },
    },
    timezone: "+05:30",
    logging: console.log,
  }
);

sequelize
  .authenticate()
  .then(() => {
    console.log("Connection has been established successfully.");
  })
  .catch((error) => {
    console.error("Unable to connect to the database: ", error);
  });
console.log(initModels);
const dbModels = initModels(sequelize);

module.exports = { sequelize, dbModels };
