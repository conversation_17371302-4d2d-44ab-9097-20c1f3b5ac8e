const applicationStatusRepository = require("../repository/application-status-repository");
const message = require("../utils/constants");

const applicationStatusDto = (reqBody) => {
  return {
    application_status_value: reqBody.application_status_value,
    application_status_label: reqBody.application_status_label,
    is_active: reqBody.is_active,
    is_delete: 0, // Default value for new records
  };
};

// Get list with pagination
const getApplicationStatusList = async (filters) => {

  
  const { page = 1, limit = 10 } = filters;
  return applicationStatusRepository.getApplicationStatusList(page, limit);
};

// Create new record
const createApplicationStatus = async (reqBody) => {
  if (!reqBody) throw new Error("No application status provided");

  const modApplicationStatus = applicationStatusDto(reqBody);

  try {
    const createdRecord = await applicationStatusRepository.createApplicationStatus(modApplicationStatus);
    return createdRecord;
  } catch (error) {
    throw new Error(error.message);
  }
};

// Update existing record
const updateApplicationStatus = async (reqBody) => {
  const updateObject = applicationStatusDto(reqBody);
  updateObject.application_status_id = reqBody.application_status_id;

  if (!updateObject.application_status_id) throw new Error("ID not found");

  return applicationStatusRepository.updateApplicationStatus(updateObject.application_status_id, updateObject);
};

// Soft delete
const deleteApplicationStatus = async ({ application_status_id }) => {
  const alreadyDeleted = await applicationStatusRepository.checkIfDeletedApplicationStatus(application_status_id);
  if (!alreadyDeleted) {
    return {
      data: { is_exist: false },
      message: "This record is already deleted",
    };
  }

  return applicationStatusRepository.deleteApplicationStatus(application_status_id, { is_delete: 1 });
};

module.exports = {
  getApplicationStatusList,
  createApplicationStatus,
  updateApplicationStatus,
  deleteApplicationStatus,
};
