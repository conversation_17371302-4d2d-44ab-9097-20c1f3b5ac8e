// src/routes/dma-customer/index.js
const express = require('express');
const verifyToken = require('../../middleware/auth');
const { restrictToRoles } = require('../../middleware/auth');
const { getAllDmaCustomers, getSupervisorCustomers, updateCustomerStatus, getSupervisorCustomerStatusCount } = require('../../controllers/dma-customer-controller');
const { validate } = require('express-validation');
const {
  getAllCustomerforDmaValidation
} = require("../../validation/dma-validation");
const dmaCustomerRoutes = express.Router();

dmaCustomerRoutes.post('/', verifyToken, restrictToRoles('contractor'), validate(getAllCustomerforDmaValidation), getAllDmaCustomers);
dmaCustomerRoutes.get('/assigned', verifyToken, restrictToRoles('supervisor'), getSupervisorCustomers);
dmaCustomerRoutes.put('/update-status', verifyToken, restrictToRoles('supervisor'), updateCustomerStatus);
dmaCustomerRoutes.get('/status-count-supervisor', verifyToken, restrictToRoles('supervisor'), getSupervisorCustomerStatusCount);

module.exports = dmaCustomerRoutes;
