const axios = require("axios");

const apiGet = async (url, params = {}, headers = {}) => {
  try {
    const response = await axios.get(url, { headers, params });
    console.log("axioGet response", response);
    return response.data;
  } catch (error) {
    throw error;
  }
};

const apiPost = async (url, data = {}, headers = {}) => {
  try {
    const response = await axios.post(url, data, { headers });
    return response.data;
  } catch (error) {
    throw error.toString();
  }
};

module.exports = { apiGet, apiPost };
