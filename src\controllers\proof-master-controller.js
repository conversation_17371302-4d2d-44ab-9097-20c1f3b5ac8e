const proofMasterService = require('../services/proof-master-service');
const { sendError, sendSuccess } = require('../helpers/responseHelper');
const message = require('../utils/constants');

const getProofMasterList = async (req, res) => {
  try {
    const { page, limit, proof_type } = req.query;

    // Ensure page and limit are valid numbers
    const validatedPage = page && page > 0 ? parseInt(page, 10) : 1;
    const validatedLimit = limit && limit > 0 ? parseInt(limit, 10) : 10;

    // Validate proof_type
    if (!proof_type || typeof proof_type !== 'string' || proof_type.trim() === '') {
      return sendError(req, res, { message: "Proof type is required and must be a valid string" }, "Bad Request", 400);
    }

    // Call service layer with proof_type
    const data = await proofMasterService.getProofMasterList({ 
      page: validatedPage, 
      limit: validatedLimit, 
      proof_type: proof_type.trim() // Trim to avoid unwanted spaces
    });

    sendSuccess(req, res, data, "Data fetched successfully");
  } catch (error) {
    sendError(req, res, error, "Bad Request");
  }
};


const createProofMaster = async (req, res) => {
  try {
    const { proof_type, proof_name } = req.body;

    // Validate required fields
    if (!proof_type) {
      return sendError(req, res, { message: "proof_type is required" }, message.BAD_REQUEST, 400);
    }
    if (!proof_name) {
      return sendError(req, res, { message: "proof_name is required" }, message.BAD_REQUEST, 400);
    }

    const data = await proofMasterService.createProofMaster(req.body);
    if (data && data.message === message.NAME_ALREADY_EXIST) {
      return sendError(req, res, data, message.BAD_REQUEST, 400);
    }

    sendSuccess(req, res, data, message.CREATED);
  } catch (error) {
    sendError(req, res, error, message.BAD_REQUEST);
  }
};

const updateProofMaster = async (req, res) => {
  try {
    const { proof_type, proof_master_id } = req.body;

    // Validate required fields
    if (!proof_master_id) {
      return sendError(req, res, { message: "proof_master_id is required" }, message.BAD_REQUEST, 400);
    }
    if (!proof_type) {
      return sendError(req, res, { message: "proof_type is required" }, message.BAD_REQUEST, 400);
    }

    const data = await proofMasterService.updateProofMaster(req.body);
    sendSuccess(req, res, data, message.UPDATED);
  } catch (error) {
    sendError(req, res, error, message.BAD_REQUEST);
  }
};

const deleteProofMaster = async (req, res) => {
  try {
    const { proof_type, proof_master_id } = req.body;

    // Validate required fields
    if (!proof_master_id) {
      return sendError(req, res, { message: "proof_master_id is required" }, message.BAD_REQUEST, 400);
    }
    if (!proof_type) {
      return sendError(req, res, { message: "proof_type is required" }, message.BAD_REQUEST, 400);
    }

    // Call service to delete proof master
    const data = await proofMasterService.deleteProofMaster(req.body);
    sendSuccess(req, res, data, message.DELETED);
  } catch (error) {
    sendError(req, res, error.message || error, message.BAD_REQUEST, 400);
  }
};
module.exports = {
  getProofMasterList,
  createProofMaster,
  updateProofMaster,
  deleteProofMaster,
};
