const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_customer_connection_transfer",
    {
      name_transfer_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      connection_transfer_reason: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_masters",
          key: "master_id",
        },
      },
      connection_transfer_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      connection_transfer_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      isu_synced: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      is_active: {
        type: DataTypes.TINYINT,
        allowNull: true,
        defaultValue: 1,
      },
      is_delete: {
        type: DataTypes.TINYINT,
        allowNull: true,
        defaultValue: 0,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_customer_connection_transfer",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK__trx_cust__ADFAFEA7FC0476F2",
          unique: true,
          fields: [{ name: "name_transfer_id" }],
        },
      ],
    }
  );
};
