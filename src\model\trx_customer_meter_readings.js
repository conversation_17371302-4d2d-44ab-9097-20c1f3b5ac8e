const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_customer_meter_readings",
    {
      customer_meter_reading_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      customer_meter_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_meter",
          key: "customer_meter_id",
        },
      },
      cycle_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_cycle",
          key: "cycle_id",
        },
      },
      meter_reading_file: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      previous_reading: {
        type: DataTypes.DECIMAL(10, 3),
        allowNull: false,
      },
      current_reading: {
        type: DataTypes.DECIMAL(10, 3),
        allowNull: false,
      },
      reading_units: {
        type: DataTypes.DECIMAL(10, 3),
        allowNull: false,
      },
      bill_amount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      due_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      meter_reading_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      submitted_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      declared_by: {
        type: DataTypes.STRING(12),
        allowNull: false,
      },
      mru_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_meter_reading_units",
          key: "mru_id",
        },
      },

      isu_synced: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_customer_meter_readings",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_customer_meter_reading_id",
          unique: true,
          fields: [{ name: "customer_meter_reading_id" }],
        },
      ],
    }
  );
};
