const { dbModels } = require("../database/connection");
const redisHelper = require("../helpers/redisHelper");
const {
  CONNECTION_STATUS_REASON,
  PROGRESS_STATUS,
  APPLICATION_STATUS,
  APPLICATION_ASSIGN_STATUS,
  PAYMENT_MODE,
  PAYMENT_SECTION_TYPE,
} = require("../utils/constants");

const customerConnectionModel = dbModels.trx_customer_connection;
const customerConnectionAddressModel = dbModels.trx_customer_connection_address;
const customerConnectionProofModel = dbModels.trx_customer_proof_documents;
const customerConnectionStatusHistory =
  dbModels.trx_customer_connection_status_history;
const customerConnectionPaymentList = dbModels.trx_connection_payments;
const customerModel = dbModels.trx_customer;
const { Op } = require("sequelize");
const azureSASHelper = require("../helpers/azureSASSDKHelper");
const { updatePayment } = require("./payment-repository");
const {getFutureDateByMonths} = require("../utils/commonFn"); 
const getCustomer = async (mobile) => {
  return await customerModel.findOne({
    where: { mobile },
  });
};

const getCustomerAddress = async (where) => {
  const results = await customerConnectionAddressModel.findOne({
    where: where || {}, // Ensures where is not null
  });

  return results;
};
const getCustomerProofDocument = async (where) => {
  const results = await customerConnectionProofModel.findOne({
    where: where || {}, // Ensures where is not null
  });

  return results;
};

const getActiveCustomer = async (mobile) => {
  return await customerModel.findOne({
    mobile,
    is_active: 1,
  });
};
const checkCreateCustomer = async (personalDetails) => {
  let customerId;
  try {
    const customerData = await customerModel.findOne({
      where: {
        mobile: personalDetails.mobile,
      },
    });
    if (customerData) {
      if (!customerData.is_active) {
        throw new Error("Customer is not active.Please contact.");
      }
      customerId = customerData.customer_id;
    } else {
      const customerParams = {
        prefix: personalDetails.prefix,
        bpcl_id: personalDetails.bpcl_id,
        first_name: personalDetails.first_name,
        middle_name: personalDetails.middle_name,
        last_name: personalDetails.last_name,
        email: personalDetails.email,
        mobile: personalDetails.mobile,
        alternate_mobile: personalDetails.alternate_mobile,
      };
      const customerData = await customerModel.create(customerParams);
      customerId = customerData.customer_id;
    }
    console.log("customerId", customerId);
    return customerId;
  } catch (err) {
    throw err;
  }
};
const createUpdateConnection = async (
  personalDetailsData,
  personaType,
  userData
) => {
  console.log("personalDetailsData", personalDetailsData.uploadDocuments);
  const { customer_connection_id, mobile } =
    personalDetailsData.customerConnectionDetails || personalDetailsData.uploadDocuments[0];
  console.log("customer_connection_id",customer_connection_id);
  

  // const customerData = {};
  // const { proof_type, proof_master_id } = personalDetailsData.uploadDocuments;
  try {
    // Check if customer_connection_id is not present in the request
    if (!customer_connection_id) {
      personalDetailsData.customerConnectionDetails.customer_id =
        await checkCreateCustomer(personalDetailsData.customerConnectionDetails);
      personalDetailsData.customerConnectionDetails.status =
          APPLICATION_STATUS.APPLICATION_SUBMITTED;
      const {
        connection_pincode,
        connection_house_no,
        connection_floor_no,
        connection_landmark,
        connection_state,
        connection_city,
      } = personalDetailsData.customerConnectionAddress;
      const whereParams = {
        connection_detail: {
          bpcl_id: personalDetailsData.customerConnectionDetails.bpcl_id,
          is_active: 1,
        },
        connection_address: {
          connection_pincode,
          connection_house_no,
          // connection_floor_no,
          connection_landmark,
          connection_state,
          connection_city,
        },
      };
      // Fetch a details of the customer from DB
      result = await getCustomerDetails(whereParams);
      if (result) {
        throw new Error("Connection already exists with the same details  ");
      }
      

      console.log(
        "personalDetailsData.customerConnectionDetails",
        personalDetailsData.customerConnectionDetails
      );

      // Save the connection details
      const connectionDetailResult = await customerConnectionModel.create(
        personalDetailsData.customerConnectionDetails
      );
      console.log("connectionDetailResult", connectionDetailResult);

      const customer_connection_id =
        connectionDetailResult.customer_connection_id;

      personalDetailsData.customerConnectionAddress.customer_connection_id =
        customer_connection_id;
      console.log(
        "personalDetailsData.customerConnectionAddress",
        personalDetailsData.customerConnectionAddress
      );

      // Save the connection address details
      const customerConnectionAddressData =
        await customerConnectionAddressModel.create(
          personalDetailsData.customerConnectionAddress
        );
      const customer_connection_address_id =
        customerConnectionAddressData.customer_connection_address_id;

      // Save the proof documents
      for (let proofDocument of personalDetailsData.uploadDocuments) {
        let folderPath =
          "customer/cus_" + customer_connection_id + "/registration/document";
        const sourceFolder = "temp/" + proofDocument.file_name;
        console.log("mmmmmmmmm>>>>>>>>>>>>>>>>>.>>.");
        proofDocument.file_path = folderPath;
        folderPath = folderPath + "/" + proofDocument.file_name;
        await azureSASHelper.moveBlobWithinContainer(sourceFolder, folderPath);

        proofDocument.customer_connection_id = customer_connection_id;
        proofDocument.file_ext = proofDocument.file_ext.replace('.', '');
        proofDocument.customer_connection_address_id =
          customer_connection_address_id;
        proofDocument.verification_status = PROGRESS_STATUS.PENDING;
        
        await customerConnectionProofModel.create(proofDocument);
      }
  
      // Insert into customer_connection_status_history
      const historyParams ={
        customer_connection_id,
        previous_status: 0, 
        current_status: APPLICATION_STATUS.APPLICATION_SUBMITTED,
        currently_assigned_to_type: CONNECTION_STATUS_REASON.TPI,
        currently_assigned_to_id: 2,
        created_by: customer_connection_id,
        created_by_type: CONNECTION_STATUS_REASON.CUSTOMER,
      };

      await addStatusHistoryRecord(historyParams);

      return {
        ...personalDetailsData.customerConnectionDetails,
        customer_connection_id,
      };
    } else {
      // Update the connection details
      personalDetailsData['customerConnectionDetails']={
        status: APPLICATION_STATUS.APPLICATION_RESUBMITTED,
        internal_status: APPLICATION_STATUS.APPLICATION_PENDING
      } ;
      // personalDetailsData.customerConnectionDetails.internal_status= 
      console.log("personalDetailsData",personalDetailsData);
      
      if(personalDetailsData.customerConnectionDetails)
      {
        // Step 2: Get master_id from mst_masters
        const reasonMaster = await getReasonId({master_value:'User-has-made-necessary-changes'});
        console.log("reasonMaster", reasonMaster);
        
        const reason_id = reasonMaster.master_id;
        personalDetailsData.customerConnectionDetails.reason_id= reason_id;
        // const reason = reasonMaster.master_name;
        {await customerConnectionModel.update(
          personalDetailsData.customerConnectionDetails,
          {
            where: { customer_connection_id },
          }
        );}
      }

      // Save the connection address details
      if(personalDetailsData.customerConnectionAddress && personalDetailsData.customerConnectionAddress?.connection_pngareas_id)
      {
        await customerConnectionAddressModel.update(
          personalDetailsData.customerConnectionAddress,
          {
            where: { customer_connection_id },
          }
        );
      }

      // Save the proof documents
      if(personalDetailsData.uploadDocuments){
        console.log("personalDetailsData.uploadDocuments",personalDetailsData.uploadDocuments);
        const getCustomerAddressDetails = await getCustomerAddress({customer_connection_id:customer_connection_id})
        console.log("getCustomerAddressDetails",getCustomerAddressDetails);
        
        const customer_connection_address_id =
        getCustomerAddressDetails.customer_connection_address_id;
        console.log("customer_connection_address_id",customer_connection_address_id);
        
        for (let proofDocument of personalDetailsData.uploadDocuments) {
          proofDocument.verification_status = PROGRESS_STATUS.PENDING;
          console.log("proofDocument", proofDocument);
          let folderPath =
          "customer/cus_" + customer_connection_id + "/registration/document";
          const sourceFolder = "temp/" + proofDocument.file_name;
          console.log("mmmmmmmmm>>>>>>>>>>>>>>>>>.>>.");
          proofDocument.file_path = folderPath;
          folderPath = folderPath + "/" + proofDocument.file_name;
          await azureSASHelper.moveBlobWithinContainer(sourceFolder, folderPath);
          
          proofDocument.customer_connection_id = customer_connection_id;
          proofDocument.file_ext = proofDocument.file_ext.replace('.', '');
          proofDocument.customer_connection_address_id =
          customer_connection_address_id;
          proofDocument.verification_status = PROGRESS_STATUS.PENDING;

          const whereCond = {
            customer_connection_id,
            proof_type: proofDocument.proof_type,
            proof_master_id: Number(proofDocument.proof_master_id),
          };
          console.log("whereCond", whereCond);
          const getCustomerProofDetails = await getCustomerProofDocument(whereCond)
          if(getCustomerProofDetails){
            const updateDocument= await customerConnectionProofModel.update(proofDocument, {
              where: whereCond,
              logging: console.log,
            });
            console.log("updateDocument",updateDocument);
          }else{
            const addDocument= await customerConnectionProofModel.create(proofDocument);
            console.log("addDocument",addDocument);
            
          }
        }
      }
      // get latest customer_connection_status_history
      const latestConnectionStatusHistory =
        await customerConnectionStatusHistory.findOne({
          where: { customer_connection_id }, // filter by customer_connection_id
          order: [["created_at", "DESC"]], // get the latest record
        });
      console.log("latestConnectionStatusHistory", latestConnectionStatusHistory);
     
      // Insert into customer_connection_status_history
      const historyParams ={
        customer_connection_id,
        previous_status: latestConnectionStatusHistory?.current_status, 
        current_status: APPLICATION_STATUS.APPLICATION_SUBMITTED,
        currently_assigned_to_type: CONNECTION_STATUS_REASON.TPI,
        currently_assigned_to_id: 2,
        created_by: customer_connection_id,
        created_by_type: CONNECTION_STATUS_REASON.CUSTOMER,
      };
      console.log("historyParams", historyParams);

      await addStatusHistoryRecord(historyParams);
      return {
        ...personalDetailsData.customerConnectionDetails,
        customer_connection_id,
      };
    }
    
  } catch (error) {
    console.error("Error in createUpdateConnection:", error);
    throw error;
  }
};

const addCustomerAddress = async (payload) => {
  try {
    return await customerConnectionAddressModel.create(payload, {
      where: { customer_connection_id: payload.customer_connection_id },
    });
  } catch (error) {
    console.log(error);
    throw error;
  }
};
const getCustomerList = async (payload) => {
  const whereCond = {
    bpcl_id: payload.bpcl_id,
    is_active: 1,
  };
  if (payload.active_status === true) {
    whereCond.status = APPLICATION_STATUS.ACTIVE_CONNECTION;
  } else if (payload.active_status === false) {
    whereCond.status = { [Op.ne]: APPLICATION_STATUS.ACTIVE_CONNECTION };
  }

  let results = await dbModels.trx_customer_connection.findAll({
    include: [
      {
        model: dbModels.mst_available_pngareas,
        as: "available_pngarea",
      },
      {
        model: dbModels.mst_masters,
        as: "tcc_reason",
      },
      {
        model: dbModels.trx_customer_connection_address,
        as: "trx_customer_connection_addresses",
      },
      {
        model: dbModels.trx_customer_proof_documents,
        as: "trx_customer_proof_documents",
      },
      {
        model: dbModels.trx_customer_connection_tpi_mapping,
        as: "trx_customer_connection_tpi_mappings",
        attributes: ["tpi_id", "tpi_status","created_at"],
        required: false,
        where: {
          tpi_status: "rework",
          is_latest: 1,
        },
        include: [
          {
            model: dbModels.trx_customer_connection_tpi_rework,
            as: "trx_customer_connection_tpi_reworks",
            attributes: ["section", "description"],
          },
        ],
      },
      {
        model: dbModels.trx_connection_payments,
        as: "trx_connection_payments",
      },
      {
        model: dbModels.trx_lmc_connection_mapping,
        as: 'trx_lmc_connection_mappings',
        attributes: [
          'lmc_connection_mapping_id',
          'customer_connection_id',
          'lmc_id',
          'created_at',
          'updated_at'
        ],
        include: [
          {
            model: dbModels.trx_mapped_lmc_connection_stages,
            as: 'trx_mapped_lmc_connection_stages',
            attributes: [
              'mapped_lmc_connection_stage_id',
              'lmc_stage',
              'visit_datetime',
              'visit_status',
              'reason',
              'tpi_engineer_id',
              'tpi_status',
              'actual_visit_date',
              'created_at'
            ],
            separate: true, // important for ordering nested includes
            order: [['mapped_lmc_connection_stage_id', 'DESC']],
            // order: [["created_at", "DESC"]], 
            include: [
              {
                model: dbModels.trx_mapped_lmc_feasibility_commission_details,
                as: 'trx_mapped_lmc_feasibility_commission_details',
                attributes: [
                  'ready_for_conversion',
                  'created_at',
                ],
              },
            ]
          },
          {
            model: dbModels.mst_admin,
            as: "lmc",
            attributes: [
              "user_role",
              "first_name",
              "last_name",
              "mobile",
              "alternate_mobile",
              "email",
              "city",
              "state",
            ],
          }

        ]
      },
    ],
    where: whereCond,
    raw: false, // Ensures Sequelize returns model instances
    nest: true, // Ensures nested objects are properly structured
  });
  const modifiedResults = results.map((record) => {
    const jsonRecord = record.toJSON();

    const connectionAddress = jsonRecord.trx_customer_connection_addresses[0] || {};
    const lmcConnection = jsonRecord.trx_lmc_connection_mappings[0] || {};
    
    const lmcStage = lmcConnection.trx_mapped_lmc_connection_stages?.[0] || {};
    const lmcDetails = {
      ...lmcStage,
      agentDetails: lmcStage.lmc || {},
    };

    const reworkMappings = jsonRecord.trx_customer_connection_tpi_mappings[0];
    // get 3 month later date 
    const createdAt = reworkMappings?.created_at;
    if(reworkMappings?.created_at){
      const reworkExpiryDate= getFutureDateByMonths(createdAt, 3);
      console.log("reworkExpiryDate",new Date(reworkExpiryDate));
      jsonRecord.rework_expiry=new Date(reworkExpiryDate);

    }

    const rework = reworkMappings?.trx_customer_connection_tpi_reworks || [];
    const connectionPayments = jsonRecord.trx_connection_payments || [];

    jsonRecord.reason=jsonRecord.tcc_reason?.master_name;
    jsonRecord.pincode=jsonRecord.available_pngarea?.pincode;
    delete jsonRecord.tcc_reason;
    delete jsonRecord.available_pngarea;

    return {
      customerConnectionDetails: jsonRecord,
      customerConnectionAddress: connectionAddress,
      customerAdditionalInfo: {
        connection_address_type: jsonRecord.connection_address_type,
        extra_connection: jsonRecord.extra_connection,
        extra_points: jsonRecord.extra_points,
        govt_scheme_id: jsonRecord.govt_scheme_id,
        security_deposit: jsonRecord.security_deposit,
        security_deposit_category: jsonRecord.security_deposit_category,
        rental_owner_name: jsonRecord.rental_owner_name,
        rental_owner_mobile: jsonRecord.rental_owner_mobile,
      },
      uploadDocuments: jsonRecord.trx_customer_proof_documents || [],
      lmcDetails,
      connectionPayments,
      rework,
    };
  });

  // Remove old field names
  modifiedResults.forEach((record) => {
    delete record.customerConnectionDetails.trx_customer_connection_addresses;
    delete record.customerConnectionDetails.trx_customer_proof_documents;
    delete record.customerConnectionDetails.trx_mapped_lmc_customer_connections;
    delete record.customerConnectionDetails.trx_connection_payments;
  });

  return modifiedResults;
};
const getCustomerDetails = async (where) => {
  where.connection_detail.is_active = 1;
  const results = await customerConnectionModel.findOne({
    // attributes: ['customer_connection_id'],
    include: [
      {
        model: customerConnectionAddressModel,
        as: "trx_customer_connection_addresses",
        ...(where.connection_address &&
        Object.keys(where.connection_address).length
          ? { where: where.connection_address }
          : {}), // Ensures Sequelize does not receive undefined
        include: [
          {
            model: dbModels.mst_available_pngareas,
            as: "connection_address_pngarea",
            attributes: ["available_pngareas_id", "pincode", "pincode_id"],
            include: [
              {
                model: dbModels.mst_pincodes,
                as: "pincodeAreasAlias",
                attributes: ["pincode"],
                include: [
                  {
                    model: dbModels.mst_areas,
                    as: "area",
                    attributes: ["area_name"],
                  },
                ],
              },
            ],
          },
          {
            model: dbModels.mst_available_pngareas,
            as: "permanent_pngarea",
            attributes: ["available_pngareas_id", "pincode", "pincode_id"],
            include: [
              {
                model: dbModels.mst_pincodes,
                as: "pincodeAreasAlias",
                attributes: ["pincode"],
                include: [
                  {
                    model: dbModels.mst_areas,
                    as: "area",
                    attributes: ["area_name"],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        model: customerConnectionProofModel,
        as: "trx_customer_proof_documents",
        ...(where.connection_proof_documents &&
        Object.keys(where.connection_proof_documents).length
          ? { where: where.connection_proof_documents }
          : {}),
        include: [
          {
            model: dbModels.mst_proof_master,
            as: "proof_master",
            attributes: ["proof_name"],
          },
        ],
      },
      {
        model: dbModels.mst_available_pngareas,
        as: "available_pngarea",
        attributes: ["available_pngareas_id", "pincode", "pincode_id"],
        include: [
          {
            model: dbModels.mst_pincodes,
            as: "pincodeAreasAlias",
            attributes: ["pincode"],
            include: [
              {
                model: dbModels.mst_areas,
                as: "area",
                attributes: ["area_name"],
              },
            ],
          },
        ],
      },
      {
        model: dbModels.mst_govt_scheme,
        attributes: ["govt_scheme_id", "scheme_name"],
        as: "govt_scheme",
        required: false,
      },
    ],
    where: where.connection_detail || {}, // Ensures where is not null
    // logging: console.log, // Logs SQL query
  });

  if (results) {
    let modifiedResults = {};
    modifiedResults.connection_details = {
      ...results.toJSON(),
    };
    modifiedResults.connection_details.is_lpg =
          (modifiedResults.connection_details?.is_lpg===1)?1:0;

    modifiedResults.connection_details.area_name =
      modifiedResults.connection_details.available_pngarea.pincodeAreasAlias.area.area_name;
    modifiedResults.connection_details.scheme_name =
      modifiedResults.connection_details.govt_scheme.scheme_name;

    // modifiedResults.connection_addresses =
    //   results.trx_customer_connection_addresses[0] || {};

    const connectionAddress =
      results.trx_customer_connection_addresses?.[0]?.toJSON?.() || {};

    // Add area_name inside connection_addresses
    connectionAddress.connection_area_name =
      connectionAddress?.connection_address_pngarea?.pincodeAreasAlias?.area
        ?.area_name || null;
    connectionAddress.permanent_pngarea_name =
      connectionAddress?.permanent_pngarea?.pincodeAreasAlias?.area
        ?.area_name || null;

    modifiedResults.connection_addresses = connectionAddress;
    
    const cleaned = results.trx_customer_proof_documents.map(instance => {
      // Extract plain data
      const data = instance.dataValues;

      // Build new object, removing `proof_master` and keeping `proof_name`
      return {
        customer_proof_document_id: data.customer_proof_document_id,
        customer_connection_id: data.customer_connection_id,
        customer_connection_address_id: data.customer_connection_address_id,
        proof_type: data.proof_type,
        proof_master_id: data.proof_master_id,
        file_name: data.file_name,
        file_path: data.file_path,
        file_ext: data.file_ext,
        file_size: data.file_size,
        verification_status: data.verification_status,
        reason: data.reason,
        is_active: data.is_active,
        created_by: data.created_by,
        updated_by: data.updated_by,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        proof_master:instance.proof_master,
        proof_name: instance.proof_master.proof_name || null  // from top-level proof_name
      };
    });
    modifiedResults.proof_documents=cleaned;
    
    delete modifiedResults.connection_addresses.connection_address_pngarea;
    delete modifiedResults.connection_addresses.permanent_pngarea;
    delete modifiedResults.connection_details.trx_customer_connection_addresses;
    delete modifiedResults.connection_details.trx_customer_proof_documents;
    delete modifiedResults.connection_details.available_pngarea;
    delete modifiedResults.connection_details.govt_scheme;

    return modifiedResults;
  }

  return results;
};

const getCustomerAddressCount = async (where) => {
  const results = await customerConnectionModel.count({
    include: [
      {
        model: customerConnectionAddressModel,
        as: "trx_customer_connection_addresses",
        ...(where.connection_address &&
        Object.keys(where.connection_address).length
          ? { where: where.connection_address }
          : {}), // Ensures Sequelize does not receive undefined
      },
    ],
    where: where.connection_detail || {}, // Ensures where is not null
  });

  return results;
};

const createRegistrationHelp = async (input) => {
  let results; // Initialize the results variable
  try {
    results = await dbModels.trx_help_with_registration.create(input); // Perform the database operation
  } catch (error) {
    console.log(error.error);

    console.error("Error inserting registration:", error); // Log the error for debugging
    results = { error: "Error occurred while creating registration." }; // Handle error gracefully
  }

  return results; // Always return results, even in case of an error
};

const getCustomerConnectionId = async (
  bpcl_id,
  house_flat_no,
  available_pngareas_id
) => {
  return await customerConnectionModel.findOne({
    where: { bpcl_id, house_flat_no, available_pngareas_id },
    attributes: ["customer_connection_address_id"],
  });
};
const getCustomerDetailsById = async (where) => {
  return await customerConnectionModel.findOne({
    where: where,
  });
};
const getConnectionPaymentDetails = async (customer_connection_id) => {
  return await customerConnectionPaymentList.findAll({
    where: { customer_connection_id },
  });
};

const customerApprovalDecline = async (req) => {
  console.log("customerApprovalDecline req",req);
  
  const { master_value, master_type, customer_connection_id, status, bpcl_id } =
    req;

  try {
    // Step 1: Get master_id from mst_masters
    const master = await dbModels.mst_masters.findOne({
      where: {
        master_value: master_value,
        master_type: master_type,
      },
    });

    if (!master) {
      console.log(
        `No matching master_id found for value: ${master_value}, type: ${master_type}`
      );
      throw new Error(
        `No matching master_id found for value: ${master_value}, type: ${master_type}`
      );
    }

    const reason_id = master.master_id;
    const reason = master.master_name;

    // Step 2: Get current status from customer_connection table
    const connection = await dbModels.trx_customer_connection.findOne({
      where: {
        customer_connection_id,
      },
    });

    if (!connection) {
      console.log(
        `No customer_connection found for ID: ${customer_connection_id}`
      );
      throw new Error(
        `No customer_connection found for ID: ${customer_connection_id}`
      );
    }

    const previous_status = connection.status;

    // Step 3: Update customer_connection table
    const [updatedRows] = await dbModels.trx_customer_connection.update(
      {
        status: "application-rejected",
        internal_status: reason_id,
      },
      {
        where: { customer_connection_id },
      }
    );

    if (updatedRows === 0) {
      throw new Error("Failed to update customer connection status.");
    }

    // Step 4: Insert into customer_connection_status_history
    const historyParams = {
      customer_connection_id,
      previous_status,
      current_status: "application-rejected",
      reason_id,
      reason,
      currently_assigned_to_type: "customer",
      currently_assigned_to_id: 10,
      created_by: 10,
    };

    await addStatusHistoryRecord(historyParams);

    return {
      success: true,
      message:
        "Customer connection updated and status history inserted successfully.",
      data: {
        customer_connection_id,
        status: "application-rejected",
        reason_id,
        reason,
      },
    };
  } catch (err) {
    console.error("Error during status update process:", err);
    throw err; // ✅ Re-throw to service layer
  }
};
const customerApprovalAccept = async (req) => {
  try {
    console.log("customerApprovalAccept req",req);
    
    const { master_value, customer_connection_id, status } =
      req;
  
    // Step 1: Update security payment details by connection id
    let updateParams = {
      payment_status: PROGRESS_STATUS.COMPLETED,
      paid_on: new Date(),
      payment_mode: PAYMENT_MODE.ONLINE,
    };
    const whereParams = {
      customer_connection_id: customer_connection_id,
      section_type: {
        [Op.in]: [
          PAYMENT_SECTION_TYPE.SECURITY_DEPOSIT,
          PAYMENT_SECTION_TYPE.EXTRA_PIPELINE,
        ],
      },
    };
    const updatePaymentResult = await updatePayment(
      updateParams,
      whereParams
    );
    console.log("updatePaymentResult", updatePaymentResult);
    
    // Step 2: Get master_id from mst_masters
    const reasonMaster = await getReasonId({master_value});
    console.log("reasonMaster", reasonMaster);

    const reason_id = reasonMaster.master_id;
    const reason = reasonMaster.master_name;

    // Step 3: Update customer_connection table
    const [updatedRows] = await dbModels.trx_customer_connection.update(
      {
        status: status,
        reason_id,
        is_security_deposit: 1,
      },
      {
        where: { customer_connection_id },
      }
    );

    if (updatedRows === 0) {
      throw new Error("Failed to update customer connection status.");
    }
    req = { ...req, reason_id, reason, status}; 
    req.currently_assigned_to_type='dma';
    req.currently_assigned_to_id=0;
    req.created_by=customer_connection_id;

    // Step 4: Insert into customer_connection_status_history
    const statusHistoryUpdate = await updateStatusHistory(req);
    console.log("statusHistoryUpdate", statusHistoryUpdate);

    return {
      success: true,
      message:
        "Customer connection updated and status history inserted successfully.",
      data: {
        customer_connection_id,
        status,
        reason_id,
        reason,
      },
    };
  } catch (err) {
    console.error("Error during status update process:", err);
    throw err; // ✅ Re-throw to service layer
  }
};

const getReasonId = async (
  params
) => {
  try {
    const { master_value } = params;

    // Step 1: Get master_id from mst_masters
    const master = await dbModels.mst_masters.findOne({
      where: {
        master_value: master_value,
      },
    });

    if (!master) {
      console.log(
        `No matching master_id found for value: ${master_value}, type: ${master_type}`
      );
      throw new Error(
        `No matching master_id found for value: ${master_value}, type: ${master_type}`
      );
    }

    return master;
   
  } catch (error) {
    console.error("Error in getReasonId:", error);
    throw error;
  }
}

const updateStatusHistory = async (
  params
) => {
  try {
    console.log("updateStatusHistory params", params);
    
    const { customer_connection_id, reason_id, reason, status, currently_assigned_to_type, currently_assigned_to_id, created_by } = params;
   
    // Step 1: Get current status from customer_connection table
    const connection = await dbModels.trx_customer_connection.findOne({
      where: {
        customer_connection_id,
      },
    });

    if (!connection) {
      console.log(
        `No customer_connection found for ID: ${customer_connection_id}`
      );
      throw new Error(
        `No customer_connection found for ID: ${customer_connection_id}`
      );
    }

    const previous_status = connection.status;

    // Step 2: Insert into customer_connection_status_history
    const historyParams = {
      customer_connection_id,
      previous_status,
      current_status: status,
      reason_id,
      reason,
      currently_assigned_to_type: currently_assigned_to_type,
      currently_assigned_to_id: currently_assigned_to_id,
      created_by: created_by,
    };
    console.log("historyParams", historyParams);

    return  await addStatusHistoryRecord(historyParams);

  } catch (error) {
    console.error("Error in getExistingHelpWIthRegRequest:", error);
    throw error;
  }
}

const getExistingHelpWIthRegRequest = async (
  bpclId,
  flatNo,
  availablePngareasId
) => {
  try {
    const customer = await dbModels.trx_help_with_registration.findOne({
      where: {
        bpcl_id: bpclId,
        flat_no: flatNo,
        available_pngareas_id: availablePngareasId,
      },
    });
    return customer;
  } catch (error) {
    console.error("Error in getExistingHelpWIthRegRequest:", error);
    throw error;
  }
};

const addStatusHistoryRecord = async (params) => {
  try {
    const {
      customer_connection_id,
      previous_status,
      current_status,
      reason_id,
      reason,
      currently_assigned_to_type,
      currently_assigned_to_id,
      created_by,
    } = params;
    const statusHistory = {
      customer_connection_id,
      previous_status,
      current_status,
      reason_id,
      description: reason,
      currently_assigned_to_type,
      currently_assigned_to_id,
      created_by,
      created_by_type: CONNECTION_STATUS_REASON.CUSTOMER,
    };

    console.log("statusHistory", statusHistory);

    return await customerConnectionStatusHistory.create(statusHistory);
  } catch (error) {
    console.error("Error in addStatusHistoryRecord:", error);
    throw error;
  }
};

const updateCustomer = async (payload, where) => {
  try {
    console.log("where", where);
    console.log("payload", payload);

    const customer = await customerConnectionModel.findOne({
      where,
    });

    if (!customer) {
      throw new Error("Customer not found");
    }

    await customerConnectionModel.update(payload, { where });
  } catch (error) {
    console.error("Error in updateCustomer:", error);
    throw error;
  }
};
const checkReworkExpiryCron = async () => {
  try {
    const reworkList = await dbModels.trx_customer_connection.findAll({
      include: [
        {
          model: dbModels.trx_customer_connection_tpi_mapping,
          as: "trx_customer_connection_tpi_mappings",
          attributes: ["tpi_id", "tpi_status", "created_at"],
          // required: false,
          where: {
            tpi_status: "rework",
            is_latest: 1,
          },
          include: [
            {
              model: dbModels.trx_customer_connection_tpi_rework,
              as: "trx_customer_connection_tpi_reworks",
              attributes: ["section", "description", "created_at"],
            },
          ],
        },
      ],
      where: { is_active: 1, status: "application-rework" },
      raw: false, // Ensures Sequelize returns model instances
      nest: true, // Ensures nested objects are properly structured
    });
    if (!reworkList) {
      throw new Error("Rework not found");
    }
    const connectionIdArray = reworkList
      .filter(item => {
        const createdAt = item.trx_customer_connection_tpi_mappings[0].dataValues.created_at;
        const now = new Date();
        const diffDays = (now - createdAt) / (1000 * 60 * 60 * 24);
        console.log("diffDays",diffDays);
        
        return diffDays > 1;
      })
      .map(item => item.customer_connection_id);

    if(connectionIdArray && connectionIdArray.length > 0){
      // Step 2: Get master_id from mst_masters
      const reasonMaster = await getReasonId({master_value:'rework-expiry'});
      const reason_id = reasonMaster.master_id;
      
      await customerConnectionModel.update({
        status:'application-rejected',
        internal_status:'application-rejected',
        reason_id:reason_id
      }, {
        where: {
          customer_connection_id: {
            [Op.in]: connectionIdArray
          }
        }
      });
    }
    return reworkList;
  } catch (error) {
    console.error("Error in updateCustomer:", error);
    throw error;
  }
};

module.exports = {
  createUpdateConnection,
  getCustomerList,
  createRegistrationHelp,
  getCustomerConnectionId,
  getCustomerDetails,
  getConnectionPaymentDetails,
  getCustomer,
  getActiveCustomer,
  getCustomerDetailsById,
  getCustomerAddressCount,
  addCustomerAddress,
  customerApprovalDecline,
  customerApprovalAccept,
  getExistingHelpWIthRegRequest,
  addStatusHistoryRecord,
  updateCustomer,
  getReasonId,
  checkReworkExpiryCron
};
