const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_extra_point_request",
    {
      extra_point_request_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
        unique: "UK_tepr_cci_ccai_ept_eps",
      },
      customer_connection_address_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection_address",
          key: "customer_connection_address_id",
        },
        unique: "UK_tepr_cci_ccai_ept_eps",
      },
      extra_point_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique: "UK_tepr_cci_ccai_ept_eps",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      extra_point_status: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "pending",
        unique: "UK_tepr_cci_ccai_ept_eps",
      },
    },
    {
      sequelize,
      tableName: "trx_extra_point_request",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "IX_FK_trxextrapointrequest_customer_connection_address_id",
          fields: [{ name: "customer_connection_address_id" }],
        },
        {
          name: "IX_FK_trxextrapointrequest_customer_connection_id",
          fields: [{ name: "customer_connection_id" }],
        },
        {
          name: "PK_extra_point_request_id",
          unique: true,
          fields: [{ name: "extra_point_request_id" }],
        },
        {
          name: "UK_tepr_cci_ccai_ept_eps",
          unique: true,
          fields: [
            { name: "customer_connection_id" },
            { name: "customer_connection_address_id" },
            { name: "extra_point_type" },
            { name: "extra_point_status" },
          ],
        },
      ],
    }
  );
};
