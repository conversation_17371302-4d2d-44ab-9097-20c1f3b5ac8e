const express = require('express');
const { validate } = require("express-validation");
const {
    createProofMasterValidation,
    updateProofMasterValidation,
    deleteProofMasterValidation,
    getProofMasterValidation,
} = require("../../validation/proof-master-validation ");

const proofMasterRoutes = express.Router();
const proofMasterController = require('../../controllers/proof-master-controller');

// Define routes
proofMasterRoutes.get('/list', validate(getProofMasterValidation), proofMasterController.getProofMasterList); // GET method for listing
proofMasterRoutes.post('/create', validate(createProofMasterValidation), proofMasterController.createProofMaster); // POST method for creation
proofMasterRoutes.put('/update', validate(updateProofMasterValidation), proofMasterController.updateProofMaster); // PUT method for updating
proofMasterRoutes.delete('/delete', validate(deleteProofMasterValidation), proofMasterController.deleteProofMaster); // DELETE method for deletion

module.exports = proofMasterRoutes;
