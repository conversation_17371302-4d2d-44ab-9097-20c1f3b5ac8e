const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_document_metadata",
    {
      document_metadata_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      ref_table_row_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      ref_table_type: {
        type: DataTypes.STRING(30),
        allowNull: false,
      },
      file_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      file_path: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      file_ext: {
        type: DataTypes.STRING(5),
        allowNull: false,
      },
      file_size: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      file_dimension: {
        type: DataTypes.STRING(15),
        allowNull: false,
      },
      mime_type: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      file_type: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      jsonData: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_document_metadata",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_document_metadata_id",
          unique: true,
          fields: [{ name: "document_metadata_id" }],
        },
      ],
    }
  );
};
