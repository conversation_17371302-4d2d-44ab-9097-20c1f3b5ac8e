const { Op } = require('sequelize');
const capabilityListDetail = require("../model/capability.list.model");

const getAllActiveCapabilities = async () => {
    return await capabilityListDetail.findAll({
        where: {
            is_deleted: 0,
            is_active: 1,
        },
        raw: true
    });
};

const getCapabilitiesByParentIds = async (parentIds) => {
    return await capabilityListDetail.findAll({
        where: {
            is_deleted: 0,
            is_active: 1,
            [Op.or]: [
                { capability_list_id: parentIds },
                { parent_id: parentIds }
            ]
        },
        raw: true
    });
};

const getCapabilitiesWithParentIdZero = async () => {
    return await capabilityListDetail.findAll({
        where: {
            parent_id: 0,
            is_active: 1,
            is_deleted: 0,
        },
    });
};

const bulkCreateCapabilityList = async (dto) => {
    return await capabilityListDetail.bulkCreate(dto);
};

const showCapabilityList = async (id) => {
    const data = await capabilityListDetail.findByPk(id);
    return data;
};

const updateCapabilityList = async (id, dto) => {
    const data = await showCapabilityList(id);
    return data.update(dto);
}

const deleteCapabilityList = async (id, dto) => {
    const data = await updateCapabilityList(id, dto);
    return data;
}

module.exports = {
    getAllActiveCapabilities,
    updateCapabilityList,
    showCapabilityList,
    bulkCreateCapabilityList,
    deleteCapabilityList,
    getCapabilitiesWithParentIdZero,
    getCapabilitiesByParentIds,
};