const { sendSuccess, sendError } = require("../helpers/responseHelper");
const FOSuperVisorService = require("../services/fo-supervisor-service");

exports.getMROListing = async (req, res) => {
  try {
    const result = await FOSuperVisorService.getMROListing(req);

    if (result.notFound) {
      return sendError(req, res, {}, "No MRO listing found.", 404);
    }

    if (result.success) {
      return sendSuccess(req, res, result, "MRO listing fetched successfully.", 200);
    }

    return sendError(req, res, {}, "Unexpected response from service.", 500);
  } catch (error) {
    console.error("Controller error in getMROListing:", error);
    return sendError(req, res, error, error.message || "Internal server error", 500);
  }
};

// Controller for getting MRO Ticket details
exports.getMROTicketDetails = async (req, res) => {
  try {
    // Call service to get MRO Ticket details
    const result = await FOSuperVisorService.getMROTicketDetails(req.body);

    // If no result found, return a 404 error (Not Found)
    if (!result || result.length === 0) {
      return sendError(req, res, { message: 'No data found for the given MRO ticket' }, "No MRO ticket found", 404);
    }

    // Return success response with the formatted data
    sendSuccess(req, res, result, "Data fetched successfully.");
    
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getDetailedMROCustomerInfo = async (req, res) => {
  try {
    const result = await FOSuperVisorService.getDetailedMROCustomerInfo(req.body);

    if (!result) {
      return sendError(req, res, null, "Customer data not found", 404);
    }

    sendSuccess(req, res, result, "Fetched data successfully", 200);
  } catch (error) {
    console.error("Controller Error:", error);
    sendError(req, res, error, "Internal Server Error", 500);
  }
};

exports.getGeographicalAreas = async (req, res) => {
  try {
    const result = await FOSuperVisorService.getGeographicalAreas(req.body);

    if (!result || (Array.isArray(result) && result.length === 0)) {
      return res.status(404).json({
        success: false,
        message: "No geographical area data found.",
        data: null,
      });
    }

    return res.status(200).json({
      success: true,
      message: "Fetched data successfully.",
      data: result,
    });

  } catch (error) {
    console.error("Error in getGeographicalAreas:", error);

    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching geographical areas.",
      error: error.message || "Internal Server Error",
    });
  }
};


exports.getFOMetricsGA = async (req, res) => {
  try {
    const result = await FOSuperVisorService.getFOMetricsGA(req);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.meterReadingUploads = async (req, res) => {
  try {
    if (!req.file) {
      return sendError(req, res, {}, "Please upload meter reading image.", 400);
    }

    const supervisorId = req.user?.admin_id;

    const result = await FOSuperVisorService.saveMeterReading(req,supervisorId);

    if (!result.success) {
      return sendError(req, res, result.error || {}, result.message, 400);
    }

    return sendSuccess(req, res, result.data, result.message, 201);
  } catch (error) {
    return sendError(req, res, error, "Internal server error", 500);
  }
};

