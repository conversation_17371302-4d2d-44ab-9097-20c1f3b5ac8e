const { sendSuccess, sendError } = require('../helpers/responseHelper');
const dashBoarGraphdService = require('../services/dash-board-service');

exports.dashBoarGraphdDetails = async (req, res) => {
  try {
    // Ensure required fields are provided
    if (!req.body.customer_connection_id) {
      return sendError(req, res, {}, 'customer_connection_id is required.');
    }

    // Call service layer with req.body and req.files
    const result = await dashBoarGraphdService.dashBoarGraphdDetailsService(
      req.body
    );

    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.notification = async (req, res) => {
  console.log('1', req);
  try {
    // Call service layer with req.query
    const result = await dashBoarGraphdService.notificationDetails(req.query);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.banner = async (req, res) => {
  try {
    // Ensure required fields are provided
    if (!req.query.page_type) {
      return sendError(req, res, {}, 'page_type is required.');
    }
    // Call service layer with req.query
    const result = await dashBoarGraphdService.bannerImages(req.query);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.viewNotification = async (req, res) => {
  try {
    console.log("reqBody = ", req.body);
    
    // Call service layer with req.query
    const result = await dashBoarGraphdService.ViewNotificationDetails(req.body);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};