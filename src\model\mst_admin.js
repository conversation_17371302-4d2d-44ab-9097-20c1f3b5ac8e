const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_admin",
    {
      admin_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      persona_role_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_persona_role",
          key: "persona_role_id",
        },
      },
      bpcl_admin_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      user_role: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      prefix: {
        type: DataTypes.STRING(5),
        allowNull: null,
      },
      first_name: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      last_name: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      mobile: {
        type: DataTypes.STRING(10),
        allowNull: false,
        unique: "UK_mstadmin_mobile",
      },
      alternate_mobile: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: "UK_mstadmin_email",
      },
      bpcl_id: {
        type: DataTypes.STRING(30),
        allowNull: true,
      },
      position_id: {
        type: DataTypes.STRING(30),
        allowNull: true,
      },
      from_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      to_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING(40),
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING(40),
        allowNull: true,
      },
      last_logged_in: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      is_locked: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      password: {
        type: DataTypes.BLOB,
        allowNull: true,
      },
    },
    {
      sequelize,
      tableName: "mst_admin",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_mstadmin_admin_id",
          unique: true,
          fields: [{ name: "admin_id" }],
        },
        {
          name: "UK_mstadmin_email",
          unique: true,
          fields: [{ name: "email" }],
        },
        {
          name: "UK_mstadmin_mobile",
          unique: true,
          fields: [{ name: "mobile" }],
        },
      ],
    }
  );
};
