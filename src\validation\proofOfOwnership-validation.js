const { Joi } = require('express-validation');

const createProofOfOwnershipValidation = {
  body: Joi.object({
    
      ownership_name: Joi.string()
        .min(3)
        .max(200)
        .required(),
      is_active: Joi.number().optional().default(true).messages({
        'boolean.base': 'isActive must be a boolean value',
      }),
 
  }),
};


const updateProofOfOwnershipValidation = {
  body: Joi.object({

    proof_of_ownership_id: Joi.number().integer().optional().messages({
        'number.base': 'ProofOfOwnership_id must be a number',
      }),
      ownership_name: Joi.string()
        .max(200)
        .optional()
        .messages({
          'string.max': 'Name should not exceed 200 characters',
          'any.custom': '{{#message}}',
        }),
      is_active: Joi.number()
        .optional()
        .messages({
          'boolean.base': 'isActive must be a boolean value',
        }),
    }),
};

const deleteProofOfOwnershipValidation = {
  body: Joi.object({
    proof_of_ownership_id: Joi.number().integer().optional().messages({
      'number.base': 'proofOfOwnership_id must be a number',
    }),
  }),
};

const getProofOfOwnershipValidation = {
  body: Joi.object({
    page: Joi.number().integer().min(1).allow(null, '').default(null).messages({
      'number.base': 'Page must be a number',
      'number.min': 'Page must be greater than 0',
    }),
    limit: Joi.number().integer().min(1).allow(null, '').default(null).messages({
      'number.base': 'Limit must be a number',
      'number.min': 'Limit must be greater than 0',
    }),
    search: Joi.string().max(200).allow(null, '').messages({
      'string.max': 'Search query should not exceed 200 characters',
    }),
    orderBy: Joi.string().valid('asc', 'desc').allow(null, '').messages({
      'string.valid': 'OrderBy must be either "asc" or "desc"',
    }),
    orderName: Joi.string().allow(null, '').messages({
      'string.base': 'OrderName must be a string',
    }),
  }),
};

module.exports = {
  createProofOfOwnershipValidation,
  updateProofOfOwnershipValidation,
  deleteProofOfOwnershipValidation,
  getProofOfOwnershipValidation,
};
