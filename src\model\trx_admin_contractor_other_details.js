const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_admin_contractor_other_details",
    {
      tcaod_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_tacod_adminid_gaareaid",
      },
      ga_area_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_geographical_areas",
          key: "geographical_area_id",
        },
        unique: "UK_tacod_adminid_gaareaid",
      },
      agency_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_agencies",
          key: "agency_id",
        },
      },
      document_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: "other",
      },
      file_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      file_path: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      file_ext: {
        type: DataTypes.STRING(4),
        allowNull: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_admin_contractor_other_details",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_tacod_admin_mapped_ga_area_id",
          unique: true,
          fields: [{ name: "tcaod_id" }],
        },
        {
          name: "UK_tacod_adminid_gaareaid",
          unique: true,
          fields: [{ name: "admin_id" }, { name: "ga_area_id" }],
        },
      ],
    }
  );
};
