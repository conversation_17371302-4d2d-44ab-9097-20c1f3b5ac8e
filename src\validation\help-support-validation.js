const { Joi } = require('express-validation');
const {
  MOBILE_REGEX,
} = require('../constant/regex');

const reportIssueValidation = Joi.object({
  alternate_mobile_number: Joi.string().regex(MOBILE_REGEX).length(10).messages({
    'string.pattern.base': 'Mobile number must be exactly 10 digits.',
    'string.length': 'Mobile number must be exactly 10 digits.'
  }).allow(null, '').default(null),

  issue_category_id: Joi.number().required(),
  issue_type_id: Joi.number().required(),
  description: Joi.string().required(),

  customer_connection_id: Joi.number().allow(null, ''),
  bp_id: Joi.number().allow(null, ''),
}).or('customer_connection_id', 'bp_id').messages({
  'object.missing': 'Either customer_connection_id or bp_id is required.'
});

const deleteDocumentValidation = Joi.object({
  customer_connection_id: Joi.number().min(1).required(),
});

const uploadDocumentValidation = Joi.object({
  customer_connection_id: Joi.number().min(1).required(),
});

module.exports = {
  reportIssueValidation,
  deleteDocumentValidation,
  uploadDocumentValidation
};
