const proofMasterRepository = require('../repository/proof-master-repository');
const message = require('../utils/constants');

// const proofMasterDto = (reqBody) => ({
//   proof_master_id: reqBody.proof_master_id,
//   proof_type: reqBody.proof_type,
//   proof_name: reqBody.proof_name,
//   display_order: reqBody.display_order,
//   is_active: reqBody.is_active,
//   is_delete: reqBody.is_delete,
// });

const getProofMasterList = async ({ page, limit, proof_type }) => {
  const offset = (page - 1) * limit;

  try {
    // Fetch total count for pagination
    const totalRecords = await proofMasterRepository.countProofs({ proof_type });

    // Calculate total pages
    const totalPages = Math.ceil(totalRecords / limit);

    // Validate page number
    if (page > totalPages) {
      return {
        data: [],
        meta: {
          totalRecords,
          totalPages,
          currentPage: page,
        },
      };
    }

    // Fetch filtered records with pagination
    const proofMasters = await proofMasterRepository.getProofsByType({ proof_type, offset, limit });

    return {
      data: proofMasters,
      meta: {
        totalRecords,
        totalPages,
        currentPage: page,
      },
    };
  } catch (error) {
    throw new Error(`Database error: ${error.message}`);
  }
};


const createProofMaster = async (reqBody) => {
  const existing = await proofMasterRepository.checkExistingName(reqBody.proof_name);
  if (existing) {
    return { is_exist: true, message: message.NAME_ALREADY_EXIST };
  }
  return await proofMasterRepository.createProofMaster(reqBody);
};

const updateProofMaster = async (reqBody) => {
  const id = reqBody.proof_master_id;
  if (!id) {
    throw new Error("ID not found");
  }

  // const dto = proofMasterDto(reqBody);
  return proofMasterRepository.updateProofMaster(id, reqBody);
};

const deleteProofMaster = async (reqBody) => {
  const { proof_master_id, proof_type } = reqBody;

  // Validate required fields
  if (!proof_master_id) {
    throw new Error("proof_master_id is required");
  }
  if (!proof_type) {
    throw new Error("proof_type is required");
  }

  // Check if the proof_master_id and proof_type exist
  const existingRecord = await proofMasterRepository.checkProofMasterExistence(proof_master_id, proof_type);
  if (!existingRecord) {
    throw new Error("Record not found or already deleted");
  }

  // Perform the soft delete
  return proofMasterRepository.deleteProofMaster(proof_master_id, { is_delete: 0 });
};


module.exports = {
  getProofMasterList,
  createProofMaster,
  updateProofMaster,
  deleteProofMaster,
};
