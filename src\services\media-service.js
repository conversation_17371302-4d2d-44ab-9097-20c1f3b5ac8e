const tpiRepository = require("../repository/tpi-repository");
const message = require("../utils/constants");
const SASTokenHelper = require("../helpers/azureSASSDKHelper");
const OCRHelper = require("../helpers/OCRHelper");

const mediaUpload = async (req) => {
  try {
    const permission = 'racwd';
    let sasToken = await SASTokenHelper.generateSASToken(permission);
    sasToken = sasToken.replaceAll("%3A",":");
    const imageUploaded = await SASTokenHelper.azureSingleFileUpload(req, {}, sasToken, 'banners');
    return imageUploaded;
  } catch (error) {
    console.error("Error in getLmcCasesWithFilters:", error);
    throw error;
  }
};

const getAzureImage = async (req) => {
  try {
    const permission = 'racwd';
    const sasToken = await SASTokenHelper.generateSASToken(permission);
    const mediaPath = req.body.foldername+'/'+req.body.media;
    const imageUploaded = await SASTokenHelper.getAzureSingleFile(req, sasToken, mediaPath);
    
    return imageUploaded;
  } catch (error) {
    console.error("Error in get image:", error);
    throw error;
  }
};

const getAzureDownload = async (req) => {
  try {
    const permission = 'racwd';
    const sasToken = await SASTokenHelper.generateSASToken(permission);
    const mediaPath = req.body.foldername+'/'+req.body.media;
    const imageUploaded = await SASTokenHelper.getAzureDownload(req, sasToken, mediaPath);
    
    return imageUploaded;
  } catch (error) {
    console.error("Error in get image:", error);
    throw error;
  }
};

const deleteAzure = async (req) => {
  try {
    const permission = 'racwd';
    const sasToken = await SASTokenHelper.generateSASToken(permission);
    const mediaPath = req.body.foldername+'/'+req.body.media;
    const container = process.env.AZURE_CONTAINER_NAME;
    const imageUploaded = await SASTokenHelper.deleteBlob(container, mediaPath);
    
    return imageUploaded;
  } catch (error) {
    console.error("Error in delete document:", error);
    throw error;
  }
};

const OCRUpload = async (req) => {
  try {
    const permission = 'racwdt';
    let sasToken = await SASTokenHelper.generateSASToken(permission);
    sasToken = sasToken.replaceAll("%3A",":");
    const imageUploaded = await SASTokenHelper.azureSingleFileUpload(req, {}, sasToken, 'test');

    let result = await OCRHelper.getOCRImageDetails(imageUploaded.blob_url);
    // console.log("Successsss ??????", result);
    return result;
  } catch (error) {
    console.error("Error in getLmcCasesWithFilters:", error);
    throw error;
  }
};

const imageRead = async (req) => {
  try {
    let result = await OCRHelper.getOCRImageDetails();
    // console.log("Successsss ??????", result);
    return result;
  } catch (error) {
    console.error("Error in getLmcCasesWithFilters:", error);
    throw error;
  }
};

module.exports = {
  mediaUpload,
  getAzureImage,
  getAzureDownload,
  deleteAzure,
  OCRUpload,
  imageRead
};
