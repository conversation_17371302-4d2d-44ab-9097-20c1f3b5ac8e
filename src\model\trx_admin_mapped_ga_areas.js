const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_admin_mapped_ga_areas",
    {
      amga_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_trx_amga_adminid_gaareaid",
      },
      ga_area_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_geographical_areas",
          key: "geographical_area_id",
        },
        unique: "UK_trx_amga_adminid_gaareaid",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_admin_mapped_ga_areas",
      schema: "dbo",
      timestamps: true,
      createdAt: 'created_at', 
      updatedAt: 'updated_at', 
      indexes: [
        {
          name: "PK_amga_id",
          unique: true,
          fields: [{ name: "amga_id" }],
        },
        {
          name: "UK_trx_amga_adminid_gaareaid",
          unique: true,
          fields: [{ name: "admin_id" }, { name: "ga_area_id" }],
        },
      ],
    }
  );
};
