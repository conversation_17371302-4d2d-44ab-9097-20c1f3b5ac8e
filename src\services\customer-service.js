const {
  createUpdateConnection,
  getCustomerList,
  createRegistrationHelp,
  getCustomerDetails,
  getCustomerAddressCount,
  getConnectionPaymentDetails,
  getCustomer,
  getActiveCustomer,
  customerApprovalDecline,
  customerApprovalAccept,
  getExistingHelpWIthRegRequest,
  updateCustomer,
  checkReworkExpiryCron
} = require("../repository/customer-repository");
const {
  getPipelineDetails,
  updatePipelineDetails,
  updateMapCustomerConnectionDetails,
} = require("../repository/lmc-repository");

const { slugify, generateTempId } = require("../utils/commonFn");
const path = require("path");
const fs = require("fs");
const jwtHelper = require("../helpers/jwtHelper");
const redisHelper = require("../helpers/redisHelper");
const { APPLICATION_EXPIRY_TIME } = require("../../config");
const azureSASHelper = require("../helpers/azureSASSDKHelper");
const { log } = require("console");
const { Op } = require("sequelize");
const {
  PAYMENT_SECTION_TYPE,
  PROGRESS_STATUS,
  PAYMENT_MODE,
  APPLICATION_DECLINE_TYPE,
  DECLINE_REASON,
} = require("../utils/constants");
const { env } = require("process");
const { ACCOUNT_TYPE } = require("../utils/constants");
const Joi = require("joi");
const validator = require("../middleware/validator");
const {
  getAreaDetailsById,
  getGovtSchemeById,
  getProofById,
} = require("../repository/master-repository");
const { createDmaHelpWithRegMapping } = require("../repository/dma-repository");
const { dbModels } = require("../database/connection");
const { updateHelpWithRegistration } = require("../repository/dma-customer-repository");


const getValidCustomer = async (mobile) => {
  return await getActiveCustomer(mobile);
};
const customerLogin = async (mobile) => {
  const result = { token: "", customerExists: false };
  try {
    const user = await getCustomer(mobile);
    if (user && !user.is_active) {
      throw new Error("Customer is inactive");
    }
    let jwtPayload = { mobile };
    if (!user) {
      const redisData = await redisHelper.getData(`BPCLID_${mobile}`);
      if (redisData) {
        result.customerExists = true;
      }
    } else {
      jwtPayload = user;
      result.customerExists = true;
    }
    const tokenResult = await jwtHelper.createPngToken(jwtPayload, true);
    if (tokenResult.error) {
      throw new Error("Failed to generate token: " + tokenResult.error);
    }

    result.token = tokenResult.token;
    result.refreshToken = tokenResult.refreshToken;
    return result;
  } catch (error) {
    console.error(
      "Customer Refresh Token error details:",
      error.name,
      error.message,
      error.stack
    );
    throw error;
  }
};

const customerRefreshToken = async (userData) => {
  const result = { token: "" };
  try {
    delete userData.exp;
    delete userData.iat;
    const tokenResult = await jwtHelper.createPngToken(userData);
    if (tokenResult.error) {
      throw new Error("Failed to generate token: " + tokenResult.error);
    }

    result.token = tokenResult.token;

    return result;
  } catch (error) {
    console.error(
      "Customer Refresh Token error details:",
      error.name,
      error.message,
      error.stack
    );
    throw error;
  }
};

const customerLogout = async (pngToken) => {
  try {
    return await redisHelper.deleteData(pngToken);
  } catch (error) {
    console.error(
      "Customer Logout error details:",
      error.name,
      error.message,
      error.stack
    );
    throw error;
  }
};
const createTempRegistrationId = async (
  bpclId,
  flatNo,
  pngAreaId,
  personaDetails = null
) => {
  const connectionRedisKey = redisHelper.getRedisPngAvailableConnectionKey(
    bpclId,
    flatNo,
    pngAreaId,
    personaDetails
  );
  const checkConnectionRedisKeyData =
    await redisHelper.getData(connectionRedisKey);
  let tempRegistrationId;

  tempRegistrationId = generateTempId();
  if (!checkConnectionRedisKeyData) {
    await redisHelper.setData(
      tempRegistrationId,
      connectionRedisKey,
      APPLICATION_EXPIRY_TIME
    );
    await redisHelper.setData(
      connectionRedisKey,
      {
        registrationId: tempRegistrationId,
      },
      APPLICATION_EXPIRY_TIME
    );
    await redisHelper.setData(
      `BPCLID_${bpclId}`,
      tempRegistrationId,
      APPLICATION_EXPIRY_TIME
    );
  } else {
    tempRegistrationId = checkConnectionRedisKeyData?.registrationId
      ? checkConnectionRedisKeyData?.registrationId
      : tempRegistrationId;
  }
  return { connectionRedisKey, tempRegistrationId };
};

const setAndGetPersonaDetails = (personaType, userData) => {
  return personaType != "customer" &&
    userData.customer_type &&
    userData.customer_type == "self-registered"
    ? { personaType, personaUserId: userData.admin_id }
    : null;
};

const updateHelpWithRegistrationCustomer = async (detailsData, userData) => {
  try {
    if (detailsData?.bpcl_id) {
      await updateHelpWithRegistration(detailsData, userData);
    }
  } catch (error) {
    console.error("Error in updateHelpWithRegistrationCustomer:", error);
    throw error;
  }
};


const submitDetailsToCache = async (detailsData, personaType, userData) => {
  try {
    console.log("Received detailsData:", detailsData); // Debugging
    const personaDetails = setAndGetPersonaDetails(personaType, userData);
    // Generate a Redis key based on the payload
    const { connectionRedisKey, tempRegistrationId } =
      await createTempRegistrationId(
        detailsData?.bpcl_id,
        detailsData?.house_flat_no,
        detailsData?.available_pngareas_id,
        personaDetails
      );
    // Fetch existing data from Redis
    let getConnectionDetailsFromRedis =
      (await redisHelper.getData(connectionRedisKey)) || {};
    console.log(
      "1- getConnectionDetailsFromRedis",
      getConnectionDetailsFromRedis
    );

    getConnectionDetailsFromRedis.registrationId = tempRegistrationId;
    if (detailsData?.connection_address) {
      const whereParams = {
        connection_detail: {
          bpcl_id: detailsData.bpcl_id,
          available_pngareas_id: Number(detailsData.available_pngareas_id),
        },
        connection_address: {
          connection_house_no: detailsData.house_flat_no.toString(),
        },
      };

      // Chekecka details of the customer from DB
      const checkConnectionExist = await getCustomerAddressCount(whereParams);
      console.log("checkConnectionExist", checkConnectionExist);

      if (
        detailsData.old_available_pngareas_id !==
        detailsData.available_pngareas_id
      ) {
        const oldConnectionRedisKey =
          await redisHelper.getRedisPngAvailableConnectionKey(
            detailsData?.bpcl_id,
            detailsData?.old_house_flat_no,
            detailsData?.old_available_pngareas_id
          );
        // Fetch existing data from Redis
        console.log("oldConnectionRedisKey", oldConnectionRedisKey);
        let getOldConnectionDetails = await redisHelper.getData(
          oldConnectionRedisKey
        );
        console.log("getOldConnectionDetails", getOldConnectionDetails);
        if (getOldConnectionDetails) {
          getConnectionDetailsFromRedis = getOldConnectionDetails;
          console.log(
            "2- getConnectionDetailsFromRedis",
            getConnectionDetailsFromRedis
          );
          await redisHelper.deleteData(connectionRedisKey);
        }
      }
      if (
        !detailsData.customer_connection_id &&
        (checkConnectionExist || checkConnectionExist > 0)
      ) {
        throw new Error("Connection already exists with the same details");
      }
    }
    if (detailsData.step_no == 1 && personaType == "dma") {
      const helpWithRegParams = {
        bpcl_id: detailsData.bpcl_id,
        flat_no: detailsData.house_flat_no,
        available_pngareas_id: detailsData.available_pngareas_id,
        fullname:
          detailsData.personal_details.first_name +
          " " +
          detailsData.personal_details.middle_name +
          " " +
          detailsData.personal_details.last_name,
        mobile: detailsData.personal_details.mobile,
        pincode: 0,
        area: "",
        email: detailsData.personal_details.email,
        customer_type: "self-registered",
      };
      const helpWithRegResult = await createRegistrationHelpService(
        helpWithRegParams,
        personaDetails
      );
      console.log(
        "helpWIthRegResult",
        helpWithRegResult,
        helpWithRegResult.help_with_registration_id,
        helpWithRegResult.respStatus
      );
      if (
        helpWithRegResult &&
        helpWithRegResult.help_with_registration_id &&
        !helpWithRegResult.respStatus
      ) {
        const helpWithRegMappingParams = {
          help_with_registration_id:
            helpWithRegResult.help_with_registration_id,
          dma_supervisor_assigned_id: userData.admin_id,
          hwrm_status: "inprogress",
          is_active: true,
        };
        const helpWithRegMappingResult = await createDmaHelpWithRegMapping(
          helpWithRegMappingParams
        );
        console.log("helpWithRegMappingResult", helpWithRegMappingResult);
      }
    }
    // Update only provided fields while keeping existing ones

    const result = await updateRedisData(
      getConnectionDetailsFromRedis,
      connectionRedisKey,
      detailsData,
      personaType,
      userData
    );
    return { result };
  } catch (err) {
    console.error("Error in submitDetailsToCache:", err);
    throw err;
  }
};
const updateRedisData = async (
  getConnectionDetails,
  connectionRedisKey,
  detailsData,
  personaType,
  userData
) => {
  try {
    console.log("getConnectionDetails", getConnectionDetails); // Debugging

    const expireTime = APPLICATION_EXPIRY_TIME; // 1 hour in seconds
    const createdBy = personaType != "customer" ? userData.admin_id : 0;
    const createdByType =
      personaType != "customer" ? "dma-supervisor" : "customer";

    // Check if the key exists in Redis
    getConnectionDetails.customerConnectionDetails = mergeData(
      getConnectionDetails.customerConnectionDetails,
      {
        bpcl_id: detailsData?.bpcl_id,
        customer_connection_id: detailsData?.customer_connection_id,
        available_pngareas_id: detailsData?.available_pngareas_id,
        prefix: detailsData?.personal_details?.prefix,
        first_name: detailsData?.personal_details?.first_name,
        middle_name: detailsData?.personal_details?.middle_name,
        last_name: detailsData?.personal_details?.last_name,
        email: detailsData?.personal_details?.email,
        mobile: detailsData?.personal_details?.mobile,
        alternate_mobile: detailsData?.personal_details?.alternate_mobile,
        dob: detailsData?.personal_details?.dob,
        no_of_family_members:
          detailsData?.personal_details?.no_of_family_members,
        parents_name: detailsData?.personal_details?.parents_name,
        spouse_name: detailsData?.personal_details?.spouse_name,
        connection_address_type:
          detailsData?.addition_info?.connection_address_type,
        is_extra_connection:
          detailsData?.addition_info?.extra_connection === "no" ? 0 : 1,
        extra_points: detailsData?.addition_info?.extra_points,
        govt_scheme_id: detailsData?.addition_info?.govt_scheme_id,
        // is_security_deposit:
        // detailsData?.addition_info?.security_deposit === "false" ? 0 : 1,
        // security_deposit_category:// detailsData?.addition_info?.security_deposit_category,
        account_type: ACCOUNT_TYPE.POSTPAID,
        is_lpg: detailsData?.addition_info?.is_lpg,
        lpg_consumer_no: detailsData?.addition_info?.lpg_consumer_no,
        lpg_distributor_name: detailsData?.addition_info?.lpg_distributor_name,
        lpg_omc_name: detailsData?.addition_info?.lpg_omc_name,
        rental_owner_name: detailsData?.addition_info?.rental_owner_name,
        rental_owner_mobile: detailsData?.addition_info?.rental_owner_mobile,
        lpg_consent: 0,
        rental_owner_name: detailsData?.addition_info?.rental_owner_name,
        rental_owner_mobile: detailsData?.addition_info?.rental_owner_mobile,
        is_active: 1,
        approved_date: null,
        onboarding_date: null,
        installation_date: null,
        disconnected_date: null,
        status: "application-pending",
        isu_synced: 0,
        is_delete: 0,
        created_by: createdBy,
        created_by_type: createdByType,
        createdAt: new Date(),
        updatedAt: new Date(),
        updated_by: 0,
      }
    );
    if (detailsData?.connection_address) {
      getConnectionDetails.customerConnectionAddress = mergeData(
        getConnectionDetails.customerConnectionAddress,
        {
          connection_pngareas_id:
            detailsData?.connection_address?.connection_pngareas_id,
          connection_pincode:
            detailsData?.connection_address?.connection_pincode,
          connection_landmark:
            detailsData?.connection_address?.connection_landmark,
          connection_block_no:
            detailsData?.connection_address?.connection_block_no,
          connection_floor_no:
            detailsData?.connection_address?.connection_floor_no,
          connection_house_no:
            detailsData?.connection_address?.connection_house_no,
          connection_building_house_name:
            detailsData?.connection_address?.connection_building_house_name,
          connection_state: detailsData?.connection_address?.connection_state,
          connection_city: detailsData?.connection_address?.connection_city,
          connection_district:
            detailsData?.connection_address?.connection_district,
          same_as_connection:
            detailsData?.connection_address?.same_as_connection,

          permanent_pngareas_id:
            detailsData?.connection_address?.permanent_pngareas_id,
          permanent_pincode: detailsData?.connection_address?.permanent_pincode,
          permanent_landmark:
            detailsData?.connection_address?.permanent_landmark,
          permanent_block_no:
            detailsData?.connection_address?.permanent_block_no,
          permanent_floor_no:
            detailsData?.connection_address?.permanent_floor_no,
          permanent_building_house_name:
            detailsData?.connection_address?.permanent_building_house_name,
          permanent_house_no:
            detailsData?.connection_address?.permanent_house_no,
          permanent_state: detailsData?.connection_address?.permanent_state,
          permanent_city: detailsData?.connection_address?.permanent_city,
          permanent_district:
            detailsData?.connection_address?.permanent_district,
          created_by: 0,
          updated_by: 0,
          connection_type: detailsData?.connection_address?.connection_type,
          connection_change_reason:
            detailsData?.connection_address?.connection_change_reason,
        }
      );
    }

    getConnectionDetails.customerAdditionalInfo = mergeData(
      getConnectionDetails.customerAdditionalInfo,
      {
        connection_address_type:
          detailsData?.addition_info?.connection_address_type,
        extra_connection: detailsData?.addition_info?.extra_connection,
        extra_points: detailsData?.addition_info?.extra_points,
        govt_scheme_id: detailsData?.addition_info?.govt_scheme_id,
        // security_deposit: detailsData?.addition_info?.security_deposit,
        // security_deposit_category:detailsData?.addition_info?.security_deposit_category,
        is_lpg: detailsData?.addition_info?.is_lpg,
        lpg_consumer_no: detailsData?.addition_info?.lpg_consumer_no,
        lpg_distributor_name: detailsData?.addition_info?.lpg_distributor_name,
        lpg_omc_name: detailsData?.addition_info?.lpg_omc_name,
        rental_owner_name: detailsData?.addition_info?.rental_owner_name,
        rental_owner_mobile: detailsData?.addition_info?.rental_owner_mobile,
      }
    );
    console.log("Updated getConnectionDetails:", getConnectionDetails);

    // Store updated data back in Redis
    let result = await redisHelper.setData(
      connectionRedisKey,
      getConnectionDetails,
      expireTime
    );

    console.log("Redis set result:", result);
    return result;
  } catch (error) {
    console.error("Error in updateRedisData:", error);
    throw error;
  }
};
// Helper function to merge new data while keeping old data intact
const mergeData = (existingData = {}, newData = {}) => {
  return {
    ...existingData,
    ...Object.fromEntries(
      Object.entries(newData).filter(
        ([_, value]) => value !== null && value !== undefined
      )
    ),
  };
};
const uploadDocuments = async (
  detailsData,
  files,
  personaType = "customer",
  userData = null
) => {
  try {
    console.log("Received files:", files); // Debugging

    const personaDetails = setAndGetPersonaDetails(personaType, userData);
    const redisKey = redisHelper.getRedisPngAvailableConnectionKey(
      detailsData.bpcl_id,
      detailsData.house_flat_no,
      detailsData.available_pngareas_id,
      personaDetails
    );
    const { connectionRedisKey, tempRegistrationId } =
      await createTempRegistrationId(
        detailsData?.bpcl_id,
        detailsData?.house_flat_no,
        detailsData?.available_pngareas_id,
        personaDetails
      );
    await uploadDocRedis(redisKey, detailsData, files);

    return { message: "Files uploaded and stored in Redis.", files };
  } catch (error) {
    console.error("Error in uploadDocuments:", error);
    throw error;
  }
};
const uploadDocRedis = async (redisKey, detailsData, files) => {
  try {
    files = [files];
    // Fetch a details of the customer from Redis
    let getConnectionDetails = await redisHelper.getData(redisKey);
    console.log("getConnectionDetails", getConnectionDetails);
    console.log("detailsData", detailsData);
    getConnectionDetails = getConnectionDetails ? getConnectionDetails : {};
    // if (getConnectionDetails) {
    // get file ext
    const extname = path.extname(files[0].filename);

    let proofDoc = {
      proof_type: detailsData.proof_type,
      proof_master_id: detailsData.proof_master_id,
      file_name: files[0].filename,
      file_path: "temp",
      file_mimetype: files[0].mimetype,
      file_size: files[0].size,
      file_ext: extname,
      verification_status: "application-pending",
    };
    detailsData.customer_connection_id
      ? (proofDoc.customer_connection_id = detailsData.customer_connection_id)
      : "";
    if (getConnectionDetails && getConnectionDetails.uploadDocuments) {
      // Function to count occurrences of proof_type
      const countProofTypes = (documents) => {
        return documents.reduce((acc, doc) => {
          acc[doc.proof_type] = (acc[doc.proof_type] || 0) + 1;
          return acc;
        }, {});
      };

      // Get counts
      const proofTypeCounts = countProofTypes(
        getConnectionDetails.uploadDocuments
      );
      console.log("Proof type counts:", proofTypeCounts);

      if (proofTypeCounts.photo_proof && proofTypeCounts.photo_proof > 2) {
        throw new Error(
          "Photo Proof document upload limit exceeded. Maximum 2 allowed."
        );
      } else if (
        proofTypeCounts.proof_of_ownership &&
        proofTypeCounts.proof_of_ownership > 2
      ) {
        throw new Error(
          "Photo Proof document upload limit exceeded. Maximum 2 allowed."
        );
      }

      //check file exist
      const foundDocument = getConnectionDetails.uploadDocuments.find(
        (doc) =>
          doc.proof_type === proofDoc.proof_type &&
          doc.proof_master_id === proofDoc.proof_master_id
      );

      if (foundDocument) {
        throw new Error(
          "This Proof Type already uploaded. Please delete the existing one to upload new one"
        );
      }
      let uploadDocuments = getConnectionDetails.uploadDocuments;
      uploadDocuments.push(proofDoc);
    } else {
      let uploadDocuments = [];
      uploadDocuments.push(proofDoc);
      getConnectionDetails.uploadDocuments = uploadDocuments;
    }

    // Store the updated file details with upload document in Redis
    await redisHelper.setData(redisKey, getConnectionDetails, 3600);
    // } else {
    //   throw new Error(
    //     "No data found for this Connection, Cache Expiered. Please Try again filling all details"
    //   );
    // }
  } catch (error) {
    console.error("Error in uploadDocuments:", error);
    throw error;
  }
};

const getCustomerListService = async (payload) => {
  let connectionList = [];

  const connectionListDB = await getCustomerList(payload);
  console.log("connectionListDB", connectionListDB);

  if (connectionListDB) {
    connectionList = connectionListDB;
  }

  if (!payload.active_status) {
    // Fetch data for all matching Redis keys
    const keys = await redisHelper.scanKeysByPrefix(
      `bpclid_${payload.bpcl_id}`
    );
    if (keys.length === 0) {
      console.log("No keys found for prefix:", `bpclid_${payload.bpcl_id}`);
      // return null;
    } else {
      // Fetch values for all Redis keys found in the scan
      const connectionListRedis = await Promise.all(
        keys.map((key) => redisHelper.getData(key))
      );
      if (connectionListRedis) {
        for (let payload of connectionListRedis) {
          if (connectionListDB) {
            const existingconnection = connectionListDB.find(
              (customer) =>
                payload.customerConnectionDetails?.customer_connection_id &&
                customer.customerConnectionDetails.customer_connection_id ===
                  payload.customerConnectionDetails?.customer_connection_id
            );
            if (!existingconnection) {
              connectionList.push(payload);
            }
          } else {
            connectionList.push(payload);
          }
        }
      }
    }
  }
  if (connectionList.length == 0) {
    throw new Error("No data found for this Connection.");
  }
  return connectionList;
};

const createRegistrationHelpService = async (
  requestParams,
  personaDetails = null
) => {
  const { connectionRedisKey, tempRegistrationId } =
    await createTempRegistrationId(
      requestParams.bpcl_id,
      requestParams.flat_no,
      requestParams.available_pngareas_id,
      personaDetails
    );

  const checkRequestExist = await getExistingHelpWIthRegRequest(
    requestParams.bpcl_id,
    requestParams.flat_no,
    requestParams.available_pngareas_id
  );

  requestParams.address = " ";
  requestParams.temp_registration_id = tempRegistrationId;
  requestParams.status = "not-started";
  if (checkRequestExist) {
    checkRequestExist.respStatus = 1;
    return checkRequestExist;
  } else {
    return await createRegistrationHelp(requestParams);
  }
};

const CustomerDetailById = async (payload, personaType, userData) => {
  try {
    let result;
    const personaDetails = setAndGetPersonaDetails(personaType, userData);
    if ((payload.house_flat_no, payload.available_pngareas_id)) {
      // Redis key based on the current payload values
      const redisKey = redisHelper.getRedisPngAvailableConnectionKey(
        payload.bpcl_id,
        payload.house_flat_no,
        payload.available_pngareas_id,
        personaDetails
      );

      // Fetch a details of the customer from Redis
      result = await redisHelper.getData(redisKey);
    }

    if (result) {
      // Fetch a getProofName from DB
      if (result.uploadDocuments) {
        for (let i = 0; i < result.uploadDocuments.length; i++) {
          const proofName = await getProofById(
            result.uploadDocuments[i].proof_master_id
          );
          console.log("proofName", proofName);

          result.uploadDocuments[i].proof_name = proofName.proof_name;
        }
      }

      // Fetch a getGovtSchemeById details name from DB
      if (result.customerConnectionDetails?.govt_scheme_id) {
        const getSchemaDetails = await getGovtSchemeById(
          result.customerConnectionDetails.govt_scheme_id
        );
        result.customerConnectionDetails.scheme_name =
          getSchemaDetails.scheme_name;
      }
      // Fetch a getConnectionAreaDetails details name from DB
      if (result.customerConnectionAddress?.connection_pngareas_id) {
        const getConnectionAreaDetails = await getAreaDetailsById(
          result.customerConnectionAddress?.connection_pngareas_id
        );
        result.customerConnectionAddress.connection_area_name =
          getConnectionAreaDetails?.pincodeAreasAlias?.area?.area_name;

        // Fetch a getPermanentAreaDetails details name from DB
        if (
          result.customerConnectionAddress.connection_pngareas_id &&
          result.customerConnectionAddress.permanent_pngareas_id &&
          result.customerConnectionAddress.connection_pngareas_id !==
            result.customerConnectionAddress.permanent_pngareas_id
        ) {
          const getPermanentAreaDetails = await getAreaDetailsById(
            result.customerConnectionAddress?.permanent_pngareas_id
          );
          result.customerConnectionAddress.permanent_pngarea_name =
            getPermanentAreaDetails.pincodeAreasAlias?.area?.area_name;
        } else {
          result.customerConnectionAddress.permanent_pngarea_name =
            getConnectionAreaDetails.pincodeAreasAlias?.area?.area_name;
        }
      }
      // result;
    }
    console.log("result", result);

    if (payload.customer_connection_id) {
      // Fetch a details of the customer from DB
      const dbResult = await getCustomerDetails({
        connection_detail: {
          customer_connection_id: payload.customer_connection_id,
        },
      });
      if (dbResult) {
        const customerAdditionalInfo = {
          connection_address_type:
            dbResult.connection_details.connection_address_type,
          extra_connection: dbResult.connection_details.is_extra_connection===1?'yes':'no',
          extra_points: dbResult.connection_details.extra_points,
          govt_scheme_id: dbResult.connection_details.govt_scheme_id,
          // security_deposit: dbResult.connection_details.security_deposit,
          // security_deposit_category:dbResult.connection_details.security_deposit_category,
          is_lpg: dbResult.connection_details.is_lpg,
          lpg_consumer_no: dbResult.connection_details.lpg_consumer_no,
          lpg_distributor_name:
            dbResult.connection_details.lpg_distributor_name,
          lpg_omc_name: dbResult.connection_details.lpg_omc_name,
          rental_owner_name: dbResult.connection_details.rental_owner_name,
          rental_owner_mobile: dbResult.connection_details.rental_owner_mobile,
        };
        dbResult.additional_info = customerAdditionalInfo;
        if (payload.get_type && payload.get_type === "rework") {
          let modifiedResults = {};
          if (
            result &&
            result.customerConnectionAddress?.connection_pngareas_id
          ) {
            console.log("redis");
          } else {
            console.log("DB");
          }
          modifiedResults.customerConnectionDetails =
            result && result.customerConnectionDetails.mobile
              ? result.customerConnectionDetails
              : dbResult.connection_details;

          modifiedResults.customerConnectionAddress =
            result && result.customerConnectionAddress?.connection_pngareas_id
              ? result.customerConnectionAddress
              : dbResult.connection_addresses;

          modifiedResults.customerAdditionalInfo =
            result && result.customerAdditionalInfo?.connection_address_type
              ? result.customerAdditionalInfo
              : dbResult.additional_info;

          modifiedResults.uploadDocuments =
            result && result.uploadDocuments
              ? result.uploadDocuments
              : dbResult.proof_documents;

          result = modifiedResults;
        } else {
          result = dbResult;
        }
      }
    }

    if (result) {
      return result;
    }
    throw new Error("No data found for this Connection.");
  } catch (err) {
    console.error("Error in submitDetailsToCache:", err);
    throw err;
  }
};

const submitDetailToDB = async (detailsData, file, personaType, userData) => {
  try {
    const personaDetails = setAndGetPersonaDetails(personaType, userData);
    // Generate a new key based on the current payload values
    const connectionRedisKey = redisHelper.getRedisPngAvailableConnectionKey(
      detailsData.bpcl_id,
      detailsData.house_flat_no,
      detailsData.available_pngareas_id,
      personaDetails
    );

    // Fetch a details of the customer from Redis
    const getConnectionRedis = await redisHelper.getData(connectionRedisKey);

    if (getConnectionRedis) {
      validator("customer-validation", "getCustomerDetailValidation");
      // Save the details of customer from Redis to DB
      const result = await createUpdateConnection(
        getConnectionRedis,
        personaType,
        userData
      );
      console.log("result", result);
      if (
        result &&
        result.customer_connection_id &&
        result.status == "application-submitted"
      ) {
        await dbModels.trx_help_with_registration.update(
          { customer_connection_id: result.customer_connection_id },
          { where: { temp_registration_id: getConnectionRedis.registrationId } }
        );
      }
      if (result.status == "application-submitted") {
        const tokenData = await jwtHelper.createPngToken(result, true);
        console.log("tokenData", tokenData);

        result.token = tokenData.token;
        result.refreshToken = tokenData.refreshToken;
      }
      // Delete from the Redis
      await redisHelper.deleteData(connectionRedisKey);

      return result;
    } else {
      throw new Error(
        // personaDetails,
        // connectionRedisKey,
        // getConnectionRedis,
        // message:
        "No data found for this Connection, Cache Expiered. Please Try again filling all details"
      );
    }
    return { result };
  } catch (err) {
    console.error("Error in submitDetailsToCache:", err);
    console.log("5", err.parameters);

    throw err;
  }
};

const customerDocumentDeleteService = async (
  detailsData,
  personaType,
  userData
) => {
  try {
    const {
      bpcl_id,
      house_flat_no,
      available_pngareas_id,
      proof_type,
      proof_master_id,
    } = detailsData;
    const personaDetails = setAndGetPersonaDetails(personaType, userData);
    // Generate Redis Key
    const redisKey = redisHelper.getRedisPngAvailableConnectionKey(
      bpcl_id,
      house_flat_no,
      available_pngareas_id,
      personaDetails
    );
    console.log(redisKey);
    // Fetch a document  details of the customer from Redis
    return await documentDeleteRedis(redisKey, proof_type, proof_master_id);
  } catch (error) {
    console.error("Error in deleteDocument:", error);
    throw error;
  }
};

const documentDeleteRedis = async (redisKey, proof_type, proof_master_id) => {
  try {
    // Fetch a details of the customer from Redis

    // Fetch connection details from Redis
    let getConnectionDetails = await redisHelper.getData(redisKey);
    console.log(getConnectionDetails);

    if (!getConnectionDetails) {
      throw new Error("No data found for this Connection, Cache Expired.");
    }
    // Check if `uploadDocuments` exists
    if (
      !getConnectionDetails.uploadDocuments ||
      !Array.isArray(getConnectionDetails.uploadDocuments)
    ) {
      throw new Error("No documents found in Redis.");
    }

    // Find the document to delete
    const documentToDelete = getConnectionDetails.uploadDocuments.find(
      (doc) =>
        doc.proof_type == proof_type && doc.proof_master_id == proof_master_id
    );

    if (!documentToDelete) {
      throw new Error(
        `No document found with proof_type: ${proof_type} and proof_master_id: ${proof_master_id}`
      );
    }
    console.log("documentToDelete", documentToDelete);

    // Delete the file from Azure Blob Storage
    const container = process.env.AZURE_CONTAINER_NAME;
    const azureBlobContainerName =
      documentToDelete.file_path + "/" + documentToDelete.file_name;
    // const azureBlobFileName = documentToDelete.file_name;
    await azureSASHelper.deleteBlob(container, azureBlobContainerName);

    // ✅ Remove the document from Redis
    getConnectionDetails.uploadDocuments =
      getConnectionDetails.uploadDocuments.filter(
        (doc) =>
          !(
            doc.proof_type == proof_type &&
            doc.proof_master_id == proof_master_id
          )
      );
    if (
      getConnectionDetails.uploadDocuments &&
      getConnectionDetails.uploadDocuments.length == 0
    ) {
      delete getConnectionDetails.uploadDocuments;
    }
    // ✅ Update Redis
    await redisHelper.setData(redisKey, getConnectionDetails, 3600);

    return `Document for proof_type: ${proof_type} and proof_master_id: ${proof_master_id} deleted successfully.`;
  } catch (error) {
    console.error("Error in deleteDocument:", error);
    throw error;
  }
};

const pincodeConnectionCheckService = async (params) => {
  try {
    console.log("params", params);

    const { bpcl_id, available_pngareas_id, house_flat_no } = params;
    // Get Redis Key
    const redisKey = redisHelper.getRedisPngAvailableConnectionKey(
      bpcl_id,
      house_flat_no,
      available_pngareas_id
    );

    // check a details of the connection from Redis
    let getConnectionDetails = await redisHelper.getData(redisKey);

    if (getConnectionDetails) {
      throw new Error("Connection exists.");
    } else {
      const whereParams = {
        connection_detail: {
          bpcl_id,
          available_pngareas_id,
        },
        connection_address: {
          connection_house_no: house_flat_no.toString(),
        },
      };
      console.log("whereParams", whereParams);

      // Fetch a details of the customer from DB
      result = await getCustomerAddressCount(whereParams);
      if (result || result > 0) {
        throw new Error("Connection exists.");
      } else {
        const expireTime = APPLICATION_EXPIRY_TIME; // 1 hour in seconds

        // Check if the key exists in Redis
        const customerConnectionDetails = {
          bpcl_id: params?.bpcl_id,
          available_pngareas_id: params?.available_pngareas_id,
          createdAt: new Date(),
          connection_pincode: params?.connection_pincode,
          status: "application-pending",
        };

        const customerConnectionAddress = {
          connection_pincode: params?.connection_pincode,
          connection_pngareas_id: params?.available_pngareas_id,
          connection_landmark: params?.connection_landmark,
          connection_block_no: params?.connection_block_no,
          connection_floor_no: params?.connection_floor_no,
          connection_house_no: params?.connection_house_no,
          connection_building_house_name:
            params?.connection_building_house_name,
          connection_state: params?.connection_state,
          connection_city: params?.connection_city,
          connection_district: params?.connection_district,
        };
        getConnectionDetails = {
          customerConnectionDetails,
          customerConnectionAddress,
        };
        // Store updated data back in Redis
        await redisHelper.setData(redisKey, getConnectionDetails, expireTime);
        return "Connection not exists.";
      }
    }
  } catch (error) {
    console.error("Error in pincodeConnectionCheckService:", error);
    throw error;
  }
};
const extraChargeApprovalService = async (params) => {
  try {
    // get lmc cusomter connection table details by connection id
    const getLmcConnectionResult = await getPipelineDetails(
      params.customer_connection_id
    );
    console.log("getLmcConnectionResult", getLmcConnectionResult);
    if (getLmcConnectionResult) {
      // update lmc mapping cusomter connection table
      const updateCustomerParams = {
        customer_approval: params.approval_status,
      };
      const updateMapCustomerConnectionDetailsResult =
        await updateMapCustomerConnectionDetails(updateCustomerParams, {
          customer_connection_id: params.customer_connection_id,
        });
      if (updateMapCustomerConnectionDetailsResult) {
        const updatePipelineParams = {
          customer_consent: params.approval_status,
          extra_charge_approval: params.approval_status,
        };
        const updatePipelineDetailsResult = await updatePipelineDetails(
          updatePipelineParams,
          {
            mapped_lmc_customer_connection_id:
              getLmcConnectionResult.mapped_lmc_customer_connection_id,
          }
        );
        console.log("updatePipelineDetailsResult", updatePipelineDetailsResult);
      } // update lmc pipeline table
      return;
    } else {
      throw new Error("No data found for this Connection.");
    }
  } catch (error) {
    console.error("Error in extraChargeApprovalService:", error);
    throw error;
  }
};

const securityDepositApprovalService = async (params) => {
  try {
    if (params.approval_status === 1) {
      approveParams = {
        master_value: "Team-visit-schedule-is-in-progress",
        customer_connection_id: params.customer_connection_id,
        status: "installation-schedule-pending",
      };
      await customerApprovalAccept(approveParams);
    } else {
      declineParams = {
        master_value: APPLICATION_DECLINE_TYPE.SECURITY_DEPOSIT,
        master_type: DECLINE_REASON,
        customer_connection_id: params.customer_connection_id,
        bpcl_id: params.bpcl_id,
      };
      await customerApprovalDecline(declineParams);
    }

    return;
  } catch (error) {
    console.error("Error in extraChargeApprovalService:", error);
    throw error;
  }
};

const connectionPaymentDetailService = async (params) => {
  try {
    // get cusomter connection Payment Detail by connection id
    return await getConnectionPaymentDetails(params.customer_connection_id);
  } catch (error) {
    console.error("Error in extraChargeApprovalService:", error);
    throw new Error("Error extra Charge Approval Service Service");
  }
};

const customerApprovalDeclineService = async (params) => {
  try {
    const result = await customerApprovalDecline(params);
    console.log("rr", result);
    return result;
  } catch (error) {
    console.error("Error in customerApprovalDecline:", error);
    throw new Error("Error customerApprovalDecline Service");
  }
};

const getCustomerDetailsByMobile = async (requestBody) => {
  return await getCustomer(requestBody.mobile);
};
const checkReworkExpiryService = async () => {
  return await checkReworkExpiryCron();
};

module.exports = {
  submitDetailsToCache,
  uploadDocuments,
  getCustomerListService,
  createRegistrationHelpService,
  CustomerDetailById,
  submitDetailToDB,
  customerDocumentDeleteService,
  pincodeConnectionCheckService,
  extraChargeApprovalService,
  securityDepositApprovalService,
  connectionPaymentDetailService,
  customerLogin,
  customerRefreshToken,
  getValidCustomer,
  customerApprovalDeclineService,
  customerLogout,
  getCustomerDetailsByMobile,
  updateRedisData,
  uploadDocRedis,
  documentDeleteRedis,
  customerRefreshToken,
  checkReworkExpiryService,
  updateHelpWithRegistrationCustomer,
};
