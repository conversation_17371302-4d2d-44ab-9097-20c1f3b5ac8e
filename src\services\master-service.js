const masterRepository = require("../repository/master-repository");
const message = require("../utils/constants");

const listIssueType = async (params) => {
  try {
    const result = masterRepository.getMasterIssueList(params);
    return result;
  } catch (error) {
    console.error("Error in listIssueType:", error);
    throw error;
  }
 
};

const getGovtSchemeList = async ({ page, limit }) => {
  console.log(page,limit);
   
  return masterRepository.getGovtSchemeList(page, limit);
};

const getProofMasterList = async ({ page, limit, proof_type }) => {
  const offset = (page - 1) * limit;

  try {
    // Fetch total count for pagination
    const totalRecords = await masterRepository.countProofs({ proof_type });

    // Calculate total pages
    const totalPages = Math.ceil(totalRecords / limit);

    // Validate page number
    if (page > totalPages) {
      return {
        data: [],
        meta: {
          totalRecords,
          totalPages,
          currentPage: page,
        },
      };
    }

    // Fetch filtered records with pagination
    const proofMasters = await masterRepository.getProofsByType({ proof_type, offset, limit });

    return {
      data: proofMasters,
      meta: {
        totalRecords,
        totalPages,
        currentPage: page,
      },
    };
  } catch (error) {
    throw new Error(`Database error: ${error.message}`);
  }
};
const getMasterList = async (req) => {
  const {master_type} = req;
  console.log("inside service",master_type);
  return masterRepository.getMasterList(master_type);
};
const getServiceRatesList = async (payload) => {
  const serviceTypeSlug = payload.service_type_slug
  const data = masterRepository.getServiceRatesList(serviceTypeSlug);
  return data;
};

module.exports = {
  listIssueType,
  getGovtSchemeList,
  getProofMasterList,
  getMasterList,
  getServiceRatesList
};