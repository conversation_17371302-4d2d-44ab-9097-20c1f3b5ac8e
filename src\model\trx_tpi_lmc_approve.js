const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_tpi_lmc_approve', {
    tpi_lmc_approve_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    tpi_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_admin',
        key: 'admin_id'
      },
      unique: "UK_tpilmcapprove_tpi_id_lmc_supervisor_id"
    },
    lmc_supervisor_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_admin',
        key: 'admin_id'
      },
      unique: "UK_tpilmcapprove_tpi_id_lmc_supervisor_id"
    },
    approve_status: {
      type: DataTypes.STRING(10),
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'trx_tpi_lmc_approve',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_tpi_lmc_approve",
        unique: true,
        fields: [
          { name: "tpi_lmc_approve_id" },
        ]
      },
      {
        name: "UK_tpilmcapprove_tpi_id_lmc_supervisor_id",
        unique: true,
        fields: [
          { name: "tpi_id" },
          { name: "lmc_supervisor_id" },
        ]
      },
    ]
  });
};
