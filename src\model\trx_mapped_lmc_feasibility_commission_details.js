const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_mapped_lmc_feasibility_commission_details",
    {
      tmlfcd_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      mapped_lmc_connection_stage_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "trx_mapped_lmc_connection_stages",
          key: "mapped_lmc_connection_stage_id",
        },
      },
      house_type: {
        type: DataTypes.STRING(15),
        allowNull: false,
      },
      mdpe_pipeline_length: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      riser_length: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      gi_pipeline_length: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      meter_type: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      ready_for_conversion: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      activation_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      is_latest: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_mapped_lmc_feasibility_commission_details",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_tmlfcd_id",
          unique: true,
          fields: [{ name: "tmlfcd_id" }],
        },
      ],
    }
  );
};
