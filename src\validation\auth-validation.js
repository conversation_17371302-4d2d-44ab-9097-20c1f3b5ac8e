const Joi = require('joi');

const loginValidation = {
  body: Joi.object({
    username: Joi.string()
      .required()
      .custom((value, helpers) => {
        const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
        const isMobile = /^[6-9]\d{9}$/.test(value); // You can adjust the regex based on your region

        if (!isEmail && !isMobile) {
          return helpers.error("any.invalid");
        }

        return value;
      })
      .messages({
        'any.required': 'Username is required',
        'any.invalid': 'Please provide a valid email address or mobile number',
        'string.base': 'Username must be a string',
      }),

    password: Joi.string()
      .min(6)
      .required()
      .messages({
        'string.min': 'Password must be at least 6 characters long',
        'any.required': 'Password is required',
      }),
  }),
};

const verifyotp = {
    body: Joi.object({
        username: Joi.string()
        .required()
        .custom((value, helpers) => {
          const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
          const isMobile = /^[6-9]\d{9}$/.test(value); // You can adjust the regex based on your region
  
          if (!isEmail && !isMobile) {
            return helpers.error("any.invalid");
          }
  
          return value;
        })
        .messages({
          'any.required': 'Username is required',
          'any.invalid': 'Please provide a valid email address or mobile number',
          'string.base': 'Username must be a string',
        }),
      otp: Joi.string().required(),
    }),
  };

  const changePasswordValidation ={
    body: Joi.object({
    email: Joi.string().email().required(),
  
    current_password: Joi.string().min(6).required(),
  
    new_password: Joi.string().min(6).required(),
  
    re_enter_password: Joi.string()
      .valid(Joi.ref('new_password'))
      .required()
      .messages({
        'any.only': 'Re-entered password must match new password',
      }),
  })
  };
  

module.exports = { loginValidation,verifyotp,changePasswordValidation };