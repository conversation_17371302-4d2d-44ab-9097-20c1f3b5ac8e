{"openapi": "3.0.0", "info": {"title": "CMS API Services System", "version": "1.0.0", "description": "This is Common API Services system", "contact": {"name": "", "email": ""}}, "servers": [{"url": "https://Png-nj-cms.azurewebsites.net"}], "components": {"securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}, "security": [{"Authorization": []}], "paths": {"/capabilities/list": {"post": {"tags": ["CMS"], "summary": "Get list by capability id", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"capability_id": {"type": "integer", "example": 1}}, "required": []}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/capabilities/modules": {"post": {"tags": ["CMS"], "summary": "Get all capability modules", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}, "required": []}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/capabilities/create": {"post": {"tags": ["CMS"], "summary": "Create capabilities", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"capabilities": {"type": "array", "items": {"type": "object", "properties": {"capabilities": {"type": "string", "example": "Capability 1"}, "slug": {"type": "string", "example": "capability-1"}, "parent_id": {"type": "integer", "example": 0}, "is_active": {"type": "boolean", "example": 0}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}, "created_by": {"type": "integer", "example": 1}}, "required": ["capabilities", "created_by"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/capabilities/update": {"post": {"tags": ["CMS"], "summary": "Update capabilities", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"capability_list_id": {"type": "integer", "example": 1}, "capabilities": {"type": "string", "example": "Capability 1"}, "slug": {"type": "string", "example": "capability-1"}, "parent_id": {"type": "integer", "example": 0}, "is_active": {"type": "boolean", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Firefox"}, "updated_by": {"type": "integer", "example": 1}}, "required": ["capability_list_id", "updated_by"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/capabilities/delete": {"post": {"tags": ["CMS"], "summary": "Delete capabilities", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"capability_list_id": {"type": "integer", "example": 1}, "updated_by": {"type": "integer", "example": 1}}, "required": []}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tiles-master/list": {"post": {"tags": ["CMS"], "summary": "Get all tiles master list", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "search": {"type": "string", "example": ""}, "orderBy": {"type": "string", "example": ""}, "orderName": {"type": "string", "example": ""}}, "required": []}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tiles-master/create": {"post": {"tags": ["CMS"], "summary": "Create tiles master", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "example": ""}, "description": {"type": "string", "example": ""}, "media_id": {"type": "integer", "example": 1}, "action_url": {"type": "string", "example": ""}, "services": {"type": "string", "example": ""}, "clickable": {"type": "string", "example": ""}, "tiles_type": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": ""}, "browser": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tiles-master/update": {"post": {"tags": ["CMS"], "summary": "Update tiles master", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"tiles_id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": ""}, "description": {"type": "string", "example": ""}, "media_id": {"type": "integer", "example": 1}, "action_url": {"type": "string", "example": ""}, "services": {"type": "string", "example": ""}, "clickable": {"type": "string", "example": ""}, "tiles_type": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": ""}, "browser": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/tiles-master/delete": {"post": {"tags": ["CMS"], "summary": "Delete tiles master", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"tiles_id": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": ""}, "browser": {"type": "string", "example": ""}}, "required": ["tiles_id"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/roles/list": {"post": {"tags": ["CMS"], "summary": "Get all roles list or by role id", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"role_id": {"type": "integer", "example": 1}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "search": {"type": "string", "example": ""}, "orderBy": {"type": "string", "example": ""}, "orderName": {"type": "string", "example": ""}, "all_records": {"type": "boolean", "example": true}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/roles/create": {"post": {"tags": ["CMS"], "summary": "Create roles", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"role_name": {"type": "string", "example": "Admin"}, "slug": {"type": "string", "example": "admin"}, "default_capability": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/roles/update": {"post": {"tags": ["CMS"], "summary": "Update roles", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"role_id": {"type": "integer", "example": 1}, "role_name": {"type": "string", "example": "Editor"}, "slug": {"type": "string", "example": "admin"}, "default_capability": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Firefox"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/roles/delete": {"post": {"tags": ["CMS"], "summary": "Delete roles", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"role_id": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Firefox"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/media/list": {"post": {"tags": ["CMS"], "summary": "Get all media list", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "search": {"type": "string", "example": ""}, "orderBy": {"type": "string", "example": ""}, "orderName": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/media/upload": {"post": {"tags": ["CMS"], "summary": "Upload media", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"media_title": {"type": "string", "example": ""}, "media_alt_text": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/media/update": {"post": {"tags": ["CMS"], "summary": "Update media", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"media_id": {"type": "integer", "example": 1}, "media_title": {"type": "string", "example": ""}, "media_alt_text": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Firefox"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/media/delete": {"post": {"tags": ["CMS"], "summary": "Delete media", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"media_id": {"type": "integer", "example": 1}, "media_name": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Firefox"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/banners/list": {"post": {"tags": ["CMS"], "summary": "Get all banner list", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "search": {"type": "string", "example": ""}, "orderBy": {"type": "string", "example": ""}, "orderName": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/banners/create": {"post": {"tags": ["CMS"], "summary": "Create banner", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"banner_title": {"type": "string", "example": "<PERSON>ple Banner Title"}, "banner_img_url": {"type": "string", "example": "https://example.com/banner.jpg"}, "action_url": {"type": "string", "example": "https://example.com/action"}, "screen_id": {"type": "integer", "example": 1}, "position": {"type": "string", "example": "Top"}, "is_active": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/banners/update": {"post": {"tags": ["CMS"], "summary": "Update banner", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"banner_id": {"type": "integer", "example": 1}, "banner_title": {"type": "string", "example": ""}, "banner_img_url": {"type": "string", "example": ""}, "action_url": {"type": "string", "example": ""}, "screen_id": {"type": "integer", "example": 1}, "position": {"type": "string", "example": ""}, "is_active": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/banners/delete": {"post": {"tags": ["CMS"], "summary": "Delete banner", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"banner_id": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Firefox"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/user/list": {"post": {"tags": ["CMS"], "summary": "Get all User list", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "search": {"type": "string", "example": ""}, "orderBy": {"type": "string", "example": ""}, "orderName": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/user/create": {"post": {"tags": ["CMS"], "summary": "Create user", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": ""}, "last_name": {"type": "string", "example": ""}, "phone_no": {"type": "string", "example": ""}, "email": {"type": "string", "example": ""}, "role_id": {"type": "integer", "example": 1}, "password": {"type": "string", "example": "Test@123#"}, "capabilities_ids": {"type": "array", "example": [1, 2, 3]}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/user/update": {"post": {"tags": ["CMS"], "summary": "Update user", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": ""}, "last_name": {"type": "string", "example": ""}, "phone_no": {"type": "string", "example": ""}, "email": {"type": "string", "example": ""}, "role_id": {"type": "integer", "example": 1}, "capabilities_ids": {"type": "array", "example": [1, 2, 3]}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/user/delete": {"post": {"tags": ["CMS"], "summary": "Delete user", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "integer", "example": 1}, "updated_by": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Firefox"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/screens/list": {"post": {"tags": ["CMS"], "summary": "Get all Screens list", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"search": {"type": "string", "example": ""}, "orderBy": {"type": "string", "example": "asc", "enum": ["asc", "desc"]}, "orderName": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/splash/list": {"post": {"tags": ["CMS"], "summary": "Get all Splash list", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "search": {"type": "string", "example": ""}, "orderBy": {"type": "string", "example": ""}, "orderName": {"type": "string", "example": ""}, "all_records": {"type": "boolean", "example": true}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/splash/create": {"post": {"tags": ["CMS"], "summary": "Create Splash", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"splash_title": {"type": "string", "example": ""}, "splash_media_id": {"type": "integer", "example": 1}, "action_url": {"type": "string", "example": ""}, "splash_start_date": {"type": "string", "example": ""}, "splash_end_date": {"type": "string", "example": ""}, "is_active": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/splash/update": {"post": {"tags": ["CMS"], "summary": "Update Splash", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"splash_id": {"type": "integer", "example": 1}, "splash_title": {"type": "string", "example": ""}, "splash_media_id": {"type": "integer", "example": 1}, "action_url": {"type": "string", "example": ""}, "splash_start_date": {"type": "string", "example": ""}, "splash_end_date": {"type": "string", "example": ""}, "is_active": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/splash/delete": {"post": {"tags": ["CMS"], "summary": "Delete Splash", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"splash_id": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Firefox"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/offer-master/list": {"post": {"tags": ["CMS"], "summary": "Get all offer master list", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "search": {"type": "string", "example": ""}, "orderBy": {"type": "string", "example": ""}, "orderName": {"type": "string", "example": ""}, "master_type": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/offer-master/create": {"post": {"tags": ["CMS"], "summary": "Create Offer master", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "example": ""}, "media_id": {"type": "integer", "example": 1}, "master_type": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/offer-master/update": {"post": {"tags": ["CMS"], "summary": "Update Offer Master", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"offer_master_id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": ""}, "media_id": {"type": "integer", "example": 1}, "master_type": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/offer-master/delete": {"post": {"tags": ["CMS"], "summary": "Delete Offer Master", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"offer_master_id": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Firefox"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/events/list": {"post": {"tags": ["CMS"], "summary": "Get events list for All, History and Incoming events", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"action": {"type": "string", "example": "list"}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "search": {"type": "string", "example": ""}, "orderBy": {"type": "string", "example": ""}, "orderName": {"type": "string", "example": ""}, "event_start_date": {"type": "string", "example": ""}, "event_end_date": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/events/create": {"post": {"tags": ["CMS"], "summary": "Create events", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"event_title": {"type": "string", "example": ""}, "event_description": {"type": "string", "example": ""}, "event_images": {"type": "string", "example": ""}, "event_type": {"type": "integer", "example": 1}, "event_dates": {"type": "array", "items": {"type": "string", "example": ""}}, "event_location": {"type": "string", "example": ""}, "event_lat": {"type": "string", "example": ""}, "event_long": {"type": "string", "example": ""}, "event_for": {"type": "integer", "example": 1}, "event_vehicle_type": {"type": "integer", "example": 1}, "action": {"type": "string", "example": "create"}, "event_mode": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/events/update": {"post": {"tags": ["CMS"], "summary": "Update event", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"event_id": {"type": "integer", "example": 1}, "event_title": {"type": "string", "example": ""}, "event_description": {"type": "string", "example": ""}, "event_images": {"type": "string", "example": ""}, "event_type": {"type": "integer", "example": 1}, "event_dates": {"type": "array", "items": {"type": "string", "example": ""}, "example": ["", ""]}, "event_location": {"type": "string", "example": ""}, "event_lat": {"type": "string", "example": ""}, "event_long": {"type": "string", "example": ""}, "event_for": {"type": "string", "example": ""}, "event_vehicle_type": {"type": "string", "example": ""}, "action": {"type": "string", "example": ""}, "event_mode": {"type": "integer", "example": ""}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/events/delete": {"post": {"tags": ["CMS"], "summary": "Delete event", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"event_id": {"type": "integer", "example": 1}, "action": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/offer/list": {"post": {"tags": ["CMS"], "summary": "Get offers list for All", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "search": {"type": "string", "example": ""}, "orderBy": {"type": "string", "example": ""}, "orderName": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/offer/status-update": {"post": {"tags": ["CMS"], "summary": "Update status of offer", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"offer_id": {"type": "integer", "example": 1}, "status": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/offer/create": {"post": {"tags": ["CMS"], "summary": "Create events", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"offer_type_id": {"type": "integer", "example": 1}, "offer_banner_id": {"type": "integer", "example": 1}, "vehicle_type": {"type": "integer", "example": 1}, "applicable_services": {"type": "integer", "example": 2}, "offer_validity_start": {"type": "string", "example": "YYYY-MM-DD 10:45:01.628"}, "offer_validity_end": {"type": "string", "example": "YYYY-MM-DD 10:45:01.628"}, "discount_amount": {"type": "string", "example": "1"}, "min_avail": {"type": "integer", "example": 1}, "discount_percentage": {"type": "integer", "example": 1}, "discount_value": {"type": "integer", "example": 1}, "free_services": {"type": "integer", "example": 1}, "status": {"type": "string", "example": "Pending"}, "remark": {"type": "string", "example": ""}, "ip_address": {"type": "string", "example": "**************"}, "browser": {"type": "string", "example": "chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/offer/delete": {"post": {"tags": ["CMS"], "summary": "Delete offer", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"offer_id": {"type": "integer", "example": 1}, "ip_address": {"type": "string", "example": "***********"}, "browser": {"type": "string", "example": "Chrome"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "SSO Login - Secure Login API", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"email": {"description": "email", "type": ""}, "password": {"description": "password", "type": ""}}, "required": ["email", "password"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string", "description": "JWT token"}}}}}}}}}, "x-extraInfo": {"bizOwner": "", "bizOwnerMail": "", "CORSAllowOrigins": "*", "endpointSecurityAuthType": "", "endpointSecurityPassword": "", "endpointSecurityScheme": "secured", "endpointSecurityUsername": "", "tags": "OMNI_NJ_CMS", "techOwner": "", "techOwnerMail": "", "tiersCollection": "Unlimited", "visibility": "public"}}}