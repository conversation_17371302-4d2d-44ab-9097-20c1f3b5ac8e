const { decryptCrypto, encryptCrypto } = require("../utils/encrypt-decrypt");

const successResponse = (res, message, data = null, statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message: message,
    data: data,
  });
};

const errorResponse = (res, message, errors = [], statusCode = 400) => {
  return res.status(statusCode).json({
    success: false,
    message: message,
    errors: errors,
  });
};

const sendSuccess = (req, res, data, message) => {
  let encryptionNeeded = req.headers["encryption"];

  if (encryptionNeeded === process.env.ENCRYPTION_TRUE && data) {
    data = encryptCrypto(JSON.stringify(data));
    // console.log("data==" + decryptCrypto(data));
  }
  return sendResponse(req, res, data, message, 200, "success");
};
const sendError = (req, res, data, message, status = 400) => {
  let encryptionNeeded = req.headers["encryption"];
  if (encryptionNeeded === process.env.ENCRYPTION_TRUE && data) {
    data = encryptCrypto(JSON.stringify(data));
  }
  return sendResponse(req, res, data, message, status, "failed");
};
const sendResponse = (
  req,
  res,
  data,
  message = "",
  httpStatusCode = 200,
  status = "success"
) => {
  httpStatusCode = httpStatusCode || 200;
  let responseFormat = {
    status: status,
    statusCode: httpStatusCode,
    data: [],
    message: "",
  };
  responseFormat.data = data;
  responseFormat.message = message;
  return res.status(httpStatusCode).json(responseFormat);
};

module.exports = { successResponse, errorResponse, sendSuccess, sendError };
