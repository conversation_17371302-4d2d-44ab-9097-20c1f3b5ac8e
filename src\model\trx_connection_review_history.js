const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_connection_review_history",
    {
      connection_review_history_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      review_stage: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
      },
      review_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      remarks: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_connection_review_history",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_connection_review_history_id",
          unique: true,
          fields: [{ name: "connection_review_history_id" }],
        },
      ],
    }
  );
};
