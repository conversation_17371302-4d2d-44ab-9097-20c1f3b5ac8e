const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_mapped_lmc_installation_details",
    {
      mapped_lmc_installation_technical_detail_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      mapped_lmc_connection_stage_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "trx_mapped_lmc_connection_stages",
          key: "mapped_lmc_connection_stage_id",
        },
      },
      regulator_no: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      installation_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      testing_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      rfc_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      gi_length: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      gi_length_for_tapping: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      copper_length: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      appliance_valve: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      isolation_ball_valve: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      brass_fitting_set: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      gi_plug: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      half_two_nipple: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      half_three_nipple: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      half_four_nipple: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      elbow: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      equal_tee: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      socket: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      union_value: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      anaconda: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      elbow_mf: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      checklist: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      is_latest: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_mapped_lmc_installation_details",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_mapped_lmc_feasibility_details",
          unique: true,
          fields: [{ name: "mapped_lmc_installation_technical_detail_id" }],
        },
      ],
    }
  );
};
