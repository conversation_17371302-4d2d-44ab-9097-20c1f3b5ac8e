const { mailConfig } = require("../../config");
const FoAdminRepository = require("../repository/fo-admin-repository");
const {
  paginateQuery,
  formatAddress,
  formatDate,
} = require("../utils/commonFn");
const otpHelper = require("../helpers/smsEmailHelper");

const getMROListing = async (req) => {
  try {
    const { page, limit, offset } = paginateQuery(req);
    const search = req.body.search || "";
    const status = req.body.status || "new";

    let area_ids = []
    if (req?.body?.area_ids) {
      area_ids = req?.body?.area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(Boolean);
    }

    const { count, rows } = await FoAdminRepository.getMROListing(
      status,
      search,
      limit,
      offset,
      area_ids
    );

    const formatted = rows.map((item) => ({
      mro_ticket: item.mro_ticket,
      total_customers: item.total_customers,
      total_pending: item.total_pending,
      total_completed: item.total_completed,
      total_averaged_out: item.total_averaged_out,
      posted_date: item.created_at
        ? new Date(item.created_at).toLocaleDateString("en-GB", {
            day: "numeric",
            month: "short",
            year: "numeric",
          })
        : null,
      scheduled_mr_date: item.scheduled_mr_date
        ? new Date(item.scheduled_mr_date).toLocaleDateString("en-GB", {
            day: "numeric",
            month: "short",
            year: "numeric",
          })
        : null,
      supervisor_name:
        item.supervisor_first_name && item.supervisor_last_name
          ? `${item.supervisor_first_name} ${item.supervisor_last_name}`
          : null,
      action: item.supervisor_first_name ? "Reassign FO" : "Assign FO",
    }));

    return {
      success: true,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
      totalCount: count,
      data: formatted,
    };
  } catch (error) {
    console.error("Error in getMROListing:", error);
    throw error;
  }
};
const getAllFOSupervisors = async (req) => {
  try {
    const { approve_status, search = "", fromDate, toDate } = req.body;

    const { page, limit, offset } = paginateQuery(req);
    const persona_role_id = req?.user?.persona_role_id;

    let area_ids = []
    if (req?.body?.area_ids) {
      area_ids = req?.body?.area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(Boolean);
    }

    const { data, total, pages } = await FoAdminRepository.getAllFOSupervisors({
      approve_status,
      persona_role_id,
      search,
      page,
      limit,
      offset,
      fromDate,
      toDate,
      area_ids
    });

    const formatted = data.map((supervisor) => ({
      supervisor_id: supervisor.admin_id,
      name: `${supervisor.first_name} ${supervisor.last_name}`,
      mobile: supervisor.mobile,
      email: supervisor.email,
      remark: supervisor["trx_tpi_onboard_approves.remark"] ?? "",
      onboarded_on: formatDate(supervisor.created_at),
      rejected_date: formatDate(
        supervisor["trx_tpi_onboard_approves.updated_at"]
      ),
      request_date: formatDate(
        supervisor["trx_tpi_onboard_approves.created_at"]
      ),
      revoke_date: formatDate(
        supervisor.updated_at
      ),
      is_active: supervisor.is_active,
      approve_status: supervisor["trx_tpi_onboard_approves.approve_status"],
    }));

    return {
      success: true,
      currentPage: page,
      totalPages: pages,
      totalCount: total,
      data: formatted,
    };
  } catch (error) {
    console.error("Error in getAllFOSupervisors:", error);
    throw new Error("Failed to fetch FO Supervisors");
  }
};

const getMROTicketDetails = async (payload) => {
  try {
    const mroRecords = await FoAdminRepository.getMROTicketDetails(payload?.mro_ticket);
    // Format date as "dd MMM yyyy"
    const formatDate = (date) => {
      if (!date) return "-";
      return new Date(date).toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });
    };

    // Format address
    const formatAddress = (item) => {
      return [
        item["trx_meter_reading_units.customer_connection.trx_customer_connection_addresses.connection_address"],
        item["trx_meter_reading_units.customer_connection.trx_customer_connection_addresses.connection_city"],
        item["trx_meter_reading_units.customer_connection.trx_customer_connection_addresses.connection_district"],
        item["trx_meter_reading_units.customer_connection.trx_customer_connection_addresses.connection_state"],
        item["trx_meter_reading_units.customer_connection.trx_customer_connection_addresses.connection_pincode"],
      ]
        .filter(Boolean)
        .join(", ");
    };

    // Format a single record
    const formatMROTicketDetailsRaw = (item) => ({
      customer_connection_id: item["trx_meter_reading_units.customer_connection.customer_connection_id"],
      customer_name: `${item["trx_meter_reading_units.customer_connection.first_name"] || ""} ${item["trx_meter_reading_units.customer_connection.last_name"] || ""}`.trim(),
      bp_id: item["trx_meter_reading_units.customer_connection.bp_id"] || "",
      ca_no: item["trx_meter_reading_units.customer_connection.ca_no"] || "",
      email: item["trx_meter_reading_units.customer_connection.email"] || "",
      mobile_number: item["trx_meter_reading_units.customer_connection.mobile"] || "xxxxxxxxxx",
      meter_number: item["trx_meter_reading_units.customer_connection.trx_customer_meters.meter_no"] || "",
      meter_type: item["trx_meter_reading_units.customer_connection.trx_customer_meters.meter_type"] || "",
      mro_status: item["trx_meter_reading_units.mru_status"] || item.mro_status,
      mro_id: item.mro_id,
      mru_id: item["trx_meter_reading_units.mru_id"],
      scheduled_mr_date: formatDate(item["trx_meter_reading_units.scheduled_mr_date"]),
      equipment_no: item["trx_meter_reading_units.equipment_no"] || "-",
      address: formatAddress(item),
      last_meter_reading: item["trx_meter_reading_units.trx_customer_meter_readings.created_at"]
        ? formatDate(item["trx_meter_reading_units.trx_customer_meter_readings.created_at"])
        : "-",
      current_reading: item["trx_meter_reading_units.trx_customer_meter_readings.current_reading"] ?? "-",
      uploaded_by: item["trx_meter_reading_units.trx_customer_meter_readings.declared_by"] || "-",
      meter_reading_details: "View"
    });

    // Map all records
    const formattedRecords = mroRecords.map(formatMROTicketDetailsRaw);

    return formattedRecords;
  } catch (error) {
    console.error("Error in getMROTicketDetailsService:", error);
    throw new Error("Error occurred while fetching MRO ticket details.");
  }
};

const getDetailedMROCustomerInfo = async (payload) => {
  try {
    const customer = await FoAdminRepository.getDetailedMROCustomerInfo(
      payload?.customer_connection_id
    );
    if (!customer) return null;

    const meterReadingFile =
      customer["related_meter_reading_orders.trx_customer_meter_readings.meter_reading_file"];
    const meterReadingFileName = meterReadingFile?.split("/").pop() || "";
    const meterReadingFilePath = meterReadingFile
      ? meterReadingFile.substring(0, meterReadingFile.lastIndexOf("/"))
      : "";


    const formatted = {
      meter_reading_file: meterReadingFile,
      meter_reading_file_name: meterReadingFileName,
      meter_reading_file_path: meterReadingFilePath,
      meter_reading_id:
        customer["related_meter_reading_orders.trx_customer_meter_readings.customer_meter_reading_id"],
      meter_reading:
        customer["related_meter_reading_orders.trx_customer_meter_readings.current_reading"],
      bp_id: customer.bp_id || "N/A",
      ca_no: customer.ca_no || "N/A",
      customer_name: `${customer.first_name} ${customer.last_name}`.trim(),
      address: formatAddress({
        permanent_address: customer["trx_customer_connection_addresses.permanent_address"],
        connection_address: customer["trx_customer_connection_addresses.connection_address"],
        connection_pincode: customer["trx_customer_connection_addresses.connection_pincode"],
        connection_city: customer["trx_customer_connection_addresses.connection_city"],
        connection_state: customer["trx_customer_connection_addresses.connection_state"],
        connection_district: customer["trx_customer_connection_addresses.connection_district"],
      }),
      mro_ticket: customer["related_meter_reading_orders.mro_ticket"] || "-",
      remarks:  customer["related_meter_reading_orders.remarks"] ,
      mro_id: customer["related_meter_reading_orders.mro_id"] || "-",
      scheduled_mr_date: formatDate(customer["related_meter_reading_orders.scheduled_mr_date"]),
      meter_no:  customer["trx_customer_meters.meter_no"] ,
      equipment_no: customer["related_meter_reading_orders.equipment_no"] || "-",
      meter_reading_type:
        customer["trx_customer_meters.trx_customer_meter_readings.declared_by"] || "Customer",
    };

    return formatted;
  } catch (error) {
    console.error("Service Error - getDetailedMROCustomerInfo:", error);
    throw error;
  }
};

const assignOSupervisors = async (req) => {
  try {
    const customer = await FoAdminRepository.assignSupervisorToMROOrders(
      req?.body
    ); // use the earlier function

    return customer;
  } catch (error) {
    console.error("Error in getFormattedMROCustomerList:", error);
    return [];
  }
};
const revokeFOSupervisor = async (req) => {
  try {
    const supervisor = await FoAdminRepository.getFOSupervisor(req?.body); // use the earlier function
    if (!supervisor) {
      return { success: false, message: "Supervisor not found." };
    }
    
    const name = `${supervisor.first_name} ${supervisor.last_name}`;
    const email = supervisor.email;
    const supervisorId = supervisor.admin_id;
    const response = await FoAdminRepository.revokeFOSupervisor(req?.body); // use the earlier function

    
        if (response) {
          const emailBody = formatMessageFOSupervisorRevokeAccess(
            name,
            supervisorId,
            mailConfig.bcc
          );
          await otpHelper.emailOTPNotification(
            email,
            "Your FO Supervisor Account Has Been Deactivated",
            emailBody
          );
        }

    return response;
  } catch (error) {
    console.error("Error in getFormattedMROCustomerList:", error);
    return [];
  }
};
const getMROKPIs = async (req) => {
  try {
    const customer = await FoAdminRepository.getMROKPIs(req?.body);

    return customer;
  } catch (error) {
    console.error("Error in getFormattedMROCustomerList:", error);
    return [];
  }
};
const getAreaMetrics = async (supervisorId) => {
  const newReading =
    await FoAdminRepository.getGeographicalAreasMetrics(supervisorId);

  return newReading;
};
const getSupervisorMROListing = async (req) => {
  try {
    let { area_ids , supervisorId} = req.body;

    if (!supervisorId) {
      throw new Error("Supervisor ID is required.");
    }

    if (area_ids) {
      area_ids = area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(Boolean);
    }

    if (!Array.isArray(area_ids) || area_ids.length === 0 || area_ids.includes(0)) {
      return [];
    }


    const allData = await FoAdminRepository.getSupervisorMROListing(supervisorId, area_ids);

    if (!allData || allData.length === 0) {
      return { notFound: true };
    }

   return allData
  } catch (error) {
    console.error("Service Error - getMROListing:", error);
    throw error;
  }
};

const formatMessageFOSupervisorRevokeAccess = (name, lmcId, supportContact) => {
  return `
    <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <br/>

      <p>Dear <strong>${name}</strong>,</p>

      <br/>

      <p>
        We would like to inform you that your FO Supervisor account (ID: <strong>${lmcId}</strong>) 
        has been marked as inactive. As a result, you will no longer be able to access the portal 
        using your credentials.
      </p>

      <br/>

      <p>
        If you require further assistance,  please contact <strong>${supportContact}</strong>.
      </p>

      <br/>

      <p>Best regards,</p>
      <p><strong>BPCL Team</strong></p>
    </div>
  `;
};
module.exports = {
  getMROListing,
  getAllFOSupervisors,
  getMROTicketDetails,
  getDetailedMROCustomerInfo,
  assignOSupervisors,
  revokeFOSupervisor,
  getMROKPIs,
  getAreaMetrics,
  getSupervisorMROListing
};
