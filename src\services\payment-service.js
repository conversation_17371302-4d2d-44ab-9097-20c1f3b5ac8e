const paymentRepository = require('../repository/payment-repository');
const redisHelper = require('../helpers/redisHelper');
const otpHelper = require('../helpers/smsEmailHelper');
const otpService = require("./otp-service");
const pdfHelper = require('../helpers/pdfHelper');
const fs = require('fs');
const path = require('path');
const generateKey = (connectionId, number) => {
  return `${connectionId}_${number}`;
};

const getCurrentBill = async (payload) => {
  console.log('2', payload);
  const result = await paymentRepository.getCurrentBillDetails(payload);
  return result;
};

const getPaymentHistory = async (payload) => {
  const result = await paymentRepository.getPaymentHistoryDetails(payload);
  return result;
};

const checkNumberDetails = async (payload) => {
  let result = await paymentRepository.checkNumberResult(payload);
  console.log('result', result);

  if (result == 'yes') {
    const otp = Math.floor(100000 + Math.random() * 900000);
    payload.subject = "Number Verification";
    payload.otp = otp;
    payload.template = `OTP for your request ${otp}`;
    const otpResult = await otpService.sendOtp(
      payload
    );
    console.log("OTP Response:", otpResult);

    return {
      status: result,
      message: 'OTP generated and sent successfully',
      otpResponse: otpResult,
    };
  }

  return {
    status: result,
    message: 'Not Found',
  };
};

const generatePdf = async (payload) => {
  const result = await paymentRepository.getPaymentInvoiceDetails(payload);
  try {
    const buffer = await pdfHelper.generatePDFBuffer(result); // Generate PDF

    const base64PDF = buffer.toString('base64');
    return base64PDF; // Return the file path
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error; //  Re-throw for better debugging
  }
};

module.exports = { generatePdf };

module.exports = {
  getCurrentBill,
  getPaymentHistory,
  checkNumberDetails,
  generatePdf,
};
