const dotEnv = require("dotenv").config();
const { createClient } = require("redis");

// Create a Redis instance.
// By default, it will connect to localhost:6379.
// We are going to cover how to specify connection options soon.

let redisInstance;
const cacheHostName = process.env.AZURE_MANAGED_REDIS_HOST_NAME;
const cachePassword = process.env.AZURE_MANAGED_REDIS_ACCESS_KEY;
if (!cacheHostName) throw Error("AZURE_MANAGED_REDIS_HOST_NAME is empty");
if (!cachePassword) throw Error("AZURE_MANAGED_REDIS_ACCESS_KEY is empty");

(async () => {
  redisInstance = createClient({
    url: cacheHostName,
    password: cachePassword,
  });
  redisInstance.on("error", (error) => console.error(`Error : ${error}`));

  await redisInstance.connect();

  console.log("Redis connection has been established successfully");
})();

const redisHelper = {
  setData: async (keyName, keyValue, expireTimeinSeconds = 0) => {
    const newKeyvalue =
      typeof keyValue !== "string" ? JSON.stringify(keyValue) : keyValue;
    console.log(`Storing in Redis - Key: ${keyName}, ${newKeyvalue}`);
    const result = await redisInstance.set(keyName, newKeyvalue);
    console.log("result", result);
    if (result == "OK" && expireTimeinSeconds) {
      await redisInstance.expire(keyName, expireTimeinSeconds);
    }
    return result;
  }, // Returns a promise which resolves to "OK" when the command succeeds.

  getData: async (keyName) => {
    let result = await redisInstance.get(keyName);
    try {
      result = JSON.parse(result);
    } catch (e) {}
    return result;
  }, // Returns a promise which resolves to "OK" when the command succeeds.
  incrValue: async (keyName) => {
    return await redisInstance.incr(keyName);
  },
  deleteData: async (keyName) => {
    const result = await redisInstance.del(keyName);
    return result;
  },

  incrValue: async (keyName) => {
    return await redisInstance.incr(keyName);
  },

  getRedisPngAvailableConnectionKey: (
    bpcl_id,
    house_flat_no,
    area_id,
    personaDetails = null
  ) => {
    const personaKey = personaDetails
      ? personaDetails.personaType + "_" + personaDetails.personaUserId + "_"
      : "";

    return `${personaKey}bpclid_${bpcl_id}_area_${area_id}_hno_${house_flat_no}`;
  },
  getRedisPermanentAddressChangeRequestKey: (
    customer_connection_id,
    house_flat_no,
    area_id
  ) => {
    return `ServiceRequest_PAC_${customer_connection_id}_area_${area_id}_hno_${house_flat_no}`;
  },
  getRedisNewAddressRequestKey: (
    customer_connection_id,
    house_flat_no,
    area_id
  ) => {
    return `ServiceRequest_NA_${customer_connection_id}_area_${area_id}_hno_${house_flat_no}`;
  },

  getRedisRegistrationIdKey: (pngAvailableConnectionKey) => {
    return `KEY_${pngAvailableConnectionKey}`;
  },

  getBpclIdKey: (bpclId) => {
    return `BPCL_ID_${bpclId}`;
  },
  getPngTokenKey: (mobile) => {
    return `PNG_TOKEN_${mobile}`;
  },
  getIssueSupportDocKey: (connectionId) => {
    return `issueReport_CI_${connectionId}`;
  },
  scanKeysByPrefix: async (prefix) => {
    try {
      let cursor = 0;
      let keys = [];

      do {
        // Use `scan` correctly for node-redis v4+
        const reply = await redisInstance.scan(cursor, {
          MATCH: `${prefix}*`, // Match keys with prefix
          COUNT: 100, // Fetch 100 keys at a time
        });

        cursor = reply.cursor; // Correctly get the next cursor position
        keys.push(...reply.keys); // Get the keys array
      } while (cursor !== 0); // Ensure cursor check is a number (not a string)

      return keys;
    } catch (error) {
      console.error("Error in scanKeysByPrefix:", error);
      throw error;
    }
  },
  getNameTransferKey: (connectionId) => {
    return `nameTransfer_helpSupport_${connectionId}`;
  },
  otpForMstAdminLogin: (userId) => {
    return `mst_admin_user_${userId}`;
  },
  getRedisInstallationConnectionKey: (
    mapped_lmc_connection_stage_id,
    mapped_lmc_customer_connection_id
  ) => {
    return `lmcstage_${mapped_lmc_connection_stage_id}_lmccustomer_${mapped_lmc_customer_connection_id}`;
  },
};

module.exports = redisHelper;
