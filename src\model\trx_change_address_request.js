const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_change_address_request",
    {
      change_address_request_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      request_ticket_no: {
        type: DataTypes.STRING(12),
        allowNull: false,
        unique: "UK_tcar_cari",
      },
      address_request_type: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      old_customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      new_customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      transfer_reason: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_masters",
          key: "master_id",
        },
      },
      transfer_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      old_customer_connection_address_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection_address",
          key: "customer_connection_address_id",
        },
      },
      new_customer_connection_address_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection_address",
          key: "customer_connection_address_id",
        },
      },
      request_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
        defaultValue: "pending",
      },
      address_request_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
        defaultValue: "pending",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_change_address_request",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_change_address_request_id",
          unique: true,
          fields: [{ name: "change_address_request_id" }],
        },
        {
          name: "UK_tcar_cari",
          unique: true,
          fields: [{ name: "request_ticket_no" }],
        },
      ],
    }
  );
};
