const { dbModels } = require('../database/connection');

// Check if record is already deleted
const checkIfDeletedApplicationStatus = async (id) => {
  return await dbModels.mst_application_status.findOne({
    where: { application_status_id: id, is_delete: 0 }, // Only fetch deleted records
    raw: true,
  });
};

// Get list with pagination
const getApplicationStatusList = async (page, limit) => {
    const offset = (page - 1) * limit;

    // Fetch total records for pagination
    const totalRecords = await dbModels.mst_application_status.count({ where: { is_delete: 0 } });
    const totalPages = Math.ceil(totalRecords / limit);

    // If the requested page exceeds total pages, return empty data
    if (page > totalPages) {
        return { data: [], meta: { totalRecords, totalPages, currentPage: page } };
    }

    // Fetch the data with pagination
    const data = await dbModels.mst_application_status.findAll({
        where: { is_delete: 0 },
        offset,
        limit,
    });

    return { data, meta: { totalRecords, totalPages, currentPage: page } };
};

  

// Create new record
const createApplicationStatus = async (dto) => {
  return await dbModels.mst_application_status.create(dto);
};

// Update existing record
const updateApplicationStatus = async (id, dto) => {
  const data = await dbModels.mst_application_status.findByPk(id);
  return data.update(dto);
};

// Soft delete record
const deleteApplicationStatus = async (id, dto) => {
  const data = await dbModels.mst_application_status.findByPk(id);
  if (!data) throw new Error("Record already deleted");
  return data.update(dto);
};

module.exports = {
  getApplicationStatusList,
  checkIfDeletedApplicationStatus,
  createApplicationStatus,
  updateApplicationStatus,
  deleteApplicationStatus,
};
