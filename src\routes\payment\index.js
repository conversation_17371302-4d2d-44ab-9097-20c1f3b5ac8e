const { validate } = require("express-validation");
const express = require("express");
const paymentRoutes = express.Router();
const paymentController = require("../../controllers/payment-controller");
const {
  currentBillValidation,
  paymentHistoryValidation,
  checkNumberValidation,
} = require("../../validation/payment-validation");
const {
  verifyPngToken,
} = require("../../middleware/customer-auth");

paymentRoutes.get(
  "/current-bill",
  verifyPngToken("guest"),
  validate(currentBillValidation),
  paymentController.currentBill
);
paymentRoutes.post(
  "/payment-history",
  verifyPngToken("guest"),
  validate(paymentHistoryValidation),
  paymentController.paymentHistory
);
paymentRoutes.post(
  "/download-pdf",
  verifyPngToken("guest"),
  paymentController.downloadPDF
);
paymentRoutes.post(
  "/check-number",
  verifyPngToken("guest"),
  validate(checkNumberValidation),
  paymentController.checkNumber
);

module.exports = { paymentRoutes };
