const express = require('express');
const { validate } = require("express-validation");
const {
  createProofOfOwnershipValidation,
  updateProofOfOwnershipValidation,
  deleteProofOfOwnershipValidation,
  getProofOfOwnershipValidation,
  } = require("../../validation/proofOfOwnership-validation");

const proofOfOwnershiptRoutes = express.Router();
const proofOfOwnershipController = require('../../controllers/proof-of-ownership-controller');

proofOfOwnershiptRoutes.post('/list', proofOfOwnershipController.getProofOfOwnershipList);
proofOfOwnershiptRoutes.post('/create', validate(createProofOfOwnershipValidation),proofOfOwnershipController.createProofOfOwnershipList);
proofOfOwnershiptRoutes.post('/update' ,validate(updateProofOfOwnershipValidation),proofOfOwnershipController.updateProofOfOwnershipList);
proofOfOwnershiptRoutes.post('/delete',validate(deleteProofOfOwnershipValidation), proofOfOwnershipController.deleteProofOfOwnershipList);

module.exports = proofOfOwnershiptRoutes; // Export without curly braces
