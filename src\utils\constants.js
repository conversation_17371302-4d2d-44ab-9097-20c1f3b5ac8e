module.exports = {
  REGISTER_SUCCESSFULLY: "User Created Successfully",
  NAME_ALREADY_EXIST: "This Name is already exist",
  MOBILE_ALREADY_EXIST: "This Phone no is already registered. Please use other",
  OTP_NEW_USER: "OTP generated and stored for new user.",
  OTP_UPDATE_EXISTING_USER: "OTP updated and stored for existing user.",
  DATABASE_ERROR: "Database Error",
  NOT_FOUND: "Not Found",
  Data_FOUND: "Data Found",
  FORBIDDEN: "Forbidden",
  UNAUTHORIZED: "Unauthorized",
  INTERNAL_SERVER_ERROR: "Internal Server Error",
  BAD_REQUEST: "Bad Request",
  OTP_VERIFIED: "OTP verified successfully!",
  INVALID_OTP: "Invalid OTP.",
  USER_UPDATED_SUCCESSFULLY: "User updated successfully.",
  USER_NOT_FOUND: "User not found.",
  USER_LOGIN_SUCCESSFULLY: "Login Successfully",
  INVALID_CREDENTIALS: "Invalid credentials",
  OTP_EXPIRED: "OTP expired. Please generate a new one.",
  SIGNED_IN: "You are logged in successfully",
  SOME_ERROR_OCCUR: "Some error occured during the process. Please try again",
  INVALID_CREDENTIAL: "Invalid credentials",
  FETCHED: "Data fetched Successfully",
  CREATED: "Data created Successfully",
  UPDATED: "Data updated Successfully",
  DELETED: "Data deleted Successfully",
  EVENT_SUCCESSFULLY: "Event Created Successfully",
  EVENT_UPDATED: "Event Updated Successfully",
  EVENT_NOT_FOUND: "Event Not Found",
  EVENT_DELETED: "Event Deleted Successfully",
  ACTION_NOT_SUPPORTED: "Action Not Supported",
  USER_UPDATED: "user updated Successfully",
  USER_DELETED: "user deleted Successfully",
  USER_FETCHED: "user fetched Successfully",
  INVALID_ROLE_ARRAY: "Invalid roles data; roles must be an array",
  ERROR_CREATE_ROLE: "Error creating role: ",
  ERROR_LIST_TILE: "Error fetching tile: ",
  ERROR_CREATE_TILE: "Error creating tile: ",
  ERROR_UPDATE_TILE: "Error updating tile: ",
  ERROR_DELETE_TILE: "Error deleting tile: ",
  INVALID_TOKEN: "Invalid Token or expired Please login again",
  INVALID_REFRESH_TOKEN: "Invalid Refresh Token.Please try again",
  TOKEN_REQUIRED: "A Token is required for authentication",
  REFRESH_TOKEN_REQUIRED: "A Refresh Token is required for authentication",
  NOTIFICATION_MESSAGE: "This is a test message",
  NOTIFICATION_TITLE: "This is a test message",
  NOTIFICATION_TOPIC: "all_user",
  NOTIFICATION_SENT_SUCCESSFULLY: "Notification sent successfully.",
  USER_ACCOUNT_DEACTIVATED:
    "Your account has been deactivated. Please contact support team",
  USER_ACCOUNT_DELETED:
    "Your account has been deleted. Please contact support for assistance",
  FILE_UPLOAD_FAILD: "File upload failed.",
  MEDIA_FETCHED: "Media fetched successfully",
  MEDIA_NOT_EXIST: "File not exist",
  PLEASE_UPLOAD_FILE: "Please upload file",
  MEDIA_DELETED: "Media deleted successfully",
  IS_DELETED: 0,
  MEDIA_MAPPED: "Media associate with other features",
  INVALID_FILE_FRMATE:
    "Invalid file format. Only JPEG, JPG, and PNG files are allowed.",
  IS_DELETED_YES: 1,
  IS_DELETED_NO: 0,
  ERROR_CREATE_BANNER: "Error creating banner: ",
  PROVIDE_ENCRYPTED: "Please provide encrypted payload",
  PROVIDE_DECRYPTED: "Please provide decrypted payload",
  INVALID_PAYLOAD: "Invalid payload",
  PASSWORD_UNMATCHED: "Old password does not match",
  PASSWORD_UPDATE_FAILED:
    "Password update failed. Please ensure your verification otp is correct and try again. If the issue persists, contact support.",
  BANNER_EXIST: "Banner already exists",
  TITLE_ALREDY_EXIST: "Title already exists",
  ROLE_EXIST: "Role already exists",
  TILE_EXIST: "Tile already exists",
  OFFER_EXIST: "Offer already exists",
  EVENT_EXIST: "Event already exists",
  CONTENT_EXIST: "Content already exists",
  STATUS_CODE: {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500,
    SERVER_ERROR: 500,
  },
  CONNECTION_STATUS_REASON: {
    TPI: "tpi",
    CUSTOMER: "customer",
    BPCLADMIN: "bpclAdmin",
    ADMIN: "admin",
    DMA: "dma",
    LMC: "lmc",
  },
  PROGRESS_STATUS: {
    PENDING: "pending",
    INPROGRESS: "in-Progress",
    APPROVED: "approved",
    COMPLETED: "completed",
    REJECTED: "rejected",
    REWORK: "rework",
    DECLINED: "declined",
  },
  APPLICATION_STATUS: {
    APPLICATION_PENDING: "application-pending",
    APPLICATION_SUBMITTED: "application-submitted",
    APPLICATION_RESUBMITTED: "application-resubmitted",
    APPLICATION_REWORK: "application-rework",
    APPLICATION_INREVIEW: "application-inreview",
    APPLICATION_APPROVED: "application-approved",
    APPLICATION_REJECTED: "application-rejected",
    ONBOARDING_PENDING: "onboarding-pending",
    ONBOARDING_REWORK: "onboarding-rework",
    ONBOARDING_INREVIEW: "onboarding-inreview",
    ONBOARDING_APPROVED: "onboarding-approved",
    ONBOARDING_REJECTED: "onboarding-rejected",
    INSTALLATION_PENDING: "installation-pending",
    INSTALLATION_REWORK: "installation-rework",
    INSTALLATION_INREVIEW: "installation-inreview",
    INSTALLATION_APPROVED: "installation-approved",
    INSTALLATION_REJECTED: "installation-rejected",
    ACTIVE_CONNECTION: "active-connection",
  },
  APPLICATION_ASSIGN_STATUS: {
    ASSIGN_TO_TPI: "assigned-to-tpi",
    PIPELINE_CUSTOMER_REJECTED: "pipeline-customer-rejected",
    ASSIGNED_TO_LMC: "assigned-to-lmc",
  },
  UPDATE_CONNECTION_TYPE: {
    TRANSFERCONNECTION: "transfer-connection",
    PERMANENTADDRESS: "permanent-address",
    NAMETRANSFERCONNECTION: "name-transfer-connection",
    TEMPORARYDISCONNECTION: "temporary-disconnection",
    TEMPORARYRECONNECTION: "temporary-reconnection",
    PERMANENTDISCONNECTION: "permanent-disconnection",
    PERMANENTRECONNECTION: "permanent-reconnection",
    RECONNECTION: "reconnection",
  },
  PAYMENT_SECTION_TYPE: {
    SECURITY_DEPOSIT: "security-deposit",
    EXTRA_PIPELINE: "extra-pipeline",
    METER_READINGS: "meter-readings",
    NAME_TRANSFER: "name-transfer",
    OTHER_CHARGES: "other-charges",
  },
  PAYMENT_MODE: {
    CASH: "cash",
    ONLINE: "online",
  },
  APPLICATION_DECLINE_TYPE: {
    SECURITY_DEPOSIT: "security-deposit",
    EXTRA_CHARGE: "extra-charge",
    JOINT_METER_READING: "joint-meter-reading",
  },
  DECLINE_REASON: "decline_reason",
  HELP_WITH_REGISTARION_CONNECTION_EXIST:
    "Help with Registration Request Already Exist",
  ACCOUNT_TYPE: {
    PREPAID: "prepaid",
    POSTPAID: "postpaid",
  },
  NAME_TRANSFER_EMAIL_SUBJECT: "Action required: accept name transfer request",
};
