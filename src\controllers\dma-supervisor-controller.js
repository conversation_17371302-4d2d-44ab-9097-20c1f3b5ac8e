const { sendSuccess, sendError } = require('../helpers/responseHelper');
const dmaSupervisorRepository = require('../repository/dma-repository');
const crypto = require('crypto');
const { encryptPassword } = require('../utils/encrypt-decrypt');
const {generatePassword} = require('../utils/commonFn');
const SASTokenHelper = require("../helpers/azureSASSDKHelper");

exports.createSupervisor = async (req, res) => {
    try {
        const existingUser = await dmaSupervisorRepository.checkExistingDmaUser(
            req.body.email, 
            req.body.mobile
        );

        if (existingUser) {
            return sendError(
                req, 
                res, 
                null, 
                'Supervisor with this email or mobile already exists',
                400
            );
        }

        const plainPassword = generatePassword();
        const encryptedPassword = encryptPassword(plainPassword);

        const supervisorData = {
            first_name: req.body.first_name,
            last_name: req.body.last_name,
            email: req.body.email,
            mobile: req.body.mobile,
            alternate_mobile: req.body.alternate_mobile,
            bpclAdminId: req.user.bpcl_admin_id,
            password: encryptedPassword,
            persona_role_id: req.body.persona_role_id,
        };

        // Normalize area_id from form-data (can be string or array)
    let areaIds = req.body.area_id;
    if (!Array.isArray(areaIds)) {
      areaIds = [areaIds];
    }

    // Convert to integers (optional but recommended)
    areaIds = areaIds.map(id => parseInt(id, 10)).filter(id => !isNaN(id));

        const supervisor = await dmaSupervisorRepository.createDmaSupervisor(
            supervisorData,
            req.user.admin_id,
            areaIds
        );

         // Upload files after creating the supervisor
        const permission = "racwd";
        const sasToken = await SASTokenHelper.generateSASToken(permission);

        const baseFilePath = "admin/adm_" + supervisor.admin_id + "/SupervisorOnboarding/documents";

        const uploadedFiles = await SASTokenHelper.azureSingleFileUpload(
            req,
            {},
            sasToken,
            baseFilePath
        );

        if (!uploadedFiles || uploadedFiles.length === 0) {
            throw new Error("No files uploaded or failed to upload files.");
        }

        const supervisorJson = supervisor.toJSON();
        const { 
            password,
            city, 
            state, 
            last_logged_in,
            ...supervisorResponse 
        } = supervisorJson;

        sendSuccess(req, res, supervisorResponse, 'Supervisor created successfully');
    } catch (error) {
        console.error('Error in createSupervisor:', error);
        sendError(req, res, null, error.message || 'Failed to create supervisor');
    }
};

exports.getSupervisorById = async (req, res) => {
    try {
        const supervisor = await dmaSupervisorRepository.getDmaSupervisorById(
            req.params.supervisorId
        );

        if (!supervisor) {
            return sendError(req, res, null, 'Supervisor not found', 404);
        }

        sendSuccess(req, res, supervisor, 'Supervisor details retrieved successfully');
    } catch (error) {
        console.error('Error in getSupervisorById:', error);
        sendError(req, res, null, error.message || 'Failed to retrieve supervisor details');
    }
};

exports.getAllSupervisors = async (req, res) => {
    try {
        const contractorId = req.user.admin_id;
        //const contractorId = 471;
        const area_id = req.body.area_id
        const supervisors = await dmaSupervisorRepository.getAllSupervisors(contractorId,area_id);

        sendSuccess(req, res, supervisors, 'Supervisors retrieved successfully');
    } catch (error) {
        console.error('Error in getAllSupervisors:', error);
        sendError(req, res, null, error.message || 'Failed to retrieve supervisors');
    }
};

exports.deleteSupervisors = async (req, res) => {
    try {
        const contractorId = req.user.admin_id;
        const { supervisor_ids } = req.body;

        const result = await dmaSupervisorRepository.deleteSupervisors(supervisor_ids, contractorId);

        if (result.unauthorized.length > 0) {
            return sendError(
                req, 
                res, 
                { unauthorized: result.unauthorized }, 
                'Some supervisors could not be deleted due to unauthorized access',
                403
            );
        }

        sendSuccess(req, res, { deleted: supervisor_ids }, 'Supervisors deleted successfully');
    } catch (error) {
        console.error('Error in deleteSupervisors:', error);
        sendError(req, res, null, error.message || 'Failed to delete supervisors');
    }
};
exports.getAreaList = async (req, res) => {
  try {
    const data = await dmaSupervisorRepository.getAreaListContractor(req);
    sendSuccess(req, res, data, "Area fetched successfully");
  } catch (error) {
    console.error("Error in getAreaList controller:", error);
    sendError(req, res, error, "Failed to get area list");
  }
};

exports.getAllSupervisorsList = async (req, res) => {
    try {
        const contractorId = req.user.admin_id;
        const area_id = req.body.area_id;
        const page = req.body.page;
        const limit = req.body.limit;
        const search = req.body.search;
        const sortBy = req.body.sortBy;
        const sortOrder = req.body.sortOrder;
        const supervisors = await dmaSupervisorRepository.getAllSupervisorsList(contractorId,area_id,page,limit,search,sortBy,sortOrder);

        sendSuccess(req, res, supervisors, 'Supervisors retrieved successfully');
    } catch (error) {
        console.error('Error in getAllSupervisors:', error);
        sendError(req, res, null, error.message || 'Failed to retrieve supervisors');
    }
};

module.exports = {
    createSupervisor: exports.createSupervisor,
    getSupervisorById: exports.getSupervisorById,
    getAllSupervisors: exports.getAllSupervisors,
    deleteSupervisors: exports.deleteSupervisors,
    getAreaList: exports.getAreaList,
    getAllSupervisorsList: exports.getAllSupervisorsList
};
