const path = require("path");
const nodeExternals = require("webpack-node-externals");

module.exports = {
  entry: "./index.js", // your main API entry
  target: "node",
  externals: [nodeExternals()], // exclude node_modules
  output: {
    path: path.resolve(__dirname, "dist"),
    filename: "server.js",
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        use: "babel-loader",
        exclude: [
          /node_modules/,
          path.join(__dirname, "src/swagger/swagger.json"),
        ],
      },
    ],
  },
};
