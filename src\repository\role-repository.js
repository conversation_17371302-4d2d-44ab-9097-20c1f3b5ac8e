const { literal } = require("sequelize");
const {User, Role} = require("../model/role-user-association.model");
const { Op } = require('sequelize');

const getUserRoles = async (params) => {
  return await Role.findAndCountAll({
    ...params,
    distinct: true,
    include: [
      {
        model: User,
        as: "createdBy",
        attributes: [
          [
            literal(
              `CONCAT("createdBy"."first_name", ' ', "createdBy"."last_name")`
            ),
            "created_by_name",
          ],
        ],
      },
      {
        model: User,
        as: "updatedBy",
        attributes: [
          [
            literal(
              `CONCAT("updatedBy"."first_name", ' ', "updatedBy"."last_name")`
            ),
            "updated_by_name",
          ],
        ],
      },
    ],
  });
};

const showRoleList = async (id) => {
  const data = await Role.findByPk(id);
  return data;
};

const updateUserRole = async (updateData) => {
  const { role_id, ...rest } = updateData;
  await Role.update(rest, {
    where: { role_id },
  });
  return await Role.findOne({
    where: {
      role_id: role_id,
    },
    raw: true,
  });
};
const createRole = async (dto) => {
  return await Role.create(dto);
};

const roleExist = async (params) => {
  const { role_name } = params;
  return await Role.findOne({
    where: { role_name: { [Op.like]: `%${role_name}%` }, is_deleted: 0 },
    attributes: { exclude: ['updated_by', 'created_by'] },
    raw: true,
  });
};

const deleteUserRole = async (role_id, rest) => {
  return await Role.update(rest, {
    where: { role_id: role_id },
  });
};

const getRolesCount = async () => {
  const whereClause = {
    is_deleted: 0,
    is_active: 1,
  };

  const data = await Role.count({
    where: whereClause,
  });

  return data;
};

module.exports = {
  getUserRoles,
  showRoleList,
  updateUserRole,
  createRole,
  deleteUserRole,
  getRolesCount,
  roleExist
};
