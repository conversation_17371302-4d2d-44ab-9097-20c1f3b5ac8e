const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_admin_onboard_approve",
    {
      admin_onboard_approve_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      contractor_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_adminonboardapprove_contractor_id_approved_by",
      },
      contractor_type: {
        type: DataTypes.STRING(4),
        allowNull: false,
      },
      approved_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_adminonboardapprove_contractor_id_approved_by",
      },
      approve_status: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      remark: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      sequelize,
      tableName: "trx_admin_onboard_approve",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_admin_onboard_approve_id",
          unique: true,
          fields: [{ name: "admin_onboard_approve_id" }],
        },
        {
          name: "UK_adminonboardapprove_contractor_id_approved_by",
          unique: true,
          fields: [{ name: "contractor_id" }, { name: "approved_by" }],
        },
      ],
    }
  );
};
