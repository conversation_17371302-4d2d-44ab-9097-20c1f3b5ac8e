const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_alteration_request', {
    alteration_request_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    customer_connection_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer_connection',
        key: 'customer_connection_id'
      }
    },
    customer_connection_address_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer_connection_address',
        key: 'customer_connection_address_id'
      }
    },
    area_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_masters',
        key: 'master_id'
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    alteration_request_status: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_alteration_request',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_alteration_request_id",
        unique: true,
        fields: [
          { name: "alteration_request_id" },
        ]
      },
    ]
  });
};
