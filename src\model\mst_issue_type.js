const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('mst_issue_type', {
    issue_type_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    issue_category_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_masters',
        key: 'master_id'
      },
      unique: "UK_mstissuetype_issuecategoryid_customertype_issuetypename"
    },
    customer_type: {
      type: DataTypes.STRING(15),
      allowNull: false
    },
    issue_type_name: {
      type: DataTypes.STRING(50),
      allowNull: true,
      unique: "UK_mstissuetype_issuecategoryid_customertype_issuetypename"
    },
    issue_type_value: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'mst_issue_type',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_issue_type_id",
        unique: true,
        fields: [
          { name: "issue_type_id" },
        ]
      },
      {
        name: "UK_mstissuetype_issuecategoryid_customertype_issuetypename",
        unique: true,
        fields: [
          { name: "issue_category_id" },
          { name: "issue_type_name" },
        ]
      },
    ]
  });
};
