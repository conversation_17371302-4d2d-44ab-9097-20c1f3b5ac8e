const { Joi } = require("express-validation");
const {
  PHONE_NO_INVALID,
  PINCODE_INVALID,
  ALTERNATE_PHONE_NO_INVALID,
} = require("../constant/messages");
const {
  MOBILE_REGEX,
  ALPHASPACE_REGEX,
  PINCODE_REGEX,
} = require("../constant/regex");

const getConnectionDetailsByIdValidation = Joi.object({
  //bpcl_id: Joi.string().min(1).required(),
  // png_available_address: Joi.string().required(),
  available_pngareas_id: Joi.number().required(),
}).required();

const personalDetailsSCHEMA = Joi.object({
  prefix: Joi.string().required().valid("mr", "mrs", "ms", "other"),
  first_name: Joi.string().required().regex(ALPHASPACE_REGEX).messages({
    "string.pattern.base": "Only alphabet with space is allowed",
  }),
  middle_name: Joi.string().allow("", null).regex(ALPHASPACE_REGEX).messages({
    "string.pattern.base": "Only alphabet with space is allowed",
  }),
  last_name: Joi.string().required().regex(ALPHASPACE_REGEX).messages({
    "string.pattern.base": "Only alphabet with space is allowed",
  }),
  email: Joi.string().email().allow("", null),
  mobile: Joi.string().regex(MOBILE_REGEX).required().messages({
    "string.pattern.base": PHONE_NO_INVALID,
  }),
  alternate_mobile: Joi.string()
    .regex(MOBILE_REGEX)
    .messages({
      "string.pattern.base": ALTERNATE_PHONE_NO_INVALID,
    })
    .allow(null, ""),
  parent_spouse: Joi.string().required().valid("parent", "spouse"),
  parents_name: Joi.string().when("parent_spouse", {
    is: "parent",
    then: Joi.string().required().messages({
      "any.required": 'parent name is required when parent_spouse is "parent"',
      "string.empty":
        'parent name cannot be empty when parent_spouse is "parent"',
    }),
    otherwise: Joi.string().optional().allow("", null),
  }),
  dob: Joi.date().required(),
  no_of_family_members: Joi.number().optional().allow("", null),
  spouse_name: Joi.string().when("parent_spouse", {
    is: "spouse",
    then: Joi.string().required().messages({
      "any.required": 'spouse name is required when parent_spouse is "spouse"',
      "string.empty":
        'spouse name cannot be empty when parent_spouse is "spouse"',
    }),
    otherwise: Joi.string().optional().allow("", null),
  }),
}).required();

const connectionAddressSCHEMA = Joi.object({
  connection_pngareas_id: Joi.number().required(),
  connection_pincode: Joi.string().regex(PINCODE_REGEX).required().messages({
    "string.pattern.base": PINCODE_INVALID,
  }),
  connection_landmark: Joi.string().allow(null, ""),
  connection_block_no: Joi.string().allow(null, ""),
  connection_floor_no: Joi.string().allow(null, ""),
  connection_house_no: Joi.string().allow(null, ""),
  connection_building_house_name: Joi.string().required(),
  connection_state: Joi.string().required(),
  connection_city: Joi.string().required(),
  connection_district: Joi.string().required(),
  same_as_connection: Joi.boolean().allow("", null),
  permanent_pngareas_id: Joi.number().required(),
  permanent_pincode: Joi.string().regex(PINCODE_REGEX).required().messages({
    "string.pattern.base": PINCODE_INVALID,
  }),
  permanent_landmark: Joi.string().allow(null, ""),
  permanent_block_no: Joi.string().allow(null, ""),
  permanent_floor_no: Joi.string().allow(null, ""),
  permanent_house_no: Joi.string().allow(null, ""),
  permanent_building_house_name: Joi.string().required(),
  permanent_state: Joi.string().required(),
  permanent_city: Joi.string().required(),
  permanent_district: Joi.string().required(),
  connection_type: Joi.string().required().valid("original", "change-address"),
  connection_change_reason: Joi.string()
    .required()
    .valid("relocation", "same-as-connection-address"),
});

const additionalInfoSCHEMA = Joi.object({
  connection_address_type: Joi.string()
    .required()
    .valid("owned", "rented", "leased", "company_owned"),
  extra_connection: Joi.string().required().valid("yes", "no"),
  extra_points: Joi.number().min(0).max(2).required(),
  govt_scheme_id: Joi.number().min(1).required(),
  security_deposit: Joi.string()
    .allow("", null)
    .optional()
    .valid("true", "false"),
  is_lpg: Joi.number().min(0).max(2).required(),
  lpg_consumer_no: Joi.string().when("is_lpg", {
    is: 1,
    then: Joi.string().required(),
    otherwise: Joi.forbidden(),
  }),
  lpg_distributor_name: Joi.string().when("is_lpg", {
    is: 1,
    then: Joi.string().required(),
    otherwise: Joi.forbidden(),
  }),
  lpg_omc_name: Joi.string().when("is_lpg", {
    is: 1,
    then: Joi.string().required(),
    otherwise: Joi.forbidden(),
  }),
  security_deposit_category: Joi.string()
    .allow("", null)
    .optional()
    .when("security_deposit", {
      is: "yes",
      then: Joi.required(),
    }),
  rental_owner_name: Joi.string().when("connection_address_type", {
    is: "rented",
    then: Joi.string().required(),
    otherwise: Joi.forbidden(),
  }),
  rental_owner_mobile: Joi.string().when("connection_address_type", {
    is: "rented",
    then: Joi.string().regex(MOBILE_REGEX).required().messages({
      "string.pattern.base": PHONE_NO_INVALID,
    }),
    otherwise: Joi.forbidden(),
  }),
});

function atLeastOneRequired(schema) {
  const keys = Object.keys(schema.describe().keys);

  return Joi.object()
    .keys(
      keys.reduce((acc, key) => {
        acc[key] = schema.extract(key).allow("", null).optional(); // Allow empty strings & null
        return acc;
      }, {})
    )
    .custom((value, helpers) => {
      const hasValidField = keys.some(
        (key) => value[key] && value[key] !== "" && value[key] !== null
      );
      if (!hasValidField) {
        return helpers.error("object.missing");
      }
      return value;
    })
    .messages({
      "object.missing": 'At least one field is required on "save".',
    });
}
const customerLoginValidation = Joi.object({
  request: Joi.string().required(),
});

const bpclIdRequired = Joi.when("personaType", {
  is: "dma",
  then: Joi.string()
    .regex(MOBILE_REGEX)
    .messages({
      "string.pattern.base": PHONE_NO_INVALID,
    })
    .required(),
  otherwise: Joi.string().allow(null, "").regex(MOBILE_REGEX).messages({
    "string.pattern.base": PHONE_NO_INVALID,
  }),
});
const customerTypeRequired = Joi.when("personaType", {
  is: "dma",
  then: Joi.string().required().valid("self-registered", "assigned"),
  otherwise: Joi.string().allow(null, "").valid("self-registered", "assigned"),
});
const personaTypeRequired = Joi.string()
  .allow(null, "")
  .valid("customer", "dma")
  .default("customer");

const registerCustomerValidationAll = Joi.object({
  personaType: personaTypeRequired,
  bpcl_id: bpclIdRequired,
  customer_type: customerTypeRequired,
  submit_type: Joi.string().required().valid("save", "next"),
  // png_available_address: Joi.string().required(),
  old_available_pngareas_id: Joi.number().allow(null, ""),
  old_house_flat_no: Joi.string().allow(null, ""),
  available_pngareas_id: Joi.number().required(),
  house_flat_no: Joi.string().required(),
  customer_connection_id: Joi.number().allow(null, ""),
  step_no: Joi.number().valid(1, 2, 3).required(),

  personal_details: Joi.alternatives().conditional("step_no", {
    is: 1,
    then: Joi.when("submit_type", {
      is: "next",
      then: personalDetailsSCHEMA,
      otherwise: atLeastOneRequired(personalDetailsSCHEMA), // Uses fork() to allow at least one field
    }),
    otherwise: Joi.forbidden(),
  }),

  connection_address: Joi.alternatives().conditional("step_no", {
    is: 2,
    then: Joi.when("submit_type", {
      is: "next",
      then: connectionAddressSCHEMA,
      otherwise: atLeastOneRequired(connectionAddressSCHEMA),
    }),
    otherwise: Joi.forbidden(),
  }),

  addition_info: Joi.alternatives().conditional("step_no", {
    is: 3,
    then: Joi.when("submit_type", {
      is: "next",
      then: additionalInfoSCHEMA,
      otherwise: atLeastOneRequired(additionalInfoSCHEMA),
    }),
    otherwise: Joi.forbidden(),
  }),

  from_dma: Joi.boolean().optional(),
});

const customerDocumentsValidation = Joi.object({
  //bpcl_id: Joi.string().min(1).required(),
  available_pngareas_id: Joi.number().required(),
  house_flat_no: Joi.string().required(),
  proof_type: Joi.string()
    .required()
    .valid(
      "proof_of_ownership",
      "proof_of_rented",
      "photo_proof",
      "category_of_security_deposit",
      "proof_of_security_deposit_waiver"
    ),
  proof_master_id: Joi.number().min(1).required(),

  fileData: Joi.string().required(),
});

const registrationHelpValidationAll = Joi.object({
  //bpcl_id: Joi.string().min(1).required(),
  fullname: Joi.string().required(),
  block_no: Joi.string().allow("").optional(),
  email: Joi.string().email().required(),
  mobile: Joi.string().regex(MOBILE_REGEX).length(10).required().messages({
    "string.pattern.base": "Mobile number must be exactly 10 digits.",
    "string.length": "Mobile number must be exactly 10 digits.",
  }),
  pincode: Joi.number().required(),
  area: Joi.string().required(),
  flat_no: Joi.string().required(),
  floor_no: Joi.string().allow("").optional(),
  available_pngareas_id: Joi.number().required(),
  customer_type: Joi.string().optional(),
  building_house_name: Joi.string().required(),
  landmark: Joi.string().allow("").optional(),
});

const customerPersonalDetailValidation = personalDetailsSCHEMA;

const getCustomerDetailValidation = Joi.object({
  personaType: personaTypeRequired,
  bpcl_id: bpclIdRequired,
  customer_type: customerTypeRequired,
  get_type: Joi.string().allow("", null),
  customer_connection_id: Joi.number().min(1).allow("", null),
  available_pngareas_id: Joi.number(),
  house_flat_no: Joi.string(),
});

const paymentApprovalValidation = Joi.object({
  payment_type: Joi.string()
    .valid("security-deposit", "extra-pipeline", "name-transfer")
    .required(),
  customer_connection_id: Joi.number().min(1).required(),
  approval_status: Joi.number().valid(1, 0).required(),
});

const checkConnectionPincodeValidation = Joi.object({
  //bpcl_id: Joi.string().min(1).required(),
  connection_pincode: Joi.string()
    .regex(PINCODE_REGEX)
    .allow("", null)
    .messages({
      "string.pattern.base": PINCODE_INVALID,
    }),
  connection_house_no: Joi.string().allow("", null),
  connection_floor_no: Joi.string().allow("", null),
  connection_block_no: Joi.string().allow("", null),
  connection_building_house_name: Joi.string().required(),
  connection_landmark: Joi.string().allow("", null),
  connection_state: Joi.string().allow("", null),
  connection_city: Joi.string().allow("", null),
  available_pngareas_id: Joi.number().min(1).required(),
  house_flat_no: Joi.string().required(),
});
const saveDetailValidation = Joi.object({
  personaType: personaTypeRequired,
  bpcl_id: bpclIdRequired,
  customer_type: customerTypeRequired,
  available_pngareas_id: Joi.number().min(1).required(),
  house_flat_no: Joi.string().required(),
});

const connectionListValidation = Joi.object({
  //bpcl_id: Joi.string().min(1).required(),
  // available_pngareas_id: Joi.number().min(1).required(),
  // available_pngareas_id: Joi.number().min(1).allow("", null),
  // house_flat_no: Joi.number().allow("", null),
  active_status: Joi.boolean().allow("", null),
});
const getConnectionPayValidation = Joi.object({
  customer_connection_id: Joi.number().required(),
});
const deleteDocumentValidation = Joi.object({
  personaType: personaTypeRequired,
  bpcl_id: bpclIdRequired,
  customer_type: customerTypeRequired,
  available_pngareas_id: Joi.number().min(1).required(),
  house_flat_no: Joi.string().required(),
  proof_type: Joi.string()
    .required()
    .valid(
      "proof_of_ownership",
      "photo_proof",
      "proof_of_rented",
      "category_of_security_deposit",
      "proof_of_security_deposit_waiver"
    ),
  proof_master_id: Joi.number().min(1).required(),
});

const uploadDocumentValidation = Joi.object({
  personaType: personaTypeRequired,
  bpcl_id: bpclIdRequired,
  customer_type: customerTypeRequired,
  available_pngareas_id: Joi.number().min(1).required(),
  house_flat_no: Joi.string().required(),
  proof_type: Joi.string()
    .required()
    .valid(
      "proof_of_ownership",
      "proof_of_rented",
      "photo_proof",
      "category_of_security_deposit",
      "proof_of_security_deposit_waiver"
    ),
  proof_master_id: Joi.number().min(1).required(),
  customer_connection_id: Joi.number().min(1).allow("", null),
});

const getDetailByMobileValidation = Joi.object({
  personaType: personaTypeRequired,
  mobile: Joi.number().required(),
});

module.exports = {
  registerCustomerValidationAll,
  customerDocumentsValidation,
  customerPersonalDetailValidation,
  registrationHelpValidationAll,
  getConnectionDetailsByIdValidation,
  getCustomerDetailValidation,
  paymentApprovalValidation,
  checkConnectionPincodeValidation,
  saveDetailValidation,
  connectionListValidation,
  deleteDocumentValidation,
  getConnectionPayValidation,
  customerLoginValidation,
  uploadDocumentValidation,
  getDetailByMobileValidation,
};
