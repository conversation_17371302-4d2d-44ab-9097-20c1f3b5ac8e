const { dbModels } = require("../database/connection");

const getGovtSchemeList = async (page, limit) => {
  const offset = (page - 1) * limit;
  const totalRecords = await dbModels.mst_govt_scheme.count({
    where: { is_active: 1 },
  });
  const totalPages = Math.ceil(totalRecords / limit);
  if (page > totalPages) {
    return { data: [], meta: { totalRecords, totalPages, currentPage: page } };
  }
  const data = await dbModels.mst_govt_scheme.findAll({
    where: { is_active: 1 },
    offset,
    limit,
  });
  return { data, meta: { totalRecords, totalPages, currentPage: page } };
};

const createGovtSchemeList = async (dto) =>
  dbModels.mst_govt_scheme.create(dto);

const showGovtSchemeList = async (id) => dbModels.mst_govt_scheme.findByPk(id);

const updateGovtSchemeList = async (id, dto) => {
  const scheme = await showGovtSchemeList(id);
  return scheme.update(dto);
};

const deleteGovtSchemeList = async (id, dto) => {
  const scheme = await showGovtSchemeList(id);
  return scheme.update(dto);
};

const CheckExistingname = async (name) =>
  dbModels.mst_govt_scheme.findOne({ where: { scheme_name: name }, raw: true });

module.exports = {
  getGovtSchemeList,
  createGovtSchemeList,
  showGovtSchemeList,
  updateGovtSchemeList,
  deleteGovtSchemeList,
  CheckExistingname,
};
