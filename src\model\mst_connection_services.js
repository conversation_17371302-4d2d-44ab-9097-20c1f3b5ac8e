const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('mst_connection_services', {
    connection_service_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    service_type: {
      type: DataTypes.STRING(15),
      allowNull: false
    },
    service_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: "UK_mstconnectionservices_service_name"
    },
    service_slug: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'mst_connection_services',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_connection_service_id",
        unique: true,
        fields: [
          { name: "connection_service_id" },
        ]
      },
      {
        name: "UK_mstconnectionservices_service_name",
        unique: true,
        fields: [
          { name: "service_name" },
        ]
      },
    ]
  });
};
