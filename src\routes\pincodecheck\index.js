const express = require("express");
const pinCodeRouter = express.Router();
// const { validate } = require("express-validation");
const validator = require("../../middleware/validator");
const pincodeCheck = require("../../controllers/pincode-check-controller.js");
const { verifyPngToken } = require("../../middleware/customer-auth.js");

pinCodeRouter.post(
  "/check-pincode",
  validator("pincode-validation", "checkPincodeValidation"),
  verifyPngToken("guest", true),
  pincodeCheck.checkPincodeAvailability
);

module.exports = pinCodeRouter;
