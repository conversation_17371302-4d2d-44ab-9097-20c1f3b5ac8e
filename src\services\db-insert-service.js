const dbInsertRepo = require("../repository/db-insert-repository");

const createMRO = async (data) => {
  const result = await dbInsertRepo.createMeterReadingOrder(data);
  console.log("Insert result:", result);
  return result;
};

const createMappedGAAreaService = async (data) => {
  const result = await dbInsertRepo.createMappedGAArea(data);
  console.log("Insert result:", result);
  return result;
};

const createAdminService = async (data) => {
  const result = await dbInsertRepo.createAdmin(data);
  console.log('Insert result:', result);
  return result;
};

const createMeterReadingUnitsService = async (units) => {
  const result = await dbInsertRepo.createMeterReadingUnits(units);
  console.log('Meter Reading Units created:', result.length);
  return result;
};

module.exports = {
  createMRO,
  createMappedGAAreaService,
  createAdminService,
  createMeterReadingUnitsService
};
