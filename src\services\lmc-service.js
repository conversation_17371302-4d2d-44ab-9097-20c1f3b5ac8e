const lmcRepository = require("../repository/lmc-repository");
const redisHelper = require("../helpers/redisHelper");
const message = require("../utils/constants");
const azureSASHelper = require("../helpers/azureSASSDKHelper");
const ocrHelper = require("../helpers/OCRHelper");


const { formatAddress, paginateQuery, formatDate } = require("../utils/commonFn");
const { Op } = require("sequelize");
const SASTokenHelper = require("../helpers/azureSASSDKHelper");
const { path } = require("pdfkit");

const getLmcConnectionDetails = async (params) => {
  try {
    const result = lmcRepository.getPipelineDetails(
      params.customer_connection_id
    );
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};
const getLmcConnectionPaymentDetails = async (params) => {
  try {
    const result = lmcRepository.getPipelineDetails(
      params.customer_connection_id
    );
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};

const getLmcCasesSummery = async (req) => {
  try {
    let area_ids = []
    if (req?.query?.area_ids) {
      area_ids = req.query.area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(Boolean);
    }

    if (!Array.isArray(area_ids) || area_ids.length === 0 || area_ids.includes(0)) {
      return [];
    }

    const results = await lmcRepository.getLmcCasesSummery(req?.user?.admin_id, area_ids);
    
    return results || {
      total_pending_cases: 0,
      pending_feasibility_check: 0,
      pending_installations: 0,
      pending_commissioning: 0,
      current_rework_requests: 0,
      active_connections: 0,
    };
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};


const getLmcCases = async (params, lmc_id) => {
  try {
    const {
      ticketType,
      search,
      page = 1,
      limit = 1000,
      visit_status,
      tpi_status,
      lmc_stage,
      caseType
    } = params;

    const offset = (page - 1) * limit;

    const whereCondition = {
      lmc_id,
      is_active: 1,
    };

    const stageCondition = {
      is_latest: 1,
      is_active: 1,
    };

    let area_ids = []
    if (params?.area_ids) {
      area_ids = params?.area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(Boolean);
    }

    if (!area_ids || area_ids.length === 0 ||  params?.area_ids == 0 ) {
      return [];
    }
    
    switch (ticketType) {
      case "pending":
        stageCondition.tpi_status = { [Op.in]: ["pending", "pending-eo"] };
        stageCondition.visit_status = "completed";
        break;
      case "rejected":
        stageCondition.tpi_status = { [Op.in]: ["rejected", "rejected-eo"] };
        stageCondition.visit_status = "completed";
        if (lmc_stage === "commissioning") whereCondition.customer_approval = 0;
        break;
      case "approved":
        stageCondition.tpi_status = { [Op.in]: ["completed", "completed-eo"] };
        break;
      case "activated":
        stageCondition.tpi_status = { [Op.in]: ["completed", "completed-eo"] };
        stageCondition.visit_status = "completed";
        stageCondition.lmc_stage = "commissioning";
        stageCondition.ready_for_conversion = 1;
        // if (lmc_stage === "commissioning") whereCondition.customer_approval = 1;
        break;
      case "customer-approval":
        whereCondition.customer_approval = 0;
        stageCondition.visit_status = "completed";
        break;
      case "open":
        stageCondition.tpi_status = { [Op.in]: ["no-submission", "resubmit","resubmit-eo"] };
        stageCondition.visit_status = {
          [Op.in]: [
            "scheduled",
            "rescheduled",
            "to-be-rescheduled",
            "not-scheduled",
          ],
        };
        if (lmc_stage === "commissioning") {
          stageCondition.tpi_status = {
            [Op.in]: ["no-submission", "resubmit", "pending"],
          };
        }
        break;
    }
    switch (caseType) {
      case "active":
        stageCondition.tpi_status = {
          [Op.in]: [
            "no-submission",
            "pending",
            "pending-eo",
            "resubmit",
            "resubmit-eo",
          ],
        };
        // stageCondition.visit_status = {
        //   [Op.in]: [
        //     "scheduled",
        //     "rescheduled",
        //     "to-be-rescheduled",
        //     "not-scheduled",
        //     "pending",
        //     "failed"
        //   ]
        // }
        break;
      case "closed":
        stageCondition[Op.or] = [
          // Rejected in any stage
          {
            tpi_status: { [Op.in]: ["rejected", "rejected-eo"] },
          },
          // Completed, but only in commissioning stage
          {
            tpi_status: { [Op.in]: ["completed", "completed-eo"] },
            visit_status: { [Op.in]: ["completed"] },
            lmc_stage: "commissioning",
          },
        ];
        break;
    }

    if (visit_status) stageCondition.visit_status = visit_status;
    if (tpi_status) stageCondition.tpi_status = tpi_status;
    if (lmc_stage) stageCondition.lmc_stage = lmc_stage;

    const searchCondition = search
      ? {
          [Op.or]: [
            { "$customer_connection.customer_connection_id$": { [Op.like]: `%${search}%` } },
            { "$customer_connection.first_name$": { [Op.like]: `%${search}%` } },
            { "$customer_connection.last_name$": { [Op.like]: `%${search}%` } },
            { "$customer_connection.email$": { [Op.like]: `%${search}%` } },
            { "$customer_connection.mobile$": { [Op.like]: `%${search}%` } },
          ],
        }
      : {};

    const { count, rows } = await lmcRepository.getLmcCases(
      whereCondition,
      stageCondition,
      searchCondition,
      limit,
      offset,
      area_ids
    );

    const cases = rows.map((caseItem) => ({
      mapped_lmc_customer_connection_id: caseItem.lmc_connection_mapping_id || "N/A",
      lmc_stage: caseItem.trx_mapped_lmc_connection_stages?.[0]?.lmc_stage || "N/A",
      customer_approval: caseItem.customer_approval ?? "N/A",
      visit_status: caseItem.trx_mapped_lmc_connection_stages?.[0]?.visit_status || "Unknown",
      visit_datetime: caseItem.trx_mapped_lmc_connection_stages?.[0]?.visit_datetime || "N/A",
      tpi_status: caseItem.trx_mapped_lmc_connection_stages?.[0]?.tpi_status || "Not submitted",
    
      customer_connection_id: caseItem.customer_connection?.customer_connection_id || "N/A",
      bp_id: caseItem.customer_connection?.bp_id || "N/A",
      full_name: `${caseItem.customer_connection?.first_name || ""} ${caseItem.customer_connection?.last_name || ""}`.trim(),
      email: caseItem.customer_connection?.email || "",
      mobile: caseItem.customer_connection?.mobile || "",
    
      pincode: caseItem.customer_connection?.trx_customer_connection_addresses?.[0]?.connection_pincode || "N/A",
    
      // Optional pipeline documents, if enabled later
      // file_path: caseItem.trx_mapped_lmc_connection_stages?.[0]?.trx_lmc_pipeline_documents?.[0]?.file_path || "N/A",
    }));
    
    return {
      cases,
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    };
  } catch (error) {
    console.error("Service Error - getLmcCasesService:", error);
    throw error;
  }
};

const getLmcTickets = async (req, lmc_stage) => {
  try {
    const { ticketType, search, visit_status, tpi_status } = req.query;
    const { page, limit, offset } = paginateQuery(req);
    const lmc_id = req?.user?.admin_id;
    const whereCondition = {  is_latest: 1};
    const holdCondition = {};
    let securityDepositFilter = null;
    let area_ids = []
    if (req?.query?.area_ids) {
      area_ids = req?.query?.area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(Boolean);
    }

    if (!area_ids || area_ids.length === 0 ) {
      return [];
    }
    // Handle ticket types
    switch (ticketType) {
      case "pending":
        whereCondition.tpi_status = { [Op.in]: ["pending", "pending-eo"] };
        whereCondition.visit_status = "completed";
        break;
      case "hold":
        holdCondition.ready_for_conversion = 0;
        whereCondition.visit_status = "completed";
        whereCondition.tpi_status = { [Op.in]: ["completed", "completed-eo"] };
        break;
      case "rejected":
        whereCondition.tpi_status = { [Op.in]: ["rejected", "rejected-eo"] };
        whereCondition.visit_status = "completed";
        break;
      case "approved":
        whereCondition.tpi_status = { [Op.in]: ["completed", "completed-eo"] };
        break;
      case "activated":
        whereCondition.tpi_status = { [Op.in]: ["completed", "completed-eo"] };
        whereCondition.visit_status = "completed";
        holdCondition.ready_for_conversion = 1;
        break;
      case "customer-approval":
        securityDepositFilter = 0; // Apply this only for customer approval
        break;
      case "open":
        whereCondition.tpi_status = { [Op.in]: ["no-submission", "resubmit","resubmit-eo"] };
        whereCondition.visit_status = {
          [Op.in]: ["scheduled", "rescheduled", "to-be-rescheduled", "not-scheduled"],
        };
        break;
    }

    if (lmc_stage === 'commissioning' && ticketType !== 'customer-approval') {
      securityDepositFilter = 1;
    }
    if (lmc_stage) whereCondition.lmc_stage = lmc_stage;
    if (visit_status) whereCondition.visit_status = visit_status;
    if (tpi_status) whereCondition.tpi_status = tpi_status;

    const searchCondition = search
      ? {
          [Op.or]: [
            { "$lmc_connection_mapping.customer_connection.customer_connection_id$": { [Op.like]: `%${search}%` } },
            { "$lmc_connection_mapping.customer_connection.first_name$": { [Op.like]: `%${search}%` } },
            { "$lmc_connection_mapping.customer_connection.last_name$": { [Op.like]: `%${search}%` } },
            { "$lmc_connection_mapping.customer_connection.email$": { [Op.like]: `%${search}%` } },
            { "$lmc_connection_mapping.customer_connection.mobile$": { [Op.like]: `%${search}%` } },
          ],
        }
      : {};

    const { count, rows: cases } = await lmcRepository.getLmcTickets(
      whereCondition,
      searchCondition,
      limit,
      offset,
      securityDepositFilter,
      lmc_id,
      area_ids,
      holdCondition
    );

    const formattedCases = cases.map((caseItem) => ({
      mapped_lmc_connection_stage_id: caseItem.mapped_lmc_connection_stage_id,
      mapped_lmc_customer_connection_id:
        caseItem.lmc_connection_mapping?.customer_connection?.customer_connection_id || "N/A",
      lmc_stage: caseItem.lmc_stage || "N/A",
      customer_approval: caseItem.customer_approval ?? "N/A",
      visit_status: caseItem.visit_status || "Unknown",
      visit_datetime: caseItem.visit_datetime || "N/A",
      tpi_status: caseItem.tpi_status || "Not submitted",
      customer_connection_id:
        caseItem.lmc_connection_mapping?.customer_connection?.customer_connection_id || "N/A",
      bp_id:
        caseItem.lmc_connection_mapping?.customer_connection?.bp_id || "N/A",
      full_name: `${caseItem.lmc_connection_mapping?.customer_connection?.first_name || ""} ${caseItem.lmc_connection_mapping?.customer_connection?.last_name || ""}`.trim(),
      email: caseItem.lmc_connection_mapping?.customer_connection?.email || "",
      mobile: caseItem.lmc_connection_mapping?.customer_connection?.mobile || "",
      pincode:
        caseItem.lmc_connection_mapping?.customer_connection?.trx_customer_connection_addresses?.[0]?.connection_pincode || "N/A",
      file_path:
        caseItem.trx_lmc_pipeline_documents?.[0]?.file_path || "N/A",
      address:
        formatAddress(caseItem.lmc_connection_mapping?.customer_connection?.trx_customer_connection_addresses?.[0]) || "N/A",
    }));

    return {
      cases: formattedCases,
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    };
  } catch (error) {
    console.error("Error in getLmcFeasibilityTickets:", error);
    return res.status(500).json({ message: "Internal Server Error" });
  }
};



const getLmcStageDetails = async (params) => {
  try {
    const { mapped_lmc_connection_stage_id } = params;
    const result = lmcRepository.getLmcStageDetails(
      mapped_lmc_connection_stage_id
    );
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};

const saveLmcScheduleVisit = async (payload, lmc_id) => {
  try {
    const { mapped_lmc_connection_stage_id, schedule_date } = payload;
    const result = lmcRepository.saveLmcScheduleVisit(
      mapped_lmc_connection_stage_id,
      schedule_date,
      lmc_id
    );
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};

const uploadPipelineDrawing = async (payload, file, lmc_id) => {
  try {
    const { customer_connection_id, file_type, lmc_stage } = payload;

    if (!customer_connection_id) {
      return {
        success: false,
        message: "Missing required parameters: customer_connection_id or file",
      };
    }

    // Generate Redis key
    const redisKey = `pipelineDrawing:${customer_connection_id}:${file_type}:${lmc_stage}`;

    // Extract file details
    const filePath = file.path;
    const fileName = file.originalname || filePath.split("/").pop(); // Fallback to extracted filename
    const fileExt = filePath.split(".").pop(); // Extract file extension

    // Store extracted data in Redis
    const cacheData = {
      customer_connection_id,
      file_name: fileName,
      file_path: filePath,
      file_ext: fileExt,
      file_type: file_type || "unknown",
      lmc_stage: lmc_stage || "unknown",
    };

    await redisHelper.setData(redisKey, cacheData, 604800); // Cache for 7 days

    return {
      success: true,
      message: "Pipeline drawing uploaded successfully.",
      data: cacheData,
    };
  } catch (error) {
    console.error("Error in uploadPipelineDrawing:", error);
    return {
      success: false,
      message: "Error processing the uploaded document",
      error: error.message,
    };
  }
};

const submitPipelineDrawing = async (payload, file, lmc_id) => {
  try {
    const {
      customer_connection_id,
      file_type,
      customer_consent_request,
      pipeline_size,
      lmc_stage,
    } = payload;

    // Validate required parameters
    if (!customer_connection_id || !pipeline_size) {
      return {
        success: false,
        message:
          "Missing required parameters: customer_connection_id or pipeline_size.",
      };
    }

    // Validate customer consent for large pipelines
    if (pipeline_size > 15 && !customer_consent_request) {
      return {
        success: false,
        message:
          "Customer consent is required for pipeline sizes over 15 meters.",
      };
    }

    // Extract file details safely
    const filePath = file.path;
    const fileName = file.originalname || filePath.split("/").pop();
    const fileExt = fileName.split(".").pop() || null;

    // Validate file existence
    if (!file || !filePath) {
      return {
        success: false,
        message: "File upload is required.",
      };
    }

    // Prepare data for saving
    const data = {
      customer_connection_id,
      customer_consent_request: customer_consent_request || null,
      pipeline_size,
      file_name: fileName,
      file_path: filePath,
      file_ext: fileExt,
      file_type,
      lmc_stage: lmc_stage || null,
    };

    // Save pipeline drawing details in the database
    const result = await lmcRepository.savePipelineDrawing(data);

    return result;
  } catch (error) {
    console.error("Error in submitPipelineDrawing:", error);
    return {
      success: false,
      message: "Error processing the uploaded document.",
      error: error.message,
    };
  }
};

const getLmcInstallationTickets = async (params, lmc_id) => {
  try {
    const result = lmcRepository.getLmcTickets(params, "installation", lmc_id);
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};
const getLmcCommissioningTickets = async (params, lmc_id) => {
  try {
    const result = lmcRepository.getLmcTickets(params, "commissioning", lmc_id);
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};

const submitLmcJMR = async (payload, files, lmc_id) => {
  try {
    const redisKey = getRedisLMCMeterReadingConnectionKey(
      payload.customer_connection_id
    );
    let cachedData = (await redisHelper.getData(redisKey)) || {};
    // if (!cachedData || !cachedData.meter_reading_file) {
    //   return {
    //     success: false,
    //     message: "Please upload meter reading first."
    //   };
    // }

    const result = lmcRepository.saveLmcJMR(
      payload,
      files.path,
      cachedData.meter_reading_file,
      lmc_id
    );
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};
const getLmcJMRSubmissionDetails = async (params, lmc_id) => {
  try {
    const result = lmcRepository.getLMCMeterReadingDetails(params, lmc_id);
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};
const getRedisLMCMeterReadingConnectionKey = (customerConnectionId) => {
  return `lmc_meter_reading:${customerConnectionId}`;
};

const processLmcMeterReadingImage = async (payload, file) => {
  try {
    const { customer_connection_id } = payload;
    // Extract meter reading value using third-party service
    // const meterReadingValue = await processImageWithThirdParty(file);
    // console.log(">>>>>>>>>rrrrr", file);
    const ocrReadingValue = await ocrHelper.getOCRImageDetails(file.blob_url);
    console.log(">>>>>>>>>rrrrr", ocrReadingValue);
    let readingOne = ocrReadingValue.fields['Meter Reading 1'].valueInteger;
    let readingTwo = ocrReadingValue.fields['Meter reading 2'].valueInteger;
    let readingThree = ocrReadingValue.fields['Meter Reading 3'].valueInteger;
    let readingFour = ocrReadingValue.fields['Meter Reading 4'].valueInteger;
    let readingFive = ocrReadingValue.fields['Meter Reading 5'].valueInteger;
    let readingSix = ocrReadingValue.fields['Meter Reading 6'].valueInteger;
    let readingSeven = ocrReadingValue.fields['Meter Reading 7'].valueInteger;
    let readingEight = ocrReadingValue.fields['Meter Reading 8'].valueInteger;

    let meterReadingValue = readingOne + '' + readingTwo + '' + readingThree + '' + readingFour + '' + readingFive + '' + readingSix + '' + readingSeven + '' + readingEight;
    console.log(">>>>>>", meterReadingValue);
    // if(meterReadingValue.length != 8 && (isNaN(readingOne) || isNaN(readingTwo) || isNaN(readingThree) || isNaN(readingFour) || isNaN(readingFive) || isNaN(readingSix) || isNaN(readingSeven) || isNaN(readingEight))){
    //   return { error: "Failed to read Image, please reupload" };
    // }

    // if (!meterReadingValue) {
    //   return { error: "Failed to read Image, please reupload" };
    // }

    // Generate Redis key
    const redisKey = getRedisLMCMeterReadingConnectionKey(
      customer_connection_id
    );

    // Store the extracted data in Redis
    const cacheData = {
      customer_connection_id,
      meter_reading_value: meterReadingValue,
      meter_reading_file: file.folderName,
      file_name: file.filename,
      meter_no:  ocrReadingValue.fields['Meter No'].valueString
    };
console.log('meterReadingValue', cacheData); // Output: "uu u 2 3"
    await redisHelper.setData(redisKey, cacheData, 604800); // Cache for 7 days

    return {
      message: "Meter reading processed successfully.",
      data: cacheData,
    };
  } catch (error) {
    console.error("Error in processMeterReadingImage:", error);
    throw new Error("Error processing the uploaded document");
  }
};

// Mock function for third-party image processing
const processImageWithThirdParty = async (file) => {
  try {
    // Simulate third-party API call to process the image

    // Replace with actual third-party API integration
    const extractedValue = Math.floor(Math.random() * 10); // Mocked extracted meter reading value
    return extractedValue;
  } catch (error) {
    console.error("Error processing image with third-party tool:", error);
    throw new Error("Failed to extract meter reading from image.");
  }
};

// Controller function to get form details for a customer
const getFormDetails = async (req) => {
  try {
    // Fetch customer details based on provided customer_connection_id
    const customer = await lmcRepository.getCustomerDetailsById(
      req?.body?.customer_connection_id
    );
    if (!customer) throw new Error("Customer not found");

    // Assuming `formatAddress` is a utility function for address formatting
    const address = customer?.trx_customer_connection_addresses?.[0];
    const formattedAddress = address ? formatAddress(address) : 'Address not available';

    // Fetch form details from the mapped LMC connection stage
    const formDetails = customer?.trx_lmc_connection_mappings?.[0]?.trx_mapped_lmc_connection_stages?.[0];

    // Return the response with form details directly fetched from the database
    return {
      bp_id: customer.bp_id,
      customer_connection_id: customer.customer_connection_id,
      customer_name: `${customer.first_name} ${customer.last_name}`,
      address: formattedAddress,
      form_number: formDetails?.form_no || 'Not Available',
      service_order_no: formDetails?.service_order_no || 'Not Available',
      service_request_no: formDetails?.service_request_no || 'Not Available',
    };
  } catch (error) {
    console.error("Error in getFormDetails:", error);
    throw error;
  }
};

const submitFeasibilityFrom = async (req) => {
  try {
    const {
      mapped_lmc_connection_stage_id,
      house_type,
      mdpe_pipeline_length,
      riser_length,
      gi_pipeline_length,
      meter_type,
      actual_visit_date,
      customer_connection_id,
    } = req.body;

    if (!mapped_lmc_connection_stage_id || !customer_connection_id) {
      throw new Error("Missing required fields: mapped_lmc_connection_stage_id or customer_connection_id");
    }

    const feasibilityData = {
      mapped_lmc_connection_stage_id: Number(mapped_lmc_connection_stage_id),
      house_type,
      mdpe_pipeline_length: parseFloat(mdpe_pipeline_length),
      riser_length: parseFloat(riser_length),
      gi_pipeline_length: parseFloat(gi_pipeline_length),
      meter_type,
      actual_visit_date,
      customer_connection_id
    };

    const lmc_id = req?.user?.admin_id;
    if (!lmc_id) {
      throw new Error("LMC ID is missing. User session might be invalid.");
    }

    let filePath = "customer/cus_" + customer_connection_id + "/lmc_supervisor/feasibility";
    const permission = "racwd";
    const sasToken = await SASTokenHelper.generateSASToken(permission);

    // Handle file upload
    const imageUploaded = await SASTokenHelper.azureMultipleFileUpload(req, {}, sasToken, filePath);
    if (!imageUploaded || imageUploaded.length === 0) {
      throw new Error("No files uploaded or failed to upload files.");
    }

    const documentFiles = imageUploaded.map((file) => {
      const extension = file?.file?.split(".").pop() || "";
      return {
        mapped_lmc_connection_stage_id: feasibilityData.mapped_lmc_connection_stage_id,
        file_type: file.category,
        file_name: file.fileName,
        file_path: filePath,
        file_ext: `${extension}`,
        created_by: lmc_id,
      };
    });

    // Submit feasibility form
    return await lmcRepository.submitFeasibilityForm(feasibilityData, documentFiles, lmc_id);

  } catch (error) {
    console.error("Error in submitFeasibilityForm:", error);
    return {
      success: false,
      message: error.message || "Error processing the feasibility form",
      error: error.stack || error.message,
    };
  }
};


const getFeasibilityFormDetails = async (req) => {
  try {
    const { mapped_lmc_connection_stage_id } = req.body;

    const feasibilityData = await lmcRepository.getFeasibilityFormDetails(mapped_lmc_connection_stage_id);

    if (!feasibilityData) {
      return {
        success: false,
        message: "No feasibility data found",
      };
    }

    const mappedStage = feasibilityData.mapped_lmc_connection_stage || {};

    const response = {
      visit_status: mappedStage.visit_status || 'NA',
      actual_visit_date: mappedStage.actual_visit_date ? formatDate(mappedStage.actual_visit_date) : 'NA',
      visit_datetime: mappedStage.visit_datetime ? formatDate(mappedStage.visit_datetime) : 'NA',
      date_of_creation: feasibilityData.createdAt ? formatDate(feasibilityData.createdAt) : 'NA',
      house_type: feasibilityData.house_type || 'NA',
      mdpe_pipeline_length: Number(feasibilityData.mdpe_pipeline_length || 0),
      riser_length: Number(feasibilityData.riser_length || 0),
      gi_pipeline_length: Number(feasibilityData.gi_pipeline_length || 0),
      meter_type: feasibilityData.meter_type || 'NA',
      documents: mappedStage.trx_lmc_pipeline_documents?.map(doc => ({
        file_name: doc.file_name,
        file_path: doc.file_path,
        file_type: doc.file_type,
      })) || [],
    };

    return {
      success: true,
      message: "Feasibility data fetched successfully",
      data: response,
    };

  } catch (error) {
    console.error("Service error in getFeasibilityFormDetails:", error);
    return {
      success: false,
      message: "Error processing the request",
      error: error.message,
    };
  }
};


const submitCommissioningForm = async (req) => {
  try {
    let {
      mapped_lmc_connection_stage_id,
      ready_for_conversion,
      meter_type,
      actual_visit_date,
      customer_connection_id,
      meter_no,
      meter_reading,
      is_manual,
    } = req.body;

    if (!mapped_lmc_connection_stage_id || !customer_connection_id) {
      throw new Error(
        "Missing required fields: mapped_lmc_connection_stage_id or customer_connection_id"
      );
    }
    is_manual = Number(is_manual);

    const feasibilityData = {
      mapped_lmc_connection_stage_id: Number(mapped_lmc_connection_stage_id),
      meter_type,
      actual_visit_date,
      ready_for_conversion,
    };

    const lmc_id = req?.user?.admin_id;
    if (!lmc_id) {
      throw new Error("LMC ID is missing. User session might be invalid.");
    }

    const redisKey = getRedisLMCMeterReadingConnectionKey(
      customer_connection_id
    );
    let cachedData = (await redisHelper.getData(redisKey)) || {};

    const permission = "racwd";
    const sasToken = await SASTokenHelper.generateSASToken(permission);

    // File upload
    const baseFilePath = `customer/cus_${customer_connection_id}/lmc_supervisor/commissioning`;
    const uploadedFiles = await SASTokenHelper.azureMultipleFileUpload(
      req,
      {},
      sasToken,
      baseFilePath
    );

    if (!uploadedFiles || uploadedFiles.length === 0) {
      throw new Error("No files uploaded or failed to upload files.");
    }

    // Prepare files for commissioning documents
    const commissioningFiles = uploadedFiles.map((file) => {
      const extension = file?.file?.split(".").pop() || "";
      return {
        mapped_lmc_connection_stage_id:
          feasibilityData.mapped_lmc_connection_stage_id,
        file_type: file.category,
        file_name: file.fileName,
        file_path: baseFilePath,
        file_ext: `${extension}`,
        created_by: lmc_id,
      };
    });

    // Find JMR approval file (category = jmr)
    const jmrApprovalFileObj = uploadedFiles.find(
      (file) => file.category === "jmr-customer-approval"
    );
    if (!jmrApprovalFileObj) {
      throw new Error("JMR (customer approval) file not uploaded.");
    }

    // Find meter reading file (category = meter_reading)
    const meterReadingFileObj = uploadedFiles.find(
      (file) => file.category === "jmr"
    );
    if (!meterReadingFileObj) {
      throw new Error("Meter reading image not uploaded.");
    }
    let jmrFilePath =
      "customer/cus_" + customer_connection_id + "/meterreading";
    jmrFilePath = cachedData.jmrFilePath + "/" + cachedData.file_name;
    const source = `temp/${cachedData.file_name}`;

    await azureSASHelper.moveBlobWithinContainer(source, jmrFilePath);
    // Save JMR
    const saveJmrPayload = {
      customer_connection_id,
      mapped_lmc_connection_stage_id,
      meter_no: cachedData.meter_no,
      meter_type,
      meter_reading: cachedData.meter_reading_value,
      is_manual: is_manual || false,
    };

    const jmrResult = await lmcRepository.saveLmcJMR(
      saveJmrPayload,
      baseFilePath + "/" + jmrApprovalFileObj.file,
      jmrFilePath,
      lmc_id
    );

    if (jmrResult?.error) {
      throw new Error(jmrResult.error);
    }
    const tpi_status = Number(is_manual) === 1 ? "pending" : "completed";

    // Submit commissioning form
    const result = await lmcRepository.submitCommissioningForm(
      feasibilityData,
      commissioningFiles,
      lmc_id,
      tpi_status
    );
    if (ready_for_conversion === 0) {
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 2);

      const formattedDate = futureDate.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });

      return {
        success: true,
        message: `Connection on hold.\n\nYou have submitted the commissioning form for customer with BP ID ${jmrResult?.bp_id || customer_connection_id}.\n\nThe connection is temporarily put on hold and will be activated after 2 months: ${formattedDate}`,
        data: result,
      };
    }

    return {
      success: true,
      message: "Commissioning form submitted successfully",
      data: result,
    };
  } catch (error) {
    console.error("Error in submitCommissioningForm:", error);
    return {
      success: false,
      message: error.message || "Error processing the commissioning form",
      error: error.stack || error.message,
    };
  }
};



const getCommissioningFormDetails = async (req) => {
  try {
    const { mapped_lmc_connection_stage_id } = req.body;

    const feasibilityData = await lmcRepository.getCommissioningFormDetails(mapped_lmc_connection_stage_id);

    if (!feasibilityData) {
      return {
        success: false,
        message: "No feasibility data found",
      };
    }

    const mappedStage = feasibilityData.mapped_lmc_connection_stage || {};
    const meterReading = feasibilityData.meter_reading || {};

    const response = {
      visit_status: mappedStage.visit_status || 'NA',
      actual_visit_date: mappedStage.actual_visit_date ? formatDate(mappedStage.actual_visit_date) : 'NA',
      visit_datetime: mappedStage.visit_datetime ? formatDate(mappedStage.visit_datetime) : 'NA',
      date_of_creation: feasibilityData.created_at ? formatDate(feasibilityData.created_at) : 'NA',
      house_type: feasibilityData.house_type || 'NA',
      mdpe_pipeline_length: Number(feasibilityData.mdpe_pipeline_length || 0),
      riser_length: Number(feasibilityData.riser_length || 0),
      gi_pipeline_length: Number(feasibilityData.gi_pipeline_length || 0),
      meter_type: feasibilityData.meter_type || 'NA',
      meter_details: {
        meter_number: meterReading.customer_meter?.meter_no || 'NA',
        meter_type: meterReading.customer_meter?.meter_type || 'NA',
        meter_reading: meterReading.current_reading || 'NA',
        reading_units: meterReading.reading_units || 'NA',
        reading_taken_at: meterReading.created_at ? formatDate(meterReading.created_at) : 'NA',
      },
      documents: (mappedStage.trx_lmc_pipeline_documents || []).map(doc => ({
        file_name: doc.file_name,
        file_path: doc.file_path,
        file_type: doc.file_type,
      })),
    };

    return {
      success: true,
      message: "Commissioning form data fetched successfully",
      data: response,
    };

  } catch (error) {
    console.error("Service error in getCommissioningFormDetailsService:", error);
    return {
      success: false,
      message: "Error processing the request",
      error: error.message,
    };
  }
};
const saveInstallationDetailsToCache = async (step, detailsData) => {
  try {

    const { mapped_lmc_connection_stage_id, mapped_lmc_customer_connection_id } = detailsData;

    if (!mapped_lmc_connection_stage_id || !mapped_lmc_customer_connection_id) {
      throw new Error('Missing required data: mapped_lmc_connection_stage_id or mapped_lmc_customer_connection_id');
    }


    const connectionRedisKey = await redisHelper.getRedisInstallationConnectionKey(
      mapped_lmc_connection_stage_id,
      mapped_lmc_customer_connection_id
    );

    let getConnectionDetailsFromRedis =
      (await redisHelper.getData(connectionRedisKey)) || {};

    if (detailsData.tempRegistrationId) {
      getConnectionDetailsFromRedis.registrationId = detailsData.tempRegistrationId;
    }

    const result = await updateConnectionStepInRedis(
      getConnectionDetailsFromRedis,
      detailsData.detailsData,
      step,
      connectionRedisKey
    );

    return { result };
  } catch (err) {
    console.error("Error in saveInstallationDetailsToCache:", err);
    throw err;
  }
};


const updateConnectionStepInRedis = async (getConnectionDetails, detailsData, step,connectionRedisKey) => {
  try {
    console.log("Existing getConnectionDetails:", getConnectionDetails);

    const expireTime = 604800; // 7 days expiry

    switch (step) {
      case "technicalDetails":
        // Initialize technicalDetails if not present
        getConnectionDetails.technicalDetails = getConnectionDetails.technicalDetails || {};
        getConnectionDetails.technicalDetails = mergeData(
          getConnectionDetails.technicalDetails,
          {
            actual_visit_date: detailsData?.actual_visit_date,
            regulator_no: detailsData?.regulator_no,
            installation_date: detailsData?.installation_date,
            testing_date: detailsData?.testing_date,
            rfc_date: detailsData?.rfc_date,
            house_type: detailsData?.house_type,
            mdpe_pipeline_length: detailsData?.mdpe_pipeline_length,
            riser_length: detailsData?.riser_length,
            gi_pipeline_length: detailsData?.gi_pipeline_length,
            meter_no: detailsData?.meter_no,
          }
        );
        break;

      case "billOfMaterial":
        // Initialize billOfMaterial if not present
        getConnectionDetails.billOfMaterial = getConnectionDetails.billOfMaterial || {};
        getConnectionDetails.billOfMaterial = mergeData(
          getConnectionDetails.billOfMaterial,
          {
            gi_length: detailsData?.gi_length,
            gi_length_tapping: detailsData?.gi_length_tapping,
            copper_length: detailsData?.copper_length,
            appliance_valve_half_inch: detailsData?.appliance_valve_half_inch,
            isolation_ball_valve: detailsData?.isolation_ball_valve,
            brass_fitting_set: detailsData?.brass_fitting_set,
            gi_plug_half_inch: detailsData?.gi_plug_half_inch,
            half_inch_2_inch_nipple: detailsData?.half_inch_2_inch_nipple,
            half_inch_3_inch_nipple: detailsData?.half_inch_3_inch_nipple,
            half_inch_4_inch_nipple: detailsData?.half_inch_4_inch_nipple,
            elbow: detailsData?.elbow,
            equal_tee: detailsData?.equal_tee,
            socket: detailsData?.socket,
            union: detailsData?.union,
            anaconda: detailsData?.anaconda,
            elbow_mf: detailsData?.elbow_mf,
          }
        );
        break;

      case "checklist":
        // Initialize checklist if not present
        getConnectionDetails.checklist = getConnectionDetails.checklist || {};
        getConnectionDetails.checklist = detailsData?.checklist
        break;

      default:
        throw new Error(`Invalid step provided: ${step}`);
    }

    console.log("Updated getConnectionDetails:", getConnectionDetails);

    let result = await redisHelper.setData(
      connectionRedisKey,
      getConnectionDetails,
      expireTime
    );

    console.log("Redis set result:", result);
    return result;
  } catch (error) {
    console.error("Error in updateConnectionStepInRedis:", error);
    throw error;
  }
};

const mergeData = (existingData = {}, newData = {}) => {
  return {
    ...existingData,
    ...Object.fromEntries(
      Object.entries(newData).filter(
        ([_, value]) => value !== 'NA' && value !== undefined
      )
    ),
  };
};


const InstallationDocumentsUpload = async (detailsData, files) => {
  try {
    console.log("Received files:", files);

    if (!files || Object.keys(files).length === 0) {
      throw new Error("No files uploaded.");
    }

    const redisKey = redisHelper.getRedisInstallationConnectionKey(
      detailsData.mapped_lmc_connection_stage_id,
      detailsData.mapped_lmc_customer_connection_id,
    );

    // Save grouped files directly
    const formattedFiles =await uploadDocRedis(redisKey, detailsData, files);


    return { 
      message: "Files uploaded and stored in Redis.", 
      files: formattedFiles
    };

  } catch (error) {
    console.error("Error in InstallationDocumentsUpload:", error);
    throw error;
  }
};



const uploadDocRedis = async (redisKey, detailsData, files) => {
  try {
    let getConnectionDetails = await redisHelper.getData(redisKey);
    if (!getConnectionDetails) {
      throw new Error("No data found in cache. Please try again after filling basic details.");
    }

    getConnectionDetails.uploadDocuments = [];

    const allowedExtensions = ['png', 'jpg', 'jpeg', 'pdf'];
    const maxFileSize = 5 * 1024 * 1024;

    for (const category in files) {
      const categoryFileGroups = files[category]; // array of arrays
      for (const fileGroup of categoryFileGroups) {
        for (const file of fileGroup) {
          const extname = (file?.fileName?.split('.').pop() || '').toLowerCase();

          if (!allowedExtensions.includes(extname)) {
            throw new Error(`Invalid file type: ${extname}. Only PNG, JPG, JPEG, and PDF allowed.`);
          }

          // You might not have the size in file object from frontend – ensure it's added or skip check
          if (file?.size && file.size > maxFileSize) {
            throw new Error(`File size exceeds 5MB limit.`);
          }

          const installationDoc = {
            mapped_lmc_connection_stage_id: detailsData.mapped_lmc_connection_stage_id,
            file_name: file.fileName,
            file_path: file.folderName || 'temp',
            file_ext: extname,
            verification_status: "pending",
            file_status: "pending",
            file_type: file.category || category,
          };

          getConnectionDetails.uploadDocuments.push(installationDoc);
        }
      }
    }

    await redisHelper.setData(redisKey, getConnectionDetails, 604800);

    return getConnectionDetails.uploadDocuments;

  } catch (error) {
    console.error("Error in uploadDocRedis:", error.message);
    throw error;
  }
};



const saveInstallationFromRedis = async (detailsData, lmc_id) => {
  const redisKey = await redisHelper.getRedisInstallationConnectionKey(
    detailsData.mapped_lmc_connection_stage_id,
    detailsData.mapped_lmc_customer_connection_id
  );
  const redisData = await redisHelper.getData(redisKey);

  if (!redisData) throw new Error("No installation data found in Redis.");
  const installationData = {
    ...redisData.technicalDetails,
    ...redisData.billOfMaterial,
    mapped_lmc_connection_stage_id: detailsData.mapped_lmc_connection_stage_id,
    mapped_lmc_customer_connection_id : detailsData.mapped_lmc_customer_connection_id
  };
  installationData.checklist = redisData.checklist

  let documentFiles = [];

  if (redisData?.uploadDocuments?.length) {
    const baseFolder = `customer/cus_${detailsData?.mapped_lmc_customer_connection_id}/lmc_supervisor/installation`;
  
    documentFiles = await Promise.all(redisData.uploadDocuments.map(async (doc) => {
      const source = `temp/${doc.file_name}`;
      const destination = `${baseFolder}/${doc.file_name}`;
  
      await azureSASHelper.moveBlobWithinContainer(source, destination);
  
      return {
        file_type: doc.file_type || 'others',
        file_name: doc.file_name,
        file_path: baseFolder, 
        file_ext: doc.file_ext,
      };
    }));
  }

  const result = await lmcRepository.submitInstallationForm(installationData, documentFiles, lmc_id);

  return result;
};
const getInstallationDetailsFromCache = async (detailsData) => {
  const redisKey = await redisHelper.getRedisInstallationConnectionKey(
    detailsData.mapped_lmc_connection_stage_id,
    detailsData.mapped_lmc_customer_connection_id
  );

  const redisData = await redisHelper.getData(redisKey);

  if (!redisData) {
    throw new Error("No installation data found in Redis.");
  }

  const customerAddress = await lmcRepository.getCustomerAddressDetails(detailsData.mapped_lmc_customer_connection_id);

  const formattedData = {
    area: customerAddress?.connection_address_pngarea?.pincodeAreasAlias?.area?.area_name || null,
    city: customerAddress?.connection_city || 'NA',
    house_no: customerAddress?.connection_house_no || customerAddress?.connection_floor_no || 'NA',
    house_address: customerAddress?.connection_address || 'NA'
  };
  
  const installationDetails = {
    technicalDetails: {
      ...(redisData.technicalDetails || {}),
      ...formattedData
    },
    billOfMaterial: redisData.billOfMaterial || {},
    checklist: redisData.checklist || {},
    uploadDocuments: redisData.uploadDocuments || [],
    mapped_lmc_connection_stage_id: detailsData.mapped_lmc_connection_stage_id,
    mapped_lmc_customer_connection_id: detailsData.mapped_lmc_customer_connection_id,
  };
  

  return installationDetails;
};

const getPipelineChecklist = async (req) => {
  try {
    // Call the repository to fetch the data
    const checklistData = await lmcRepository.getPipelineChecklist();

    if (!checklistData) {
      return {
        success: false,
        message: "No pipeline checklist data found",
      };
    }


    return {
      success: true,
      message: "Pipeline checklist details fetched successfully",
      data: checklistData,
    };

  } catch (error) {
    console.error("Service error in getPipelineChecklistDetailsService:", error);
    return {
      success: false,
      message: "Error processing the request",
      error: error.message,
    };
  }
};
const getInstallationFormData = async (mapped_lmc_connection_stage_id) => {
  try {
    // Fetch stage details
    const stage = await lmcRepository.getStageDetails(mapped_lmc_connection_stage_id);
    if (!stage) {
      throw new Error("Mapped LMC Connection Stage ID not found.");
    }
    const customer_connection_id = stage["lmc_connection_mapping.customer_connection_id"];

    // Fetch installation and documents
    const installationDetails = await lmcRepository.getInstallationDetails(mapped_lmc_connection_stage_id);
    const uploadedDocuments = await lmcRepository.getUploadedDocuments(mapped_lmc_connection_stage_id);

    // Fetch address from customer connection


    const meterDetails = await lmcRepository.getMeterDetails(customer_connection_id);

    // Fetch house type from latest feasibility/commissioning details
    const latestCommissioning = await lmcRepository.getLatestFeasibilityCommissioning(mapped_lmc_connection_stage_id);

    const customerAddress = await lmcRepository.getCustomerAddressDetails(customer_connection_id);

    // Build response
    return {
      technicalDetails: {
        actual_visit_date: stage.actual_visit_date,
        regulator_number: installationDetails?.regulator_no || 'NA',
        installation_date: installationDetails?.installation_date || 'NA',
        testing_date: installationDetails?.testing_date || 'NA',
        rfc_date: installationDetails?.rfc_date || 'NA',
        house_type_floor: installationDetails?.house_type || 'NA',
        house_type: latestCommissioning?.house_type || installationDetails?.house_type || 'NA',
        mdpe_pipeline_length: latestCommissioning?.mdpe_pipeline_length || installationDetails?.mdpe_pipeline_length || 'NA',
        riser_length: latestCommissioning?.riser_length || installationDetails?.riser_length || 'NA',
        gi_pipeline_length: latestCommissioning?.gi_pipeline_length || installationDetails?.gi_pipeline_length || 'NA',
        meter_no: meterDetails?.meter_no || 'NA',
        area: customerAddress?.connection_address_pngarea?.pincodeAreasAlias?.area?.area_name || null,
        city: customerAddress?.connection_city || 'NA',
        house_no: customerAddress?.connection_house_no || customerAddress?.connection_floor_no || 'NA',
        house_address: customerAddress?.connection_address || 'NA'
      },
      billOfMaterial: {
        gi_length: installationDetails?.gi_length || 0,
        gi_length_tapping: installationDetails?.gi_length_for_tapping || 0,
        copper_length: installationDetails?.copper_length || 0,
        appliance_valve_half_inch: installationDetails?.appliance_valve || 0,
        isolation_ball_valve: installationDetails?.isolation_ball_valve || 0,
        brass_fitting_set: installationDetails?.brass_fitting_set || 0,
        gi_plug_half_inch: installationDetails?.gi_plug || 0,
        half_inch_2_inch_nipple: installationDetails?.half_two_nipple || 0,
        half_inch_3_inch_nipple: installationDetails?.half_three_nipple || 0,
        half_inch_4_inch_nipple: installationDetails?.half_four_nipple || 0,
        elbow: installationDetails?.elbow || 0,
        equal_tee: installationDetails?.equal_tee || 0,
        socket: installationDetails?.socket || 0,
        union: installationDetails?.union_value || 0,
        anaconda: installationDetails?.anaconda || 0,
        elbow_mf: installationDetails?.elbow_mf || 0,
      },
      checklist: installationDetails?.checklist || {},
      uploadDocuments: uploadedDocuments.map(doc => ({
        mapped_lmc_connection_stage_id: doc.mapped_lmc_connection_stage_id,
        file_name: doc.file_name,
        file_path: doc.file_path,
        file_ext: doc.file_ext,
        verification_status: doc.verification_status,
        file_status: doc.file_status,
        file_type: doc.file_type,
      })),
      mapped_lmc_connection_stage_id,
    };
  } catch (error) {
    console.error("Service error in getInstallationFormData:", error);
    throw new Error(`Error fetching installation form data: ${error.message}`);
  }
};
const getInstallationPrefill = async (customer_connection_id) => {
  try {
    // Fetch the full address record with associations
    const customerAddress = await lmcRepository.getCustomerAddressDetails(customer_connection_id);

    if (!customerAddress) {
      return {
        success: false,
        message: "No address data found",
      };
    }

    // Fetch feasibility details with stage and document info
    const feasibilityData = await lmcRepository.getFeasibilityDeatailsforInstallation(customer_connection_id);

    if (!feasibilityData) {
      return {
        success: false,
        message: "No feasibility data found",
      };
    }

    const mappedStage = feasibilityData?.mapped_lmc_connection_stage || {};

    const formattedData = {
      area: customerAddress?.connection_address_pngarea?.pincodeAreasAlias?.area?.area_name || null,
      house_type: feasibilityData.house_type || 'NA',
      city: customerAddress.connection_city || 'NA',
      house_no: customerAddress.connection_house_no || customerAddress.connection_floor_no || 'NA',
      house_address: customerAddress.connection_address || 'NA',
      visit_status: mappedStage.visit_status || 'NA',
      actual_visit_date: mappedStage.actual_visit_date ? formatDate(mappedStage.actual_visit_date) : 'NA',
      visit_datetime: mappedStage.visit_datetime ? formatDate(mappedStage.visit_datetime) : 'NA',
      date_of_creation: feasibilityData.createdAt ? formatDate(feasibilityData.createdAt) : 'NA',
      mdpe_pipeline_length: Number(feasibilityData.mdpe_pipeline_length || 0),
      riser_length: Number(feasibilityData.riser_length || 0),
      gi_pipeline_length: Number(feasibilityData.gi_pipeline_length || 0),
      meter_type: feasibilityData.meter_type || 'NA',
      documents: Array.isArray(mappedStage.trx_lmc_pipeline_documents)
        ? mappedStage.trx_lmc_pipeline_documents.map((doc) => ({
            file_name: doc.file_name,
            file_path: doc.file_path,
            file_type: doc.file_type,
          }))
        : [],
    };

    return {
      success: true,
      data: formattedData,
    };

  } catch (error) {
    console.error("Service error in getCustomerAddressDetails:", error);
    return {
      success: false,
      message: "Error processing the request",
      error: error.message,
    };
  }
};




module.exports = {
  getLmcConnectionDetails,
  getLmcConnectionPaymentDetails,
  getLmcCasesSummery,
  getLmcCases,
  getLmcTickets,
  saveLmcScheduleVisit,
  uploadPipelineDrawing,
  submitPipelineDrawing,
  getLmcInstallationTickets,
  getLmcCommissioningTickets,
  submitLmcJMR,
  getLmcJMRSubmissionDetails,
  processLmcMeterReadingImage,
  getFormDetails,
  submitFeasibilityFrom,
  getCommissioningFormDetails,
  submitCommissioningForm,
  getFeasibilityFormDetails,
  saveInstallationDetailsToCache,
  InstallationDocumentsUpload,
  saveInstallationFromRedis,
  getInstallationDetailsFromCache,
  getPipelineChecklist,
  getInstallationFormData,
  getLmcStageDetails,
  getInstallationPrefill
};
