const { dbModels } = require("../database/connection");

// const checkPincodeFromDB = async (pincode) => {
//   // Placeholder function for future database operations
//   try {
//     const locations = await dbModels.mst_available_pngareas.findAll({
//       where: { pincode },
//       attributes: {
//         exclude: ["created_by", "updated_by"],
//       }, // Exclude unnecessary fields
//       include: [
//         {
//           model: dbModels.mst_pincodes,
//           as: "pincodeAreasAlias",
//           attributes: ["pincode"],
//           include: [
//             {
//               model: dbModels.mst_areas,
//               as: "area",
//               attributes: ["area_name"],
//               include: [
//                 {
//                   model: dbModels.mst_geographical_areas,
//                   as: "geographical_area",
//                   attributes: ["ga_area_name"],
//                   include: [
//                     {
//                       model: dbModels.mst_cities,
//                       as: "city",
//                       attributes: ["city_title"],
//                     },
//                     {
//                       model: dbModels.mst_states,
//                       as: "state",
//                       attributes: ["state_title"],
//                     },
//                   ],
//                 },
//               ],
//             },
//           ],
//         },
//       ],
//       raw: true,
//       nest: true,
//     });

//     console.log(locations);

//     return locations.length > 0 ? locations : null;
//   } catch (error) {
//     console.error("Error fetching pincode data:", error);
//     throw error;
//   }
// };


const checkPincodeFromDB = async (pincode) => {
  try {
    const whereClause = {};

    // Case 2: Add filter only if pincode is provided
    if (pincode !== null && pincode !== undefined && pincode !== "") {
      whereClause.pincode = pincode;
    }

    const locations = await dbModels.mst_available_pngareas.findAll({
      where: whereClause, // dynamic where clause
      attributes: {
        exclude: ["created_by", "updated_by"],
      },
      include: [
        {
          model: dbModels.mst_pincodes,
          as: "pincodeAreasAlias",
          attributes: ["pincode"],
          include: [
            {
              model: dbModels.mst_areas,
              as: "area",
              attributes: ["area_name"],
              include: [
                {
                  model: dbModels.mst_geographical_areas,
                  as: "geographical_area",
                  attributes: ["ga_area_name"],
                  include: [
                    {
                      model: dbModels.mst_cities,
                      as: "city",
                      attributes: ["city_title"],
                    },
                    {
                      model: dbModels.mst_states,
                      as: "state",
                      attributes: ["state_title"],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      raw: true,
      nest: true,
    });

    console.log(locations);
    return locations.length > 0 ? locations : null;
  } catch (error) {
    console.error("Error fetching pincode data:", error);
    throw error;
  }
};


module.exports = {
  checkPincodeFromDB,
};
