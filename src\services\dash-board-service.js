const dashBoardRepository = require('../repository/dash-board-repository');
const SASTokenHelper = require("../helpers/azureSASSDKHelper");

const getDateRange = (duration) => {
  let startDate;
  const endDate = new Date();

  switch (duration) {
    case "6_months":
      startDate = new Date();
      startDate.setMonth(endDate.getMonth() - 6);
      break;
    case "1_year":
      startDate = new Date();
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    case "2_year":
      startDate = new Date();
      startDate.setFullYear(endDate.getFullYear() - 2);
      break;
    case "3_year":
      startDate = new Date();
      startDate.setFullYear(endDate.getFullYear() - 3);
      break;
    default:
      throw new Error("Invalid duration. Use '6_months', '1_year', '2_year', or '3_year'.");
  }

  // Convert to ISO string and slice to keep only the date part
  return {
    startDate: startDate.toISOString().split("T")[0],
    endDate: endDate.toISOString().split("T")[0],
  };
};
const dashBoarGraphdDetailsService = async (payload) => {
  const { customer_connection_id, duration } = payload;
  if (!duration) {
      throw new Error("Duration is required.");
     }
  const { startDate, endDate } = getDateRange(duration);
  const result = await dashBoardRepository.dashBoarGraphdDetails(customer_connection_id, startDate, endDate);
  if (result.length === 0 ) {
    return "no data found"
   }

  const labels = result.map(item => {
    const date = new Date(item.cycle.start_date);
    return date.toLocaleString('en-US', { year: 'numeric', month: 'short' }); // e.g., "2025 Mar"
  });

  const transformedResult = result.map(item => item.reading_units);
  const average = transformedResult.reduce((sum, val) => sum + val, 0) / transformedResult.length;
  const highUsage = transformedResult.map(val => (val >= average ? val : 0));
  const averageUsage = transformedResult.map(val => (val < average ? val : 0));
  const highestUsage = Math.max(...transformedResult);
  const respo ={ 
    "labels":labels,
    "averageUsage":average,
    "timePeriod":`${labels[0]} to ${labels[labels.length - 1]}`,
    "highestUsage":highestUsage,
    datasets: [
      {
        label: "Average Usage",
        data: averageUsage, // Blue bars
      },
      {
        label: "High Usage",
        data: highUsage, // Yellow bar in November
      },
    ],
  
  }
  
  return respo ;
};

const notificationDetails =async(payload) =>{
  const result =await dashBoardRepository.fetchNotification(payload)
  return result
};

const bannerImages =async(payload) =>{
  let result =await dashBoardRepository.fetchBannerImages(payload)
  for (let i=0; i<result.length; i++){ 
    const permission = 'racwd';
    const sasToken = await SASTokenHelper.generateSASToken(permission);
    const mediaPath =result[i].banner_file_path+'/'+result[i].banner_file_name;
    const imageUploaded = await SASTokenHelper.getAzureSingleFile(payload, sasToken, mediaPath);
    
    result[i].base64= imageUploaded;
  }
  return result
};

const ViewNotificationDetails = async(payload) =>{
  console.log("payload =", payload);
  const whereCondition = {
    name_transfer_request_id : payload.alert_request_id
  }
  const result =await dashBoardRepository.viewNotification(whereCondition);
  console.log("result=", result);

  const modifiedResult = {
    name_transfer_request_id: result.name_transfer_request_id,
    name_transfer_status: result.name_transfer_status,
  
    requestdBy: {
      ...result.existing_connection,
      trx_customer_connection_addresses: {
        ...result.existing_connection.trx_customer_connection_addresses
      }
    },
  
    requestdFor: {
      customer_connection_id: result.new_connection.customer_connection_id,
      bpcl_id: result.new_connection.bpcl_id,
      available_pngareas_id: result.new_connection.available_pngareas_id,
      connection_house_no: result.new_connection.trx_customer_connection_addresses.connection_house_no
    }
  };
  
  return modifiedResult
};

 
module.exports = {
  dashBoarGraphdDetailsService,
  notificationDetails,
  bannerImages,
  ViewNotificationDetails
};
