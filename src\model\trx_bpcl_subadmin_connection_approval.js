const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_bpcl_subadmin_connection_approval",
    {
      bsca_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
        unique:
          "UK_trx_bsca_cci_mapping_id_mapping_type_bpclsubadmin_bsa_status_seq_no",
      },
      mapping_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique:
          "UK_trx_bsca_cci_mapping_id_mapping_type_bpclsubadmin_bsa_status_seq_no",
      },
      mapping_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique:
          "UK_trx_bsca_cci_mapping_id_mapping_type_bpclsubadmin_bsa_status_seq_no",
      },
      bpcl_subadmin_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique:
          "UK_trx_bsca_cci_mapping_id_mapping_type_bpclsubadmin_bsa_status_seq_no",
      },
      bpcl_subadmin_status: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: "pending",
        unique:
          "UK_trx_bsca_cci_mapping_id_mapping_type_bpclsubadmin_bsa_status_seq_no",
      },
      remarks: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_latest: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      sequence_no: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
        unique:
          "UK_trx_bsca_cci_mapping_id_mapping_type_bpclsubadmin_bsa_status_seq_no",
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_bpcl_subadmin_connection_approval",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_bsca_id",
          unique: true,
          fields: [{ name: "bsca_id" }],
        },
        {
          name: "UK_trx_bsca_cci_mapping_id_mapping_type_bpclsubadmin_bsa_status_seq_no",
          unique: true,
          fields: [
            { name: "mapping_id" },
            { name: "mapping_type" },
            { name: "bpcl_subadmin_id" },
            { name: "bpcl_subadmin_status" },
            { name: "sequence_no" },
          ],
        },
      ],
    }
  );
};
