const customerProfileRepository = require("../repository/customer-profile-repository");
const redisHelper = require('../helpers/redisHelper');
const otpHelper = require("../helpers/smsEmailHelper");

const generateRedisKey =  (bp_id= "",customer_connection_id= "", email = "",number = "") => {
  try{
    if (!number && !email) throw new Error('Either number or email is required.');
    let key= number && email 
      ? `${number}_${email}` 
      : `${number ? `number_${number}` : `email_${email}`}`;
    customer_connection_id && (key = `connectionId_${customer_connection_id}_${key}`);
    bp_id && (key = `bpId_${bp_id}_${key}`);
    return key;

  }catch (error) {  
    console.error("Error in generating redis key:", error);
    throw error;
  }
};

const sendOtp = async (payload) => {
  try {
    const key = generateRedisKey(
      payload.bp_id || "",
      payload.customer_connection_id || "",
      payload.email || "",
      payload.number || ""
    );

    const otp = (Math.floor(100000 + Math.random() * 900000)).toString(); 

    payload.subject = "Email OTP Verification";
    payload.otp = otp;
    payload.template = `OTP for your request ${otp}`;

    await redisHelper.setData(key, otp, 600);

    let otpResult = "";
    let otpResultSms = "";

    if (payload.email) {
      otpResult = await otpHelper.emailOTPNotification(
        payload.email,
        payload.subject,
        payload.template,
      );
    }

    if (payload.number) {
      otpResultSms = await otpHelper.smsOTPNotification(payload.number, otp);
    }

    console.log("OTP Response:", otpResult, otpResultSms);

    return {
      message: "OTP generated and sent successfully",
      otpResponse: otpResult,
    };
  } catch (error) {
    console.error("Error in sendOtp:", error);
    throw new Error("Failed to generate or send OTP");
  }
};

const otpVerifyEmailAndSms = async (payload) => {

  try {
    //console.log("payload````", payload);

    //const key = generateKey(payload.email, payload.number);
    let key = generateRedisKey(payload.bp_id?payload.bp_id:"",payload.customer_connection_id?payload.customer_connection_id:"",payload.email?payload.email:"", payload.number?payload.number:"");
    const result = await redisHelper.getData(key);
    if (!result){
      throw new Error("OTP expired or not found");
    } 
    if (result.toString() === payload.OTP) {
      await redisHelper.deleteData(key);
      return "otp Verified";
    }
    throw new Error("Not Verified");
  } catch (error) {
    throw (error); 
  }

};

module.exports = {
  sendOtp,
  otpVerifyEmailAndSms,
};
