const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_lmc_pipeline', {
    lmc_pipeline_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    mapped_lmc_customer_connection_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_mapped_lmc_customer_connection',
        key: 'mapped_lmc_customer_connection_id'
      }
    },
    house_type: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    pipeline_size: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    extra_charge: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    customer_consent_request: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    customer_consent: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    extra_charge_approval: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    pipeline_status: {
      type: DataTypes.STRING(10),
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_lmc_pipeline',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_lmc_pipeline_id",
        unique: true,
        fields: [
          { name: "lmc_pipeline_id" },
        ]
      },
    ]
  });
};
