const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_customer_name_transfer_request', {
    name_transfer_request_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    existing_connection_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer_connection',
        key: 'customer_connection_id'
      }
    },
    new_connection_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer_connection',
        key: 'customer_connection_id'
      }
    },
    name_transfer_reason: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_masters',
        key: 'master_id'
      }
    },
    prefix: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    first_name: {
      type: DataTypes.STRING(25),
      allowNull: false
    },
    middle_name: {
      type: DataTypes.STRING(25),
      allowNull: true
    },
    last_name: {
      type: DataTypes.STRING(25),
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    mobile: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    alternate_mobile: {
      type: DataTypes.STRING(10),
      allowNull: true
    },
    name_transfer_status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: "pending"
    },
    approved_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    isu_synced: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_customer_name_transfer_request',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_name_transfer_request_id",
        unique: true,
        fields: [
          { name: "name_transfer_request_id" },
        ]
      },
    ]
  });
};
