const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_customer_connection_tpi_rework",
    {
      customer_connection_tpi_rework_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_tpi_mapping_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection_tpi_mapping",
          key: "customer_connection_tpi_mapping_id",
        },
        unique: "UK_trx_cctr_customer_connection_tpi_mapping_id_section",
      },
      section: {
        type: DataTypes.STRING(20),
        allowNull: true,
        unique: "UK_trx_cctr_customer_connection_tpi_mapping_id_section",
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },

      is_latest: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_customer_connection_tpi_rework",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_customer_connection_tpi_rework",
          unique: true,
          fields: [{ name: "customer_connection_tpi_rework_id" }],
        },
        {
          name: "UK_trx_cctr_customer_connection_tpi_mapping_id_section",
          unique: true,
          fields: [
            { name: "customer_connection_tpi_mapping_id" },
            { name: "section" },
          ],
        },
      ],
    }
  );
};
