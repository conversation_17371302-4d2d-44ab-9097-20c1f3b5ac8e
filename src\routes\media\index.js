const { validate } = require('express-validation');
const express = require("express");
const mediaRoute = express.Router();
const mediaController = require("../../controllers/media-controller");
const { uploadMultiple, uploadSingle } = require('../../middleware/newMulter');
const {
  getMedia,
} = require('../../validation/media-validation');

mediaRoute.post(
  "/upload",
  uploadSingle,
  mediaController.mediaUpload
);

mediaRoute.post(
  "/image",
  validate(getMedia),
  mediaController.getAzureImage
);

mediaRoute.post(
  "/download",
  validate(getMedia),
  mediaController.getAzureDownload
);

mediaRoute.post(
  "/delete",
  validate(getMedia),
  mediaController.deleteAzure
);


mediaRoute.post(
  "/ocr-upload",
  uploadSingle,
  mediaController.OCRUpload
);

mediaRoute.post(
  "/image-reading",
  uploadSingle,
  mediaController.imageRead
);


module.exports = { mediaRoute };
