const proofOfOwnershipRepository = require("../repository/proof-of-ownership-repository");
const message = require("../utils/constants");
const { dbModels } = require('../database/connection');

const proofOfOwnershipDto = (reqBody) => {
    console.log(reqBody);
    
    return {
        id: reqBody.proof_of_ownership_id,
        ownership_name: reqBody.ownership_name,
        is_active: reqBody.is_active,
    };
};

const getProofOfOwnershipList = async (reqBody) => {
  const page = reqBody.page || 1; // Default to page 1 if not provided
  const limit = reqBody.limit || 10; // Default limit to 10 if not provided

  const id = reqBody.proof_of_ownership_id; // Check if a specific ID is requested
  if (id) {
      return proofOfOwnershipRepository.showProofOfOwnershipList(id);
  }

  return proofOfOwnershipRepository.getproofOfOwnershipList(page, limit);
};



const createProofOfOwnershipList = async (reqBody) => {
    
    const already = await checkExistingname(reqBody.ownership_name);
    
  if (already) {
    return {
      data: {
        is_exist: true,
      },
      message: message.NAME_ALREADY_EXIST,
    };
  }

    if (!reqBody) {
        throw new Error("No proofofownership provided");
    }
    const modProofOfOwnership = {
        ...proofOfOwnershipDto(reqBody),
                 
    };

    try {
        // Using Sequelize to insert a single record into the database
        const createdScheme = await proofOfOwnershipRepository.createProofOfOwnershipList(modProofOfOwnership); 
        return createdScheme;
        
    } catch (error) {
        throw new Error(error.message);
    }
};


const updateProofOfOwnershipList = async (reqBody) => {
    const updateObject = proofOfOwnershipDto(reqBody);
    updateObject.proofOfOwnership_id = reqBody.proof_of_ownership_id;
   
    
    if (!updateObject.proofOfOwnership_id) {
        return "ID not found";
    }
    
    return proofOfOwnershipRepository.updateProofOfOwnershipList(
        updateObject.proofOfOwnership_id,
        updateObject
    );
};

const checkExistingname = async (reqBody) => {
    
    try {
      const response = await proofOfOwnershipRepository.checkExistingname(reqBody);
      return response;
    } catch (err) {
      console.log(err);
      throw err;
    }
  };

const deleteProofOfOwnershipList = async (reqBody) => {
    const proofOfOwnershipId = reqBody.proof_of_ownership_id;
  
    return proofOfOwnershipRepository.deleteProofOfOwnershipList(proofOfOwnershipId, {
        is_delete: 0,
    });
  };
module.exports = {
    getProofOfOwnershipList,
    createProofOfOwnershipList,
    updateProofOfOwnershipList,
    deleteProofOfOwnershipList,
    checkExistingname,
};
