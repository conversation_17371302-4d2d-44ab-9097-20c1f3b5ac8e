const { apiGet } = require("../helpers/axiosHelper");
const { sendSuccess, sendError } = require("../helpers/responseHelper");
const masterService = require("../services/master-service");

exports.issueTypeList = async (req, res) => {
  try {
    console.log("req.body", req.body);

    const result = await masterService.listIssueType(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getGovtSchemeList = async (req, res) => {
  console.log(req.query);

  // try {
  const { page, limit } = req.query;
  const validatedPage = page && page > 0 ? parseInt(page, 10) : 1;
  const validatedLimit = limit && limit > 0 ? parseInt(limit, 10) : 10;

  const data = await masterService.getGovtSchemeList({
    page: validatedPage,
    limit: validatedLimit,
  });
  console.log("1", data);

  sendSuccess(req, res, data, "Data fetched successfully");
  // } catch (error) {
  //     sendError(req, res, error, "Bad Request");
  // }
};
exports.sampleIsu = async (req, res) => {
  try {
    const url =
      "http://dev.sap.png.hellobpcl.in/sap/public/ping?sap-client=111";
    const result = await apiGet(url);
    sendSuccess(req, res, result, "Data fetched successfully");
  } catch (error) {
    sendError(req, res, error, error.toString());
  }
};

exports.getProofMasterList = async (req, res) => {
  try {
    const { page, limit, proof_type } = req.query;

    // Ensure page and limit are valid numbers
    const validatedPage = page && page > 0 ? parseInt(page, 10) : 1;
    const validatedLimit = limit && limit > 0 ? parseInt(limit, 10) : 10;

    // Validate proof_type
    if (
      !proof_type ||
      typeof proof_type !== "string" ||
      proof_type.trim() === ""
    ) {
      return sendError(
        req,
        res,
        { message: "Proof type is required and must be a valid string" },
        "Bad Request",
        400
      );
    }

    // Call service layer with proof_type
    const data = await masterService.getProofMasterList({
      page: validatedPage,
      limit: validatedLimit,
      proof_type: proof_type.trim(),
      // Trim to avoid unwanted spaces
    });

    sendSuccess(req, res, data, "Data fetched successfully");
  } catch (error) {
    sendError(req, res, error, "Bad Request");
  }
};

exports.getMasterList = async (req, res) => {
  try {
    const data = await masterService.getMasterList(req.body);
    console.log("data", data);
    //const data =  "Data fetched successfully"
    sendSuccess(req, res, data, "Data fetched successfully");
  } catch (error) {
    sendError(req, res, error, "Bad Request");
  }
};
exports.getServiceRate = async (req, res) => {
  try {
    const data = await masterService.getServiceRatesList(req.body);
    //const data =  "Data fetched successfully"
    sendSuccess(req, res, data, "Data fetched successfully");
  } catch (error) {
    sendError(req, res, error, "Bad Request");
  }
};
