const tpiRepository = require("../repository/ga-customer-repository");
const message = require("../utils/constants");
const { emailOTPNotification } = require('../helpers/smsEmailHelper');
const { decryptPassword } = require('../utils/encrypt-decrypt');


const ongoingList = async (params) => {
  try {
    const result = await tpiRepository.ongoingList(params);
    return result;
  } catch (error) {
    console.error("Error in ongoingList:", error);
    throw error;
  }
};

const completedList = async (params) => {
  try {
    const result = await tpiRepository.completedList(params);
    return result;
  } catch (error) {
    console.error("Error in completedList:", error);
    throw error;
  }
};

const ongoingSummaryCounts = async (params) => {
  try {
    const result = await tpiRepository.ongoingSummaryCounts(params);
    return result;
  } catch (error) {
    console.error("Error in ongoingList:", error);
    throw error;
  }
};

const completedSummaryCounts = async (params) => {
  try {
    const result = await tpiRepository.completedSummaryCounts(params);
    return result;
  } catch (error) {
    console.error("Error in completedList:", error);
    throw error;
  }
};

const areaWiseList = async (params) => {
  try {
    if(params.tab_type == "registaration"){
      return await tpiRepository.getCustomersAreaWiseforRegistaration(params);
    }else{
      return await tpiRepository.getCustomersAreaWiseForActive(params);
    }
    
  } catch (error) {
    console.error("Error in areaWiseList:", error);
    throw error;
  }
};

const areaCountData = async (params) => {
  try {
    if(params.tab_type == "registaration"){
      return await tpiRepository.getCustomerStageCountsForRegistration(params);
    }else{
      return await tpiRepository.getCustomerStageCountsForActive(params);
    }
    
  } catch (error) {
    console.error("Error in areaCountData:", error);
    throw error;
  }
};

const contractorAreaList = async (params) => {
  try {
    const result = await tpiRepository.contractorAreaList(params);
    return result;
  } catch (error) {
    console.error("Error in completedList:", error);
    throw error;
  }
};

const lmcAreaList = async (params) => {
  try {
    const result = await tpiRepository.lmcAreaList(params);
    return result;
  } catch (error) {
    console.error("Error in completedList:", error);
    throw error;
  }
};

const dmaAreaList = async (params) => {
  try {
    const result = await tpiRepository.dmaAreaList(params);
    return result;
  } catch (error) {
    console.error("Error in completedList:", error);
    throw error;
  }
};

const foAreaList = async (params) => {
  try {
    const result = await tpiRepository.foAreaList(params);
    return result;
  } catch (error) {
    console.error("Error in completedList:", error);
    throw error;
  }
};

module.exports = {
  
  ongoingList,
  completedList,
  ongoingSummaryCounts,
  completedSummaryCounts,
  areaWiseList,
  areaCountData,
  contractorAreaList,
  lmcAreaList,
  dmaAreaList,
  foAreaList
};
