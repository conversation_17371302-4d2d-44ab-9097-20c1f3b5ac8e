const { Joi } = require("express-validation");

const getAreaValidation = {
  body: Joi.object({
    ga_id: Joi.number().integer().required(),
  }),
};

const contractorOnboardingValidation = {
  body: Joi.object({
    ga_id: Joi.number().integer().required(),

    contractor_type: Joi.string().max(50).required(),

    mobile: Joi.string()
      .pattern(/^[6-9]\d{9}$/)
      .required()
      .messages({
        "string.pattern.base":
          "Mobile number must be a valid 10-digit Indian number",
      }),

    alt_mobile: Joi.string()
      .pattern(/^[6-9]\d{9}$/)
      .allow(null, "")
      .messages({
        "string.pattern.base":
          "Alternate mobile number must be a valid 10-digit Indian number",
      }),

    email: Joi.string().email().required(),

    first_name: Joi.string().min(1).max(50).required(),
    middle_name: Joi.string().allow("", null),
    last_name: Joi.string().min(1).max(50).required(),

    agency: Joi.string().min(1).required(),

    area_id: Joi.alternatives()
      .try(
        Joi.number().integer(),
        Joi.array().items(Joi.number().integer()).min(1)
      )
      .required(),

    document_type: Joi.string().max(50).required(),
  }).prefs({ convert: true }), // 👈 this coerces string '1' to number 1
};

const contractorApprovalValidation = {
  body: Joi.object({
    ga_id: Joi.number().integer().positive().required().messages({
      "number.base": "GA ID must be a number",
      "number.integer": "GA ID must be an integer",
      "number.positive": "GA ID must be a positive number",
      "any.required": "GA ID is required",
    }),
    type: Joi.string()
      .valid("pending", "approved", "rejected")
      .required()
      .messages({
        "string.base": "Type must be a string",
        "any.only": 'Type must be either "pending", "approved", or "rejected"',
        "any.required": "Type is required",
      }),
  }),
};
const contractorAcceptRejectValidation = {
  body: Joi.object({
    ga_id: Joi.number().integer().required(),
    contractor_id: Joi.number().integer().required(),
    type: Joi.string().valid("accept", "reject").required(),
    remark: Joi.string().max(200).optional(),
  }),
};
const subAdminOnboardingValidation = {
  body: Joi.object({
    ga_id: Joi.number().integer().required(),
    sub_admin_type: Joi.number().integer().required(),
    title: Joi.string().valid("mr", "mrs", "ms", "other").required(),
    emp_name: Joi.string().required(),
    bpcl_id: Joi.string().required(),
    position_id: Joi.string().required(),
    mobile: Joi.string()
      .pattern(/^[0-9]{10}$/)
      .required(),
    alt_mobile: Joi.string()
      .pattern(/^[0-9]{10}$/)
      .allow(null, "")
      .optional(),
    email: Joi.string().email().required(),
    period_from: Joi.date().iso().required(),
    period_to: Joi.date().iso().required(),
  }),
};
const tpiSalesAreaWiseValidation = {
  body: Joi.object({
    ga_area_id: Joi.number().integer().required(),
    page: Joi.number().integer().allow("", null),
    limit: Joi.number().integer().allow("", null),
    area_id: Joi.array().items(Joi.number()).allow("", null),
    search: Joi.string().allow("", null),
  }),
};

const detailsByPersonaRoleValidation = {
  body: Joi.object({
    persona_role: Joi.string().valid("dma", "fo", "lmc").required(),
    geographical_area_id: Joi.number().integer().required(),
  }),
};
const customerApprovalListValidation = {
  body: Joi.object({
    geographical_area_id: Joi.number().integer().required(),
    status: Joi.string().valid("pending", "resubmission", "closed").required(),
  }),
};

const customerPersonalDetailsValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
  }),
};
const customerLmcInstallationDetailsValidation = {
  body: Joi.object({
    mapped_lmc_connection_stage_id: Joi.number().integer().required(),
  }),
};
const customerManualGmrDetailsValidation = {
  body: Joi.object({
    tmma_id: Joi.number().integer().required(),
  }),
};

const soStatusUpdateValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    reason: Joi.string().required(),
    action: Joi.string()
      .valid('approved', 'reject', 'resubmission')
      .required(),
  })
};

const eoStatusUpdateValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    action: Joi.string()
      .valid('approved', 'reject', 'resubmission')
      .required(),
    reason: Joi.when('action', {
      is: Joi.valid('reject', 'resubmission'),
      then: Joi.string().trim().required(),
      otherwise: Joi.string().allow(null, '').optional()
    }),
  })
};

const eoJmrStatusUpdateValidation = {
  body: Joi.object({
    tmma_id: Joi.number().integer().required(),
    reason: Joi.string().required(),
    action: Joi.string()
      .valid('approved', 'reject', 'resubmission')
      .required(),
  })
};

const soCommentDetailsValidation = {  
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
  }),
};

const eoCommentDetailsValidation = {  
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
  }),
};

const eoJmrCommentDetailsValidation = {  
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
  }),
};

const commentDetailsValidation = {  
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    mapping_type: Joi.string().required(),
  }),
};

module.exports = {
  contractorOnboardingValidation,
  getAreaValidation,
  contractorApprovalValidation,
  contractorAcceptRejectValidation,
  subAdminOnboardingValidation,
  detailsByPersonaRoleValidation,
  tpiSalesAreaWiseValidation,
  soStatusUpdateValidation,
  eoStatusUpdateValidation,
  eoJmrStatusUpdateValidation,
  customerApprovalListValidation,
  customerPersonalDetailsValidation,
  customerLmcInstallationDetailsValidation,
  customerManualGmrDetailsValidation,
  soCommentDetailsValidation,
  eoCommentDetailsValidation,
  eoJmrCommentDetailsValidation,
  commentDetailsValidation
};
