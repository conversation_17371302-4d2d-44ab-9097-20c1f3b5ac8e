const { sendSuccess, sendError } = require('../helpers/responseHelper');
const customerProfileService = require('../services/customer-profile-service');

exports.addUpdateEmail = async (req, res) => {
  try {
    // Call service layer with req.body and req.files
    const result = await customerProfileService.addUpdateEmailService(req.body);

    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.addUpdateNumber = async (req, res) => {
  try {
    
    // Call service layer with req.body
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await customerProfileService.numberAddUpdate(requestBody);
    sendSuccess(req, res, result, 'Result Fetched successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.addUpdateAltNumber = async (req, res) => {
  try {
  
    // Call service layer with req.body and req.files
    const result = await customerProfileService.addUpdateAltNumberService(
      req.body
    );

    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


