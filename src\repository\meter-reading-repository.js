const { dbModels } = require("../database/connection");
const redisHelper = require("../helpers/redisHelper");
const Sequelize = require("sequelize");
const { formatMeterReading } = require("../utils/commonFn");

const getMeterReadingHistoryList = async (payload) => {
  try {
    const whereCondition = {
      customer_connection_id: payload.customer_connection_id,
    };

    // Always return only last 1 year of records
    const endDate = new Date();
    const startDate = new Date();
    startDate.setFullYear(endDate.getFullYear() - 1);

    whereCondition.created_at = {
      [Sequelize.Op.between]: [startDate, endDate],
    };

    const { count, rows } = await dbModels.trx_customer_meter_readings.findAndCountAll({
      attributes: [
        "customer_meter_reading_id",
        "customer_connection_id",
        "previous_reading",
        "meter_reading_status",
        "declared_by",
        "created_at",
        "customer_meter_id",
        "meter_reading_file",
      ],
      include: [
        {
          model: dbModels.trx_customer_meter,
          as: "customer_meter",
          attributes: ["meter_type", "meter_no", "meter_status"],
        },
        {
          model: dbModels.trx_customer_connection,
          as: "customer_connection",
          attributes: ["first_name", "last_name", "mobile", "bp_id"],
          include: [
            {
              model: dbModels.trx_customer_connection_address,
              as: "trx_customer_connection_addresses",
              attributes: ["connection_address"],
            },
          ],
        },
        {
          model: dbModels.mst_admin,
          as: "submitted_admin",
          attributes: ["first_name", "last_name"],
        },
        {
          model: dbModels.mst_cycle,
          as: "cycle",
          attributes: ["start_date", "end_date"],
        },
      ],
      where: whereCondition,
      order: [["created_at", "DESC"]],
      logging: true,
      raw: true,
    });

    if (count === 0) {
      return {
        success: false,
        statusCode: 404,
        message: "No meter reading history found in the last 1 year.",
        totalRecords: count,
        records: [],
      };
    }

    // Format records
    const formattedRecords = rows.map((record) => {
      let formattedDate = "Invalid Date";
      let isJMR = false;
      if (record.created_at) {
        const createdAt = new Date(record.created_at);
        if (!isNaN(createdAt.getTime())) {
          formattedDate = `${createdAt.getDate()}${getOrdinalSuffix(createdAt.getDate())} ${createdAt.toLocaleString("default", { month: "short" })}, ${createdAt.getFullYear()}`;
        }
      }

      let cycleLabel =
        record["cycle.start_date"] && record["cycle.end_date"]
          ? `${new Date(record["cycle.start_date"]).toLocaleString("default", { month: "short" })} - ${new Date(record["cycle.end_date"]).toLocaleString("default", { month: "short" })} ${new Date(record["cycle.end_date"]).getFullYear()}`
          : "Joint Meter Reading (JMR)";
      if (!record["cycle.start_date"]) {
        cycleLabel = "Joint Meter Reading (JMR)";
        isJMR = true;
      }

      const imagePath = record.meter_reading_file || null;
      const imageName = imagePath ? imagePath.split("/").pop() : null;
      return {
        cycle: cycleLabel,
        is_JMR: isJMR,
        customer_meter_reading_id: record["customer_meter_reading_id"],
        meter_no: record["customer_meter.meter_no"] || "N/A",
        dateSubmitted: formattedDate,
        readingValue: `${record.previous_reading} SCM`,
        type: `Declared by ${capitalize(record.declared_by)}`,
        imagePath,
        imageName,
        imageSize: "224 KB",
        customerDetails: record["customer_connection.first_name"]
          ? {
              name: `${record["customer_connection.first_name"]} ${record["customer_connection.last_name"]}`,
              bpNumber: record["customer_connection.bp_id"] || "N/A",
              mobile: record["customer_connection.mobile"],
              address: record["customer_connection.trx_customer_connection_addresses.connection_address"] || "N/A",
            }
          : null,
        agencyDetails: record["submitted_admin.first_name"]
          ? {
              contractorName: `${record["submitted_admin.first_name"]} ${record["submitted_admin.last_name"]}`,
            }
          : null,
      };
    });

    return {
      success: true,
      statusCode: 200,
      message: "Meter reading history retrieved for last 1 year.",
      totalRecords: count,
      records: formattedRecords,
    };
  } catch (error) {
    console.error("Error fetching meter reading history:", error);
    return {
      success: false,
      statusCode: 500,
      message: "An error occurred while retrieving meter reading history.",
      error: error.message || "Unknown error",
    };
  }
};



// Helper function to get ordinal suffix (1st, 2nd, 3rd, etc.)
const getOrdinalSuffix = (day) => {
  if (day > 3 && day < 21) return "th";
  switch (day % 10) {
    case 1:
      return "st";
    case 2:
      return "nd";
    case 3:
      return "rd";
    default:
      return "th";
  }
};

// Helper function to capitalize first letter
const capitalize = (str) => str.charAt(0).toUpperCase() + str.slice(1);

const getMeterReadingDetails = async (payload) => {
  try {
    const whereCondition = {
      customer_connection_id: payload.customer_connection_id,
    };
    const currentDate = new Date();
    const initialMeterReading = await dbModels.trx_customer_meter_readings.findOne({
      attributes: [
        "customer_connection_id",
        "created_at",
        "current_reading",
      ],
      where: whereCondition,
      order: [["created_at", "ASC"]],
      raw: true,
    });
    console.log("initialMeterReading",initialMeterReading);
    if(!initialMeterReading){
      throw new Error ("No Meter Reading Found For This Connection");
    }
    const currentCycle = await dbModels.mst_cycle.findOne({
      where: {
        start_date: { [Sequelize.Op.gt]: currentDate },
      },
      order: [["start_date", "ASC"]],
      attributes: ["start_date", "end_date", "cycle_id"],
      raw: true,
    });
    console.log("currentCycle",currentCycle);
    ////////////////////////////// meterReadingDetails start //////////
    const meterReadingDetails = await dbModels.trx_customer_meter_readings.findAll({
      attributes: [
        'customer_meter_reading_id',
        'reading_units',
        'meter_reading_status',
        'declared_by',
        'created_at',
        'current_reading',
        'customer_meter_id',
        'customer_connection_id',
      ],
      where: whereCondition,
      include: [
        {
          model: dbModels.mst_cycle,
          as: 'cycle',
          attributes: ['cycle_id', 'start_date', 'end_date'],
        },
        {
          model: dbModels.trx_customer_meter,
          as: 'customer_meter',
          attributes: [
            'meter_type',
            'meter_no',
            'meter_status',
            'meter_installation_datetime',
            ['is_active', 'meter_is_active'],
          ],
        },
        {
          model: dbModels.trx_customer_connection,
          as: 'customer_connection',
          attributes: [
            'account_type',
            'connection_address_type',
            'onboarding_date',
            'is_extra_connection',
            'extra_points',
            'is_active',
            'bpcl_id',
          ],
        },
      ],
      order: [['created_at', 'DESC']],
      raw: false, // Set to true if you want plain objects instead of Sequelize instances
    });
    const lastestMeterDetails = meterReadingDetails[0];
    
    console.log("meterReadingDetails",meterReadingDetails);

    /////////////////////////////meterReadingDetails end/////////////
    const meterReadingList = await dbModels.trx_customer_meter_readings.findAll({
      attributes: [
        "customer_connection_id",
        "reading_units",
        "meter_reading_status",
        "declared_by",
        "created_at",
        "current_reading",
      ],
      where: whereCondition,
      order: [["created_at", "DESC"]],
      raw: false,
    });
    console.log("meterReadingList",meterReadingList);
    
    const latestMeterReading=meterReadingList[0];

    // Count how many times declared_by is 'customer'
    const customerDeclarations = meterReadingList.filter(r => r.declared_by === 'customer').length;
    console.log("customerDeclarations",customerDeclarations);
    // const meterDetails = await dbModels.trx_customer_meter.findOne({
    //   where: { customer_connection_id: payload.customer_connection_id },
    //   attributes: [
    //     "meter_type",
    //     "meter_no",
    //     "meter_status",
    //     "meter_installation_datetime",
    //     "is_active",
    //   ],
    //   raw: false,
    // });

    // const connectionDetails = await dbModels.trx_customer_connection.findOne({
    //   where: { customer_connection_id: payload.customer_connection_id },
    //   attributes: [
    //     "account_type",
    //     "connection_address_type",
    //     "onboarding_date",
    //     "is_extra_connection",
    //     "extra_points",
    //     "is_active",
    //   ],
    //   raw: true,
    // });

    if (!lastestMeterDetails?.customer_meter || !lastestMeterDetails?.customer_connection) {
      return {
        success: false,
        statusCode: 404,
        message:
          "Connection or meter details not found for the given criteria.",
      };
    }
    ////////////// MRO Details Start //////////
    const MRODetails= await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id: payload.customer_connection_id },
      attributes: [
        "customer_connection_id",
        "first_name",
        "last_name",
        "email",
        "mobile",
        "bp_id",
        "ca_no",
      ],
      raw: false,
    });
    /////////////  MRO Details End  /////////////
    const meterUsage = latestMeterReading?.cycle_id ? latestMeterReading?.reading_units : "-"
      ? `${latestMeterReading.reading_units} SCM`
      : "N/A";
    const meterReadingValue = latestMeterReading?.current_reading
      ? `${latestMeterReading.current_reading} SCM`
      : "N/A";
    const initialMeterReadingValue = initialMeterReading?.current_reading
      ? `${formatMeterReading(initialMeterReading.current_reading)} SCM`
      : "N/A";
    const submittedDate = latestMeterReading?.created_at
      ? new Date(latestMeterReading.created_at).toLocaleDateString("en-GB", {
        day: "numeric",
        month: "short",
        year: "numeric",
      })
      : "N/A";

    const nextCycle =
      currentCycle && currentCycle.start_date && currentCycle.end_date
        ? `${new Date(currentCycle.start_date).toLocaleDateString("en-GB", { day: "numeric", month: "short" })} - ${new Date(currentCycle.end_date).toLocaleDateString("en-GB", { day: "numeric", month: "short", year: "numeric" })}`
        : "N/A";
// const customerConnection= lastestMeterDetails.customer_connection;
console.log("lastestMeterDetails.customer_connection.customer_connection_is_active",lastestMeterDetails.customer_connection.is_active);
console.log("lastestMeterDetails.customer_connection.customer_connection_is_active",lastestMeterDetails.customer_connection.account_type);
console.log("lastestMeterDetails.customer_connection.customer_connection_is_active",lastestMeterDetails.customer_connection.is_extra_connection);

    return {
      success: true,
      statusCode: 200,
      message: "Meter and connection details retrieved successfully.",
      data: {
        meterType: lastestMeterDetails.customer_meter.meter_type || "N/A",
        startedOn: lastestMeterDetails.customer_meter.meter_installation_datetime
          ? new Date(
              lastestMeterDetails.customer_meter.meter_installation_datetime
            ).toLocaleDateString("en-GB", {
              day: "2-digit",
              month: "short",
              year: "numeric",
            })
          : "N/A",
          
        accountType: lastestMeterDetails.customer_connection.account_type || "N/A",
        meterNo: lastestMeterDetails.customer_meter.meter_no || "N/A",
        meterUsage: meterUsage,
        connection: `${(lastestMeterDetails.customer_connection.extra_points || 0) + 1} Connection`,
        connectionAddressType:
          lastestMeterDetails.customer_connection.connection_address_type || "N/A",
        status: lastestMeterDetails.customer_connection.is_active ? "Active" : "Inactive",
        meterReading: meterReadingValue,
        initialMeterReading: initialMeterReadingValue,
        submittedDate: submittedDate,
        meterStatus: lastestMeterDetails.customer_meter.meter_status || "N/A",
        nextCycle: nextCycle,
        customerDeclarationsCount: customerDeclarations,
        MRODetails: MRODetails? MRODetails : {},
      },
    };
  } catch (error) {
    console.error("Error fetching meter reading details:", error);
    throw error;
  }
};

const getLastMeterReading = async (payload) => {
  try {
    const { customer_connection_id } = payload;

    // Check if the account is active
    const customerAccount = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id, is_active: 1 },
      raw: true,
      attributes: ["is_active"],
    });

    if (!customerAccount) {
      return {
        success: false,
        statusCode: 403,
        message:
          "Activate Account to Upload Reading. Your account is currently suspended. Please submit a service request to reactivate your account and restore access to gas service and meter reading uploads",
        upload_status: "account_suspended",
      };
    }

    const currentDate = new Date();
    const latestCycle = await dbModels.mst_cycle.findOne({
      where: {
        start_date: { [Sequelize.Op.lte]: currentDate }, // Start date is before or on today
        end_date: { [Sequelize.Op.gte]: currentDate }, // End date is after or on today
      },
      attributes: ["start_date", "end_date", "cycle_id"],
      raw: true,
    });

    if (!latestCycle) {
      throw new Error("No billing cycle found.");
    }

    const { cycle_id, start_date, end_date } = latestCycle;

    // Fetch the most recent meter reading within the latest cycle's date range
    const result = await dbModels.trx_customer_meter_readings.findOne({
      where: {
        customer_connection_id,
      },
      raw: true,
      order: [["created_at", "DESC"]],
      attributes: [
        "customer_meter_reading_id",
        "customer_connection_id",
        "current_reading",
        "previous_reading",
        "cycle_id",
        "due_date",
        "meter_reading_status",
        "meter_reading_file",
        "declared_by",
        "created_at",
      ],
      include: [
        {
          model: dbModels.trx_customer_connection,
          as: "customer_connection",
          attributes: ["first_name", "last_name", "mobile", "bp_id"],
          include: [
            {
              model: dbModels.trx_customer_connection_address,
              as: "trx_customer_connection_addresses",
              attributes: ["connection_address"],
            },
          ],
        },
        {
          model: dbModels.mst_cycle,
          as: "cycle",
          attributes: ["start_date", "end_date", "cycle_id"],
        },
      ],
    });

    if (!result) {
      return {
        success: false,
        statusCode: 404,
        message: "No meter reading found for the given customer connection.",
        upload_status: "reading_not_found",
        cycleDetails: latestCycle
          ? {
              cycle_id: latestCycle.cycle_id,
              dateRange: `${new Date(latestCycle.start_date).toLocaleString("default", { month: "short" })} - ${new Date(latestCycle.end_date).toLocaleString("default", { month: "short" })} ${new Date(latestCycle.end_date).getFullYear()}`,
            }
          : null,
      };
    }
    const paymentData = await dbModels.trx_connection_meter_bills.findOne({
      where: {
        customer_meter_reading_id: result.customer_meter_reading_id,
        payment_status: "completed",
      },
    });
    console.log("payemntData", paymentData);
    // Check if the bill is due
    if (
      result.due_date &&
      new Date(result.due_date).toDateString() < new Date().toDateString() &&
      !paymentData
    ) {
      return {
        success: false,
        statusCode: 403,
        message:
          "Pay Bill to Upload Meter Reading. Your gas service has been temporarily placed on hold due to an overdue payment. Please clear the outstanding balance to enable meter reading uploads.",
        upload_status: "bill_due",
      };
    }

    if (result.cycle_id !== latestCycle.cycle_id) {
      return {
        success: false,
        statusCode: 404,
        message: "No meter reading found for the given customer connection.",
        upload_status: "reading_not_found",
        cycleDetails: latestCycle
          ? {
              cycle_id: latestCycle.cycle_id,
              dateRange: `${new Date(latestCycle.start_date).toLocaleString("default", { month: "short" })} - ${new Date(latestCycle.end_date).toLocaleString("default", { month: "short" })} ${new Date(latestCycle.end_date).getFullYear()}`,
            }
          : null,
      };
    }

    // Extract image details
    let imagePath = result.meter_reading_file || null;
    let imageName = imagePath ? imagePath.split("/").pop() : null;

    // Format date
    let formattedDate = "Invalid Date";
    if (result.created_at) {
      const createdAt = new Date(result.created_at);
      if (!isNaN(createdAt.getTime())) {
        formattedDate = `${createdAt.getDate()}${getOrdinalSuffix(createdAt.getDate())} ${createdAt.toLocaleString("default", { month: "short" })}, ${createdAt.getFullYear()}`;
      }
    }

    // Format cycle
    let cycleLabel =
      result["cycle.start_date"] && result["cycle.end_date"]
        ? `${new Date(result["cycle.start_date"]).toLocaleString("default", { month: "short" })} - ${new Date(result["cycle.end_date"]).toLocaleString("default", { month: "short" })} ${new Date(result["cycle.end_date"]).getFullYear()}`
        : "Joint Meter Reading (JMR)";

    if (!result["cycle.start_date"]) {
      cycleLabel = "Joint Meter Reading (JMR)";
    }

    // Check if a reading has been submitted for the current cycle
    if (result.meter_reading_status === "completed" && latestCycle.cycle_id) {
      return {
        success: true,
        statusCode: 200,
        message:
          "Meter reading for the current cycle has already been submitted.",
        upload_status:
          result.declared_by == "averaged_out"
            ? "averaged_out"
            : "details_submitted",
        data: {
          cycle: cycleLabel,
          dateSubmitted: formattedDate,
          readingValue: `${result.current_reading} SCM`,
          type: `Declared by ${capitalize(result.declared_by)}`,
          imagePath,
          imageName,
          imageSize: "224 KB",
          dueDate: result["due_date"],
          customerDetails: result["customer_connection.first_name"]
            ? {
                name: `${result["customer_connection.first_name"]} ${result["customer_connection.last_name"]}`,
                bpNumber: result["customer_connection.bp_id"] || "N/A",
                mobile: result["customer_connection.mobile"],
                address:
                  result[
                    "customer_connection.trx_customer_connection_addresses.connection_address"
                  ] || "N/A",
              }
            : null,
        },
      };
    }

    return {
      success: true,
      statusCode: 200,
      message: "Meter reading retrieved successfully.",
      data: {
        upload_status:
          result.declared_by == "averaged_out"
            ? "averaged_out"
            : "details_submitted",
        cycle: cycleLabel,
        dateSubmitted: formattedDate,
        readingValue: `${result.current_reading} SCM`,
        type: `Declared by ${capitalize(result.declared_by)}`,
        imagePath,
        imageName,
        imageSize: "224 KB",
        dueDate: result["due_date"],
        customerDetails: result["customer_connection.first_name"]
          ? {
              name: `${result["customer_connection.first_name"]} ${result["customer_connection.last_name"]}`,
              bpNumber: result["customer_connection.bp_id"] || "N/A",
              mobile: result["customer_connection.mobile"],
              address:
                result[
                  "customer_connection.trx_customer_connection_addresses.connection_address"
                ] || "N/A",
            }
          : null,
      },
    };
  } catch (error) {
    console.error("Error fetching meter reading details:", error);
    return {
      success: false,
      statusCode: 500,
      message: "An error occurred while retrieving meter reading details.",
      error: error.message || "Unknown error",
    };
  }
};
const saveNewReading = async (cachedData,filePath) => {
  try {
    // Validate required input
    if (!cachedData.current_reading) {
      return { success: false, message: "Please resubmit the reading." };
    }

    // Check if the customer meter exists and is active
    const customerMeter = await dbModels.trx_customer_meter.findOne({
      where: {
        customer_connection_id: Number(cachedData.customer_connection_id),
        meter_status: "completed",
      },
      attributes: ["customer_meter_id", "is_active", "meter_status"],
    });

    if (!customerMeter) {
      return {
        success: false,
        message: "No active meter found for the given customer connection.",
      };
    }

    // Check if a record already exists for the same cycle and declared_by
    let existingReading = await dbModels.trx_customer_meter_readings.findOne({
      where: {
        cycle_id: cachedData.cycle_id,
        declared_by: cachedData.declared_by,
        customer_meter_id: customerMeter.customer_meter_id,
      },
    });

    const latestCycle = await dbModels.mst_cycle.findOne({
      order: [["start_date", "DESC"]],
      attributes: ["start_date", "end_date", "cycle_id"],
      raw: true,
      where: {
        cycle_id: cachedData.cycle_id,
      },
    });

    const data = {
      customer_connection_id: cachedData?.customer_connection_id,
      customer_meter_id: customerMeter?.customer_meter_id,
      previous_reading: cachedData?.previous_reading || 0,
      current_reading: cachedData?.current_reading || 0,
      reading_units: cachedData?.reading_units,
      meter_reading_file: filePath,
      meter_reading_status: "completed",
      declared_by: cachedData?.declared_by,
      created_by: cachedData?.created_by || 0,
      created_at: new Date(),
      cycle_id: cachedData?.cycle_id,
      due_date: latestCycle?.end_date,
    };

    let result;
    if (existingReading) {
      // Update the existing reading
      await dbModels.trx_customer_meter_readings.update(data, {
        where: {
          customer_meter_reading_id: existingReading?.customer_meter_reading_id,
        },
      });

      // Fetch the updated record
      existingReading = await dbModels.trx_customer_meter_readings.findOne({
        where: {
          customer_meter_reading_id: existingReading?.customer_meter_reading_id,
        },
      });

      result = {
        ...existingReading?.dataValues,
        previous_reading: formatReading(existingReading?.previous_reading),
        current_reading: formatReading(existingReading?.current_reading),
        uploadStatus: "details_updated", // Indicate that the record was updated
      };
      return {
        success: true,
        message: "Meter reading updated successfully.",
        data: result,
      };
    } else {
      // Insert new meter reading
      const newReading =
        await dbModels.trx_customer_meter_readings.create(data);

      result = {
        ...newReading.dataValues,
        previous_reading: formatReading(existingReading?.previous_reading),
        current_reading: formatReading(existingReading?.current_reading),
        uploadStatus: "details_submitted", // Indicate that it's a new record
      };
      return {
        success: true,
        message: "Meter reading stored successfully.",
        data: result,
      };
    }
  } catch (error) {
    return {
      success: false,
      message: "An error occurred while saving the meter reading.",
      error: error.message,
    };
  }
};

const getJointMeterReading = async (payload) => {
  try {
    const { cycle_id } = payload;

    // Fetch last meter readings submitted by both agent and customer
    const meterReadings = await dbModels.trx_customer_meter_readings.findAll({
      attributes: [
        "customer_meter_reading_id",
        "customer_connection_id",
        "previous_reading",
        "current_reading",
        "reading_units",
        "meter_reading_status",
        "declared_by",
        "created_at",
      ],
      include: [
        {
          model: dbModels.trx_customer_meter,
          as: "customer_meter",
          attributes: [
            "meter_type",
            "meter_no",
            "meter_status",
            "meter_installation_datetime",
          ],
        },
        {
          model: dbModels.trx_customer_connection,
          as: "customer_connection",
          attributes: ["first_name", "last_name", "mobile", "bp_id"],
          include: [
            {
              model: dbModels.trx_customer_connection_address,
              as: "trx_customer_connection_addresses",
              attributes: ["connection_address"],
            },
          ],
        },
        {
          model: dbModels.mst_cycle,
          as: "cycle",
          attributes: ["start_date", "end_date"],
        },
        // {
        //   model: dbModels.mst_admin,
        //   as: "submitted_by_mst_admin",
        //   attributes: ["first_name", "last_name"],
        // },
      ],
      where: {
        cycle_id,
        declared_by: ["agent", "customer"], // Filter only readings by agent or customer
      },
      order: [["created_at", "DESC"]],
    });

    // Separate readings based on declared_by field
    const agentReading =
      meterReadings.find((r) => r.declared_by === "agent") || null;
    const customerReading =
      meterReadings.find((r) => r.declared_by === "customer") || null;

    return {
      agentReading,
      customerReading,
    };
  } catch (error) {
    console.error("Error fetching meter readings:", error);
    throw error;
  }
};
const getCustomerMeterRetails = async (customer_connection_id, meter_no) => {
  try {
    // Find meter details matching connection ID and meter number
    const meterDetails = await dbModels.trx_customer_meter.findOne({
      where: {
        customer_connection_id: customer_connection_id,
        meter_no: meter_no,
        is_active: 1,
      },
      attributes: [
        "customer_meter_id",
        "meter_type",
        "meter_no",
        "meter_status",
        "meter_installation_datetime",
        "is_active",
      ],
      raw: true,
    });

    // If no matching meter found, return null
    if (!meterDetails) {
      return null;
    }

    return meterDetails;
  } catch (error) {
    console.error("Error in getCustomerMeterRetails:", error);
    return null;
  }
};

const formatReading = (reading) => {
  return String(reading || 0).padStart(4, "0"); // Ensures 4-digit format
};

module.exports = {
  getMeterReadingHistoryList,
  getMeterReadingDetails,
  getLastMeterReading,
  saveNewReading,
  getJointMeterReading,
  getCustomerMeterRetails,
};
