const jwt = require("jsonwebtoken");
const fs = require("fs");
const { JWT, PNG_TOKEN_EXPIRY_TIME } = require("../../config");
const redisHelper = require("./redisHelper");
const { refreshToken } = require("firebase-admin/app");
const publicKEY = process.env.JWT_PUBLIC_TOKEN_KEY;
const privateKEY = process.env.JWT_PRIVATE_TOKEN_KEY;

const signOptions = JWT.JWT_OPTIONS;
const jwtHelper = {
  setData: function (payload, expiresIn = 0) {
    if (expiresIn > 0) {
      signOptions.expiresIn = expiresIn;
    }
    try {
      payload = payload.toJSON();
    } catch (error) {}
    return jwt.sign(payload, privateKEY, signOptions);
  },

  verifyToken: async (token) => {
    try {
      if (!token) {
        throw new Error("Token is missing");
      }
      const decoded = jwt.verify(token, privateKEY);
      return decoded;
    } catch (err) {
      throw new Error(`Token verification failed: ${err.message}`);
    }
  },

  createToken: async function (payload, expiresIn = 0) {
    let result = {
      token: null,
      error: null,
    };
    try {
      result.token = await this.setData(payload, expiresIn);
      if (!result.token) {
        throw new Error("Token generation failed");
      }
    } catch (err) {
      result.error = err.toString();
    }
    return result;
  },

  checkToken: async function (token) {
    let result = {
      tokenData: null,
      error: null,
    };
    try {
      result.tokenData = await this.verifyToken(token);
    } catch (err) {
      result.error = err.toString();
    }
    return result;
  },
  isValidToken: async function (token) {
    let result = {
      tokenData: null,
      error: null,
    };
    try {
      result.tokenData = await this.verifyToken(token);
    } catch (err) {
      result.error = err.toString();
    }
    return result;
  },
  createPngToken: async function (payload, createRefreshToken = false) {
    let result = {
      token: null,
      error: null,
      refreshToken: null,
    };
    try {
      result = await this.createToken(payload);
      // console.log("payload", payload, result);
      await redisHelper.setData(result.token, payload, PNG_TOKEN_EXPIRY_TIME);
      if (createRefreshToken) {
        const refreshTokenData = await this.createToken(
          { ...payload, tokenType: "refreshToken" },
          JWT.REFRESH_TOKEN_OPTIONS.expiresIn
        );
        if (!refreshTokenData.error) {
          result.refreshToken = refreshTokenData.token;
        }
      }
    } catch (err) {
      result.error = err.toString();
    }
    return result;
  },
  verifyPngToken: async (token) => {
    try {
      if (!token) {
        throw new Error("Token is missing");
      }
      const decoded = jwt.verify(token, privateKEY);
      const redisResult = await redisHelper.getData(token);
      if (!redisResult) {
        throw new Error(`Token verification failed`);
      }
      return decoded;
    } catch (err) {
      throw new Error(`Token verification failed: ${err.message}`);
    }
  },
};

module.exports = jwtHelper;
