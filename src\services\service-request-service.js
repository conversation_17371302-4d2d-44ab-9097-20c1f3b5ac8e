const serviceRequestRepository = require("../repository/service-request-repository");
const customerRepository = require("../repository/customer-repository");
const dashBoardRepository = require('../repository/dash-board-repository');
const {
  PROGRESS_STATUS,
  UPDATE_CONNECTION_TYPE,
  ACCOUNT_TYPE,
} = require("../utils/constants");
const { generateRequestId } = require("../utils/commonFn");
const redisHelper = require("../helpers/redisHelper");
const { APPLICATION_EXPIRY_TIME } = require("../../config");
const { mergeData } = require("../utils/commonFn");
const path = require("path");
const customerService = require("../services/customer-service");
 
const addressChangeRequestService = async (payload) => {
  try {
    const {
      customer_connection_id,
      customer_connection_address_id,
      address_request_type,
      reason_id,
      transfer_date,
      available_pngareas_id,
      house_flat_no,
    } = payload;
    console.log("payload", payload);

    // Check if a transfer request already exists for the given connection ID
    const whereCond = {
      old_customer_connection_id: customer_connection_id,
      address_request_type,
    };
    const existingRequest =
      await serviceRequestRepository.checkChangeAddressRequestExist(whereCond);

    if (existingRequest) {
      const error = new Error("Request Already Exist");
      error.details = existingRequest;
      throw error;
      // throw new Error(
      //   `Request already exists Request ID: ${existingRequest.change_address_request_id} Your ${address_request_type} request was scheduled on ${existingRequest.transfer_date}. We'll contact you soon at your registered number and email address.`
      // );
    }

    //generate unique requestID
    const request_ticket_no = generateRequestId();

    // Insert new transfer request if no existing request is found
    const insertParam = {
      request_ticket_no,
      old_customer_connection_id: customer_connection_id,
      address_request_type,
    };

    switch (address_request_type) {
      case UPDATE_CONNECTION_TYPE.TRANSFERCONNECTION:
        insertParam.transfer_date = transfer_date;
        insertParam.customer_connection_address_id =
          customer_connection_address_id;
        insertParam.transfer_reason = reason_id;
        insertParam.old_customer_connection_address_id =
          customer_connection_address_id;
        break;
      case UPDATE_CONNECTION_TYPE.PERMANENTADDRESS:
        let getRedistData = await CustomerDetailById(payload);
        getRedistData.customerConnectionAddress.customer_connection_id =
          customer_connection_id;
        getRedistData.customerConnectionAddress.connection_type =
          address_request_type;
        console.log("getRedistData", getRedistData);
        // console.log("permanent_address",permanent_address);
        insertParam.customer_connection_address_id =
          getRedistData.customer_connection_address_id;
        insertParam.old_customer_connection_address_id =
          getRedistData.customer_connection_address_id;

        //insert permanent address details to customer connection address
        const result = await customerRepository.addCustomerAddress(
          getRedistData.customerConnectionAddress
        );
        console.log("result", result);
        insertParam.new_customer_connection_address_id =
          result.customer_connection_address_id;
        break;
    }
    console.log("insertParam", insertParam);

    const result =
      await serviceRequestRepository.insertChangeAddressRequest(insertParam);
      console.log(" insertChangeAddressRequest result",result);
      
    return result;
  } catch (error) {
    console.error("Error in  addressChangeRequest service:", error);
    throw error;
  }
};
const submitEnableAddressRequestService = async (payload) => {
  try {
    const {
      customer_connection_id,
      customer_connection_address_id,
      address_request_type,
      house_flat_no,
      available_pngareas_id,
    } = payload;
    console.log("payload", payload);

    // Check if a transfer request already exists for the given connection ID
    const whereCond = {
      old_customer_connection_id: customer_connection_id,
      old_customer_connection_address_id: customer_connection_address_id,
      address_request_type,
      // request_status: "approved",
      // address_request_status: "submitted",
    };
    console.log("whereCond", whereCond);
    
    const existingRequest =
      await serviceRequestRepository.checkChangeAddressRequestExist(whereCond);
    console.log("existingRequest", existingRequest);

    if (existingRequest) {
      // if(existingRequest.request_status === "approved"){
      //   throw new Error(
      //     `Transfer Request not exists for Your Enable Connection Request. We'll contact you soon at your registered number and email address.`
      //   );
      // }else
      if(existingRequest.request_status === "pending"){
        const error = new Error("Transfer Request exists for Your Enable Connection Request. But not approved yet. We'll contact you soon at your registered number and email address.");
        error.details = existingRequest;
        throw error;
        // throw new Error(
        //   `Transfer Request exists for Your Enable Connection Request. But not approved yet. We'll contact you soon at your registered number and email address.`
        // );
      }else if(existingRequest.address_request_status === "submitted"){
        
        const error = new Error("Reported Issue Already Exist");
        error.details = existingRequest;
        throw error;
        // throw new Error(
        //   `Request already exists Request ID: ${existingRequest.change_address_request_id} Your Enable Connection request was created on ${existingRequest.updatedAt}. We'll contact you soon at your registered number and email address.`
        // );
      }
    }else{
      const error = new Error("Transfer Request not exists for Your Enable Connection Request. We'll contact you soon at your registered number and email address.");
      // error.details = existingRequest;
      throw error;
      // throw new Error(
      //   `Transfer Request not exists for Your Enable Connection Request. We'll contact you soon at your registered number and email address.`
      // );
    }
    const redisKey = redisHelper.getRedisNewAddressRequestKey(
      customer_connection_id,
      house_flat_no,
      available_pngareas_id
    );
    let getConnectionRedis = await redisHelper.getData(redisKey);
    console.log("1- getConnectionRedis", getConnectionRedis);
    if (getConnectionRedis) {
      getConnectionRedis.customerConnectionDetails.connection_type =
        "transfer-connection";
      delete getConnectionRedis.customerConnectionDetails
        .customer_connection_id;
      // Save the details of customer from Redis to DB
    }else{
      throw new Error(
        "No data found for this Connection, Cache Expiered. Please Try again filling all details"
      );
    }
    const result =
      await customerRepository.createUpdateConnection(getConnectionRedis);
    console.log("result", result);
    // Delete from the Redis
    await redisHelper.deleteData(redisKey);
    if(existingRequest)
    {

      // update service request table
      const updateParam = {
        new_customer_connection_id: result.customer_connection_id,
        address_request_status: "submitted",
      };
      const whereRequestCond = {
        request_ticket_no: existingRequest.request_ticket_no,
        old_customer_connection_id: customer_connection_id,
      };
      await serviceRequestRepository.updateChangeAddressRequest(
        updateParam,
        whereRequestCond
      );
      return await serviceRequestRepository.checkChangeAddressRequestExist(whereCond);
      // console.log("newExistingRequest", newExistingRequest);

      // return {change_address_request_id: existingRequest.change_address_request_id};
    }
  } catch (error) {
    console.error("Error in  submitEnableAddressRequest service:", error);
    throw error;
  }
};
const submitPermanentAddressChangeDetailsToCache = async (detailsData) => {
  try {
    console.log("Received detailsData:", detailsData); // Debugging

    const expireTime = APPLICATION_EXPIRY_TIME; // 1 hour in seconds

    // Generate a Redis key based on the payload
    const connectionRedisKey =
      await redisHelper.getRedisPermanentAddressChangeRequestKey(
        detailsData?.customer_connection_id,
        detailsData?.permanent_address.permanent_house_no,
        detailsData?.permanent_address.permanent_pngareas_id
      );
    // Fetch existing data from Redis
    let getConnectionDetails =
      (await redisHelper.getData(connectionRedisKey)) || {};
    console.log("1- getConnectionDetails", getConnectionDetails);

    // getConnectionDetails.registrationId = tempRegistrationId;
    if (detailsData?.permanent_address) {
      const whereParams = {
        connection_detail: {
          customer_connection_id: Number(detailsData.customer_connection_id),
        },
      };

      console.log("whereParams", whereParams);
      // Chekecka details of the customer from DB
      const checkConnectionExist =
        await customerRepository.getCustomerDetails(whereParams);
      console.log("checkConnectionExist", checkConnectionExist);

      // if(detailsData.old_available_pngareas_id !== detailsData.available_pngareas_id){
      //   const oldConnectionRedisKey= await redisHelper.getRedisPngAvailableConnectionKey(detailsData?.bpcl_id,
      //     detailsData?.old_house_flat_no,
      //     detailsData?.old_available_pngareas_id);
      //     // Fetch existing data from Redis
      //   console.log("oldConnectionRedisKey",oldConnectionRedisKey);
      //   let getOldConnectionDetails = await redisHelper.getData(oldConnectionRedisKey);
      //   console.log("getOldConnectionDetails",getOldConnectionDetails);
      //   if(getOldConnectionDetails){
      //     getConnectionDetails=getOldConnectionDetails;
      //     console.log("2- getConnectionDetails",getConnectionDetails);
      //     await redisHelper.deleteData(connectionRedisKey)
      //   }
      // }
      if (!checkConnectionExist || checkConnectionExist < 1) {
        throw new Error("Connection Not exists for this customer");
      }
      getConnectionDetails.customer_connection_id =
        detailsData.customer_connection_id;
      getConnectionDetails.customer_connection_address_id =
        detailsData.customer_connection_address_id;
      getConnectionDetails.address_request_type =
        detailsData.address_request_type;
      getConnectionDetails.reason_id = detailsData.reason_id;
      getConnectionDetails.customerConnectionAddress = mergeData(
        getConnectionDetails.customerConnectionAddress,
        {
          connection_pngareas_id:
            checkConnectionExist?.connection_addresses.connection_pngareas_id,
          connection_pincode:
            checkConnectionExist?.connection_addresses.connection_pincode,
          connection_landmark:
            checkConnectionExist?.connection_addresses.connection_landmark,
          connection_block_no:
            checkConnectionExist?.connection_addresses.connection_block_no,
          connection_floor_no:
            checkConnectionExist?.connection_addresses.connection_floor_no,
          connection_house_no:
            checkConnectionExist?.connection_addresses.connection_house_no,
          connection_building_house_name:
            detailsData?.connection_address?.connection_building_house_name,
          connection_state:
            checkConnectionExist?.connection_addresses.connection_state,
          connection_city:
            checkConnectionExist?.connection_addresses.connection_city,
          connection_district:
            checkConnectionExist?.connection_addresses.connection_district,
          connection_building_house_name:
            checkConnectionExist?.connection_addresses.connection_building_house_name,
          permanent_pngareas_id:
            detailsData?.permanent_address?.permanent_pngareas_id,
          permanent_pincode: detailsData?.permanent_address?.permanent_pincode,
          permanent_landmark:
            detailsData?.permanent_address?.permanent_landmark,
          permanent_block_no:
            detailsData?.permanent_address?.permanent_block_no,
          permanent_floor_no:
            detailsData?.permanent_address?.permanent_floor_no,
          permanent_building_house_name:
            detailsData?.connection_address?.permanent_building_house_name,
          permanent_house_no:
            detailsData?.permanent_address?.permanent_house_no,
          permanent_state: detailsData?.permanent_address?.permanent_state,
          permanent_city: detailsData?.permanent_address?.permanent_city,
          permanent_district:
            detailsData?.permanent_address?.permanent_district,
          permanent_building_house_name:
            detailsData?.permanent_address?.permanent_building_house_name,
          created_by: 0,
          updated_by: 0,
          connection_type: detailsData?.address_request_type,
        }
      );
    }

    // Store updated data back in Redis
    let result = await redisHelper.setData(
      connectionRedisKey,
      getConnectionDetails,
      expireTime
    );

    console.log("Redis set result:", result);

    return { result };
  } catch (err) {
    console.error("Error in submitDetailsToCache:", err);
    throw err;
  }
};
const uploadDocuments = async (detailsData, files) => {
  try {
    console.log("Received files:", files); // Debugging

    const redisKey = redisHelper.getRedisPermanentAddressChangeRequestKey(
      detailsData.customer_connection_id,
      detailsData.house_flat_no,
      detailsData.available_pngareas_id
    );

    // Fetch a details of the customer from Redis
    let getConnectionDetails = await redisHelper.getData(redisKey);

    if (getConnectionDetails) {
      // get file ext
      const extname = path.extname(files.filename);

      const proofDoc = {
        proof_type: detailsData.proof_type,
        proof_master_id: detailsData.proof_master_id,
        file_name: files.filename,
        file_path: files.path,
        file_mimetype: files.mimetype,
        file_size: files.size,
        file_ext: extname,
        verification_status: "application-pending",
      };

      if (getConnectionDetails.uploadDocuments) {
        // Function to count occurrences of proof_type
        const countProofTypes = (documents) => {
          return documents.reduce((acc, doc) => {
            acc[doc.proof_type] = (acc[doc.proof_type] || 0) + 1;
            return acc;
          }, {});
        };

        // Get counts
        const proofTypeCounts = countProofTypes(
          getConnectionDetails.uploadDocuments
        );
        console.log("Proof type counts:", proofTypeCounts);

        if (proofTypeCounts.photo_proof && proofTypeCounts.photo_proof > 2) {
          throw new Error(
            "Photo Proof document upload limit exceeded. Maximum 2 allowed."
          );
        } else if (
          proofTypeCounts.proof_of_ownership &&
          proofTypeCounts.proof_of_ownership > 2
        ) {
          throw new Error(
            "Photo Proof document upload limit exceeded. Maximum 2 allowed."
          );
        }

        //check file exist
        const foundDocument = getConnectionDetails.uploadDocuments.find(
          (doc) =>
            doc.proof_type === proofDoc.proof_type &&
            doc.proof_master_id === proofDoc.proof_master_id
        );

        if (foundDocument) {
          throw new Error(
            "This Proof Type already uploaded. Please delete the existing one to upload new one"
          );
        }
        let uploadDocuments = getConnectionDetails.uploadDocuments;
        uploadDocuments.push(proofDoc);
      } else {
        let uploadDocuments = [];
        uploadDocuments.push(proofDoc);
        getConnectionDetails.uploadDocuments = uploadDocuments;
      }

      // Store the updated file details with upload document in Redis
      await redisHelper.setData(redisKey, getConnectionDetails, 3600);
    } else {
      throw new Error(
        "No data found for this Connection, Cache Expiered. Please Try again filling all details"
      );
    }

    return { message: "Files uploaded and stored in Redis.", files };
  } catch (error) {
    console.error("Error in uploadDocuments:", error);
    throw error;
  }
};
const CustomerDetailById = async (payload) => {
  try {
    let result;
    const redisKey = redisHelper.getRedisPermanentAddressChangeRequestKey(
      payload.customer_connection_id,
      payload.house_flat_no,
      payload.available_pngareas_id
    );
    console.log("redisKey", redisKey);

    // Fetch a details of the customer from Redis
    result = await redisHelper.getData(redisKey);
    console.log("Redis result:", result);

    if (result) {
      return result;
    }
    throw new Error("No data found for this Connection.");
  } catch (err) {
    console.error("Error in submitDetailsToCache:", err);
    throw err;
  }
};

const submitNewAddressDetailsToCache = async (detailsData) => {
  try {
    console.log("Received detailsData:", detailsData); // Debugging

    const expireTime = APPLICATION_EXPIRY_TIME; // 1 hour in seconds

    // Generate a Redis key based on the payload
    const connectionRedisKey = await redisHelper.getRedisNewAddressRequestKey(
      detailsData?.customer_connection_id,
      detailsData?.house_flat_no,
      detailsData?.available_pngareas_id
    );
    // Fetch existing data from Redis
    let getConnectionDetails =
      (await redisHelper.getData(connectionRedisKey)) || {};
    console.log("1- getConnectionDetails", getConnectionDetails);
    if (detailsData?.connection_address) {
    // getConnectionDetails.registrationId = tempRegistrationId;
      const whereParams = {
        connection_detail: {
          customer_connection_id: Number(detailsData.customer_connection_id),
          available_pngareas_id: Number(detailsData.old_available_pngareas_id),
        },
        connection_address: {
          connection_house_no: detailsData.old_house_flat_no?.toString(),
        },
      };
      console.log("whereParams", whereParams);

      // Chekecka details of the customer from DB
      const checkConnectionExist =
        await customerRepository.getCustomerDetails(whereParams);
      console.log("checkConnectionExist", checkConnectionExist);

      getConnectionDetails.customerConnectionDetails = mergeData(
        getConnectionDetails.customerConnectionDetails,
        {
          bpcl_id: detailsData?.bpcl_id,
          customer_connection_id: detailsData?.customer_connection_id,
          available_pngareas_id: detailsData?.available_pngareas_id,
          prefix: checkConnectionExist.connection_details?.prefix,
          first_name: checkConnectionExist.connection_details?.first_name,
          middle_name: checkConnectionExist.connection_details?.middle_name,
          last_name: checkConnectionExist.connection_details?.last_name,
          email: checkConnectionExist.connection_details?.email,
          mobile: checkConnectionExist.connection_details?.mobile,
          alternate_mobile:
            checkConnectionExist.connection_details?.alternate_mobile,
          parents_name:
            checkConnectionExist.connection_details?.parents_name ?? "",
          spouse_name: checkConnectionExist.connection_details?.spouse_name ?? "",
         
          is_active: 1,
          approved_date: null,
          onboarding_date: null,
          installation_date: null,
          disconnected_date: null,
          status: "application-pending",
          transfer_request_id:detailsData.transfer_request_id,
          isu_synced: 0,
          is_delete: 0,
          created_by: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
          updated_by: 0,
          connection_address_type:
            detailsData?.addition_info?.connection_address_type,
          extra_connection: detailsData?.addition_info?.extra_connection,
          extra_points: detailsData?.addition_info?.extra_points,
          govt_scheme_id: detailsData?.addition_info?.govt_scheme_id,
          // security_deposit: detailsData?.addition_info?.security_deposit,
          // security_deposit_category:
          // detailsData?.addition_info?.security_deposit_category,
          is_lpg: detailsData?.addition_info?.is_lpg,
          lpg_consumer_no: detailsData?.addition_info?.lpg_consumer_no,
          lpg_distributor_name: detailsData?.addition_info?.lpg_distributor_name,
          lpg_omc_name: detailsData?.addition_info?.lpg_omc_name,
          lpg_consent:0,
          rental_owner_name: detailsData?.addition_info?.rental_owner_name,
          rental_owner_mobile: detailsData?.addition_info?.rental_owner_mobile,
        }
      );

      getConnectionDetails.customerConnectionAddress = mergeData(
        getConnectionDetails.customerConnectionAddress,
        {
          connection_pngareas_id:
            detailsData?.connection_address?.connection_pngareas_id,
          connection_pincode: detailsData?.connection_address?.connection_pincode,
          connection_landmark:
            detailsData?.connection_address?.connection_landmark,
          connection_block_no:
            detailsData?.connection_address?.connection_block_no,
          connection_floor_no:
            detailsData?.connection_address?.connection_floor_no,
          connection_house_no:
            detailsData?.connection_address?.connection_house_no,
          connection_building_house_name:
            detailsData?.connection_address?.connection_building_house_name,
          connection_state: detailsData?.connection_address?.connection_state,
          connection_city: detailsData?.connection_address?.connection_city,
          connection_district:
            detailsData?.connection_address?.connection_district,
          permanent_pngareas_id:
            detailsData?.connection_address?.permanent_pngareas_id,
          permanent_pincode: detailsData?.connection_address?.permanent_pincode,
          permanent_landmark: detailsData?.connection_address?.permanent_landmark,
          permanent_block_no: detailsData?.connection_address?.permanent_block_no,
          permanent_floor_no: detailsData?.connection_address?.permanent_floor_no,
          permanent_house_no: detailsData?.connection_address?.permanent_house_no,
          permanent_building_house_name:
            detailsData?.connection_address?.permanent_building_house_name,
          permanent_state: detailsData?.connection_address?.permanent_state,
          permanent_city: detailsData?.connection_address?.permanent_city,
          permanent_district: detailsData?.connection_address?.permanent_district,
          created_by: 0,
          updated_by: 0,
          connection_type: detailsData?.connection_address?.connection_type,
          connection_change_reason:
            detailsData?.connection_address?.connection_change_reason,
        }
      );
    // console.log("detailsData?.addition_info",detailsData?.addition_info);
    }else{
      getConnectionDetails.customerConnectionDetails.connection_address_type=
            detailsData?.addition_info?.connection_address_type,
      getConnectionDetails.customerConnectionDetails.is_extra_connection=
        detailsData?.addition_info?.extra_connection === "no" ? 0 : 1,
      getConnectionDetails.customerConnectionDetails.extra_points= detailsData?.addition_info?.extra_points,
      getConnectionDetails.customerConnectionDetails.govt_scheme_id= detailsData?.addition_info?.govt_scheme_id,
      getConnectionDetails.customerConnectionDetails.rental_owner_name= detailsData?.addition_info?.rental_owner_name,
      getConnectionDetails.customerConnectionDetails.rental_owner_mobile= detailsData?.addition_info?.rental_owner_mobile,
     
      // getConnectionDetails.customerConnectionDetails.account_type= ACCOUNT_TYPE.POSTPAID,
      // getConnectionDetails.customerConnectionDetails.is_security_deposit=
      // detailsData?.addition_info?.security_deposit === "false" ? 0 : 1,
      // getConnectionDetails.customerConnectionDetails.security_deposit_category=
      // detailsData?.addition_info?.security_deposit_category,
          
      getConnectionDetails.customerAdditionalInfo = mergeData(
        getConnectionDetails.customerAdditionalInfo,
        {
          connection_address_type:
            detailsData?.addition_info?.connection_address_type,
          extra_connection: detailsData?.addition_info?.extra_connection,
          extra_points: detailsData?.addition_info?.extra_points,
          govt_scheme_id: detailsData?.addition_info?.govt_scheme_id,
          // security_deposit: detailsData?.addition_info?.security_deposit,
          // security_deposit_category:
          // detailsData?.addition_info?.security_deposit_category,
          is_lpg: detailsData?.addition_info?.is_lpg,
          lpg_consumer_no: detailsData?.addition_info?.lpg_consumer_no,
          lpg_distributor_name: detailsData?.addition_info?.lpg_distributor_name,
          lpg_omc_name: detailsData?.addition_info?.lpg_omc_name,
          lpg_consent:0,
          rental_owner_name: detailsData?.addition_info?.rental_owner_name,
          rental_owner_mobile: detailsData?.addition_info?.rental_owner_mobile,
        }
      );
    }
    console.log("Updated getConnectionDetails:", getConnectionDetails);
    // Store updated data back in Redis
    let result = await redisHelper.setData(
      connectionRedisKey,
      getConnectionDetails,
      expireTime
    );

    console.log("Redis set result:", result);

    return { result };
  } catch (err) {
    console.error("Error in submitDetailsToCache:", err);
    throw err;
  }
};
const uploadNewAddressDocumentsService = async (detailsData, files) => {
  try {
    console.log("Received files:", files); // Debugging

    const redisKey = redisHelper.getRedisNewAddressRequestKey(
      detailsData.customer_connection_id,
      detailsData.house_flat_no,
      detailsData.available_pngareas_id
    );

    await customerService.uploadDocRedis(redisKey, detailsData, files);

    return { message: "Files uploaded and stored in Redis.", files };
  } catch (error) {
    console.error("Error in uploadDocuments:", error);
    throw error;
  }
};

const documentDeleteService = async (detailsData) => {
  try {
    const {
      customer_connection_id,
      house_flat_no,
      available_pngareas_id,
      proof_type,
      proof_master_id,
    } = detailsData;
    // Generate Redis Key
    const redisKey = redisHelper.getRedisNewAddressRequestKey(
      customer_connection_id,
      house_flat_no,
      available_pngareas_id
    );
    console.log("redisKey",redisKey);

    // Fetch connection document details from Redis
    await customerService.documentDeleteRedis(
      redisKey,proof_type,proof_master_id
    );
    
  } catch (error) {
    console.error("Error in deleteDocument:", error);
    throw error;
  }
};

const CustomerNewAddressDetailById = async (payload) => {
  try {
    let result;
    const redisKey = redisHelper.getRedisNewAddressRequestKey(
      payload.customer_connection_id,
      payload.house_flat_no,
      payload.available_pngareas_id
    );
    console.log("redisKey", redisKey);

    // Fetch a details of the customer from Redis
    result = await redisHelper.getData(redisKey);
    console.log("Redis result:", result);

    if (result) {
      return result;
    }
    throw new Error("No data found for this Connection.");
  } catch (err) {
    console.error("Error in submitDetailsToCache:", err);
    throw err;
  }
};

const extraPointsService = async (payload) => {
  const {
    customer_connection_id,
    customer_connection_address_id,
    extra_point_type,
  } = payload;
  const result = await serviceRequestRepository.extraPoints(
    customer_connection_id,
    extra_point_type,
    customer_connection_address_id
  );
  return result;
};

const connectionServiceRequest = async (payload) => {
  const {
    customer_connection_id,
    customer_connection_address_id,
    request_type,
    request_reason_id,
    estimated_duration_id,
    request_sub_type,
    disconnection_date,
    engineer_visit_date,
  } = payload;
  console.log("payload", payload);

  const whereCond = {
    customer_connection_id,
    request_type,
    disconnection_type: request_sub_type,
  };
  console.log("whereCond", whereCond);

  // Check if a transfer request already exists for the given connection ID
  const existingRequest =
    await serviceRequestRepository.checkConnectionRequestExist(whereCond);
  console.log("existingRequest", existingRequest);

  if (existingRequest) {
    const error = new Error("Reported Issue Already Exist");
    error.details = existingRequest;
    throw error;
    // throw new Error(
    //   `Request already exists Request ID: ${existingRequest.connection_request_id} Your ${payload.update_request_type} request was scheduled on ${existingRequest.connection_transfer_date}. We'll contact you soon at your registered number and email address.`
    // );
  }
  // Insert new transfer request if no existing request is found
  const insertParam = {
    customer_connection_id,
    customer_connection_address_id,
    request_type,
    disconnection_type: request_sub_type,
    request_reason_id,
    request_status: PROGRESS_STATUS.PENDING,
  };

  switch (payload.update_request_type) {
    case UPDATE_CONNECTION_TYPE.TEMPORARYDISCONNECTION:
      insertParam.estimated_duration_id = estimated_duration_id;
      break;
    case UPDATE_CONNECTION_TYPE.TEMPORARYRECONNECTION:
      insertParam.engineer_visit_date = engineer_visit_date;
      break;
    case UPDATE_CONNECTION_TYPE.PERMANENTDISCONNECTION:
      insertParam.disconnection_date = disconnection_date;
      break;
    case UPDATE_CONNECTION_TYPE.PERMANENTRECONNECTION:
      insertParam.engineer_visit_date = engineer_visit_date;
      break;
  }
  const result =
    await serviceRequestRepository.addConnectionRequestService(insertParam);
  return result;
};

const alterationModification = async (payload) => {
  const result =
    await serviceRequestRepository.alterationModificationRequest(payload);
  return result;
};
const submitDetailsToCache = async (payload) => {
  try {
    const {
      customer_connection_id,
      name_transfer_reason_id,
      title,
      first_name,
      middle_name,
      last_name,
      mobile,
      alt_mobile,
      email,
    } = payload;

    const connectionRedisKey = redisHelper.getNameTransferKey(
      customer_connection_id
    );

    // Check if data already exists in Redis
    const redisData = await redisHelper.getData(connectionRedisKey);
    console.log("Redis Data:", redisData);

    if (redisData) {
      throw new Error("Data already exists in Redis for this connection ID.");
    }

    // Check if new customer exists in DB
    const checkNewCustomerExistOrNot =
      await serviceRequestRepository.checkNewCustomerExistOrNotInDb(mobile);


    // Store data in Redis
    const setDataInRedis = await redisHelper.setData(
      connectionRedisKey,
      {
        existing_connection_id: customer_connection_id,
        new_connection_id: checkNewCustomerExistOrNot.customer_connection_id || null,
        name_transfer_reason: name_transfer_reason_id,
        prefix: title,
        first_name,
        middle_name,
        last_name,
        mobile,
        alt_mobile,
        email,
      },
      APPLICATION_EXPIRY_TIME
    );
    if (!setDataInRedis) {
      throw new Error("Failed to set data in Redis.");
    }
    const result = await serviceRequestRepository.getPaymentDetials(
      name_transfer_reason_id
    ); // Fetch payment details for response
    const response = {
      area_id: result?.area_id,
      base_charges: result?.rates,
      handling_fees: result?.handling_fees,
      gst_percentage: result?.gst_percentage,
      gst_amount: 150,
      total_amount: 1300,
    };
    return response;
  } catch (error) {
    console.error("Error in submitDetailsToCache:", error.message);
    throw error; // Ensure the error is properly thrown and not just logged
  }
};

const nameTrasferSaveToDbService = async (payload) => {
  const customerConnId = payload.customer_connection_id;
  const connServiceId = payload.connection_service_id;
  console.log("connServiceId--->", connServiceId);

  // Chekecka details of the customer from DB
  const whereParams = {
    connection_detail: {
      customer_connection_id: Number(payload.customer_connection_id),
    },
  };

  const checkConnectionExist =
    await customerRepository.getCustomerDetails(whereParams);
  if (!checkConnectionExist || checkConnectionExist < 1) {
    throw new Error("Connection Not exists for this customer");
  }

  const connectionRedisKey = redisHelper.getNameTransferKey(customerConnId);
  let redisData = await redisHelper.getData(connectionRedisKey);
  redisData.created_by = payload.created_by;
  if (!redisData) {
    throw new Error("No data found in Redis for this connection ID.");
  }
  console.log("redisData", redisData);

  const result = await serviceRequestRepository.nameTransferSaveToDbRequest(
    redisData,
    connServiceId
  );
  await redisHelper.deleteData(connectionRedisKey);
  
  return result;
};


const transferApprovalStatusUpdate = async (requestBody) => {

  const { approval_status, name_transfer_request_id, updated_by } = requestBody;
  const whereCondition = {
    name_transfer_request_id,
  };

  const updateData = {
    name_transfer_status: approval_status === 1 ? 'approved' : 'rejected',
    // updated_by : parseInt(updated_by, 10),
    updated_by : 2,
    updated_at : new Date(),
    approved_date : new Date(),
  };
  const result = await serviceRequestRepository.transferApprovalStatusUpdate(updateData,whereCondition);

  if(result){
    const getReqInfo = await serviceRequestRepository.getSingleNameTransferRequestInfo(whereCondition);
    const insertNotification = {
      customer_connection_id : getReqInfo.new_connection_id,
      alert_type : 'Pending',
      subject : 'Action required: accept name transfer request',
      description :  `${getReqInfo.first_name} ${getReqInfo.last_name} has initiated a name transfer request in your name`,
      is_notify : 0,
      is_active : 1,
      created_at : new Date(),
      created_by : created_by,
    }
    await dashBoardRepository.createNotification(insertNotification); 
  }
  return result;
};

module.exports = {
  addressChangeRequestService,
  extraPointsService,
  alterationModification,
  submitDetailsToCache,
  connectionServiceRequest,
  alterationModification,
  submitPermanentAddressChangeDetailsToCache,
  uploadDocuments,
  CustomerDetailById,
  nameTrasferSaveToDbService,
  submitNewAddressDetailsToCache,
  uploadNewAddressDocumentsService,
  CustomerNewAddressDetailById,
  submitEnableAddressRequestService,
  transferApprovalStatusUpdate,
  documentDeleteService
};
