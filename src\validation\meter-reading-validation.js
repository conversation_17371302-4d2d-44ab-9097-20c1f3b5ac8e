const { Joi } = require('express-validation');

const getMeterReadingHistoryListValidation = {
  query: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required(),
    customer_meter_id: Joi.number().integer().min(1).optional()
  }).required(),
};

const getMeterReadingDetailsValidation = {
  query: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required(),
  }).required(),
};

const verifyMeterReadingValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required(),
    current_reading: Joi.number().positive().min(0).required(),
  }).required(),
};

const getMeterReadingUploadDetailsFromCacheValidation = {
  query: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required(),
  }).required(),
};

const getJoinMeterReadingValidation = {
  query: Joi.object({
    cycle_id: Joi.number().integer().min(1).required(),
  }).required(),
};

const submitMeterReadingDetailsValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required(),

    // Uncomment and complete if needed
    // cycle_id: Joi.number().integer().min(1).optional(),
    // meter_reading_file: Joi.string().max(100).optional(),
    // previous_reading: Joi.number().min(0).required(),
    // current_reading: Joi.number().min(0).required(),
    // reading_units: Joi.number().min(0).required(),
    // bill_amount: Joi.number().min(0).optional(),
    // due_date: Joi.date().optional(),
    // is_paid: Joi.number().integer().valid(0, 1).default(0),
    // reason: Joi.string().optional(),
    // meter_reading_status: Joi.string().valid('pending', 'inprogress', 'completed', 'failed').required(),
    // submitted_by: Joi.number().integer().min(1).optional(),
    // declared_by: Joi.string().valid('agent', 'customer', 'averaged_out').required(),
    // isu_synced: Joi.number().integer().valid(0, 1).default(0),
    // created_at: Joi.date().default(() => new Date(), 'current timestamp'),
    // created_by: Joi.number().integer().min(1).default(0),
  }),
};

module.exports = {
  getMeterReadingHistoryListValidation,
  getMeterReadingDetailsValidation,
  verifyMeterReadingValidation,
  getMeterReadingUploadDetailsFromCacheValidation,
  submitMeterReadingDetailsValidation,
  getJoinMeterReadingValidation
};
