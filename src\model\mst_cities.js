const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_cities",
    {
      city_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      district_code: {
        type: DataTypes.STRING(5),
        allowNull: true,
      },

      city_title: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      state_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_states",
          key: "state_id",
        },
      },
      city_description: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      city_status: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
    },
    {
      sequelize,
      tableName: "mst_cities",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_city_id",
          unique: true,
          fields: [{ name: "city_id" }],
        },
      ],
    }
  );
};
