const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_customer', {
    customer_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    prefix: {
      type: DataTypes.STRING(5),
      allowNull: false
    },
    bpcl_id: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: "0"
    },
    first_name: {
      type: DataTypes.STRING(25),
      allowNull: false
    },
    middle_name: {
      type: DataTypes.STRING(25),
      allowNull: true
    },
    last_name: {
      type: DataTypes.STRING(25),
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    mobile: {
      type: DataTypes.STRING(10),
      allowNull: false,
      unique: "UK_trx_customer_mobile"
    },
    alternate_mobile: {
      type: DataTypes.STRING(10),
      allowNull: true
    },
    bpcl_synced: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    bpcl_status: {
      type: DataTypes.STRING(10),
      allowNull: true
    },
    bpl_error: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_customer',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_customer_id",
        unique: true,
        fields: [
          { name: "customer_id" },
        ]
      },
      {
        name: "UK_trx_customer_mobile",
        unique: true,
        fields: [
          { name: "mobile" },
        ]
      },
    ]
  });
};
