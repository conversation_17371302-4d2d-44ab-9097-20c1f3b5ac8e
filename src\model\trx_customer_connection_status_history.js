const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_customer_connection_status_history",
    {
      customer_connection_status_history_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      previous_status: {
        type: DataTypes.STRING(30),
        allowNull: false,
        defaultValue: "application-pending",
      },
      current_status: {
        type: DataTypes.STRING(30),
        allowNull: false,
        defaultValue: "application-pending",
      },
      previous_internal_status: {
        type: DataTypes.STRING(30),
        allowNull: false,
        defaultValue: "application-pending",
      },
      current_internal_status: {
        type: DataTypes.STRING(30),
        allowNull: false,
        defaultValue: "application-pending",
      },
      reason_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_masters",
          key: "master_id",
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      currently_assigned_to_type: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      currently_assigned_to_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      created_by_type: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
    },
    {
      sequelize,
      tableName: "trx_customer_connection_status_history",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_customer_connection_status_history_id",
          unique: true,
          fields: [{ name: "customer_connection_status_history_id" }],
        },
      ],
    }
  );
};
