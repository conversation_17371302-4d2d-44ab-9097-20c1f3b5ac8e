const User = require('../model/user.model');
const { literal } = require('sequelize');
const getQueryParams = async (
  reqBody,
  searchCondition,
  attributeObj = {},
  extraWhere = {}
) => {
  let page = reqBody.page ? reqBody.page : 1;
  let limit = reqBody.limit ? reqBody.limit : 10;
  let offset = (page - 1) * limit;
  const limitCond = {
    limit: limit,
    offset: offset,
  };
  let orderBy = reqBody.orderBy ? reqBody.orderBy : 'DESC';
  let orderName = reqBody.orderName ? reqBody.orderName : 'created_at';
  let orderData = [[orderName, orderBy]];
  let attributes = attributeObj;
  const params = {
    where: {
      is_deleted: 0,
      ...searchCondition,
      ...extraWhere,
    },
  };
  if (orderData && orderData.length > 0) {
    params.order = orderData;
  }
  if (limitCond) {
    (params.offset = limitCond['offset']), (params.limit = limitCond['limit']);
  }
  if (attributes) {
    params.attributes = attributes;
  }
  if (reqBody && reqBody.all_records) {
    return {
      where: {
        is_deleted: 0,
      },
    };
  }
  return params;
};



const getMediaJoinParams = async (params) => {
  return params;
};
module.exports = {
  getQueryParams,
  getMediaJoinParams,
};
