const { sendSuccess, sendError } = require('../helpers/responseHelper');
const meterReadingService = require('../services/meter-reading-service');

exports.meterReadingHistoryList = async (req, res) => {
  try {
    // Ensure required fields are provided
    if (!req.query.customer_connection_id ) {
      return sendError(req, res, {}, 'customer_connection_id is required.');
    }

    // Call service layer with req.body and req.files
    const result = await meterReadingService.getMeterReadingHistoryListService(req.query);
    if (!result.success) {
      return sendError(req, res, null, "Internal server error",result.statusCode || 500);
    }

    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.meterReadingDetails = async (req, res) => {
  try {
    
    const result = await meterReadingService.getMeterReadingDetailsService(req.query);
    if (!result.success) {
      return sendError(req, res, null, "Internal server error",result.statusCode || 500);
    }

    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.meterReadingUploads = async (req, res) => {
  try {

    // Ensure files are uploaded
    if (!req.files) {
      return sendError(req, res, {}, 'Please upload meter reading Image.');
    }
    // Call service layer with req.body and req.files
    const result = await meterReadingService.processMeterReadingImage(req.body, req.files);
    if (!result.success) {
      return sendError(req, res, null, "Internal server error",result.statusCode || 500);
    }
    sendSuccess(req, res, result, 'Documents uploaded and Redis cache updated successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.verifyMeterReading = async (req, res) => {
  try {

    // Call service layer
    const result = await meterReadingService.verifyMeterReadingService(req.body);
    if (!result.success) {
      return sendError(req, res, null, "Internal server error",result.statusCode || 500);
    }

    sendSuccess(req, res, result, 'Meter reading verification successful.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.meterReadingUploadDetailsFromCache = async (req, res) => {
  try {

    // Call service layer
    const result = await meterReadingService.getMeterReadingUploadDetailsFromCache(req.query);
    // if (!result.success) {
    //   return sendError(req, res, {upload_status:result.upload_status}, result.message,result.statusCode || 500);
    // }

    sendSuccess(req, res, result, 'Meter reading Uploaded successful');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.meterReadingSaveDetailsFromCache = async (req, res) => {
  try {

    // Call service layer
    const result = await meterReadingService.storeMeterReadingFromCacheToDB(req.body);
    if (!result.success) {
      return sendError(req, res, null, "Internal server error",result.statusCode || 500);
    }

    sendSuccess(req, res, result, 'Meter reading Get successful.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.joinMeterReadingDetails = async (req, res) => {
  try {
    
    const result = await meterReadingService.getJointMeterReadingDetailsService(req.query);
    if (!result.success) {
      return sendError(req, res, null, "Internal server error",result.statusCode || 500);
    }

    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};