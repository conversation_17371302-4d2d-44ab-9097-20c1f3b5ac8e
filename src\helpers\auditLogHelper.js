const { dbModels } = require('../database/connection');
const ResLog = dbModels.api_log;
 
 
const createResLog = async (errorLogData) => {
  return await ResLog.create(errorLogData);
};
 
const ResponseLog = async (req, resData) => {
  try {
    if(process.env.IS_AUDIT == "true"){
      const { module_type, old_record, new_record, action, api_name, end_point } = resData;
      let createdBy = req?.body?.created_by || req?.user?.user_id;
      await createResLog({
        module_type: module_type,
        old_record: old_record !== null ? JSON.stringify(old_record) : null,
        new_record: new_record !== null ? JSON.stringify(new_record) : null,
        action: action,
        api_name: api_name,
        endpoint: end_point,
        ip_address: req.body.ip_address,
        browser: req.body.browser,
        created_by: createdBy,
        updated_by: createdBy
      }); 
    }    
  } catch (err) {
    console.error(err.message);
  }
};
 
module.exports = {
  ResponseLog,
};