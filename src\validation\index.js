
//* validators/index.js
const customer = require('./customer-validation')
const customerProfile = require('./customer-profile-validation')
const dashBoard = require('./dash-board-validation')
const master = require('./master-validation')
const payment = require('./payment-validation')
const pincode = require('./pincode-validation')
const meterReading = require('./meter-reading-validation')
const helpSupport = require('./help-support-validation')
const serviceRequest = require('./service-request-validation')
module.exports = {
  "customer-validation": customer,
  "customer-profile-validation": customerProfile,
  "dashBoard-validation": dashBoard,
  "master-validation": master,
  "payment-validation": payment,
  "pincode-validation": pincode,
  "meter-reading-validation": meterReading,
  "help-support-validation": helpSupport,
  "service-request-validation": serviceRequest,
}
