const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_tpi_dma_mapping', {
    tpi_dma_mapping_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    tpi_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_admin',
        key: 'admin_id'
      },
      unique: "UK_trxtpidmamapping_tpi_id_dma_id"
    },
    dma_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_admin',
        key: 'admin_id'
      },
      unique: "UK_trxtpidmamapping_tpi_id_dma_id"
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_tpi_dma_mapping',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_tpi_dma_mapping_id",
        unique: true,
        fields: [
          { name: "tpi_dma_mapping_id" },
        ]
      },
      {
        name: "UK_trxtpidmamapping_tpi_id_dma_id",
        unique: true,
        fields: [
          { name: "tpi_id" },
          { name: "dma_id" },
        ]
      },
    ]
  });
};
