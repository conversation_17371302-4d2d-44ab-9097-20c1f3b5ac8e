const { validate } = require('express-validation');
const thirdPartyController = require('../../controllers/third-party-controller');
const verifyToken = require('../../middleware/auth');
const decrypt = require('../../middleware/encrypt-decrypt');
const express = require('express');
const {
  getCityValidation,
} = require('../../validation/third-party-validation');
const thirdPartyRoutes = express.Router();

thirdPartyRoutes.post(
  '/get-state',
  verifyToken,
  decrypt,
  thirdPartyController.getState
);
thirdPartyRoutes.post(
  '/get-city',
  verifyToken,
  decrypt,
  validate(getCityValidation),
  thirdPartyController.getCity
);
module.exports = { thirdPartyRoutes };
