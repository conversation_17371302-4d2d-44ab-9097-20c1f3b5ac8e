const authRepository = require("../repository/auth-repository");
const jwtHelper = require("../helpers/jwtHelper");
const {
  encryptPassword,
  decryptPassword,
} = require("../utils/encrypt-decrypt");
const redisHelper = require("../helpers/redisHelper");
const { generateOtp, addTimeToDate } = require("../utils/commonFn");
const otpHelper = require("../helpers/smsEmailHelper");
const axios = require("axios");

const formatMessage = (name, otp) => {
  return `
             <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333;">
                 <p>Dear <strong>${name}</strong>,</p>
     
                 <p>Your One-Time Password (OTP) for logging into the portal is:</p>
                 <h2 style="color: #2E86C1; text-align: left;">${otp}</h2>
     
                 <p>This OTP is valid for the next <strong>10 minutes</strong>. Please do not share it with anyone for security reasons.</p>
     
                 <p>Best regards,</p>
                 <p><strong>BPCL</strong></p>
             </div>
         `;
};

const login = async (userName, password) => {
  try {
    let user;

    // Simple check for email vs mobile number
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userName);

    if (isEmail) {
      user = await authRepository.findUserByEmail(userName);
    } else {
      user = await authRepository.findUserByMobile(userName);
    }

    if (!user) {
      throw new Error("Invalid email or password");
    }
    if (user.is_active == 0) {
      throw new Error("The account is not active.");
    }

    // Decrypt stored password and compare
    const decryptedStoredPassword = decryptPassword(user.password.toString());
    if (password != decryptedStoredPassword) {
      throw new Error("Invalid email or password");
    }
    const redisKey = redisHelper.otpForMstAdminLogin(userName);
    const redisExpiryTime = 10 * 60;
    // Generate OTP & expiry time
    //const otp = generateOtp();
    const otp = "123456";
    const otpExpireDate = addTimeToDate(new Date(), 10, "minutes");
    const redisData = await redisHelper.getData(redisKey);
    if (redisData) {
      await redisHelper.deleteData(redisKey);
    }

    const setDataInRedis = await redisHelper.setData(
      redisKey,
      {
        email: user.email,
        mobile: user.mobile,
        otp,
        otpExpireDate,
      },
      redisExpiryTime
    );

    if (!setDataInRedis) {
      throw new Error("Failed to set data in Redis.");
    }

    if (user.email) {
      //send email
      const otpResult = await otpHelper.sendEmailOTP(
        user.email,
        "Login Otp",
        formatMessage(user.email, otp)
      );
      console.log("OTP Response:", otpResult);
    }
    // if(user.mobile){
    //   //send sms
    //   const otpResult = await otpHelper.sendSmsOTP(user.mobile, template);
    //   console.log("OTP Response:", otpResult);
    // }

    const { password: _, ...userData } = user.toJSON();

    let extendedFields = {};
    const extraAdminData = await authRepository.getExtendedAdminData(user.email);
    if (extraAdminData) {
      extendedFields = extraAdminData;
    }

    const extendedUserData = {
      ...userData,
      ...extendedFields,
    };
    return { user: extendedUserData };
  } catch (error) {
    throw error;
  }
};

const verifyOTP = async (req) => {
  try {
    const { username, otp } = req.body;

    let user;

    // Simple check for email vs mobile number
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(username);

    if (isEmail) {
      console.log("Email folder.");
      user = await authRepository.findUserByEmail(username);
    } else {
      console.log("Mobile folder.");
      user = await authRepository.findUserByMobile(username);
    }

    if (!user) {
      throw new Error("Invalid email or password");
    }

    const redisKey = redisHelper.otpForMstAdminLogin(username);
    const redisData = await redisHelper.getData(redisKey);

    if (!redisData) {
      throw new Error("OTP has been expired.");
    }
    if (redisData.otp.toString() !== otp) {
      throw new Error("Invalid OTP");
    }

    let userData;
    // Add subAdminRole for bpcl-sub-admin users
    if (user.user_role === "bpcl-sub-admin") {
      const subAdminRole = await authRepository.getSubAdminRole(user.admin_id);
      const plainUser = user.get({ plain: true });
      user = {
        ...plainUser,
        subAdminRole
      };
      const { password: _, ...rest } = user;
      userData = rest;
    }
    else {
      const { password: _, ...rest } = user.toJSON();
      userData = rest;
    }


    const tokenResult = await jwtHelper.createToken(userData);

    if (tokenResult.error) {
      throw new Error("Failed to generate token: " + tokenResult.error);
    }
    await authRepository.updateLastLogin(user.email);

    return { user: userData, token: tokenResult.token };
  } catch (error) {
    console.error("Error in verifyOTP:", error.message);
    throw error;
  }
};
const resetPassword = async (req) => {
  try {
    const { email, current_password, new_password } = req.body;
    const user = await authRepository.findUserByEmail(email);

    const decryptedStoredPassword = decryptPassword(user.password);
    if (current_password != decryptedStoredPassword) {
      throw new Error("Invalid current password.");
    }

    const encyptPassword = encryptPassword(new_password);
    console.log("encyptPassword--->", encyptPassword);
    const updateInDb = await authRepository.updatePasswordByEmail(
      user.email,
      encyptPassword
    );

    const response = {
      email: user.email,
    };

    return response;
  } catch (error) {
    console.error("Error in verifyOTP:", error.message);
    throw error;
  }
};

const verifyMicrosoftToken = async (accessToken) => {
  try {

    const response = await axios.get(`https://graph.microsoft.com/v1.0/me`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });
    if (response.error) {
      throw new Error(response.error || "Error in response generation.")
    }

    const email = response.data.mail;

    if (!email) {
      throw new Error("Invalid Token: Email not found in the token.");
    }

    const existUser = await authRepository.findUserByEmail(email);
    if (!existUser) {
      throw new Error("User not found.");
    }

    const { password: _, ...userData } = existUser.toJSON();
    userData.loginType = 'sso'

    const tokenResult = await jwtHelper.createToken(userData);

    if (tokenResult.error) {
      throw new Error("Failed to generate token: " + tokenResult.error);
    }

    return { user: userData, token: tokenResult.token };

  } catch (error) {
    console.error("Error in verifyMicrosoftToken:", error.message);
    throw new Error("Authentication failed during SSO login: " + error.message);
  }
};



module.exports = { login, verifyOTP, resetPassword, verifyMicrosoftToken };
