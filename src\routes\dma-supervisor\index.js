const express = require('express');
const { validate } = require('express-validation');
const verifyToken = require('../../middleware/auth');
const { restrictToRoles } = require('../../middleware/auth');
const { createDmaSupervisorValidation, deleteSupervisorsValidation, getAreaValidation,getSupervisorValidation, getAllSupervisorsValidation } = require('../../validation/dma-validation');
const dmaSupervisorController = require('../../controllers/dma-supervisor-controller');
const { uploadSingle } = require("../../middleware/newMulter");
const dmaSupervisorRoutes = express.Router();

dmaSupervisorRoutes.post(
    '/create', 
    verifyToken, 
    restrictToRoles('contractor'), 
    uploadSingle,
    validate(createDmaSupervisorValidation), 
    dmaSupervisorController.createSupervisor,
    
);

dmaSupervisorRoutes.get(
    '/:supervisorId', 
    verifyToken, 
    restrictToRoles('contractor'), 
    dmaSupervisorController.getSupervisorById
);

dmaSupervisorRoutes.post(
    '/list/all', 
    verifyToken, 
    restrictToRoles('contractor'), 
    validate(getSupervisorValidation),
    dmaSupervisorController.getAllSupervisors
);

dmaSupervisorRoutes.post(
    '/delete-multiple',
    verifyToken,
    restrictToRoles('contractor'),
    validate(deleteSupervisorsValidation),
    dmaSupervisorController.deleteSupervisors
);

dmaSupervisorRoutes.post(
  "/get-contractor-areas",
  verifyToken,
  validate(getAreaValidation),
  dmaSupervisorController.getAreaList
);

dmaSupervisorRoutes.post(
    '/supervisors-list', 
    verifyToken, 
    restrictToRoles('contractor'), 
    validate(getAllSupervisorsValidation),
    dmaSupervisorController.getAllSupervisorsList
);

module.exports = dmaSupervisorRoutes;