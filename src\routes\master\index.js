const { validate } = require('express-validation');
const verifyToken = require('../../middleware/auth');
const express = require('express');
const masterRoute = express.Router();
const decrypt = require('../../middleware/encrypt-decrypt');
const masterController = require('../../controllers/master-controller');

const {
  issuecategoryTypeListValidation,
  getGovtSchemeValidation,
  getProofMasterValidation,
  getResonMasterValidation,
  getServiceRateValidation
} = require('../../validation/master-validation');

masterRoute.post('/issue-type-list',validate(issuecategoryTypeListValidation),masterController.issueTypeList);
masterRoute.get('/govt-scheme-list', validate(getGovtSchemeValidation), masterController.getGovtSchemeList);
masterRoute.get('/proof-master-list', validate(getProofMasterValidation), masterController.getProofMasterList); // GET method for listing
masterRoute.post('/master-list', validate(getResonMasterValidation), masterController.getMasterList); 
masterRoute.post('/service-rates',validate(getServiceRateValidation), masterController.getServiceRate); 


module.exports = masterRoute;
