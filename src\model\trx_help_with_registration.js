const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_help_with_registration",
    {
      help_with_registration_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      bpcl_id: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      available_pngareas_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_available_pngareas",
          key: "available_pngareas_id",
        },
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      temp_registration_id: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      flat_no: {
        type: DataTypes.STRING(5),
        allowNull: false,
      },
      landmark: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      block_no: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      floor_no: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      building_house_name: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      fullname: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      address: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      pincode: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      area: {
        type: DataTypes.STRING(40),
        allowNull: false,
      },
      mobile: {
        type: DataTypes.STRING(12),
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: "not-started",
      },
      customer_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: "assigned",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_help_with_registration",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_help_with_registration_id",
          unique: true,
          fields: [{ name: "help_with_registration_id" }],
        },
      ],
    }
  );
};
