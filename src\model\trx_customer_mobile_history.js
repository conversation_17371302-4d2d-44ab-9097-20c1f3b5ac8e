const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_customer_mobile_history', {
    customer_mobile_history_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    old_mobile_no: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    new_mobile_no: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    old_customer_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer',
        key: 'customer_id'
      }
    },
    new_customer_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer',
        key: 'customer_id'
      }
    },
    customer_connection_ids: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_customer_mobile_history',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_customer_mobile_history_id",
        unique: true,
        fields: [
          { name: "customer_mobile_history_id" },
        ]
      },
    ]
  });
};
