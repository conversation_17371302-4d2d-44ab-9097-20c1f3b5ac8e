const { PORT } = require("./config");
const createServer = require("./src/utils/server");
const { ValidationError } = require("express-validation");
// const cors = require("cors");

const StartServer = async () => {
  const app = await createServer();
  app.use(function (err, req, res, next) {
    if (err instanceof ValidationError) {
      return res.status(err.statusCode).json(err);
    }
    return res.status(500).json(err);
  });
  app
    .listen(PORT, () => {
      console.log(`listening to port ${PORT}`);
    })
    .on("error", (err) => {
      console.log(err);
      process.exit();
    });
};

module.exports = StartServer();
