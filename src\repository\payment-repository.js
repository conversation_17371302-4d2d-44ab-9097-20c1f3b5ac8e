const { dbModels } = require('../database/connection');
const { Op, Sequelize } = require('sequelize');

// OLd current bill
// const getCurrentBillDetails = async (payload) => {
//   const date = new Date(new Date().setMonth(new Date().getMonth() - 1))
//     .toISOString()
//     .split('T')[0];
//   const connection = await dbModels.trx_customer_connection.findOne({
//     where: { bp_id: payload.bp_id }, // Filter by bpid
//     attributes: ['customer_connection_id'],
//   });
//   try {
//     const billDetails = await dbModels.trx_connection_meter_bills.findAll({
//       where: {
//         customer_connection_id: connection.customer_connection_id,
//         // customer_meter_id: payload.customer_meter_id
//       },
//       include: [
//         {
//           model: dbModels.trx_customer_meter_readings,
//           as: 'customer_meter_reading',
//           required: true,
//           include: [
//             {
//               model: dbModels.mst_cycle,
//               as: 'cycle',
//               required: true,
//               where: {
//                 start_date: { [Op.lte]: date }, // Start date should be <= given date
//                 end_date: { [Op.gte]: date }, // End date should be >= given date
//               },
//             },
//             {
//               model: dbModels.trx_customer_meter,
//               as: 'customer_meter',
//               required: true,
//               attributes: [
//                 'customer_meter_id',
//                 'customer_connection_id',
//                 'mapped_lmc_customer_connection_id',
//                 'meter_type',
//                 'meter_no',
//                 'meter_installation_datetime',
//                 'reason',
//                 'meter_status',
//               ],
//             },
//           ],
//         },
//       ],
//       raw: true,
//       nest: true,
//     });

//     return billDetails.length > 0 ? billDetails : null;
//   } catch (error) {
//     console.error('Error fetching bill details:', error);
//     throw error;
//   }
// };



// old current bill function //
const getCurrentBillDetailsOLD = async (payload) => {
  const date = new Date(new Date().setMonth(new Date().getMonth() - 1))
    .toISOString()
    .split('T')[0];

  const connection = await dbModels.trx_customer_connection.findOne({
    where: { bp_id: payload.bp_id },
    attributes: ['customer_connection_id'],
  });

  try {
    const [billDetails, extraPipelineCharges, securityDepositDetails] = await Promise.all([
      dbModels.trx_connection_payments.findOne({
        where: {
          customer_connection_id: connection.customer_connection_id,
          section_type: "meter-readings",
        },
        include: [
          {
            model: dbModels.trx_customer_meter_readings,
            as: 'customer_meter_reading',
            required: true,
            include: [
              {
                model: dbModels.mst_cycle,
                as: 'cycle',
                required: true,
              },
              {
                model: dbModels.trx_customer_meter,
                as: 'customer_meter',
                required: true,
                attributes: [
                  'customer_meter_id',
                  'customer_connection_id',
                  'mapped_lmc_customer_connection_id',
                  'meter_type',
                  'meter_no',
                  'meter_installation_datetime',
                  'reason',
                  'meter_status',
                ],
              },
              {
                model: dbModels.trx_customer_connection,
                as: "customer_connection",
                attributes: ["first_name", "last_name", "mobile", "bp_id"],
                include: [
                  {
                    model: dbModels.trx_customer_connection_address,
                    as: "trx_customer_connection_addresses",
                    attributes: ["connection_address",
                      "connection_pincode",
                      "connection_landmark",
                      "connection_block_no",
                      "connection_floor_no",
                      "connection_house_no",
                      "connection_state",
                      "connection_city",
                      "connection_district"],
                  },
                ]
              },
            ],
          },
        ],
        order: [['created_at', 'DESC']],
        raw: true,
        nest: true,
      }),

      dbModels.trx_connection_payments.findOne({
        where: {
          customer_connection_id: connection.customer_connection_id,
          section_type: "extra-pipeline",
        },
        raw: true,
      }),

      dbModels.trx_connection_payments.findOne({
        where: {
          customer_connection_id: connection.customer_connection_id,
          section_type: "security-deposit",
        },
        raw: true,
      }),
    ]);

    return {
      billDetails,
      extraPipelineCharges,
      securityDepositDetails
    };
  } catch (error) {
    console.error('Error fetching bill details:', error);
    throw error;
  }
};

// New current bill
const getCurrentBillDetails = async (payload) => {
  const date = new Date(new Date().setMonth(new Date().getMonth() - 1))
    .toISOString()
    .split('T')[0];
 
  const connection = await dbModels.trx_customer_connection.findOne({
    where: { bp_id: payload.bp_id },
    attributes: ['customer_connection_id'],
  });
 
  try {
    const [billDetails, extraPipelineCharges, securityDepositDetails] = await Promise.all([
      dbModels.trx_connection_payments.findOne({
        where: {
          customer_connection_id: connection.customer_connection_id,
          section_type: "meter-readings",
        },
        include: [
          {
            model: dbModels.trx_connection_meter_bills,
            as: 'meter_bill',
            required: true,
            include: [
              {
                model: dbModels.trx_customer_meter_readings,
                as: 'customer_meter_reading',
                required: true,
                include: [
                  {
                    model: dbModels.mst_cycle,
                    as: 'cycle',
                    required: true,
                  },
                  {
                    model: dbModels.trx_customer_meter,
                    as: 'customer_meter',
                    required: true,
                    attributes: [
                      'customer_meter_id',
                      'customer_connection_id',
                      'mapped_lmc_customer_connection_id',
                      'meter_type',
                      'meter_no',
                      'meter_installation_datetime',
                      'reason',
                      'meter_status',
                    ],
                  },
                  {
                    model: dbModels.trx_customer_connection,
                    as: "customer_connection",
                    attributes: ["first_name", "last_name", "mobile", "bp_id"],
                    include: [
                      {
                        model: dbModels.trx_customer_connection_address,
                        as: "trx_customer_connection_addresses",
                        attributes: ["connection_address",
                          "connection_pincode",
                          "connection_landmark",
                          "connection_block_no",
                          "connection_floor_no",
                          "connection_house_no",
                          "connection_state",
                          "connection_city",
                          "connection_district"],
                      },
                    ]
                  },
                ],
              },
            ]
          }
        ],
        order: [['created_at', 'DESC']],
        raw: true,
        nest: true,
      }),
 
      dbModels.trx_connection_payments.findOne({
        where: {
          customer_connection_id: connection.customer_connection_id,
          section_type: "extra-pipeline",
        },
        raw: true,
      }),
 
      dbModels.trx_connection_payments.findOne({
        where: {
          customer_connection_id: connection.customer_connection_id,
          section_type: "security-deposit",
        },
        raw: true,
      }),
    ]);
 
    const today = new Date();
    let currentStatus = '';
    let outstandingAmount = 0;
 
   
 
    const pendingBills = await dbModels.trx_connection_payments.findAll({
      where: {
        customer_connection_id: connection.customer_connection_id,
        section_type: 'meter-readings',
        payment_status: 'pending',
      },
      attributes: ['due_amount','due_date'],
      order: [['due_date', 'ASC']], // get the latest record
      raw: true,
    });
 
    billDetails.previousDueAmount = pendingBills.reduce(
      (total, bill) => total + parseFloat(bill.due_amount || 0),
      0
    );
    const isPending = ['pending', 'inprogress'].includes(billDetails.payment_status);
    billDetails.totalOutstandingAmount = isPending ? (billDetails.previousDueAmount + billDetails.total_amount) : 0;
 
    console.log("pendingBills=", pendingBills);
   
 
    const dueDate = pendingBills[0]?.due_date
      ? new Date(pendingBills[0]?.due_date)
      : null;
 
    if (billDetails.payment_status === 'completed') {
      billDetails.currentStatus = 'paid';
    } else if (billDetails.payment_status === 'pending') {
      console.log("dueDate=",dueDate);
      console.log("today=",today);
      if (dueDate && dueDate < today) {
        billDetails.currentStatus = 'overdue';
      } else {
        billDetails.currentStatus = 'due';
      }
 
      const isOverdue = today > dueDate;
      billDetails.daysLeft = isOverdue ? 0 : Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
      billDetails.daysOverdue = isOverdue ? Math.floor((today - dueDate) / (1000 * 60 * 60 * 24)) : 0;
      const months = Math.floor(billDetails.daysOverdue / 30); // Converts to full months
      const remainingDays = billDetails.daysOverdue % 30;
 
      billDetails.overDueMonths = `${months} month(s) and ${remainingDays} day(s)`;
    }
 
   
    return {
      billDetails,
      extraPipelineCharges,
      securityDepositDetails
    };
  } catch (error) {
    console.error('Error fetching bill details:', error);
    throw error;
  }
};

const getPaymentHistoryDetails = async (payload) => {
  try {
    const whereCondition = {
      customer_connection_id: payload.customer_connection_id,
    };

    // const page = parseInt(payload.page) || 1;
    // let limit = parseInt(payload.limit) || 10;
    // const offset = (page - 1) * limit;

    // if (payload.timeFilter) {
    //   // limit = 500;
    //   const startDate = new Date();
    //   startDate.setMonth(startDate.getMonth() - payload.timeFilter);
    //   const endDate = new Date();

    //   whereCondition.created_at = {
    //     [Sequelize.Op.between]: [startDate, endDate],
    //   };
    // }

    const { rows: payments, count: totalRecords } =
      await dbModels.trx_connection_payments.findAndCountAll({
        where: whereCondition,
        include: [
          {
            model: dbModels.trx_customer_connection,
            as: 'customer_connection',
            attributes: [
              'first_name',
              'middle_name',
              'last_name',
              'email',
              'mobile',
            ],
            include: [
              {
                model: dbModels.trx_customer_connection_address,
                as: 'trx_customer_connection_addresses',
                attributes: [
                  'connection_address',
                  'connection_pincode',
                  'connection_landmark',
                  'connection_block_no',
                  'connection_floor_no',
                  'connection_house_no',
                  'connection_state',
                  'connection_city',
                  'connection_district',
                ],
              },
              {
                model: dbModels.trx_customer_meter,
                as: 'trx_customer_meters',
                attributes: ['meter_type'],
              },
            ],
          },
          {
            model: dbModels.trx_connection_meter_bills,
            as: 'meter_bill',
            attributes: ['customer_meter_reading_id'],
            include: [
              {
                model: dbModels.trx_customer_meter_readings,
                as: 'customer_meter_reading',
                attributes: ['reading_units'],
              },
            ],
          },
        ],
        attributes: [
          'connection_payments_id',
          'transaction_id',
          'base_amount',
          'gst_type',
          'gst_amount',
          'total_amount',
          'payment_status',
          'section_type',
          'meter_bill_id',
          'due_date',
          'reason',
          'payment_method',
          'paid_on',
          'created_at',
          'updated_at',
        ],
        // limit,
        // offset,
        order: [['created_at', 'DESC']],
      });

    let extraPipelineCharges = null;
    let securityDepositDetails = null;

    const updatedPayments = payments.map((payment) => {
      const json = payment.toJSON();
      const meters = json.customer_connection?.trx_customer_meters || [];
      const meterType = meters.length > 0 ? meters[0].meter_type : '';

      const enrichedPayment = {
        ...json,
        type:
          meterType.toLowerCase() === 'mechanical'
            ? 'Postpaid'
            : meterType.toLowerCase() === 'smart'
              ? 'Prepaid'
              : 'Unknown',
      };

      // Extract extraPipelineCharges
      if (json.section_type === 'extra-pipeline' && !extraPipelineCharges) {
        extraPipelineCharges = {
          connection_payments_id: json.connection_payments_id,
          transaction_id: json.transaction_id,
          base_amount: json.base_amount,
          gst_type: json.gst_type,
          gst_amount: json.gst_amount,
          total_amount: json.total_amount,
          payment_status: json.payment_status,
          section_type: json.section_type,
          meter_bill_id: json.meter_bill_id,
          due_date: json.due_date,
          reason: json.reason,
          payment_method: json.payment_method,
          paid_on: json.paid_on,
          created_at: json.created_at,
        };
      }

      // Extract securityDepositDetails
      if (json.section_type === 'security-deposit' && !securityDepositDetails) {
        securityDepositDetails = {
          connection_payments_id: json.connection_payments_id,
          transaction_id: json.transaction_id,
          base_amount: json.base_amount,
          gst_type: json.gst_type,
          gst_amount: json.gst_amount,
          total_amount: json.total_amount,
          payment_status: json.payment_status,
          section_type: json.section_type,
          meter_bill_id: json.meter_bill_id,
          due_date: json.due_date,
          reason: json.reason,
          payment_method: json.payment_method,
          paid_on: json.paid_on,
          created_at: json.created_at,
        };
      }

      return enrichedPayment;
    });

    return {
      totalRecords,
      // totalPages: Math.ceil(totalRecords / limit),
      // currentPage: page,
      updatedPayments,
      ...(extraPipelineCharges && { extraPipelineCharges }),
      ...(securityDepositDetails && { securityDepositDetails }),
    };
  } catch (error) {
    console.error('Error fetching payment details:', error);
    throw error;
  }
};

const checkNumberResult = async (payload) => {
  try {
    const result = await dbModels.trx_customer_connection.findOne({
      where: {
        bp_id: payload.bp_id,
        mobile: payload.number,
      },
    });
    // Return "yes" if found, otherwise "no"
    return result ? 'yes' : 'no';
  } catch (error) {
    console.error('Error fetching payment details:', error);
    throw error;
  }
};

// const getPaymentInvoiceDetails = async (payload) => {
//   try {
//     let payments = await dbModels.trx_connection_payments.findOne({
//       where: { connection_payments_id: payload.connection_payments_id }, // Filter by connection ID from payload
//       include: [
//         {
//           model: dbModels.trx_customer_connection,
//           as: 'customer_connection',
//           attributes: [
//             'first_name',
//             'middle_name',
//             'last_name',
//             'email',
//             'bpcl_id',
//             'mobile',
//           ],
//           include: [
//             {
//               model: dbModels.trx_customer_connection_address,
//               as: 'trx_customer_connection_addresses',
//               attributes: [
//                 'connection_address',
//                 'connection_pincode',
//                 'connection_landmark',
//                 'connection_block_no',
//                 'connection_floor_no',
//                 'connection_house_no',
//                 'connection_state',
//                 'connection_city',
//                 'connection_district',
//               ],
//             },
//             {
//               model: dbModels.trx_customer_meter,
//               as: 'trx_customer_meters',
//               attributes: ['meter_type'], // Fetch meter type from trx_customer_meter
//             },
//           ],
//         },
//         {
//           model: dbModels.trx_connection_meter_bills,
//           as: 'meter_bill', //
//           attributes: ['customer_meter_reading_id'],
//           include: [
//             {
//               model: dbModels.trx_customer_meter_readings,
//               as: 'customer_meter_reading', // ✅ Match association alias
//               attributes: ['reading_units'], // ✅ Fetch reading_units
//             },
//           ],
//         },
//       ],
//       attributes: [
//         'connection_payments_id',
//         'customer_connection_id',
//         'transaction_id',
//         'base_amount',
//         'gst_type',
//         'gst_amount',
//         'total_amount',
//         'payment_status',
//         'reason',
//         'payment_method',
//         'paid_on',
//         'created_at',
//         'updated_at',
//       ],
//     });
//     console.log("before payments=", payments);
    
//     const extraPipelineCharges = await dbModels.trx_connection_payments.findOne({
//       where: { customer_connection_id: payments.customer_connection_id ,
//         section_type: "extra-pipeline",
//       },
//     }); 
    
//     const securityDepositDetails = await dbModels.trx_connection_payments.findOne({
//       where: { customer_connection_id: payments.customer_connection_id,
//         section_type: "security-deposit",
//       },
//     });

//     payments.extraPipelineCharges = extraPipelineCharges;
//     payments.securityDepositDetails = securityDepositDetails;

//     console.log("AFTER payments=", payments);

//     const meters = payments.customer_connection?.trx_customer_meters || [];
//     const meterType = meters.length > 0 ? meters[0].meter_type : '';
//     const updatedPayment = {
//       ...payments.toJSON(),
//       type:
//         meterType.toLowerCase() === 'mechanical'
//           ? 'Postpaid'
//           : meterType.toLowerCase() === 'smart'
//             ? 'Prepaid'
//             : 'Unknown',
//     };
//     return updatedPayment;
//   } catch (error) {
//     console.error('Error fetching payment details:', error);
//     throw error;
//   }
// };


const getPaymentInvoiceDetails = async (payload) => {
  try {
    const payments = await dbModels.trx_connection_payments.findOne({
      where: { connection_payments_id: payload.connection_payments_id },
      include: [
        {
          model: dbModels.trx_customer_connection,
          as: 'customer_connection',
          attributes: [
            'first_name',
            'middle_name',
            'last_name',
            'email',
            'bpcl_id',
            'mobile',
          ],
          include: [
            {
              model: dbModels.trx_customer_connection_address,
              as: 'trx_customer_connection_addresses',
              attributes: [
                'connection_address',
                'connection_pincode',
                'connection_landmark',
                'connection_block_no',
                'connection_floor_no',
                'connection_house_no',
                'connection_state',
                'connection_city',
                'connection_district',
              ],
            },
            {
              model: dbModels.trx_customer_meter,
              as: 'trx_customer_meters',
              attributes: ['meter_type'],
            },
          ],
        },
        {
          model: dbModels.trx_connection_meter_bills,
          as: 'meter_bill',
          attributes: ['customer_meter_reading_id'],
          include: [
            {
              model: dbModels.trx_customer_meter_readings,
              as: 'customer_meter_reading',
              attributes: ['reading_units'],
            },
          ],
        },
      ],
      attributes: [
        'connection_payments_id',
        'customer_connection_id',
        'transaction_id',
        'base_amount',
        'gst_type',
        'gst_amount',
        'total_amount',
        'payment_status',
        'reason',
        'payment_method',
        'paid_on',
        'created_at',
        'updated_at',
      ],
    });

    if (!payments) {
      throw new Error('Payment record not found');
    }

    // Fetch extra pipeline charges and security deposit
    const [extraPipelineCharges, securityDepositDetails] = await Promise.all([
      dbModels.trx_connection_payments.findOne({
        where: {
          customer_connection_id: payments.customer_connection_id,
          section_type: 'extra-pipeline',
        },
      }),
      dbModels.trx_connection_payments.findOne({
        where: {
          customer_connection_id: payments.customer_connection_id,
          section_type: 'security-deposit',
        },
      }),
    ]);

    // Extract meter type
    const meters = payments.customer_connection?.trx_customer_meters || [];
    const meterType = meters.length > 0 ? meters[0].meter_type : '';

    // Final response object
    const updatedPayment = {
      ...payments.toJSON(),
      type:
        meterType.toLowerCase() === 'mechanical'
          ? 'Postpaid'
          : meterType.toLowerCase() === 'smart'
          ? 'Prepaid'
          : 'Unknown',
      extraPipelineCharges: extraPipelineCharges ? extraPipelineCharges.toJSON() : null,
      securityDepositDetails: securityDepositDetails ? securityDepositDetails.toJSON() : null,
    };

    return updatedPayment;
  } catch (error) {
    console.error('Error fetching payment details:', error);
    throw error;
  }
};

const updatePayment = async (updateParams,whereCond) => {
  try {
    const result = await dbModels.trx_connection_payments.update(
      updateParams,
      {
        where: whereCond,
      }
    );
    return result;
  } catch (error) {
    console.error('Error updating payment status:', error);
    throw error;
  }
}
module.exports = {
  getCurrentBillDetails,
  getPaymentHistoryDetails,
  checkNumberResult,
  getPaymentInvoiceDetails,
  updatePayment
};
