const { validate } = require("express-validation");
const verifyToken = require("../../middleware/auth");
const { restrictToRoles } = require('../../middleware/auth');
const express = require("express");
const tpiRoute = express.Router();
const decrypt = require("../../middleware/encrypt-decrypt");
const tpiController = require("../../controllers/tpi-controller");
const lmcController = require("../../controllers/lmc-controller");


const {
  getTpiCasesSummaryValidation,
  getTpiApplicationValidation,
  getTpiStatusCountValidation,
  getFeasibilityInstallationCustomerDetailsValidation,
  customerStatusHistoryValidation,
  getTpiCustomerReviewList,
  lmcWorkApprovalStatusUpdateValidation,
  customerStatusUpdateValidation,
  lmcStatusDataListValidation,
  lmcStatusUpdateValidation,
  lmcOnboardCountValidation,
  getCustomerApplicationDetailsValidation,
  getPendingOnboardingListValidation,
  onboardingApprovalValidation,
  jmrApprovalStatusUpdateValidation,
  getTpiCategoryValidation,
  gaApprovalStatusUpdateValidation,
  commentDetailsTpiValidation
} = require('../../validation/TPI-validation');


tpiRoute.get(
  "/application-status-summary",
  validate(getTpiCasesSummaryValidation),
  tpiController.tpiCasesSummery
);
tpiRoute.get(
  "/application-status",
  validate(getTpiApplicationValidation),
  tpiController.tpiApplicationStatus
);
tpiRoute.post(
  "/customer-review-list",
  verifyToken,
  restrictToRoles("sales"),
  validate(getTpiCustomerReviewList),
  tpiController.tpiCustomeReview
);

tpiRoute.post('/status-count', verifyToken, validate(getTpiStatusCountValidation), tpiController.tpiStatusCount);

// tpiRoute.post('/feasibility-installation-customer-details',verifyToken, validate(getFeasibilityInstallationCustomerDetailsValidation), tpiController.feasibilityInstallationCustomerDetail);
tpiRoute.post(
  "/get-feasibility-details-tpi",
  verifyToken,
  restrictToRoles("engineer"),
  tpiController.getFeasibilityDetailsTpi
);
tpiRoute.post(
  "/get-installation-form-data-tpi",
  verifyToken,
  restrictToRoles("engineer"),
  tpiController.getInstallationFormDataTpi
);
tpiRoute.post('/customer-status-history', 
  verifyToken, 
  validate(customerStatusHistoryValidation), 
  tpiController.customerStatusHistory);

tpiRoute.post('/lmc-work-approval-status-update', 
  verifyToken, 
  restrictToRoles('engineer'), 
  validate(lmcWorkApprovalStatusUpdateValidation),
  tpiController.lmcWorkApprovalStatusUpdate);

tpiRoute.post('/customer-application-details', 
  verifyToken, 
  validate(getCustomerApplicationDetailsValidation),
  tpiController.getCustomerApplicationDetails
);

tpiRoute.post(
  "/customer-status-update",
  verifyToken,
  validate(customerStatusUpdateValidation),
  tpiController.customerStatusUpdate
);

tpiRoute.post('/lmc-status-data-list', 
  verifyToken, 
  validate(lmcStatusDataListValidation), 
  tpiController.lmcStatusDataList);

tpiRoute.post(
  "/lmc-status-update",
  verifyToken,
  validate(lmcStatusUpdateValidation),
  tpiController.lmcStatusUpdate
);

tpiRoute.post(
  '/pending-onboarding-list',
  verifyToken,
  validate(getPendingOnboardingListValidation),
  tpiController.getPendingOnboardingList
);

tpiRoute.post(
  '/onboarding-approval',
  verifyToken,
  validate(onboardingApprovalValidation),
  tpiController.handleOnboardingApproval
);

tpiRoute.post('/onboard-status-count', 
  verifyToken, 
  validate(lmcOnboardCountValidation),
  tpiController.onboardStatusCount);

tpiRoute.get(
  '/customer-application-status-count',
  verifyToken,
  tpiController.getCustomerApplicationStatusCount
);

tpiRoute.post(
  "/jmr-approval-list",
  verifyToken,
  tpiController.getJmrApprovalList
);

tpiRoute.post(
  "/jmr-approval-status-count", 
  verifyToken, 
  tpiController.getJmrApprovalStatusCount
);

tpiRoute.post(
  "/jmr-submission-details", 
  verifyToken, 
  tpiController.getJmrSubmissionDetails
);

tpiRoute.post(
  "/jmr-approval-status-update",
  verifyToken,
  validate(jmrApprovalStatusUpdateValidation),
  tpiController.updateJmrApprovalStatus
);

tpiRoute.post(
  "/categories",
  verifyToken,
  validate(getTpiCategoryValidation),
  tpiController.getTpiCategories
);

tpiRoute.post(
  "/update-ga-approval",
  verifyToken,
  validate(gaApprovalStatusUpdateValidation),
  tpiController.updateGaApproval
);

tpiRoute.post(
  "/comment-details-tpi",
  verifyToken,
  validate(commentDetailsTpiValidation),
  tpiController.getCommentDetailsTpi
);

module.exports = { tpiRoute };
