const { Joi } = require('express-validation');

const createApplicationStatusValidation = {
  body: Joi.object({
    application_status_value: Joi.string()
      .min(3)
      .max(200)
      .required()
      .messages({
        'string.min': 'Application status value must have at least 3 characters',
        'string.max': 'Application status value should not exceed 200 characters',
        'any.required': 'Application status value is required',
      }),
    application_status_label: Joi.string()
      .min(3)
      .max(200)
      .required()
      .messages({
        'string.min': 'Application status label must have at least 3 characters',
        'string.max': 'Application status label should not exceed 200 characters',
        'any.required': 'Application status label is required',
      }),
    is_active: Joi.number()
      .integer()
      .optional()
      .default(1)
      .messages({
        'number.base': 'is_active must be an integer',
        'number.integer': 'is_active must be an integer',
      }),
    is_delete: Joi.number()
      .integer()
      .optional()
      .default(1)
      .messages({
        'number.base': 'is_delete must be an integer',
        'number.integer': 'is_delete must be an integer',
      }),
  }),
};

const updateApplicationStatusValidation = {
  body: Joi.object({
    application_status_id: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'application_status_id must be a number',
        'number.integer': 'application_status_id must be an integer',
        'any.required': 'application_status_id is required',
      }),
    application_status_value: Joi.string()
      .max(200)
      .optional()
      .messages({
        'string.max': 'Application status value should not exceed 200 characters',
      }),
    application_status_label: Joi.string()
      .max(200)
      .optional()
      .messages({
        'string.max': 'Application status label should not exceed 200 characters',
      }),
    is_active: Joi.number()
      .integer()
      .optional()
      .messages({
        'number.base': 'is_active must be an integer',
        'number.integer': 'is_active must be an integer',
      }),
    is_delete: Joi.number()
      .integer()
      .optional()
      .messages({
        'number.base': 'is_delete must be an integer',
        'number.integer': 'is_delete must be an integer',
      }),
  }),
};

const deleteApplicationStatusValidation = {
  body: Joi.object({
    application_status_id: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'application_status_id must be a number',
        'number.integer': 'application_status_id must be an integer',
        'any.required': 'application_status_id is required',
      }),
  }),
};

const getApplicationStatusValidation = {
  body: Joi.object({
    page: Joi.number()
      .integer()
      .min(1)
      .allow(null, '')
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be greater than 0',
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .allow(null, '')
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be greater than 0',
      }),
    search: Joi.string()
      .max(200)
      .allow(null, '')
      .messages({
        'string.max': 'Search query should not exceed 200 characters',
      }),
    orderBy: Joi.string()
      .valid('asc', 'desc')
      .allow(null, '')
      .messages({
        'any.only': 'OrderBy must be either "asc" or "desc"',
      }),
    orderName: Joi.string()
      .allow(null, '')
      .messages({
        'string.base': 'OrderName must be a string',
      }),
  }),
};

module.exports = {
  createApplicationStatusValidation,
  updateApplicationStatusValidation,
  deleteApplicationStatusValidation,
  getApplicationStatusValidation,
};
