const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_meter_reading_units",
    {
      mru_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      mro_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_meter_reading_orders",
          key: "mro_id",
        },
        unique: "UK_mru_mroticket_bpid_csym_ceym",
      },
      meter_serial_no: {
        type: DataTypes.STRING(15),
        allowNull: false,
      },
      bp_id: {
        type: DataTypes.STRING(12),
        allowNull: false,
        unique: "UK_mru_mroticket_bpid_csym_ceym",
      },
      previous_meter_reading: {
        type: DataTypes.DECIMAL(10, 3),
        allowNull: true,
      },
      business_partner_no: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      business_partner_name: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      address: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      mro_creation_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      internal_meter_reading_doc_id: {
        type: DataTypes.STRING(40),
        allowNull: true,
      },
      cycle_start_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      cycle_end_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      cycle_start_year_month: {
        type: DataTypes.STRING(6),
        allowNull: true,
        unique: "UK_mru_mroticket_bpid_csym_ceym",
      },
      cycle_end_year_month: {
        type: DataTypes.STRING(6),
        allowNull: true,
        unique: "UK_mru_mroticket_bpid_csym_ceym",
      },
      scheduled_mr_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      remarks: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      meter_reading_reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      equipment_no: {
        type: DataTypes.STRING(12),
        allowNull: true,
      },
      mru_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      fo_supervisor_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_meter_reading_units",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_trxmru_mru_id",
          unique: true,
          fields: [{ name: "mru_id" }],
        },
        {
          name: "UK_mru_mroticket_bpid_csym_ceym",
          unique: true,
          fields: [
            { name: "mro_id" },
            { name: "bp_id" },
            { name: "cycle_start_year_month" },
            { name: "cycle_end_year_month" },
          ],
        },
      ],
    }
  );
};
