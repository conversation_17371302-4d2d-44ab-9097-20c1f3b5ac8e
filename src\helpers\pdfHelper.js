const PDFDocument = require('pdfkit');

// const generatePDFBuffer = (payment) => {
//   return new Promise((resolve, reject) => {
//     const doc = new PDFDocument({ size: 'A4', margin: 50 });
//     const buffers = [];

//     doc.on('data', (chunk) => buffers.push(chunk));
//     doc.on('end', () => resolve(Buffer.concat(buffers)));
//     doc.on('error', reject);

//     const customer = payment.customer_connection;
//     const address = customer.trx_customer_connection_addresses[0] || {};
//     const meter = customer.trx_customer_meters[0] || {};
//     const meter_bill = payment.meter_bill;
//     const extraPipelineCharges = payment.extraPipelineCharges;
//     const securityDepositDetails = payment.securityDepositDetails;
//     // **Header**
//     doc.fontSize(20).text('Payment Invoice', { align: 'left' }).moveDown(0.3);
//     doc
//       .fontSize(12)
//       .fillColor('gray')
//       .text(
//         `The below payment was made on ${new Date(payment.paid_on).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' })}`,
//         { align: 'left' }
//       );
//     doc.fillColor('black').moveDown(1);

//     // **Draw a rounded rectangle for details section**
//     doc.roundedRect(50, doc.y, 500, 200, 5).stroke();

//     // **Customer & Account Details (Two-column layout)**
//     const startX = 65;
//     let startY = doc.y + 10;
//     const colGap = 260;

//     doc.fontSize(12);
//     doc
//       .text(`Name:`, startX, startY, { continued: true })
//       .font('Helvetica-Bold')
//       .text(
//         ` ${customer.first_name} ${customer.middle_name} ${customer.last_name}`
//       );
//     doc
//       .font('Helvetica')
//       .text(`Account Type:`, startX + colGap, startY, { continued: true })
//       .font('Helvetica-Bold')
//       .text(` ${payment.type}`);

//     startY += 20;
//     doc
//       .font('Helvetica')
//       .text(`BP ID:`, startX, startY, { continued: true })
//       .font('Helvetica-Bold')
//       .text(` ${payment.transaction_id}`);
//     doc
//       .font('Helvetica')
//       .text(`Meter Usage:`, startX + colGap, startY, { continued: true })
//       .font('Helvetica-Bold')
//       .text(` ${meter_bill.customer_meter_reading.reading_units || 'N/A'} SCM`);

//     startY += 20;
//     doc
//       .font('Helvetica')
//       .text(`Address:`, startX, startY, { continued: true })
//       .font('Helvetica-Bold')
//       .text(
//         ` ${address.connection_city}, ${address.connection_district}...${address.connection_pincode}`
//       );

//     startY += 40;
//     doc.moveTo(startX, startY).lineTo(550, startY).stroke(); // **Divider Line**

//     startY += 15;
//     doc
//       .font('Helvetica')
//       .text(`Method:`, startX, startY, { continued: true })
//       .font('Helvetica-Bold')
//       .text(
//         ` ${payment.payment_method.charAt(0).toUpperCase() + payment.payment_method.slice(1)}`
//       );
//     doc
//       .font('Helvetica')
//       .text(`Amount Due:`, startX, startY + 20, { continued: true })
//       .font('Helvetica-Bold')
//       .text(` ₹${payment.base_amount}`);
//     doc
//       .font('Helvetica')
//       .text(`GST 5%:`, startX, startY + 40, { continued: true })
//       .font('Helvetica-Bold')
//       .text(` ₹${payment.gst_amount}`);

//     // **Total Amount to Pay Box**
//     doc.roundedRect(320, startY + 20, 200, 40, 5).fill('#E8F1FF');
//     doc
//       .fillColor('black')
//       .fontSize(12)
//       .text(`Total Amount to Paid:`, 330, startY + 30);
//     doc
//       .fontSize(16)
//       .font('Helvetica-Bold')
//       .text(`₹${payment.total_amount}`, 330, startY + 50);

//     doc.end();
//   });
// };

const generatePDFBuffer = (payment) => {
  return new Promise((resolve, reject) => {
    const doc = new PDFDocument({ size: 'A4', margin: 50 });
    const buffers = [];

    doc.on('data', (chunk) => buffers.push(chunk));
    doc.on('end', () => resolve(Buffer.concat(buffers)));
    doc.on('error', reject);

    const customer = payment.customer_connection;
    const address = customer.trx_customer_connection_addresses[0] || {};
    const meter_bill = payment.meter_bill;
    const extraPipelineCharges = payment.extraPipelineCharges;
    const securityDepositDetails = payment.securityDepositDetails;

    // === HEADER ===
    doc.fontSize(20).text('Payment Invoice', { align: 'left' }).moveDown(0.3);
    doc
      .fontSize(12)
      .fillColor('gray')
      .text(
        `The below payment was made on ${new Date(
          payment.paid_on
        ).toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })}`,
        { align: 'left' }
      );
    doc.fillColor('black').moveDown(1);

    // === DRAW MAIN BOX ===
    const startX = 50;
    let startY = doc.y;
    const boxWidth = 500;
    doc.roundedRect(startX, startY, boxWidth, 380, 5).stroke();

    const innerX = startX + 15;
    let currentY = startY + 15;
    const colGap = 250;

    doc.fontSize(12).font('Helvetica');
    doc
      .text(`Name:`, innerX, currentY, { continued: true })
      .font('Helvetica-Bold')
      .text(
        ` ${customer.first_name} ${customer.middle_name} ${customer.last_name}`
      );
    doc
      .font('Helvetica')
      .text(`Account Type:`, innerX + colGap, currentY, { continued: true })
      .font('Helvetica-Bold')
      .text(` ${payment.type}`);

    currentY += 20;
    doc
      .font('Helvetica')
      .text(`BP ID:`, innerX, currentY, { continued: true })
      .font('Helvetica-Bold')
      .text(` ${payment.transaction_id}`);
    doc
      .font('Helvetica')
      .text(`Meter Usage:`, innerX + colGap, currentY, { continued: true })
      .font('Helvetica-Bold')
      .text(
        ` ${meter_bill?.customer_meter_reading?.reading_units || 'N/A'} SCM`
      );

    currentY += 20;
    doc
      .font('Helvetica')
      .text(`Address:`, innerX, currentY, { continued: true })
      .font('Helvetica-Bold')
      .text(
        ` ${address.connection_city}, ${address.connection_district}, ${address.connection_pincode}`
      );

    currentY += 30;
    doc
      .moveTo(innerX, currentY)
      .lineTo(startX + boxWidth - 15, currentY)
      .stroke(); // Divider

    currentY += 15;
    doc
      .font('Helvetica')
      .text(`Method:`, innerX, currentY, { continued: true })
      .font('Helvetica-Bold')
      .text(` ${payment.payment_method}`);
    doc
      .font('Helvetica')
      .text(`Amount Due:`, innerX + colGap, currentY, { continued: true })
      .font('Helvetica-Bold')
      .text(` ₹${payment.base_amount}`);

    currentY += 20;
    doc
      .font('Helvetica')
      .text(`GST 5%:`, innerX, currentY, { continued: true })
      .font('Helvetica-Bold')
      .text(` ₹${payment.gst_amount}`);

    // === Extra Pipeline Charges (with separators) ===
    if (extraPipelineCharges) {
      currentY += 20;
      doc
        .moveTo(innerX, currentY)
        .lineTo(startX + boxWidth - 15, currentY)
        .stroke(); // Line before section

      currentY += 10;
      doc
        .font('Helvetica-Bold')
        .text(`Extra Pipeline Charges`, innerX, currentY);

      currentY += 15;
      doc
        .font('Helvetica')
        .text(
          `Transaction ID: ${extraPipelineCharges.transaction_id}`,
          innerX,
          currentY
        );
      doc.text(
        `Base Amount: ₹${extraPipelineCharges.base_amount}`,
        innerX + colGap,
        currentY
      );

      currentY += 15;
      doc.text(
        `GST Type: ${extraPipelineCharges.gst_type?.toUpperCase()}`,
        innerX,
        currentY
      );
      doc.text(
        `GST Amount: ₹${extraPipelineCharges.gst_amount}`,
        innerX + colGap,
        currentY
      );

      currentY += 15;
      doc.text(
        `Total Amount: ₹${extraPipelineCharges.total_amount}`,
        innerX,
        currentY
      );
      doc.text(
        `Payment Status: ${extraPipelineCharges.payment_status}`,
        innerX + colGap,
        currentY
      );

      currentY += 20;
      doc
        .moveTo(innerX, currentY)
        .lineTo(startX + boxWidth - 15, currentY)
        .stroke(); // Line after section
    }
    // === Security Deposit Details (Inline) ===
    if (securityDepositDetails) {
      currentY += 30;
      doc
        .font('Helvetica-Bold')
        .text(`Security Deposit Details`, innerX, currentY);

      currentY += 15;
      doc
        .font('Helvetica')
        .text(
          `Transaction ID: ${securityDepositDetails.transaction_id}`,
          innerX,
          currentY
        );
      doc.text(
        `Base Amount: ₹${securityDepositDetails.base_amount}`,
        innerX + colGap,
        currentY
      );

      currentY += 15;
      doc.text(
        `GST Type: ${securityDepositDetails.gst_type?.toUpperCase()}`,
        innerX,
        currentY
      );
      doc.text(
        `GST Amount: ₹${securityDepositDetails.gst_amount}`,
        innerX + colGap,
        currentY
      );

      currentY += 15;
      doc.text(
        `Total Amount: ₹${securityDepositDetails.total_amount}`,
        innerX,
        currentY
      );
      doc.text(
        `Payment Status: ${securityDepositDetails.payment_status}`,
        innerX + colGap,
        currentY
      );
    }

    // === TOTAL AMOUNT TO PAY ===
    currentY += 40;
    doc.roundedRect(320, currentY, 200, 40, 5).fill('#E8F1FF');
    doc
      .fillColor('black')
      .fontSize(12)
      .text(`Total Amount to Paid:`, 330, currentY + 10);
    doc
      .fontSize(16)
      .font('Helvetica-Bold')
      .text(`₹${payment.total_amount}`, 330, currentY + 25);

    doc.end();
  });
};

module.exports = { generatePDFBuffer };
