const { Op, Sequelize } = require("sequelize");
const { dbModels, sequelize } = require("../database/connection");

const getMROTicketDetails = async (mro_ticket) => {
  try {
    const mroRecords = await dbModels.mst_meter_reading_orders.findAll({
      attributes: [
        "mro_id",
        "scheduled_mr_date",
        "created_at",
        "mro_status"
      ],
      where: { mro_ticket },
      include: [
        {
          model: dbModels.trx_meter_reading_units,
          as: "trx_meter_reading_units",
          attributes: [
            "mru_id",
            "scheduled_mr_date",
            "created_at",
            "mru_status",
            "equipment_no",
            "bp_id"
          ],
          required: false,
          include: [
            {
              model: dbModels.trx_customer_meter_readings,
              as: "trx_customer_meter_readings",
              attributes: [
                "meter_reading_file",
                "created_at",
                "current_reading",
                "declared_by",
              ],
              required: false,
            },
            {
              model: dbModels.trx_customer_connection,
              as: "customer_connection",
              attributes: [
                "customer_connection_id",
                "first_name",
                "last_name",
                "email",
                "mobile",
                "bp_id",
                "ca_no"
              ],
              required: false,
              include: [
                {
                  model: dbModels.trx_customer_connection_address,
                  as: "trx_customer_connection_addresses",
                  attributes: [
                    "permanent_address",
                    "connection_address",
                    "connection_pincode",
                    "connection_city",
                    "connection_state",
                    "connection_district",
                  ],
                  required: false,
                },
                {
                  model: dbModels.trx_customer_meter,
                  as: "trx_customer_meters",
                  attributes: [
                    "meter_no",
                    "meter_status",
                    "meter_type",
                    "customer_meter_id",
                  ],
                  required: false,
                }
              ]
            }
          ],
        },
      ],
      raw : true
    });
    return mroRecords;
  } catch (error) {
    console.error("Error in getMROTicketDetails:", error);
    return { error: "Internal server error" };
  }
};

const getDetailedMROCustomerInfo = async (customerConnectionId) => {
  try {
    return await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id: customerConnectionId },
      attributes: [
        "customer_connection_id",
        "first_name",
        "last_name",
        "email",
        "mobile",
        "bp_id",
        "ca_no",
      ],
      include: [
        {
          model: dbModels.trx_customer_connection_address,
          as: "trx_customer_connection_addresses",
          attributes: [
            "permanent_address",
            "connection_address",
            "connection_pincode",
            "connection_city",
            "connection_state",
            "connection_district",
          ],
          required: false,
        },
        {
          model: dbModels.trx_meter_reading_units,
          as: "meter_reading_units",
          attributes: [
            "mru_id",
            "scheduled_mr_date",
            "created_at",
            "mru_status",
            "equipment_no",
            "bp_id",
            "remarks"
          ],
          required: false,
          include: [
            {
              model: dbModels.trx_customer_meter_readings,
              as: "trx_customer_meter_readings",
              attributes: [
                "meter_reading_file",
                "created_at",
                "current_reading",
                "declared_by",
              ],
              required: false,
            },
            {
              model: dbModels.mst_meter_reading_orders,
              as: "mro",
              attributes: [
                "mro_id",
                "scheduled_mr_date",
                "created_at",
                "mro_status",
                "mro_ticket"
              ],
              required: false
            },
          ],
        },
        {
          model: dbModels.trx_customer_meter,
          as: "trx_customer_meters",
          attributes: ["meter_no", "meter_status", "customer_meter_id"],
          required: false,
        },
      ],
      raw: true,
    });
  } catch (error) {
    console.error("Repository Error - getDetailedMROCustomerInfo:", error);
    throw error;
  }
};

const getGeographicalAreas = async () => {
  try {
    const geographicalAreas = await dbModels.mst_geographical_areas.findAll({
      attributes: [
        "geographical_area_id",
        "region_name",
        "state_id",
        "city_id",
        "ga_area_name",
        "is_active",
        "created_at",
        "created_by",
        "updated_at",
        "updated_by",
      ],
      order: [["region_name", "ASC"]], // Order by region_name, ascending
    });

    return geographicalAreas;
  } catch (error) {
    console.error("Error in getGeographicalAreas:", error);
    throw error;
  }
};

const getFOMetricsGA = async (admin_id, geographical_area_id = null) => {
  try {
    const amAreaWhere = {
      admin_id,
      is_active: true,
    };

    const amgaWhere = {
      admin_id,
      is_active: true,
    };

    if (geographical_area_id) {
      amgaWhere.ga_area_id = geographical_area_id;
    }

    // Count mapped geographical areas
    const totalAssignedGAs = await dbModels.trx_admin_mapped_ga_areas.count({
      where: amgaWhere,
    });

    // Count mapped areas inside GAs
    const totalAssignedAreas = await dbModels.trx_admin_mapped_areas.count({
      where: amAreaWhere,
      include: [
        {
          model: dbModels.mst_areas,
          as: 'mappedArea',
          attributes: [],
          where: {
            ...(geographical_area_id && { geographical_area_id }),
            is_active: true,
          },
          required: true,
        },
      ],
    });

    // Include logic for MROs
    const mroInclude = [ ];

    if (geographical_area_id) {
      mroInclude.push({
        model: dbModels.mst_admin,
        as: 'fo_supervisor',
        attributes: [],
        required: true,
        include: [
          {
            model: dbModels.trx_admin_mapped_areas,
            as: 'mappedAreas',
            attributes: [],
            include: [
              {
                model: dbModels.mst_areas,
                as: 'mappedArea',
                attributes: [],
                where: {
                  geographical_area_id,
                  is_active: true,
                },
                required: true,
              },
            ],
            required: true,
          },
        ],
      });
    }

    // MRO metrics summary
    const mroSummary = await dbModels.mst_meter_reading_orders.findAll({
      where: { fo_supervisor_id: admin_id },
      attributes: [
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('mro_ticket'))), 'total_mros'],
        [sequelize.literal(`SUM(CASE WHEN mro_status = 'completed' THEN 1 ELSE 0 END)`), 'total_completed'],
        [sequelize.literal(`SUM(CASE WHEN mro_status != 'completed' THEN 1 ELSE 0 END)`), 'total_pending'],
      ],
      include: mroInclude,
      raw: true,
    });

    return {
      totalAssignedGAs,
      totalAssignedAreas,
      mroSummary: mroSummary[0] || null,
    };
  } catch (error) {
    console.error('Error in getFOMetricsGA:', error);
    throw error;
  }
};


const getMROById = async (mru_id) => {
  return await dbModels.trx_meter_reading_units.findOne({
    where: { mru_id },
    attributes: ["cycle_start_date", "cycle_end_date"],
  });
};

const getCustomerMeter = async (meter_no) => {
  return await dbModels.trx_customer_meter.findOne({
    where: {
      meter_status: "completed",
      meter_no : meter_no
    },
    attributes: ["customer_meter_id","customer_connection_id"],
  });
};

const getCycle = async (start_date, end_date) => {
  return await dbModels.mst_cycle.findOne({
    where: {
      start_date: new Date(start_date),
      end_date: new Date(end_date),
    },
  });
};

const createCycle = async (start_date, end_date) => {
  return await dbModels.mst_cycle.create({
    start_date: new Date(start_date),
    end_date: new Date(end_date),
  });
};

const getLastReading = async (customer_meter_id) => {
  return await dbModels.trx_customer_meter_readings.findOne({
    where: {
      customer_meter_id,
      meter_reading_status: "completed",
    },
    order: [["created_at", "DESC"]],
  });
};

const getReadingForCycle = async (customer_meter_id, cycle_id) => {
  return await dbModels.trx_customer_meter_readings.findOne({
    where: {
      customer_meter_id,
      cycle_id,
    },
  });
};

const updateReading = async (reading_id, data) => {
  return await dbModels.trx_customer_meter_readings.update(data, {
    where: { customer_meter_reading_id: reading_id },
  });
};

const getReadingById = async (reading_id) => {
  return await dbModels.trx_customer_meter_readings.findOne({
    where: { customer_meter_reading_id: reading_id },
  });
};

const createReading = async (data) => {
  return await dbModels.trx_customer_meter_readings.create(data);
};

const getMROListing = async (admin_id, search, limit = 10, offset = 0, geographical_area_id, area_ids) => {
  try {
    const whereClause = {
      fo_supervisor_id: admin_id,
    };

    if (search) {
      whereClause.mro_ticket = {
        [Op.iLike]: `%${search}%`,
      };
    }

    const areaWhereCondition = area_ids.length > 0 ? { area_id: { [Op.in]: area_ids, } } : {};

    const adminInclude = {
      model: dbModels.mst_admin,
      as: "fo_supervisor",
      attributes: [],
      required: true,
      include: [
        {
          model: dbModels.trx_admin_mapped_ga_areas,
          as: 'trx_admin_mapped_ga_areas',
          attributes: [],
          required: !!geographical_area_id, // only require join if area filter is needed
          where: {
            ...(geographical_area_id && { ga_area_id: geographical_area_id }),
            is_active: true,
          },
        },
        {
          model: dbModels.trx_admin_mapped_areas,
          as: "trx_admin_mapped_areas",
          attributes: [],
          required: area_ids.length > 0,
          where: areaWhereCondition,
        },
      ],
    };

    const allData = await dbModels.mst_meter_reading_orders.findAll({
      where: whereClause,
      attributes: [
        "mro_ticket",
        [sequelize.fn("MIN", sequelize.col("mst_meter_reading_orders.total_mru")), "total_customers"],
        [sequelize.fn("MIN", sequelize.col("mst_meter_reading_orders.created_at")), "created_at"],
        [sequelize.fn("MIN", sequelize.col("mst_meter_reading_orders.scheduled_mr_date")), "scheduled_mr_date"],
        [sequelize.fn("MIN", sequelize.col("mst_meter_reading_orders.updated_at")), "assigned_date"],
      ],
      include: [
        adminInclude,
      ],
      group: ["mro_ticket"],
      raw: true,
    });

    // Apply pagination manually
    const paginatedRows = allData.slice(offset, offset + limit);

    return {
      count: allData.length,
      rows: paginatedRows,
    };
  } catch (error) {
    console.error("Repo error in getMROListing:", error);
    throw new Error("Failed to fetch MRO listing");
  }
};

const updateMROStatusAndRemark = async (mru_id, status, remark ) => {
  try {
    const [updatedCount] = await dbModels.trx_meter_reading_units.update(
      {
        mro_status: status,
        remarks: remark
      },
      {
        where: { mru_id },
      }
    );

    return updatedCount > 0; // true if update happened, false otherwise
  } catch (error) {
    console.error('Error updating MRO status and remark:', error);
    throw error;
  }
};


module.exports = {
  getMROTicketDetails,
  getMROListing,
  getDetailedMROCustomerInfo,
  getGeographicalAreas,
  getFOMetricsGA,
  getMROById,
  getCustomerMeter,
  getCycle,
  createCycle,
  getLastReading,
  getReadingForCycle,
  updateReading,
  getReadingById,
  createReading,
  getMROListing,
  updateMROStatusAndRemark
};
