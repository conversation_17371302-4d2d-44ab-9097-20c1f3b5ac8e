const { validate } = require("express-validation");
const verifyToken = require("../../middleware/auth");
const express = require("express");
const lmcRoute = express.Router();
const decrypt = require("../../middleware/encrypt-decrypt");
const lmcController = require("../../controllers/lmc-controller");
const { restrictToRoles } = require('../../middleware/auth');

const {
  getLmcConnectionValidation,
  getLmcCasesSummaryValidation,
  getLmcCasesValidation,
  getLmcFeasibilityTicketsValidation,
  getLmcFeasibilityVisitValidation,
  getLmcScheduleVisitValidation,
  submitDrawingValidation,
  getLmcUploadValidation,
  submitLmcJMRValidation,
  getLmcCommissioningTicketsValidation,
  getLmcMeterReadingUploadValidation,
  formDetailsValidation,
  installationSubmitSchemaValidation,
  feasibilitySubmitSchemaValidation,
  commissioningSubmitSchemaValidation,
  uploadReadingValidation,
  getStageSubmissionDetailsValidation,
  getInstallationSubmissionDetailsValidation
} = require("../../validation/lmc-validation");
const { uploadSingle, uploadMultiple, uploadMultipleTemp, uploadSingleTemp } = require("../../middleware/newMulter");

lmcRoute.post(
  "/connection-list",
  verifyToken,
  validate(getLmcConnectionValidation),
  lmcController.lmcList
);
lmcRoute.get(
  "/cases-summary",
  verifyToken,
  validate(getLmcCasesSummaryValidation),
  lmcController.lmcCasesSummery
);
lmcRoute.get(
  "/cases",
  verifyToken,
  validate(getLmcCasesValidation),
  lmcController.lmcCases
);
lmcRoute.get(
  "/feasibility-tickets",
  verifyToken,
  validate(getLmcFeasibilityTicketsValidation),
  lmcController.lmcFeasibilityTickets
);
lmcRoute.get(
  "/feasibility-visit",
  verifyToken,
  validate(getLmcFeasibilityVisitValidation),
  lmcController.getLmcStageDetails
);
lmcRoute.get(
  "/view-submission",
  verifyToken,
  validate(getLmcFeasibilityVisitValidation),
  lmcController.getLmcStageDetails
);
lmcRoute.post(
  "/schedule-visit",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(getLmcScheduleVisitValidation),
  lmcController.lmcScheduleVisit
);
lmcRoute.post(
  "/pipeline-drawing-upload",
  verifyToken,
  restrictToRoles("supervisor"),
  uploadSingle,
  validate(getLmcUploadValidation),
  lmcController.lmcDrawingUpload
);
lmcRoute.post(
  "/pipeline-submit-details",
  verifyToken,
  restrictToRoles("supervisor"),
  uploadSingle,
  validate(submitDrawingValidation),
  lmcController.lmcPipelineDrawing
);
lmcRoute.get(
  "/installation-tickets",
  verifyToken,
  validate(getLmcFeasibilityTicketsValidation),
  lmcController.lmcInstallationTickets
);
lmcRoute.get(
  "/commissioning-tickets",
  verifyToken,
  validate(getLmcCommissioningTicketsValidation),
  lmcController.lmcCommissioningTickets
);
lmcRoute.post(
  "/submit-jmr",
  verifyToken,
  restrictToRoles("supervisor"),
  uploadSingle,
  validate(submitLmcJMRValidation),
  lmcController.lmcJMRSubmit
);
lmcRoute.get(
  "/jmr-submission",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(getLmcFeasibilityVisitValidation),
  lmcController.lmcJMRSubmissionDetails
);
lmcRoute.post(
  "/upload-reading",
  verifyToken,
  restrictToRoles("supervisor"),
  uploadSingleTemp,
  validate(uploadReadingValidation),
  lmcController.lmcMeterReadingUploads
);

lmcRoute.post(
  "/form-details",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(formDetailsValidation),
  lmcController.getFormDetails
);
lmcRoute.post(
  "/feasibility-submit-details",
  verifyToken,
  restrictToRoles("supervisor"),
  uploadMultiple,
  validate(feasibilitySubmitSchemaValidation),
  lmcController.submitFeasibilityFrom
);
lmcRoute.post(
  "/get-feasibility-details",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(getStageSubmissionDetailsValidation),
  lmcController.getFeasibilityDetails
);

lmcRoute.post(
  "/commissioning-submit-details",
  verifyToken,
  restrictToRoles("supervisor"),
  uploadMultiple,
  validate(commissioningSubmitSchemaValidation),
  lmcController.submitCommissioningForm
);

lmcRoute.post(
  "/get-commissioning-details",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(getStageSubmissionDetailsValidation),
  lmcController.getCommissioningFormDetails
);
lmcRoute.post(
  "/installation-submit-details",
  verifyToken,
  restrictToRoles("supervisor"),
  lmcController.saveInstallationDetailsToCache
);
lmcRoute.post(
  "/installation-uploads",
  verifyToken,
  restrictToRoles("supervisor"),
  uploadMultipleTemp,
  lmcController.InstallationDocumentsUpload
);

lmcRoute.post(
  "/installation-submit",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(installationSubmitSchemaValidation),
  lmcController.saveInstallationDetails
);
lmcRoute.post(
  "/get-installation-details",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(getInstallationSubmissionDetailsValidation),
  lmcController.getInstallationDetails
);

lmcRoute.post(
  "/get-pipeline-checklist",
  verifyToken,
  restrictToRoles("supervisor"),
  lmcController.getPipelineChecklist
);

lmcRoute.post(
  "/get-installation-form-data",
  verifyToken,
  restrictToRoles("supervisor"),
  lmcController.getInstallationFormData
);

lmcRoute.post(
  "/installation-prefill",
  verifyToken,
  restrictToRoles("supervisor"),
  lmcController.getInstallationPrefill
);




module.exports = { lmcRoute };
