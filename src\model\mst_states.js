const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_states",
    {
      state_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      state_code: {
        type: DataTypes.STRING(5),
        allowNull: true,
      },
      state_title: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      state_description: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      status: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
    },
    {
      sequelize,
      tableName: "mst_states",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_state_id",
          unique: true,
          fields: [{ name: "state_id" }],
        },
      ],
    }
  );
};
