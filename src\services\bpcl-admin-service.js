const bpclAdminRepository = require("../repository/bpcl-admin-repository");
const crypto = require("crypto");
const { encryptPassword } = require("../utils/encrypt-decrypt");
const otpHelper = require("../helpers/smsEmailHelper");
const { generatePassword } = require("../utils/commonFn");

const formatMessageForApprovalToContractor = (
  name,
  role_name,
  areas,
  mobile,
  email,
  password
) => {
  const areaList = areas
    .map((areaMap, index) => {
      const areaName = areaMap.area?.area_name ?? "Unknown Area";
      return `${areaName}`;
    })
    .join(" ");

  return `
    <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333;">
        <p>Dear <strong>${name}</strong>,</p>

        <strong>Congratulations!</strong> You've been onboarded as a ${role_name} member for area(s) ${areaList}</p>

        <p>Please find below onboarding details:</p>

        <p><strong>${role_name} Member Details:</strong></p>
        <p>Name: ${name}</p>
        <p>Contact: ${mobile}</p>

        <p><strong>Login Details:</strong></p>
        <p>${role_name} Application Link: <a href="http://qa.api.png.hellobpcl.in">http://qa.api.png.hellobpcl.in</a></p>
        <p>Login ID: ${email}</p> 
        <p>Login Password: ${password}</p>

        <p>Best regards,</p>
        <p><strong>BPCL Onboarding Team</strong></p>
    </div>
  `;
};

const formatMessageForApprovalRejectToSubAdmin = (
  name,
  contractor_name,
  areas,
  ga_name,
  action,
  approvedBy
) => {
  const areaList = areas
    .map((areaMap, index) => {
      const areaName = areaMap.area?.area_name ?? "Unknown Area";
      return `${areaName}`;
    })
    .join(" ");

  return `
    <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333;">
        <p>Dear <strong>${name}</strong>,</p>

        <p>Onboarding of contractor ${contractor_name} for area(s): ${areaList}, under Geographical Area (GA): ${ga_name} has been ${action} by sub admin ${approvedBy}.</p>

        <p>Best regards,</p>
        <p><strong>BPCL Onboarding Team</strong></p>
    </div>
  `;
};
const formatMessageForSubAdminOnboarding = (
  name,
  gaAdminSubType,
  gaAreaName,
  mobile,
  formDate,
  toDate,
  password
) => {
  return `
  <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333;">
      <p>Dear <strong>${name}</strong>,</p>

      <strong>Congratulations!</strong> You've been onboarded as a ${gaAdminSubType} for area(s) ${gaAreaName}</p>

      <p>Please find below onboarding details:</p>
      <p><strong>Contractor Details:</strong></p>
      <p>Name: ${name}</p>
      <p>Contact: ${mobile}</p>
      <p>Password: ${password}</p>
      <p>GA Admin application Link: <a href="http://qa.api.png.hellobpcl.in">http://qa.api.png.hellobpcl.in</a></p>

      <p>You are have been assigned sub admin role for the time period from ${formDate} to ${toDate} </p>

      <p>You can login with your existing BPCL credentials.</p>

      <p>Best regards,</p>
      <p><strong>BPCL Onboarding Team</strong></p>
  </div>
`;
};
const formatMessageAreaUpdateInDiffGa = (name, gaAreaName, areas) => {
  console.log("areas", areas);
  const areaList = areas
    .map((areaMap, index) => {
      const areaName = areaMap.area_name ?? "Unknown Area";
      return `${areaName}`;
    })
    .join(" ");

  return `
  <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333;">
      <p>Dear <strong>${name}</strong>,</p>

      <strong>Congratulations!</strong> You've been onboarded for area(s) ${areaList} in ${gaAreaName} GA</p>

      <p>You can login with your existing BPCL credentials.</p>

      <p>Best regards,</p>
      <p><strong>BPCL Onboarding Team</strong></p>
  </div>
`;
};

const formatIsoToDateString = (isoString) => {
  const [year, month, day] = isoString.split("T")[0].split("-");
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  return `${day} ${months[parseInt(month) - 1]} ${year}`;
};

const checkUserExistenceInGAService = async ({ mobile, email, ga_area_id }) => {
  try {
    const existenceStatus =
      await bpclAdminRepository.checkMobileAndEmailExistInGARepo(
        mobile,
        email,
        ga_area_id
      );

    const emailMessage = existenceStatus.email.exists
      ? existenceStatus.email.sameGa
        ? "User already exists with this email in the same GA area"
        : "User already exists with this email in a different GA area"
      : "";

    const mobileMessage = existenceStatus.mobile.exists
      ? existenceStatus.mobile.sameGa
        ? "User already exists with this mobile number in the same GA area"
        : "User already exists with this mobile number in a different GA area"
      : "";

    const response = {
      status: !(existenceStatus.email.exists || existenceStatus.mobile.exists),
      message:
        existenceStatus.email.exists || existenceStatus.mobile.exists
          ? "User already exists"
          : "User does not exist with provided mobile or email",
      data: {
        email: {
          exists: existenceStatus.email.exists,
          sameGa: existenceStatus.email.sameGa,
          message: emailMessage,
        },
        mobile: {
          exists: existenceStatus.mobile.exists,
          sameGa: existenceStatus.mobile.sameGa,
          message: mobileMessage,
        },
      },
    };

    return { data: response };
  } catch (error) {
    console.error("Error in checkUserExistenceInGAService:", error);
    throw error;
  }
};

const getChannelPartnersCount = async (body) => {
  try {
    return await bpclAdminRepository.getChannelPartnersCount(body);
  } catch (error) {
    console.error("Error in getChannelPartnersCount service:", error);
    throw error;
  }
};
const contractorOnboardingService = async (req) => {
  try {
    let persona_role_id;
    let user_roles;
    let contractorTypeForDb;

    switch (req.body.contractor_type) {
      case "TPI Sales":
        persona_role_id = 2;
        user_roles = "sales";
        contractorTypeForDb = "tpi";
        break;
      case "TPI Engineer":
        persona_role_id = 2;
        user_roles = "engineer";
        contractorTypeForDb = "tpi";
        break;
      case "LMC Contractor":
        persona_role_id = 3;
        user_roles = "contractor";
        contractorTypeForDb = "lmc";
        break;
      case "DMA Contractor":
        persona_role_id = 1;
        user_roles = "contractor";
        contractorTypeForDb = "dma";
        break;
      case "FO Contractor":
        persona_role_id = 4;
        user_roles = "contractor";
        contractorTypeForDb = "fo";
        break;
      default:
        console.log("Unknown contractor type");
        break;
    }

    let areaIds = req.body.area_id;

    // Normalize to array
    if (!Array.isArray(areaIds)) {
      areaIds = [areaIds];
    }

    if (
      req.body.contractor_type == "TPI Sales" ||
      req.body.contractor_type == "TPI Engineer"
    ) {
      const checkTpiPresentInArea =
        await bpclAdminRepository.checkTPISaleEngineerRepo(
          req.body.ga_id,
          areaIds,
          user_roles
        );
      console.log("checkTpiPresentInArea--->", checkTpiPresentInArea);
      if (checkTpiPresentInArea.length > 0) {
        return {
          status: false,
          message: `Please select different area. Selected area(s) have TPI ${user_roles} assigned already`,
        };
      }
    }

    const existingUser =
      await bpclAdminRepository.userExistenceCheckForContractorOnboardingInSameGa(
        req.body.mobile,
        req.body.email,
        req.body.ga_id
      );
    let isSameGA;
    if (existingUser)
      isSameGA = (existingUser.trx_admin_mapped_areas || []).some(
        (mappedArea) =>
          Number(mappedArea?.area?.geographical_area?.geographical_area_id) ===
          Number(req.body.ga_id)
      );

    if (
      existingUser &&
      isSameGA &&
      (existingUser.email === req.body.email ||
        existingUser.mobile === req.body.mobile)
    ) {
      const response = {
        email: {
          exists: existingUser.email === req.body.email,
          message:
            existingUser.email === req.body.email
              ? "User already exists with this email"
              : null,
        },
        mobile: {
          exists: existingUser.mobile === req.body.mobile,
          message:
            existingUser.mobile === req.body.mobile
              ? "User already exists with this mobile number"
              : null,
        },
      };

      const locationMap = new Map();
      const payloadGaId = Number(req.body.ga_id);

      (existingUser.trx_admin_mapped_areas || []).forEach((mappedArea) => {
        const area = mappedArea.area;
        const geo = area?.geographical_area;

        if (
          geo &&
          area?.area_name &&
          Number(geo.geographical_area_id) === payloadGaId
        ) {
          const geoId = geo.geographical_area_id;
          const geoName = geo.ga_area_name;

          if (!locationMap.has(geoId)) {
            locationMap.set(geoId, {
              geographical_area_id: geoId,
              ga_area_name: geoName,
              area_names: new Set(),
            });
          }

          locationMap.get(geoId).area_names.add(area.area_name);
        }
      });

      // Convert Sets to arrays
      const structuredLocation = Array.from(locationMap.values()).map(
        (item) => ({
          geographical_area_id: item.geographical_area_id,
          ga_area_name: item.ga_area_name,
          area_names: Array.from(item.area_names),
        })
      );

      response.location = structuredLocation;

      // 🚫 Stop execution and return
      return {
        status: false,
        message: "User already exists",
        data: response,
      };
    }

    if (
      existingUser &&
      !isSameGA &&
      (existingUser.email === req.body.email ||
        existingUser.mobile === req.body.mobile)
    ) {
      const updateArea = bpclAdminRepository.updateAreaForContractorRepo(
        existingUser.admin_id,
        req
      );
      if (updateArea) {
        const areaNameAndGeographicalArea =
          await bpclAdminRepository.fetchAreaNameAndGeographicalAreaRepo(
            req.body.area_id
          );
        const emailBody = formatMessageAreaUpdateInDiffGa(
          existingUser.first_name,
          areaNameAndGeographicalArea?.[0]?.geographical_area.ga_area_name,
          areaNameAndGeographicalArea
        );
        console.log("emailBody", emailBody);
        await otpHelper.sendEmailNotificationWithoutCc(
          existingUser.email,
          "Contractor Onboarding approved",
          emailBody
        );
      }

      console.log("updateArea", updateArea);
      return {
        status: true,
        message: "User already exists. Area has been updated.",
        data: updateArea,
      };
    }

    const result = await bpclAdminRepository.contractorOnboardingRepo(
      persona_role_id,
      user_roles,
      contractorTypeForDb,
      req
    );

    return result;
  } catch (error) {
    console.error("Error in contractorOnboardingService service:", error);
    throw error;
  }
};
const getAreaListService = async (req) => {
  try {
    const result = await bpclAdminRepository.getAreaListRepo(req);
    return result;
  } catch (error) {
    console.error("Error in getAreaListService service:", error);
    throw error;
  }
};
const getContractorPendingApprovalService = async (req) => {
  try {
    const result = await bpclAdminRepository.contractorPendingApprovalRepo(req);
    return result;
  } catch (error) {
    console.error("Error in getAreaListService service:", error);
    throw error;
  }
};
const acceptRejectService = async (req) => {
  try {
    const { ga_id, contractor_id, type, remark } = req.body;
    const user = req.user?.admin_id;
    console.log("remark Service-->", remark);

    let response;
    const contractor =
      await bpclAdminRepository.getContractorDetails(contractor_id);
    const subAdmin = await bpclAdminRepository.getSubAdminDetails(
      contractor.created_by
    );

    const name = `${contractor.first_name} ${contractor.last_name}`;
    const mobile = contractor.mobile;
    const email = contractor.email;
    const role_name = contractor.persona_role?.role_name ?? "Role";
    const gaName =
      contractor.trx_admin_mapped_areas?.[0]?.area.geographical_area
        .ga_area_name;
    const areas = contractor.trx_admin_mapped_areas || [];

    const subAdminName = subAdmin.first_name;
    const subAdminEmail = subAdmin.email;
    const approvedBy = `${req.user?.first_name} ${req.user?.last_name}`;

    if (type === "accept") {
      const generatedPassword = generatePassword();
      const encryptedPassword = encryptPassword(generatedPassword);

      const result = await bpclAdminRepository.acceptRepo(
        ga_id,
        contractor_id,
        encryptedPassword,
        user
      );

      if (result) {
        const emailBody = formatMessageForApprovalToContractor(
          name,
          role_name,
          areas,
          mobile,
          email,
          generatedPassword
        );
        const emailBodyForSubAdmin = formatMessageForApprovalRejectToSubAdmin(
          subAdminName,
          name,
          areas,
          gaName,
          "approved",
          approvedBy
        );

        await otpHelper.sendEmailNotificationWithoutCc(
          email,
          "Contractor Onboarding approved",
          emailBody
        );
        await otpHelper.sendEmailNotificationWithoutCc(
          subAdminEmail,
          "Contractor Onboarding approved",
          emailBodyForSubAdmin
        );

        response = { message: "Contractor has been accepted successfully" };
      }
    } else if (type === "reject") {
      const result = await bpclAdminRepository.rejectRepo(
        contractor_id,
        remark,
        user
      );

      if (result) {
        const emailBodyForSubAdmin = formatMessageForApprovalRejectToSubAdmin(
          subAdminName,
          name,
          areas,
          gaName,
          "rejected",
          approvedBy
        );

        await otpHelper.sendEmailNotificationWithoutCc(
          subAdminEmail,
          "Contractor Onboarding rejected",
          emailBodyForSubAdmin
        );

        response = { message: "Contractor has been rejected successfully" };
      }
    } else {
      response = { message: "Invalid action type" };
    }

    return response;
  } catch (error) {
    console.error("Error in acceptRejectService:", error);
    throw error;
  }
};

const subAdminOnboardingService = async (req) => {
  try {
    const payload = req.body;

    const userExistenceCheck = await checkUserExistenceInGAService({
      mobile: payload.mobile,
      email: payload.email,
      ga_area_id: payload.ga_id,
    });

    if (!userExistenceCheck.data.status) {
      return userExistenceCheck;
    }

    // Step 3: Proceed with onboarding
    // const generatedPassword = generatePassword();
    const generatedPassword = "Password@123";
    const encryptedPassword = encryptPassword(generatedPassword);

    const result = await bpclAdminRepository.subAdminOnboardingRepo(
      payload,
      encryptedPassword,
      req
    );

    const getGaAreawithAreaId =
      await bpclAdminRepository.getGaAreawithAreaIdRepo(payload.ga_id);

    if (result && getGaAreawithAreaId && getGaAreawithAreaId.length > 0) {
      const {
        emp_name: name,
        sub_admin_type: subAdminType,
        mobile,
        email,
      } = payload;
      const gaAreaName = getGaAreawithAreaId[0].ga_area_name;
      const fromToDate = formatIsoToDateString(payload.period_from);
      const toToDate = formatIsoToDateString(payload.period_to);

      const emailBody = formatMessageForSubAdminOnboarding(
        name,
        subAdminType,
        gaAreaName,
        mobile,
        fromToDate,
        toToDate,
        generatedPassword
      );
      await otpHelper.sendEmailNotificationWithoutCc(
        email,
        "Domestic PNG GA Sub-Admin Onboarding",
        emailBody
      );
    }

    const response = {
      user: result,
      ga_area: getGaAreawithAreaId,
      message: "Sub-Admin has been created successfully",
    };

    return { data: response };
  } catch (error) {
    console.error("Error in subAdminOnboardingService service:", error);
    throw error;
  }
};

const geSubAdminTypeListService = async (req) => {
  try {
    const result = await bpclAdminRepository.getSubAdminTypeListRepo();
    return result;
  } catch (error) {
    console.error("Error in geSubAdminTypeListService service:", error);
    throw error;
  }
};
const getGeographicalAreaListService = async (req) => {
  try {
    const user = req.user.admin_id;
    const result = await bpclAdminRepository.getGeographicalAreaListRepo(user);
    return result;
  } catch (error) {
    console.error("Error in getGeographicalAreaListService service:", error);
    throw error;
  }
};
const getTpiSalesByAreaWiseListService = async (
  geographical_area_id,
  page = 1,
  limit = 10,
  area_id = [],
  search = null
) => {
  try {
    const result = await bpclAdminRepository.getTpiSalesByAreaWiseList(
      geographical_area_id,
      page,
      limit,
      area_id,
      search
    );
    return result;
  } catch (error) {
    console.error("Error in getTpiSalesByAreaWiseListService service:", error);
    throw error;
  }
};
const getTpiEngineerByAreaWiseListService = async (
  geographical_area_id,
  page = 1,
  limit = 10,
  area_id = [],
  search = null
) => {
  try {
    const result = await bpclAdminRepository.getTpiEngineerByAreaWiseList(
      geographical_area_id,
      page,
      limit,
      area_id,
      search
    );
    return result;
  } catch (error) {
    console.error("Error in getTpiEngineerByAreaWiseList service:", error);
    throw error;
  }
};

const detailsByPersonaRole = async (payload) => {
  try {
    const personaRoleMap = { dma: 1, lmc: 3, fo: 4 };
    const personaRoleId = personaRoleMap[payload.persona_role?.toLowerCase()];

    const geographicalAreaId = payload.geographical_area_id || null;
    const result =
      await bpclAdminRepository.detailsByPersonaRole(personaRoleId,
        geographicalAreaId);

    return result;
  } catch (error) {
    console.error("Error in detailsByPersonaRole service:", error);
    throw error;
  }
};

const getContractorManagementTPIService = async (reqBody) => {
  try {
    const { geographical_area_id, user_role } = reqBody;

    if (!geographical_area_id || !user_role) {
      throw new Error("geographical_area_id and user_role are required");
    }

    const result = await bpclAdminRepository.getContractorManagementTPIRepo(
      geographical_area_id,
      user_role
    );
    return result;
  } catch (error) {
    console.error("Error in getContractorManagementTPIService:", error);
    throw error;
  }
};

const getContractorManagementAreaUpdate = async (req) => {
  try {
    const user = req.user?.admin_id;
    let { admin_id, area_id } = req.body;

    switch (req.body.contractor_type) {
      case "TPI Sales":
        persona_role_id = 2;
        user_roles = "sales";
        contractorTypeForDb = "tpi";
        break;
      case "TPI Engineer":
        persona_role_id = 2;
        user_roles = "engineer";
        contractorTypeForDb = "tpi";
        break;
      default:
        console.log("Unknown contractor type");
        break;
    }

    let existingAreas = [];
    if (
      req.body.contractor_type == "TPI Sales" ||
      req.body.contractor_type == "TPI Engineer"
    ) {
      let newArea = [];
      area_id.forEach(async (element, index) => {
        console.log("element--->", element);
        const checkTpiPresentInArea = await bpclAdminRepository.getAreaForAPI(
          element,
          persona_role_id,
          user_roles
        );
        console.log("checkTpiPresentInArea--->", checkTpiPresentInArea);
        if (checkTpiPresentInArea.length > 0) {
          existingAreas.push({
            area_name: element,
            status: false,
          });
          // area_id.splice( index, 1 );
          // return ;
        } else {
          newArea.push(element);
        }
      });
      area_id = newArea;
    }

    console.log("aaaaaaa", area_id);
    const result = await bpclAdminRepository.getContractorManagementAreaUpdate(
      admin_id,
      area_id,
      user
    );
    return { result: result, existingAreas: existingAreas };
  } catch (error) {
    console.error("Error in getContractorManagementTPIService:", error);
    throw error;
  }
};

const getProfileData = async (req) => {
  try {
    console.log("????", req.user);
    const user = req.user?.admin_id;
    // let { admin_id, area_id } = req.body;

    // switch (req.body.contractor_type) {
    //   case "TPI Sales":
    //     persona_role_id = 2;
    //     user_roles = "sales";
    //     contractorTypeForDb = "tpi";
    //     break;
    //   case "TPI Engineer":
    //     persona_role_id = 2;
    //     user_roles = "engineer";
    //     contractorTypeForDb = "tpi";
    //     break;
    //   default:
    //     console.log("Unknown contractor type");
    //     break;
    // }

    // let existingAreas = [];
    // if (
    //   req.body.contractor_type == "TPI Sales" ||
    //   req.body.contractor_type == "TPI Engineer"
    // ) {
    //   let newArea = [];
    //   area_id.forEach(async (element, index) => {
    //     console.log("element--->", element);
    //     const checkTpiPresentInArea = await bpclAdminRepository.getAreaForAPI(
    //       element,
    //       persona_role_id,
    //       user_roles
    //     );
    //     console.log("checkTpiPresentInArea--->", checkTpiPresentInArea);
    //     if (checkTpiPresentInArea.length > 0) {
    //       existingAreas.push({
    //         area_name: element,
    //         status: false,
    //       });
    //       // area_id.splice( index, 1 );
    //       // return ;
    //     } else {
    //       newArea.push(element);
    //     }
    //   });
    //   area_id = newArea;
    // }

    // console.log("aaaaaaa", area_id);
    const result = await bpclAdminRepository.getProfileData(user);
    return result;
  } catch (error) {
    console.error("Error in getContractorManagementTPIService:", error);
    throw error;
  }
};

const getCustomerApprovalListService = async (req) => {
  try {
    const { geographical_area_id, status } = req.body;

    let internalStatus = "";

    switch (status) {
      case "pending":
        internalStatus = "application-pending-so"; //"application-pending-so"
        break;

      case "resubmission":
        internalStatus = "application-resubmit-so";
        break;

      case "closed":
        internalStatus = "closed";
        break;

      default:
        internalStatus = "unknown_status";
        break;
    }

    const result = await bpclAdminRepository.customerApprovalListRepo(
      geographical_area_id,
      internalStatus
    );

    return result;
  } catch (error) {
    console.error("Error in getCustomerApprovalListService:", error);
    throw error;
  }
};

const getCustomerPersonalDetailsService = async (req) => {
  try {
    const { customer_connection_id } = req.body;

    const result = await bpclAdminRepository.customerPersonalDetailsRepo(
      customer_connection_id
    );

    return result;
  } catch (error) {
    console.error("Error in getCustomerPersonalDetailsService:", error);
    throw error;
  }
};

const getCustomerApprovalListEoService = async (req) => {
  try {
    const { geographical_area_id, status } = req.body;

    let internalStatus = "";

    switch (status) {
      case "pending":
        internalStatus = "pending-eo";
        break;

      case "resubmission":
        internalStatus = "resubmit-eo";
        break;

      case "closed":
        internalStatus = "closed";
        break;

      default:
        internalStatus = "unknown_status";
        break;
    }

    const result = await bpclAdminRepository.customerApprovalListEoRepo(
      geographical_area_id,
      internalStatus
    );

    return result;
  } catch (error) {
    console.error("Error in getgetCustomerApprovalListEoService:", error);
    throw error;
  }
};
const getCustomerApprovalListManualGmrService = async (req) => {
  try {
    const { geographical_area_id, status } = req.body;

    let internalStatus = "";

    switch (status) {
      case "pending":
        internalStatus = "pending-jmr-eo";
        break;

      case "resubmission":
        internalStatus = "resubmit-jmr-eo";
        break;

      case "closed":
        internalStatus = "closed";
        break;

      default:
        internalStatus = "unknown_status";
        break;
    }

    const result = await bpclAdminRepository.customerApprovalListManualGmrRepo(
      geographical_area_id,
      internalStatus
    );

    return result;
  } catch (error) {
    console.error("Error in getgetCustomerApprovalListEoService:", error);
    throw error;
  }
};

const getCustomerlmcInstallationDetailsService = async (
  mapped_lmc_connection_stage_id
) => {
  try {
    // Fetch stage details
    const stage = await bpclAdminRepository.getStageDetails(
      mapped_lmc_connection_stage_id
    );
    if (!stage) {
      throw new Error("Mapped LMC Connection Stage ID not found.");
    }
    const customer_connection_id =
      stage["lmc_connection_mapping.customer_connection_id"];

    // Fetch installation and documents
    const installationDetails =
      await bpclAdminRepository.getInstallationDetails(
        mapped_lmc_connection_stage_id
      );
    const uploadedDocuments = await bpclAdminRepository.getUploadedDocuments(
      mapped_lmc_connection_stage_id
    );

    // Fetch address from customer connection

    const meterDetails = await bpclAdminRepository.getMeterDetails(
      customer_connection_id
    );

    // Fetch house type from latest feasibility/commissioning details
    const latestCommissioning =
      await bpclAdminRepository.getLatestFeasibilityCommissioning(
        mapped_lmc_connection_stage_id
      );

    // Build response
    return {
      technicalDetails: {
        actual_visit_date: stage.actual_visit_date,
        regulator_number: installationDetails?.regulator_no || 'NA',
        installation_date: installationDetails?.installation_date || 'NA',
        testing_date: installationDetails?.testing_date || 'NA',
        rfc_date: installationDetails?.rfc_date || 'NA',
        house_type_floor: installationDetails?.house_type || 'NA',
        area: installationDetails?.area || 'NA',
        // city: customerAddress?.connection_city || installationDetails?.city || 'NA',
        // house_address: installationDetails?.house_address || 'NA',
        house_type: latestCommissioning?.house_type || installationDetails?.house_type || 'NA',
        mdpe_pipeline_length: latestCommissioning?.mdpe_pipeline_length || installationDetails?.mdpe_pipeline_length || 'NA',
        riser_length: latestCommissioning?.riser_length || installationDetails?.riser_length || 'NA',
        gi_pipeline_length: latestCommissioning?.gi_pipeline_length || installationDetails?.gi_pipeline_length || 'NA',
        meter_no: meterDetails?.meter_no || 'NA',
      },
      billOfMaterial: {
        gi_length: installationDetails?.gi_length || 0,
        gi_length_tapping: installationDetails?.gi_length_for_tapping || 0,
        copper_length: installationDetails?.copper_length || 0,
        appliance_valve_half_inch: installationDetails?.appliance_valve || 0,
        isolation_ball_valve: installationDetails?.isolation_ball_valve || 0,
        brass_fitting_set: installationDetails?.brass_fitting_set || 0,
        gi_plug_half_inch: installationDetails?.gi_plug || 0,
        half_inch_2_inch_nipple: installationDetails?.half_two_nipple || 0,
        half_inch_3_inch_nipple: installationDetails?.half_three_nipple || 0,
        half_inch_4_inch_nipple: installationDetails?.half_four_nipple || 0,
        elbow: installationDetails?.elbow || 0,
        equal_tee: installationDetails?.equal_tee || 0,
        socket: installationDetails?.socket || 0,
        union: installationDetails?.union_value || 0,
        anaconda: installationDetails?.anaconda || 0,
        elbow_mf: installationDetails?.elbow_mf || 0,
      },
      checklist: installationDetails?.checklist || {},
      uploadDocuments: uploadedDocuments.map((doc) => ({
        mapped_lmc_connection_stage_id: doc.mapped_lmc_connection_stage_id,
        file_name: doc.file_name,
        file_path: doc.file_path,
        file_ext: doc.file_ext,
        verification_status: doc.verification_status,
        file_status: doc.file_status,
        file_type: doc.file_type,
      })),
      mapped_lmc_connection_stage_id,
    };
  } catch (error) {
    console.error("Error in getCustomerPersonalDetailsService:", error);
    throw error;
  }
};

const getcustomerManualGmrDetailsService = async (tmma_id) => {
  try {
    const result = await bpclAdminRepository.getJmrSubmissionDetails(tmma_id);
    if (!result) {
      throw new Error("JMR submission details not found");
    }
    return result;
  } catch (error) {
    console.error("Error in getcustomerManualGmrDetailsService:", error);
    throw error;
  }
};

const soStatusUpdate = async (req) => {
  try {
    const payload = req.body;
    const statusMap = {
      approved: {
        status: "application-approved",
        internal_status: "application-approved-so",
      },
      reject: {
        status: "application-rejected",
        internal_status: "application-rejected-so",
      },
      resubmission: {
        status: "application-inreview",
        internal_status: "application-resubmit-so",
      },
    };

    const actionKey = payload.action?.toLowerCase();

    if (!statusMap[actionKey]) {
      throw new Error(
        "Invalid action. Must be one of: approved, reject, resubmission"
      );
    }

    const { status, internal_status } = statusMap[actionKey];

    const updateResult = await bpclAdminRepository.soStatusUpdate({
      customer_connection_id: payload.customer_connection_id,
      status,
      internal_status,
      reason: payload.reason,
      updated_by: req.user?.admin_id || 0,
      actionKey,
    });

    if (!updateResult) {
      throw new Error("Failed to update customer connection status.");
    }

    const response = {
      message: `Status updated to '${payload.action}' successfully.`,
      updatedRecord: updateResult,
    };

    return { data: response };
  } catch (error) {
    console.error("Error in soStatusUpdate service:", error.message);
    throw error;
  }
};

const eoStatusUpdate = async (req) => {
  try {
    const payload = req.body;
    const actionKey = payload.action?.toLowerCase();
    const reason = payload.reason;

    const validActions = ["approved", "reject", "resubmission"];
    if (!validActions.includes(actionKey)) {
      throw new Error(
        "Invalid action. Must be one of: approved, reject, resubmission"
      );
    }

    const updatePayload = {
      customer_connection_id: payload.customer_connection_id,
      updated_by: req.user?.admin_id || 0,
      reason,
      actionKey,
    };

    if (actionKey === "approved") {
      updatePayload.customerConnection = {
        status: "onboarding-approved",
        internal_status: "onboarding-approved",
      };
      updatePayload.lmcStage = {
        lmc_stage: "installation",
        tpi_status: "eo-approved",
      };
      updatePayload.newLmcStage = {
        lmc_stage: "commissioning",
        visit_status: "not-scheduled",
        tpi_status: "no-submission",
      };
    } else if (actionKey === "reject") {
      updatePayload.customerConnection = {
        status: "application-rejected",
        internal_status: "onboarding-rejected",
      };
      updatePayload.lmcStage = {
        lmc_stage: "installation",
        tpi_status: "eo-rejected",
      };
    } else if (actionKey === "resubmission") {
      updatePayload.customerConnection = {
        status: "onboarding-inprogress",
        internal_status: "onboarding-inprogress",
      };
      updatePayload.lmcStage = {
        lmc_stage: "installation",
        tpi_status: "eo-resubmit",
        visit_status: "to-be-rescheduled",
      };
    }

    const result = await bpclAdminRepository.eoStatusUpdate(updatePayload);

    if (!result) {
      throw new Error("Failed to update EO status.");
    }

    return {
      data: {
        message: `EO action '${actionKey}' processed successfully.`,
        updatedRecord: result,
      },
    };
  } catch (error) {
    console.error("Error in eoStatusUpdate service:", error.message);
    throw error;
  }
};

const eoJmrUpdate = async (req) => {
  try {
    const payload = req.body;
    const statusMap = {
      approved: {
        tpi_status: "completed-jmr-eo",
      },
      reject: {
        tpi_status: "rejected-jmr-eo",
      },
      resubmission: {
        tpi_status: "resubmit-jmr-eo",
      },
    };

    const actionKey = payload.action?.toLowerCase();

    if (!statusMap[actionKey]) {
      throw new Error(
        "Invalid action. Must be one of: approved, reject, resubmission"
      );
    }

    const { tpi_status } = statusMap[actionKey];

    const updateResult = await bpclAdminRepository.eoJmrUpdate({
      tmma_id: payload.tmma_id,
      tpi_status,
      reason: payload.reason,
      updated_by: req.user?.admin_id || 0,
      actionKey,
    });

    if (!updateResult) {
      throw new Error("Failed to update status.");
    }

    const response = {
      message: `Status updated to '${payload.action}' successfully.`,
      updatedRecord: updateResult,
    };

    return { data: response };
  } catch (error) {
    console.error("Error in eoJmrUpdate service:", error.message);
    throw error;
  }
};

const getSoCommentDetails = async (req) => {
  try {
    const { customer_connection_id } = req.body;
    if (!customer_connection_id) {
      throw new Error("customer_connection_id is required.");
    }
    const commentDetails = await bpclAdminRepository.getSoCommentDetails(
      customer_connection_id
    );

    if (!commentDetails) {
      throw new Error(
        "No comment details found for the given customer_connection_id."
      );
    }
    return {
      data: {
        message: "SO comment details fetched successfully.",
        commentDetails,
      },
    };
  } catch (error) {
    console.error("Error in soCommentDetails service:", error.message);
    throw error;
  }
};

const getEoCommentDetails = async (req) => {
  try {
    const { customer_connection_id } = req.body;
    if (!customer_connection_id) {
      throw new Error("customer_connection_id is required.");
    }
    const commentDetails = await bpclAdminRepository.getEoCommentDetails(
      customer_connection_id
    );

    if (!commentDetails) {
      throw new Error(
        "No comment details found for the given customer_connection_id."
      );
    }
    return {
      data: {
        message: "EO comment details fetched successfully.",
        commentDetails,
      },
    };
  } catch (error) {
    console.error("Error in EOCommentDetails service:", error.message);
    throw error;
  }
};

const getEoJmrCommentDetails = async (req) => {
  try {
    const { customer_connection_id } = req.body;
    if (!customer_connection_id) {
      throw new Error("customer_connection_id is required.");
    }
    const commentDetails = await bpclAdminRepository.getEoJmrCommentDetails(
      customer_connection_id
    );

    if (!commentDetails) {
      throw new Error(
        "No comment details found for the given customer_connection_id."
      );
    }
    return {
      data: {
        message: "EO JMR comment details fetched successfully.",
        commentDetails,
      },
    };
  } catch (error) {
    console.error("Error in EOJMRCommentDetails service:", error.message);
    throw error;
  }
};

const getCommentDetails = async (req) => {
  try {
    if (!req || !req.body) {
      throw new Error("Request body is missing.");
    }

    const { customer_connection_id, mapping_type } = req.body;

    if (!customer_connection_id) {
      throw new Error("customer_connection_id is required.");
    }

    if (!mapping_type) {
      throw new Error("mapping_type is required.");
    }

    let commentDetails;
    switch (mapping_type) {
      case "customer-application":
        commentDetails = await bpclAdminRepository.getSoCommentDetails(
          customer_connection_id
        );
        break;
      case "lmc-installation":
        commentDetails = await bpclAdminRepository.getEoCommentDetails(
          customer_connection_id
        );
        break;
      case "jmr-manual":
        commentDetails = await bpclAdminRepository.getEoJmrCommentDetails(
          customer_connection_id
        );
        break;
      default:
        throw new Error(`Unsupported mapping_type: ${mapping_type}`);
    }
    if (!commentDetails || commentDetails.length === 0) {
      throw new Error(
        "No comment details found for the given customer_connection_id and mapping_type."
      );
    }
    return {
      data: {
        message: `${mapping_type.toUpperCase()} comment details fetched successfully.`,
        commentDetails,
      },
    };
  } catch (error) {
    console.error("Error in getCommentDetails service:", error.message);
    throw error;
  }
};

module.exports = {
  getChannelPartnersCount,
  contractorOnboardingService,
  getAreaListService,
  getContractorPendingApprovalService,
  acceptRejectService,
  getContractorManagementTPIService,
  subAdminOnboardingService,
  geSubAdminTypeListService,
  getGeographicalAreaListService,
  detailsByPersonaRole,
  getTpiSalesByAreaWiseListService,
  getTpiEngineerByAreaWiseListService,
  getContractorManagementAreaUpdate,
  soStatusUpdate,
  eoStatusUpdate,
  eoJmrUpdate,
  getProfileData,
  getCustomerApprovalListService,
  getCustomerPersonalDetailsService,
  getCustomerApprovalListEoService,
  getCustomerApprovalListManualGmrService,
  getCustomerlmcInstallationDetailsService,
  getcustomerManualGmrDetailsService,
  getSoCommentDetails,
  getEoCommentDetails,
  getEoJmrCommentDetails,
  getCommentDetails,
};
