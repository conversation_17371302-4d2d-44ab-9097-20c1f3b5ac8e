const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_masters",
    {
      master_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      master_type: {
        type: DataTypes.STRING(40),
        allowNull: true,
        unique: "UK_mst_masters_master_type_master_name",
      },
      master_name: {
        type: DataTypes.STRING(50),
        allowNull: true,
        unique: "UK_mst_masters_master_type_master_name",
      },
      master_value: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      parent_master_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "mst_masters",
      schema: "dbo",
      timestamps: false,
      // underscored: true,
      indexes: [
        {
          name: "PK_mstmasters_master_id",
          unique: true,
          fields: [{ name: "master_id" }],
        },
        {
          name: "UK_mst_masters_master_type_master_name",
          unique: true,
          fields: [{ name: "master_type" }, { name: "master_name" }],
        },
      ],
    }
  );
};
