// const { validate } = require("express-validation");
const {
  verifyBpclToken,
  verifyPngToken,
  verifyRefreshToken,
} = require("../../middleware/customer-auth");
const express = require("express");
const customerRoute = express.Router();
const customerController = require("../../controllers/customer-controller");
const validator = require("../../middleware/validator");
// const { registerCustomerValidationAll, registrationHelpValidationAll, checkConnectionPincodeValidation, getCustomerDetailValidation, customerLoginValidation, saveDetailValidation, connectionListValidation, deleteDocumentValidation, extraChargeApprovalValidation, getConnectionPayValidation, uploadDocumentValidation } = require("../../validation/customer-validation");
const {
  uploadMultiple,
  uploadSingle,
  uploadSingleTemp,
  uploadSingleTempNew,
} = require("../../middleware/newMulter");

customerRoute.post(
  "/save-details",
  verifyPngToken("guest", true),
  validator("customer-validation", "registerCustomerValidationAll"),
  customerController.customerDetailsToCache
);
customerRoute.post(
  "/get-details",
  verifyPngToken("guest", true, true),
  validator("customer-validation", "getCustomerDetailValidation"),
  customerController.getCustomerDetailsById
);
customerRoute.post(
  "/upload",
  verifyPngToken("guest", true),
  // uploadMultiple,
  uploadSingleTempNew("customer-validation", "uploadDocumentValidation"),
  customerController.customerUploadDocuments
);
customerRoute.post(
  "/save",
  verifyPngToken("guest", true),
  validator("customer-validation", "saveDetailValidation"),
  customerController.customerDetailsToDB
);
customerRoute.post(
  "/list",
  verifyPngToken("guest"),
  validator("customer-validation", "connectionListValidation"),
  customerController.customerList
);
customerRoute.post(
  "/registration-help",
  verifyPngToken("guest"),
  validator("customer-validation", "registrationHelpValidationAll"),
  customerController.registrationHelp
);
customerRoute.post(
  "/delete",
  verifyPngToken("guest", true),
  validator("customer-validation", "deleteDocumentValidation"),
  customerController.customerDocumentDelete
);
customerRoute.post(
  "/check-pincode-connection",
  verifyPngToken("guest", true),
  validator("customer-validation", "checkConnectionPincodeValidation"),
  customerController.pincodeConnectionCheck
);
customerRoute.post(
  "/payment-approval",
  verifyPngToken("guest"),
  validator("customer-validation", "paymentApprovalValidation"),
  customerController.paymentApproval
);

customerRoute.post(
  "/customer-approval-decline",
  customerController.customerApprovalDecline
);

customerRoute.post(
  "/connection-payment-list",
  verifyPngToken("guest"),
  validator("customer-validation", "getConnectionPayValidation"),
  customerController.connectionPaymentList
);
customerRoute.post(
  "/get-token",
  verifyBpclToken,
  validator("customer-validation", "customerLoginValidation"),
  customerController.customerLogin
);
customerRoute.get(
  "/refresh-token",
  verifyRefreshToken(),
  customerController.customerRefreshToken
);

customerRoute.post(
  "/logout",
  verifyPngToken("guest"),
  customerController.customerLogout
);

customerRoute.post(
  "/get-details-by-mobile",
  verifyPngToken("guest", true),
  validator("customer-validation", "getDetailByMobileValidation"),
  customerController.getCustomerDetailsByMobile
);

customerRoute.get(
  "/check-rework-expiry",
  customerController.checkReworkExpiry
);

module.exports = { customerRoute };
