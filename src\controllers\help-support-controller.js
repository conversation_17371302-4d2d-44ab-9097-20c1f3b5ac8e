const { sendSuccess, sendError } = require('../helpers/responseHelper');
const helpSupportService = require('../services/help-support-service');

exports.reportIssue = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await helpSupportService.addIssue(req.body);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error?.details?error.details:error , error.message);
  }
};
exports.uploadIssueDoc = async (req, res) => {
  try {
    const result = await helpSupportService.uploadDocuments(req.body,req.file);
    if(result.status==="success"){
      sendSuccess(req, res, result.data,result.message);
    }else{
      sendError(req, res, [], result.message);     
    }
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.deleteIssueDoc = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result =
      await helpSupportService.deleteUploadDocument(requestBody);
    sendSuccess(req, res, null, result.message);
  } catch (error) {
    console.error("Error in deletePngDocument:", error);
    sendError(req, res, null, error.message);
  }
};

