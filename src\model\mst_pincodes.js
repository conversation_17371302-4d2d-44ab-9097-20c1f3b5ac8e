const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('mst_pincodes', {
    pincode_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    geographical_area_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_geographical_areas',
        key: 'geographical_area_id'
      },
      unique: "UK_mstpincodes_geographical_area_id_pincode"
    },
    area_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_areas',
        key: 'area_id'
      },
      unique: "UK_mstpincodes_geographical_area_id_pincode"
    },
    pincode: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: "UK_mstpincodes_geographical_area_id_pincode"
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'mst_pincodes',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_pincode_id",
        unique: true,
        fields: [
          { name: "pincode_id" },
        ]
      },
      {
        name: "UK_mstpincodes_geographical_area_id_pincode",
        unique: true,
        fields: [
          { name: "geographical_area_id" },
          { name: "area_id" },
          { name: "pincode" },
        ]
      },
    ]
  });
};
