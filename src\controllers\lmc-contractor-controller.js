const { sendSuccess, sendError } = require('../helpers/responseHelper');
const lmcContractorService = require('../services/lmc-contractor-service');
const lmcContractorRepository = require("../repository/lmc-contractor-repository");
const crypto = require('crypto');
const { encryptPassword } = require('../utils/encrypt-decrypt');


exports.lmcCustomerAssignments = async (req, res) => {
  try {
    const contractorId = req?.user?.admin_id;
    const result = await lmcContractorService.getLmcCustomerAssignments(req.query,contractorId);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
const generatePassword = () => {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return Array.from(crypto.randomFillSync(new Uint32Array(8)))
        .map(x => charset[x % charset.length])
        .join('');
};
exports.createLMCSupervisor = async (req, res) => {
  try {
      const existingUser = await  lmcContractorRepository.getLMCSupervisorById(
          req.body.email, 
          req.body.mobile
      );
      if (existingUser) {
          return sendError(
              req, 
              res, 
              null, 
              'Supervisor with this email or mobile already exists',
              400
          );
      }

      const plainPassword = generatePassword();
      const encryptedPassword = encryptPassword(plainPassword);
      if (typeof req.body.area_ids === 'string') {
        req.body.area_ids = req.body.area_ids
          .split(',')
          .map(id => parseInt(id.trim(), 10))
          .filter(id => !isNaN(id));
      }
      const supervisorData = {
          first_name: req.body.first_name,
          last_name: req.body.last_name,
          email: req.body.email,
          mobile: req.body.mobile,
          alternate_mobile: req.body.alternate_mobile,
          bpclAdminId: req?.user?.bpcl_admin_id || 1,
          password: encryptedPassword,
          persona_role_id: req?.user?.persona_role_id,
          agency_id: req.body.agency_id,
          area_ids: req.body.area_ids
      };

      const supervisor = await lmcContractorRepository.createLMCSupervisor(
          supervisorData,
          req?.user?.admin_id,
          req?.file,
      );

      // TODO: Send email to supervisor with credentials
      // Email should contain:
      // - Login email (supervisor.email)
      // - Generated password (plainPassword)
      // - Login URL
      // - Any welcome message or instructions

      const supervisorJson = supervisor.toJSON();
      const { 
          password,
          city, 
          state, 
          last_logged_in,
          ...supervisorResponse 
      } = supervisorJson;

      sendSuccess(req, res, supervisorResponse, 'Supervisor created successfully');
  } catch (error) {
      console.error('Error in createSupervisor:', error);
      sendError(req, res, null, error.message || 'Failed to create supervisor');
  }
};


exports.getAllLMCSupervisors = async (req, res) => {
  try {
      const supervisors = await lmcContractorService.getAllLMCSupervisors(req);

      sendSuccess(req, res, supervisors, 'Supervisors retrieved successfully');
  } catch (error) {
      console.error('Error in getAllSupervisors:', error);
      sendError(req, res, null, error.message || 'Failed to retrieve supervisors');
  }
};

exports.getMyLMCSupervisors = async (req, res) => {
    try {
      const result = await lmcContractorService.getMyLMCSupervisors(req);
      sendSuccess(req, res, result, 'Fetch Data successfully.');
    } catch (error) {
      sendError(req, res, error, error.message);
    }
  };

  
  exports.revokeLMCSupervisor = async (req, res) => {
    try {
      const response = await lmcContractorService.revokeLMCSupervisor(req);
  
      if (response.success) {
        sendSuccess(req, res, response.supervisor, response.message);
      } else {
        sendError(req, res, null, response.message);
      }
    } catch (error) {
      console.error('Error in revokeLMCSupervisor:', error);
      sendError(req, res, null, error.message || 'Failed to revoke supervisor access');
    }
  };
  

exports.getGeographicalAreas = async (req, res) => {
    try {
        const geographicalAreas = await lmcContractorRepository.getGeographicalAreas();
  
        sendSuccess(req, res, geographicalAreas, 'geographicalAreas retrieved successfully');
    } catch (error) {
        console.error('Error in getAllgeographicalAreas:', error);
        sendError(req, res, null, error.message || 'Failed to retrieve geographicalAreas');
    }
  };

  exports.getSupervisorsTracking = async (req, res) => {
    try {
      const result = await lmcContractorService.getSupervisorsTracking(req);
      sendSuccess(req, res, result, 'Fetch Data successfully.');
    } catch (error) {
      sendError(req, res, error, error.message);
    }
  };


  exports.getDashboardMetrics= async (req, res) => {
    try {
        const dashboardMetrics = await lmcContractorService.getDashboardMetricsCounts(req);
  
        sendSuccess(req, res, dashboardMetrics, 'dashboardMetrics retrieved successfully');
    } catch (error) {
        console.error('Error in getAlldashboardMetrics:', error);
        sendError(req, res, null, error.message || 'Failed to retrieve dashboardMetrics');
    }
  };

  exports.getLmcCustomerAssignmentsTicket= async (req, res) => {
    try {
        const dashboardMetrics = await lmcContractorRepository.getLmcCustomerAssignmentsTicket(req?.query?.customer_connection_id);
  
        sendSuccess(req, res, dashboardMetrics, 'dashboardMetrics retrieved successfully');
    } catch (error) {
        console.error('Error in getAlldashboardMetrics:', error);
        sendError(req, res, null, error.message || 'Failed to retrieve dashboardMetrics');
    }
  };

  exports.lmcContractorCustomerAssignments = async (req, res) => {
    try {
      const supervisorId = req?.body?.supervisorId;
      const customerConnectionId = req?.body?.customerConnectionId || ''; // Default to empty remark
  
  
      const response = await lmcContractorRepository.assignMCSupervisor(supervisorId, customerConnectionId);
  
      if (response.success) {
        sendSuccess(req, res, response.supervisor, response.message);
      } else {
        sendError(req, res, null, response.message);
      }
    } catch (error) {
      console.error('Error in revokeLMCSupervisor:', error);
      sendError(req, res, null, error.message || 'Failed to revoke supervisor access');
    }
  };

  exports.getGeoAreaByUser = async (req, res) => {
    try {
      const contractorId = req?.user?.admin_id;
        const geographicalAreas = await lmcContractorRepository.getGeoAreaByUser(contractorId);
  
        sendSuccess(req, res, geographicalAreas, 'geographicalAreas retrieved successfully');
    } catch (error) {
        console.error('Error in getAllgeographicalAreas:', error);
        sendError(req, res, null, error.message || 'Failed to retrieve geographicalAreas');
    }
  };