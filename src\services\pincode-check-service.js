const pincodeRepository = require("../repository/pincode-check-repository");

const checkPincodeAvailability = async (pincode) => {
  let location = await pincodeRepository.checkPincodeFromDB(pincode);
  // if (location && Array.isArray(location) && location.length > 0) {
  //   location = location.map((loc) => {
  //     loc.state =
  //       loc.pincodeAreasAlias?.area?.geographical_area?.state?.state_title;
  //     loc.city =
  //       loc.pincodeAreasAlias?.area?.geographical_area?.city?.city_title;
  //     // loc.area = {
  //     //   area_name: loc.pincodeAreasAlias?.area?.area_name,
  //     // };
  //     loc.area_name =  loc.pincodeAreasAlias?.area?.area_name;
  //     loc.geographical_area = {
  //       state:
  //         loc.pincodeAreasAlias?.area?.geographical_area?.state?.state_title,
  //       city: loc.pincodeAreasAlias?.area?.geographical_area?.city?.city_title,
  //     };
  //     return loc;
  //   });
  //   return location;
  // } else {
  //   return "No";
  // }

  if (location && Array.isArray(location) && location.length > 0) {
    const formattedData = location.map((loc) => ({
      available_pngareas_id: loc.available_pngareas_id,
      pincode_id: loc.pincode_id,
      pincode: loc.pincode,
      state: loc.pincodeAreasAlias?.area?.geographical_area?.state?.state_title,
      city: loc.pincodeAreasAlias?.area?.geographical_area?.city?.city_title,
      area_name: loc.pincodeAreasAlias?.area?.area_name,
    }));

    return formattedData;
  } else {
    return "No";
  }

};





module.exports = { checkPincodeAvailability };
