//const userCapabilityDetailMap = require("../model/user.capability.model");
//let capabilityListDetail = require("../model/capability.list.model");
//const UserCapabilitiesMap = require("../model/user-capabilities-map.model");
const {
  User,
  UserCapabilitiesMap,
  capabilityListDetail,
} = require('../model/association.model');

const getCapabilitiesByUserId = async (userId) => {
  const params = await userCapabilityParams(userId);
  return await UserCapabilitiesMap.findAll(params);
};

const userCapabilityParams = async (userId) => {
  const params = {
    include: [
      {
        model: User,
        attributes: ['user_id', 'first_name'],
      },
      {
        model: User,
        as: 'CreatedBy',
        attributes: ['first_name'],
      },
      {
        model: User,
        as: 'UpdatedBy',
        attributes: ['first_name'],
      },
      {
        model: capabilityListDetail,
        attributes: [
          'capability_list_id',
          'parent_id',
          'slug',
          ['capabilities', 'capabilities_name'],
        ],
        include: [
          {
            model: capabilityListDetail,
            as: 'Parent',
            attributes: [
              'capability_list_id',
              'parent_id',
              'slug',
              ['capabilities', 'parent_capabilities_name'],
            ],
          },
        ],
      },
    ],
    where: {
      user_id: userId,
      is_deleted: 0,
    },
    attributes: ['capabilities'],
    raw: true,
    //logging: true,
  };
  return params;
};
module.exports = {
  getCapabilitiesByUserId,
};
