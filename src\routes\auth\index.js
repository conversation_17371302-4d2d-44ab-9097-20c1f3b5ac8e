const express = require('express');
const { validate } = require('express-validation');
const { loginValidation,verifyotp,changePasswordValidation } = require('../../validation/auth-validation');
const { login, logout,verifyOTP,resetPassword, handleMicrosoftLogin} = require('../../controllers/auth-controller');
const verifyToken = require('../../middleware/auth');


const authRoutes = express.Router();

authRoutes.post(
    '/login',
    validate(loginValidation),
    login
);
authRoutes.post(
    '/verify-otp',
    validate(verifyotp),
    verifyOTP
  );
authRoutes.post(
    '/reset-password',
    verifyToken,
    validate(changePasswordValidation),
    resetPassword
  );
authRoutes.get('/logout', verifyToken, logout);

authRoutes.post('/microsoft', handleMicrosoftLogin);

module.exports = authRoutes;