const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_bpcl_admin_roles",
    {
      bpcl_admin_role_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      bpcl_admin_role_type: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      role_name: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: "UK_mstbpcladminroles_role_name",
      },
      slug: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: "UK_mstbpcladminroles_role_slug",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "mst_bpcl_admin_roles",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_bpcl_admin_role_id",
          unique: true,
          fields: [{ name: "bpcl_admin_role_id" }],
        },
        {
          name: "UK_mstbpcladminroles_role_name",
          unique: true,
          fields: [{ name: "role_name" }],
        },
      ],
    }
  );
};
