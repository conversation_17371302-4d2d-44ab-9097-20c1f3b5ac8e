const dotEnv = require("dotenv").config();
module.exports = {
  PORT: process.env.PORT,
  FILE_OPTIONS: {
    MAX_SIZE: 5,
  },
  UPLOAD_DIR: {
    CUSTOMERS: "customers",
  },
  JWT: {
    JWT_OPTIONS: {
      expiresIn: "24h",
      //algorithm: "RS256",
    },
    REFRESH_TOKEN_OPTIONS: {
      expiresIn: "3d",
    },
  },
  PNG_TOKEN_EXPIRY_TIME: 1 * 24 * 60 * 60,
  APPLICATION_EXPIRY_TIME: 1 * 24 * 60 * 60,
  mailConfig: {
    cc: ["<EMAIL>", "<EMAIL>"],
    bcc: ["<EMAIL>", "<EMAIL>"],
  },
};
