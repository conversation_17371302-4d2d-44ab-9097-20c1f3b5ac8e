const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_customer_connection_field_officer_mapping', {
    customer_connection_fo_mapping_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    customer_connection_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer_connection',
        key: 'customer_connection_id'
      }
    },
    field_officer_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_admin',
        key: 'admin_id'
      }
    },
    cycle_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_cycle',
        key: 'cycle_id'
      }
    },
    visit_datetime: {
      type: DataTypes.DATE,
      allowNull: true
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    visit_status: {
      type: DataTypes.STRING(12),
      allowNull: false,
      defaultValue: "pending"
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_customer_connection_field_officer_mapping',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_customer_connection_fo_mapping_id",
        unique: true,
        fields: [
          { name: "customer_connection_fo_mapping_id" },
        ]
      },
    ]
  });
};
