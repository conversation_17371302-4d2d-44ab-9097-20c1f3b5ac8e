const { json } = require("sequelize");
const ErrorLog = require("../model/error-log.model");

const createErrorLog = async (errorLogData) => {
  return await ErrorLog.create(errorLogData);
};

const logError = async (
  req,
  module_type,
  record,
  action,
  error_msg,
  api_name
) => {
  const moduleType = module_type;
  const Record = record;
  const Action = action;
  const errorMsg = error_msg.message;
  const apiName = api_name;
  const createdBy = req?.body?.created_by || req?.user?.user_id;

  try {
    if (process.env.IS_ERROR_LOG == "true") {
      await createErrorLog({
        module_type: moduleType,
        record: JSON.stringify(Record),
        action: Action,
        error_msg: JSON.stringify(errorMsg),
        endpoint: req.originalUrl,
        api_name: apiName,
        ip_address: req.body.ip_address,
        browser: req.body.browser,
        created_by: createdBy,
        updated_by: createdBy,
      });
    }
  } catch (err) {
    console.error(err.message);
  }
};

module.exports = {
  logError,
};
