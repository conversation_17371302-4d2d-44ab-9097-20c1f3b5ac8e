const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_customer_connection_tpi_mapping",
    {
      customer_connection_tpi_mapping_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
        unique: "UK_tcctm_customer_connection_id_tpi_id_tpi_status_seq_no",
      },
      tpi_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_tcctm_customer_connection_id_tpi_id_tpi_status_seq_no",
      },
      tpi_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
        unique: "UK_tcctm_customer_connection_id_tpi_id_tpi_status_seq_no",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      reason_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_masters",
          key: "master_id",
        },
      },
      remarks: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_latest: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      sequence_no: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        unique: "UK_tcctm_customer_connection_id_tpi_id_tpi_status_seq_no",
      },
    },
    {
      sequelize,
      tableName: "trx_customer_connection_tpi_mapping",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "IX_FK_trxcustomerconnectiontpimapping_customer_connection_id",
          fields: [{ name: "customer_connection_id" }],
        },
        {
          name: "IX_FK_trxcustomerconnectiontpimapping_tpi_id",
          fields: [{ name: "tpi_id" }],
        },
        {
          name: "PK_customer_connection_tpi_mapping_id",
          unique: true,
          fields: [{ name: "customer_connection_tpi_mapping_id" }],
        },
        {
          name: "UK_tcctm_customer_connection_id_tpi_id_tpi_status_seq_no",
          unique: true,
          fields: [
            { name: "customer_connection_id" },
            { name: "tpi_id" },
            { name: "tpi_status" },
            { name: "sequence_no" },
          ],
        },
      ],
    }
  );
};
