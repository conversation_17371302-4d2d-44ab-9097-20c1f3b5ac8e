const { getMeterReadingHistoryList, getMeterReadingDetails, getLastMeterReading, saveNewReading, getJointMeterReading, getCustomerMeterRetails } = require('../repository/meter-reading-repository');
const redisHelper = require('../helpers/redisHelper');
const Tesseract = require('tesseract.js');
const path = require('path');
const sharp = require('sharp');
const { formatMeterReading } = require('../utils/commonFn');
const azureSASHelper = require("../helpers/azureSASSDKHelper");

const getMeterReadingHistoryListService = async (payload) => {

  const results = await getMeterReadingHistoryList(payload);

  return results;
};

const getMeterReadingDetailsService = async (payload) => {

  const result = await getMeterReadingDetails(payload);

  return result;
};

const getRedisMeterReadingConnectionKey = (customerConnectionId, customerMeterId) => {
  return `meter_reading:${customerConnectionId}`;
};


const processMeterReadingImage = async (payload, files) => {
  try {
    const { customer_connection_id, declared_by, cycle_id } = payload;

    // Extract meter reading value using third-party service
    const meterReadingValue = await processImageWithDummyData(files);

  //   if (!meterReadingValue || !meterReadingValue.meterNumber || !meterReadingValue.meterReading) {
  //     return { error: 'Failed to read Image, please reupload' };
  //   }

  //  const  customerMeterDetails =  await getCustomerMeterRetails(customer_connection_id,meterReadingValue.meterNumber);
  //  if(!customerMeterDetails){
  //   return { error: 'Invalid Meter Image , Please upload the valid meter Image' };
  //  }
   const  lastMeter_reading =  await getMeterReadingDetails(payload);
    // Generate Redis key
    const redisKey = getRedisMeterReadingConnectionKey(customer_connection_id);

    // Store the extracted data in Redis
    const cacheData = {
      customer_connection_id,
      cycle_id,
      meter_reading_value: formatMeterReading(meterReadingValue),
      meter_reading_file: 'temp',
      declared_by: declared_by,
      uploaded_at: new Date(),
      meterNo : lastMeter_reading?.data?.meterNo,
      lastReading : lastMeter_reading?.data?.meterReading,
      submittedDate : lastMeter_reading?.data?.submittedDate,
      upload_status : "details_submitted",
      file_name: files.filename,
      file_size: files.size,
    }

    await redisHelper.setData(redisKey, cacheData, 300); // Set TTL to 300 seconds (5 minutes)

    return {
      message: 'Meter reading processed and cached successfully.',
      data: cacheData,
    };
  } catch (error) {
    console.error('Error in processMeterReadingImage:', error);
    throw new Error('Error processing the uploaded document');
  }
};


const preprocessImage = async (imagePath) => {
  try {
    const processedImagePath = path.join(__dirname, 'processed_meter.png');

    // Read, convert to grayscale, apply thresholding
    await sharp(imagePath)
      .grayscale()
      .threshold(150) // Adjust threshold value if needed
      .toFile(processedImagePath);

    return processedImagePath;
  } catch (error) {
    console.error('Error preprocessing image:', error);
    return imagePath; // Fallback to original if preprocessing fails
  }
};

/**
* Extracts meter number and meter reading using OCR.
* @param {string} imagePath - Path to the image file.
* @returns {Promise<{meterNumber: string, meterReading: string}>}
*/
const extractMeterDetails = async (imagePath) => {
  try {
      console.log('Preprocessing image...');
      const processedImage = await preprocessImage(imagePath);

      const { data: { text } } = await Tesseract.recognize(processedImage, 'eng', {
          tessedit_char_whitelist: '0123456789 ',
      });


      // Extract meter number (Assumed to be a long numeric sequence)
      const meterNumberMatch = text.match(/\d{10,}/);
      const meterNumber = meterNumberMatch ? meterNumberMatch[0] : 'Not Found';

      // Extract meter reading (focusing on 8-digit formats)
      const meterReadingMatch = text.match(/\b\d{1}\s\d{3}\s\d{4}\b/); // Matches "0 000 0025"

      let meterReading = 'Not Found';

      if (meterReadingMatch) {
          meterReading = meterReadingMatch[0].replace(/\s/g, ''); // Remove spaces
          
          // Ensure meter reading is exactly 8 digits
          if (meterReading.length < 8) {
              meterReading = meterReading.padStart(8, '0'); // Pad with leading zeros if needed
          }
      }

      console.log('Extracted Meter Reading:', meterReading);

      return { meterNumber, meterReading };

  } catch (error) {
      console.error('Error extracting meter details:', error);
      throw new Error('Failed to extract meter details from image.');
  }
};

/**
 * Process the image with third-party integration and extract meter details.
 * @param {Object} file - File metadata object.
 * @returns {Promise<{meterNumber: string, meterReading: string}>}
 */
const processImageWithThirdParty = async (file) => {
    try {
        console.log('Processing image with third-party tool:', file);

        // Ensure correct image path is used
        const imagePath = path.resolve(file.destination, file.filename);

        // Extract meter details using OCR
        const extractMeterDetail = await extractMeterDetails(imagePath);
        console.log('Extracted Meter Details:', extractMeterDetail);

        return extractMeterDetail;
    } catch (error) {
        console.error('Error processing image with third-party tool:', error);
        throw new Error('Failed to extract meter reading from image.');
    }
};

// Mock function for third-party image processing
const processImageWithDummyData = async (file) => {
  try {
    // Simulate third-party API call to process the image
    console.log('Processing image with third-party tool:', file?.filename);

    // Replace with actual third-party API integration
    const extractedValue = Math.floor(Math.random() * 1000); // Mocked extracted meter reading value
    return extractedValue;
  } catch (error) {
    console.error('Error processing image with third-party tool:', error);
    throw new Error('Failed to extract meter reading from image.');
  }
};





const verifyMeterReadingService = async (payload) => {
  const { customer_connection_id, current_reading } = payload;

  if (!customer_connection_id || current_reading === undefined) {
    return { isValid: false, message: 'customer_connection_id, and current_reading are required.' };
  }

  // Generate Redis key
  const redisKey = getRedisMeterReadingConnectionKey(customer_connection_id);

  // Fetch last reading from Redis
  let cachedData = await redisHelper.getData(redisKey) || {}; // Ensure cachedData is not null

  // Fetch last recorded meter reading from DB
  const lastReading = await getLastMeterReading(payload);

  const previousReading = lastReading?.current_reading ?? 0; // Default to 0 if null
  if (previousReading !== null && current_reading < previousReading) {
    return { isValid: false, message: 'Current reading cannot be less than the previous reading.' };
  }

  // Calculate reading units safely
  const readingUnits = Math.max(0, current_reading - previousReading);

  // Merge new reading with existing cached data
  const updatedData = {
    ...cachedData, // Preserve other existing fields
    current_reading,
    previous_reading: previousReading,
    reading_units: readingUnits,
    updated_at: new Date(), // Update timestamp
  };

  console.log("Updated Data to be stored in Redis:", updatedData);

  // Store updated data in Redis
  await redisHelper.setData(redisKey, updatedData, 3600);

  return { isValid: true, message: 'Meter reading is valid and updated' };
};

const getMeterReadingUploadDetailsFromCache = async (query) => {
  const { customer_connection_id } = query;
  // Generate Redis key
  const redisKey = getRedisMeterReadingConnectionKey(customer_connection_id);
  // // Fetch data from Redis
  // let cachedData = await redisHelper.getData(redisKey);

  // if (cachedData) {
  //   return cachedData;
  // }

  // If cache is empty, fetch from DB
  const meterReadingDetails = await getLastMeterReading({ customer_connection_id });

  if (!meterReadingDetails) {
    return null; // No reading found in DB
  }

  return meterReadingDetails;
};

const storeMeterReadingFromCacheToDB = async (payload) => {

  const { customer_connection_id } = payload;
  // Generate Redis key
  const redisKey = getRedisMeterReadingConnectionKey(customer_connection_id);


  // Fetch data from Redis
  const cachedData = await redisHelper.getData(redisKey);

  if (!cachedData) {
    throw new Error('No meter reading data found.');
  }

    let filePath = "customer/cus_" + customer_connection_id + "/meterreading";
        filePath = cachedData.filePath + "/" + cachedData.file_name;
 const source = `temp/${cachedData.file_name}`;

  await azureSASHelper.moveBlobWithinContainer(source, filePath);
      
  const newReading = await saveNewReading(cachedData,filePath);


  console.log('Meter reading stored in DB:', newReading);


  return newReading
};

const getJointMeterReadingDetailsService = async (payload) => {

  const result = await getJointMeterReading(payload);

  return result;
};







module.exports = {
  getMeterReadingHistoryListService, getMeterReadingDetailsService, processMeterReadingImage, verifyMeterReadingService, getMeterReadingUploadDetailsFromCache, storeMeterReadingFromCacheToDB, getJointMeterReadingDetailsService
};
