const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_alternation_request",
    {
      alternation_request_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      customer_connection_address_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection_address",
          key: "customer_connection_address_id",
        },
      },
      area_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_masters",
          key: "master_id",
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      alternation_request_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
    },
    {
      sequelize,
      tableName: "trx_alternation_request",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK__trx_alte__3AB962153C08F0DA",
          unique: true,
          fields: [{ name: "alternation_request_id" }],
        },
      ],
    }
  );
};
