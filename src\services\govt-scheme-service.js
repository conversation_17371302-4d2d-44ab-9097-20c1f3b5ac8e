const govtSchemeRepository = require("../repository/govt-scheme-repository");
const message = require("../utils/constants");

const getGovtSchemeList = async ({ page, limit }) => {
    return govtSchemeRepository.getGovtSchemeList(page, limit);
};

const createGovtSchemeList = async (input) => {
    const already = await govtSchemeRepository.CheckExistingname(input.scheme_name);
    if (already) {
        return { message: message.NAME_ALREADY_EXIST };
    }
    return govtSchemeRepository.createGovtSchemeList(input);
};

const updateGovtSchemeList = async (id, dto) => {
    const existingScheme = await govtSchemeRepository.showGovtSchemeList(id);
    if (!existingScheme) {
        throw new Error("Scheme not found");
    }
    return govtSchemeRepository.updateGovtSchemeList(id, dto);
};

const deleteGovtSchemeList = async (id) => {
    const existingScheme = await govtSchemeRepository.showGovtSchemeList(id);
    if (!existingScheme || existingScheme.is_delete === 1) {
        throw new Error("Scheme already deleted or not found");
    }
    return govtSchemeRepository.deleteGovtSchemeList(id, { is_delete: 1 });
};

module.exports = {
    getGovtSchemeList,
    createGovtSchemeList,
    updateGovtSchemeList,
    deleteGovtSchemeList,
};