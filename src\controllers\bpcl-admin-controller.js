const bpclAdminService = require("../services/bpcl-admin-service");
const bpclAdminRepository = require("../repository/bpcl-admin-repository");
const { sendSuccess, sendError } = require("../helpers/responseHelper");

const getChannelPartnersCount = async (req, res) => {
  try {
    const bpclAdminId = req.user.admin_id;
    const counts = await bpclAdminService.getChannelPartnersCount(req.body);
    sendSuccess(
      req,
      res,
      counts,
      "Channel partners count retrieved successfully"
    );
  } catch (error) {
    console.error("Error in getChannelPartnersCount controller:", error);
    sendError(req, res, error, "Failed to retrieve channel partners count");
  }
};
const contractorOnboarding = async (req, res) => {
  try {
    //const data = "contractorOnboarding";
    const data = await bpclAdminService.contractorOnboardingService(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in contractorOnboarding controller:", error);
    sendError(req, res, error, error.message);
  }
};
const getAreaList = async (req, res) => {
  try {
    const data = await bpclAdminService.getAreaListService(req);
    sendSuccess(req, res, data, "Area fetched successfully");
  } catch (error) {
    console.error("Error in getAreaList controller:", error);
    sendError(req, res, error, "Failed to get area list");
  }
};
const getContractorPendingApprovalList = async (req, res) => {
  try {
    const data =
      await bpclAdminService.getContractorPendingApprovalService(req);
    sendSuccess(
      req,
      res,
      data,
      `${req.body.type} approval fetched successfully`
    );
  } catch (error) {
    console.error(
      "Error in getContractorPendingApprovalList controller:",
      error
    );
    sendError(req, res, error, "Failed to pending approval list");
  }
};
const contractorAcceptReject = async (req, res) => {
  try {
    const data = await bpclAdminService.acceptRejectService(req);
    sendSuccess(
      req,
      res,
      data,
      `Contrator has been ${req.body.type} successfully.`
    );
  } catch (error) {
    console.error(
      "Error in getContractorPendingApprovalList controller:",
      error
    );
    sendError(req, res, error, "Failed to pending approval list");
  }
};

const subAdminOnboarding = async (req, res) => {
  try {
    const data = await bpclAdminService.subAdminOnboardingService(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in subAdminOnboarding controller:", error);
    sendError(req, res, error, error.message);
  }
};

const getSubAdminTypeList = async (req, res) => {
  try {
    const data = await bpclAdminService.geSubAdminTypeListService(req);
    sendSuccess(req, res, data, "Sub-Admin type fetched successfully");
  } catch (error) {
    console.error("Error in getAreaList controller:", error);
    sendError(req, res, error, "Failed to get area list");
  }
};
const getGeographicalAreaList = async (req, res) => {
  try {
    const data = await bpclAdminService.getGeographicalAreaListService(req);
    sendSuccess(req, res, data, "Geographical Area fetched successfully");
  } catch (error) {
    console.error("Error in getGeographicalAreaList controller:", error);
    sendError(req, res, error, "Failed to get getGeographicalAreaList");
  }
};

const getTpiSalesListByAreaWise = async (req, res) => {
  try {
    const requestBody = req.body;
    const page = requestBody.page || 1;
    const limit = requestBody.limit || 10;
    const data = await bpclAdminService.getTpiSalesByAreaWiseListService(
      requestBody.ga_area_id,
      page,
      limit,
      requestBody.area_id,
      requestBody.search
    );
    sendSuccess(req, res, data, "Tpi sales area fetched successfully");
  } catch (error) {
    console.error("Error in getTpiSalesListByAreaWise controller:", error);
    sendError(req, res, error, "Failed to get getTpiSalesListByAreaWise");
  }
};
const getTpiEngineerListByAreaWise = async (req, res) => {
  try {
    const requestBody = req.body;
    const page = requestBody.page || 1;
    const limit = requestBody.limit || 10;
    const data = await bpclAdminService.getTpiEngineerByAreaWiseListService(
      requestBody.ga_area_id,
      page,
      limit,
      requestBody.area_id,
      requestBody.search
    );
    sendSuccess(req, res, data, "Tpi engineer area fetched successfully");
  } catch (error) {
    console.error("Error in getTpiEngineerListByAreaWise controller:", error);
    sendError(req, res, error, "Failed to get getTpiEngineerListByAreaWise");
  }
};

const detailsByPersonaRole = async (req, res) => {
  try {
    const data = await bpclAdminService.detailsByPersonaRole(req.body);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in detailsByPersonaRole controller:", error);
    sendError(req, res, error, error.message);
  }
};

const getContractorManagementTPI = async (req, res) => {
  try {
    const data = await bpclAdminService.getContractorManagementTPIService(
      req.body
    );
    sendSuccess(
      req,
      res,
      data,
      "TPI contractor management data fetched successfully"
    );
  } catch (error) {
    console.error("Error in getContractorManagementTPI controller:", error);
    sendError(
      req,
      res,
      error,
      "Failed to fetch TPI contractor management data"
    );
  }
};

const getContractorManagementAreaUpdate = async (req, res) => {
  try {
    const data = await bpclAdminService.getContractorManagementAreaUpdate(req);
    sendSuccess(req, res, data, "data updated successfully");
  } catch (error) {
    console.error(
      "Error in getContractorManagementAreaUpdate controller:",
      error
    );
    sendError(
      req,
      res,
      error,
      "Failed to update GA contractor management data"
    );
  }
};

const soStatusUpdate = async (req, res) => {
  try {
    const data = await bpclAdminService.soStatusUpdate(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in SO Status Update controller:", error);
    sendError(req, res, error, error.message);
  }
};

const eoStatusUpdate = async (req, res) => {
  try {
    const data = await bpclAdminService.eoStatusUpdate(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in EO Status Update controller:", error);
    sendError(req, res, error, error.message);
  }
};

const eoJmrUpdate = async (req, res) => {
  try {
    const data = await bpclAdminService.eoJmrUpdate(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in EO JMR Status Update controller:", error);
    sendError(req, res, error, error.message);
  }
};

const getProfileData = async (req, res) => {
  try {
    const data = await bpclAdminService.getProfileData(req);
    sendSuccess(req, res, data, "data fetched successfully");
  } catch (error) {
    console.error(
      "Error in getProfileData controller:",
      error
    );
    sendError(
      req,
      res,
      error,
      "Failed to fetch profile data"
    );
  }
};

const customerApprovalList = async (req, res) => {
  try {

    const data = await bpclAdminService.getCustomerApprovalListService(req);

    sendSuccess(req, res, data, "Data fetched successfully");
  } catch (error) {
    console.error(
      "Error in customerApprovalList controller:",
      error
    );
    sendError(
      req,
      res,
      error,
      "Failed to fetch customer Approval list data"
    );
  }
};

const customerPersonalDetails = async (req, res) => {
  try {

    const data = await bpclAdminService.getCustomerPersonalDetailsService(req);

    sendSuccess(req, res, data, "Data fetched successfully");
  } catch (error) {
    console.error(
      "Error in customerPersonalDetails controller:",
      error
    );
    sendError(
      req,
      res,
      error,
      "Failed to fetch customer personal details data"
    );
  }
};
const customerApprovalListEo = async (req, res) => {
  try {

    const data = await bpclAdminService.getCustomerApprovalListEoService(req);

    sendSuccess(req, res, data, "Data fetched successfully");
  } catch (error) {
    console.error(
      "Error in customerApprovalListEo controller:",
      error
    );
    sendError(
      req,
      res,
      error,
      "Failed to fetch customer Approval list for EO data"
    );
  }
};

const customerApprovalListManualGmr = async (req, res) => {
  try {

    const data = await bpclAdminService.getCustomerApprovalListManualGmrService(req);

    sendSuccess(req, res, data, "Data fetched successfully");
  } catch (error) {
    console.error(
      "Error in customerApprovalListManualGmr controller:",
      error
    );
    sendError(
      req,
      res,
      error,
      "Failed to fetch customer Approval list data for manual GMR(EO)."
    );
  }
};

const customerlmcInstallationDetails = async (req, res) => {
  try {

    const mapped_lmc_connection_stage_id = req.body.mapped_lmc_connection_stage_id

    const data = await bpclAdminService.getCustomerlmcInstallationDetailsService(mapped_lmc_connection_stage_id);

    sendSuccess(req, res, data, "Data fetched successfully.");
  } catch (error) {
    console.error(
      "Error in customerlmcInstallationDetails controller:",
      error
    );
    sendError(
      req,
      res,
      error,
      "Failed to fetch customer lmc installation details data."
    );
  }
};
const customerManualGmrDetails = async (req, res) => {
  try {

    const data = await bpclAdminService.getcustomerManualGmrDetailsService(req.body.tmma_id);

    sendSuccess(req, res, data, "Data fetched successfully.");
  } catch (error) {
    console.error(
      "Error in customerManualGmrDetails controller:",
      error
    );
    sendError(
      req,
      res,
      error,
      "Failed to fetch customer Manual GMR details data."
    );
  }
};

const getSoCommentDetails = async (req, res) => {
  try {
    const data = await bpclAdminService.getSoCommentDetails(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in SO Detail controller:", error);
    sendError(req, res, error, error.message);
  }
};

const getEoCommentDetails = async (req, res) => {
  try {
    const data = await bpclAdminService.getEoCommentDetails(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in EO Detail controller:", error);
    sendError(req, res, error, error.message);
  }
};

const getEoJmrCommentDetails = async (req, res) => {
  try {
    const data = await bpclAdminService.getEoJmrCommentDetails(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in EO JMR Detail controller:", error);
    sendError(req, res, error, error.message);
  }
};

const getCommentDetails = async (req, res) => {
  try {
    const data = await bpclAdminService.getCommentDetails(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in Comment Detail controller:", error);
    sendError(req, res, error, error.message);
  }
};

const getBpclAdminGeographicalAreas = async (req, res) => {
  try {
    const data = await bpclAdminRepository.getBpclAdminGeographicalAreas(req);
    sendSuccess(req, res, data, data.message);
  } catch (error) {
    console.error("Error in Geographical Areas controller:", error);
    sendError(req, res, error, error.message);
  }
};

module.exports = {
  getChannelPartnersCount,
  contractorOnboarding,
  getAreaList,
  getContractorPendingApprovalList,
  contractorAcceptReject,
  getContractorManagementTPI,
  subAdminOnboarding,
  getSubAdminTypeList,
  getGeographicalAreaList,
  detailsByPersonaRole,
  getTpiSalesListByAreaWise,
  getTpiEngineerListByAreaWise,
  getContractorManagementAreaUpdate,
  soStatusUpdate,
  eoStatusUpdate,
  eoJmrUpdate,
  getProfileData,
  customerApprovalList,
  customerPersonalDetails,
  customerApprovalListEo,
  customerApprovalListManualGmr,
  customerlmcInstallationDetails,
  customerManualGmrDetails,
  getSoCommentDetails,
  getEoJmrCommentDetails,
  getEoCommentDetails,
  getCommentDetails,
  getBpclAdminGeographicalAreas
};
