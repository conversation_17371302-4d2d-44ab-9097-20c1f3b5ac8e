const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_admin_mapped_areas",
    {
      admin_mapped_area_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_trxadminmappedareas_adminid_areaid",
      },
      area_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_areas",
          key: "area_id",
        },
        unique: "UK_trxadminmappedareas_adminid_areaid",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_admin_mapped_areas",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "IX_FK_trxadminmappedareas_adminid",
          fields: [{ name: "admin_id" }],
        },
        {
          name: "IX_FK_trxadminmappedareas_areaid",
          fields: [{ name: "area_id" }],
        },
        {
          name: "PK_trxadminmappedareas_admin_mapped_area_id",
          unique: true,
          fields: [{ name: "admin_mapped_area_id" }],
        },
        {
          name: "UK_trxadminmappedareas_adminid_areaid",
          unique: true,
          fields: [{ name: "admin_id" }, { name: "area_id" }],
        },
      ],
    }
  );
};
