const { dbModels } = require('../database/connection');

const countProofs = async ({ proof_type }) => {
  return dbModels.mst_proof_master.count({
    where: {
      proof_type,
      is_active: 1, // Adjust this based on schema
    },
  });
};
const getProofsByType = async ({ proof_type, offset, limit }) => {
  return dbModels.mst_proof_master.findAll({
    where: {
      proof_type,
      is_active: 1, // Adjust this based on schema
    },
    offset,
    limit,
  });
};

const createProofMaster = async (dto) => {
  return await dbModels.mst_proof_master.create(dto);
};

const showProofMaster = async (id) => {
  return await dbModels.mst_proof_master.findByPk(id);
};

const updateProofMaster = async (id, dto) => {
  const data = await showProofMaster(id);
  if (!data) {
    throw new Error("Proof Master record not found");
  }
  return data.update(dto);
};

const checkProofMasterExistence = async (id, proofType) => {
  return await dbModels.mst_proof_master.findOne({
    where: {
      proof_master_id: id,
      proof_type: proofType,
      is_active: 1, // Ensure it's not already deleted
    },
  });
};

const deleteProofMaster = async (id, dto) => {
  const data = await dbModels.mst_proof_master.update(dto, {
    where: {
      proof_master_id: id,
    },
  });
  return data;
};

const checkExistingName = async (name) => {
  return await dbModels.mst_proof_master.findOne({
    where: { proof_name: name },
    raw: true,
  });
};

module.exports = {
  getProofsByType,
  countProofs,
  createProofMaster,
  showProofMaster,
  updateProofMaster,
  checkProofMasterExistence,
  deleteProofMaster,
  checkExistingName,
};
