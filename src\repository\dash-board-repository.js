const { dbModels } = require('../database/connection');
const { Op, col } = require('sequelize');
const redisHelper = require('../helpers/redisHelper');
const { notification } = require('../controllers/dash-board-controller');
const dashBoarGraphdDetails = async (
  customer_connection_id,
  startDate,
  endDate
) => {
  console.log(customer_connection_id, startDate, endDate);

  try {
    return await dbModels.trx_customer_meter_readings.findAll({
      attributes: [
        'reading_units',
        'customer_connection_id',
        'customer_meter_id',
      ],
      include: [
        {
          model: dbModels.mst_cycle,
          as: 'cycle',
          attributes: ['start_date'],
          where: {
            start_date: { [Op.between]: [startDate, endDate] },
          },
        },
      ],
      where: {
        customer_connection_id: customer_connection_id,
      },
      raw: true,
      nest: true,
    });
  } catch (error) {
    console.error('Error fetching meter reading data:', error);
    throw error;
  }
};
const fetchNotification = async (payload) => {
  try {
    const data = await dbModels.trx_customer_connection.findOne({
      attributes: ['customer_connection_id'],
      include: [
        {
          model: dbModels.trx_customer_connection_address,
          as: 'trx_customer_connection_addresses', // Matching the alias
          required: false,
          attributes: ['connection_address'],
        },
        {
          model: dbModels.trx_customer_notifications,
          as: 'trx_customer_notifications', // Matching the alias
          required: false,
          attributes: [
            'notification_id',
            'customer_connection_id',
            'alert_type',
            'subject',
            'description',
            'alert_category',
            'alert_request_id'
          ],
        },
      ],
      where: {
        customer_connection_id: payload.customer_connection_id,
      },
    });
    const response = {
      notifications: data?.trx_customer_notifications,
      address: data?.trx_customer_connection_addresses[0].connection_address,
    };
    return response;
  } catch (error) {
    console.error('Error fetching notifications:', error);
    throw error;
  }
};

const fetchBannerImages = async (payload) => {
  try {
    return await dbModels.mst_banners.findAll({
      where: {
        page_type: payload.page_type,
        is_active: 1, // Corrected condition
      },
      raw:true,
    });
  } catch (error) {
    console.error('Error fetching banner images:', error);
    throw error;
  }
};

const createNotification = async (insertNotification) => {
  try {
    // Create entry in trx_tpi_onboard_approve table
    const resp = await dbModels.trx_customer_notifications.create(insertNotification);
    return resp;
  } catch (error) {
    console.error('Error in create Notification Function:', error);
    throw error;
  }
};


const viewNotification = async (whereCondition) => {
  try {

    return await dbModels.trx_customer_name_transfer_request.findOne({
      where: whereCondition,
      include: [
        {
          model: dbModels.trx_customer_connection,
          as: 'existing_connection', // Matching the alias
          attributes: [
            'customer_connection_id',
            'bpcl_id',
            'first_name',
            'last_name',
            'email',
            'mobile',
            'bp_id',
            'account_type',
            'available_pngareas_id'
          ],
          required: false,
          include: [
            {
              model: dbModels.trx_customer_connection_address,
              as: 'trx_customer_connection_addresses', // Matching the alias
              required: false,
              attributes: [
                'connection_address',
                'connection_pincode',
                'connection_landmark',
                'connection_block_no',
                'connection_floor_no',
                'connection_house_no',
                'connection_state',
                'connection_city',
                'connection_district',
              ],
            },
            {
              model: dbModels.trx_connection_payments,
              as: 'trx_connection_payments', // Matching the alias
              attributes: ['payment_status'],
              required: false,
            },
          ],
        },
        {
          model: dbModels.trx_customer_connection,
          as: 'new_connection', // Matching the alias
          required: false,
          attributes: [
            'customer_connection_id',
            'bpcl_id',
            'customer_id',
            'first_name',
            'last_name',
            'email',
            'mobile',
            'bp_id',
            'account_type',
            'available_pngareas_id'
          ],
          include: [
            {
              model: dbModels.trx_customer_connection_address,
              as: 'trx_customer_connection_addresses', // Matching the alias
              required: false,
              attributes: [
                'connection_address',
                'connection_pincode',
                'connection_landmark',
                'connection_block_no',
                'connection_floor_no',
                'connection_house_no',
                'connection_state',
                'connection_city',
                'connection_district',
              ],
            },
          ],
        },
        {
          model: dbModels.mst_masters,
          as: 'name_transfer_reason_mst_master', // Matching the alias
          attributes: ['master_type','master_name','master_value'],
          required: false,
        },
        
      ],
      attributes: [
        'name_transfer_request_id',
        'name_transfer_status',
      ],
      raw: true,
      nest: true,
    });
  } catch (error) {
    console.error('Error fetching on view Notification data:', error);
    throw error;
  }
};

module.exports = {
  dashBoarGraphdDetails,
  fetchNotification,
  fetchBannerImages,
  createNotification,
  viewNotification
};
