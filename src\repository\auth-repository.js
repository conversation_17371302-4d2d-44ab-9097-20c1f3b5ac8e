const { dbModels } = require("../database/connection");

const findUserByEmail = async (email) => {
  try {
    const user = await dbModels.mst_admin.findOne({
      where: { email },
      include: {
        model: dbModels.mst_persona_role,
        as: "persona_role",
        attributes: ["role_name"],
      },
      logging: console.log,
    });
    return user;
  } catch (error) {
    console.error("Error in findUserByEmail:", error);
    throw error;
  }
};
const findUserByMobile = async (mobile) => {
  try {
    const user = await dbModels.mst_admin.findOne({
      where: { mobile },
      include: {
        model: dbModels.mst_persona_role,
        as: "persona_role",
        attributes: ["role_name"],
      },
      logging: console.log,
    });
    return user;
  } catch (error) {
    console.error("Error in findUserByEmail:", error);
    throw error;
  }
};

const findUserByIdAndRole = async (adminId) => {
  try {
    const user = await dbModels.mst_admin.findOne({
      where: {
        admin_id: adminId,
        user_role: ["supervisor", "contractor"],
      },
      logging: console.log,
    });
    return user;
  } catch (error) {
    console.error("Error in findUserByIdAndRole:", error);
    throw error;
  }
};

const updateLastLogin = async (email) => {
  try {
    const user = await dbModels.mst_admin.findOne({
      where: { email },
      logging: console.log,
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Update last_login field
    user.last_logged_in = new Date(); // current timestamp

    await user.save(); // persist the change

    return user;
  } catch (error) {
    console.error("Error in updateLastLogin:", error);
    throw error;
  }
};

const updatePasswordByEmail = async (email, newPassword) => {
  try {
    const result = await dbModels.mst_admin.update(
      { password: newPassword },
      {
        where: { email: email },
        logging: false,
      }
    );
    return result;
  } catch (error) {
    console.error("Error updating password:", error);
    throw error;
  }
};

const getSubAdminRole = async (adminId) => {
  try {
    const roleMapping = await dbModels.trx_bpcl_admin_role_mapping.findOne({
      where: { bpcl_admin_id: adminId, is_active: true },
      include: [
        {
          model: dbModels.mst_bpcl_admin_roles,
          as: "bpcl_admin_role",
          attributes: ["slug", "role_name"]
        }
      ],
      attributes: ["bpcl_admin_role_id"],
      raw: true,
      nest: true
    });

    if (!roleMapping) {
      return null;
    }

    return roleMapping.bpcl_admin_role.slug;
  } catch (error) {
    console.error("Error in getSubAdminRole:", error);
    throw error;
  }
};

const getExtendedAdminData = async (email) => {
  const adminRecord = await dbModels.mst_admin.findOne({
    where: { email },
  });

  if (!adminRecord) {
    console.warn("Admin record not found for email:", email);
    return null;
  }

  const admin_id = adminRecord.admin_id;

  const mappingData = await dbModels.trx_bpcl_admin_role_mapping.findOne({
    where: { bpcl_admin_id: admin_id },
    attributes: ["bpcl_admin_role_id"],
  });

  const bpcl_admin_role_id = mappingData?.bpcl_admin_role_id || null;

  let subAdminRole = null;
  if (bpcl_admin_role_id) {
    subAdminRole = await dbModels.mst_bpcl_admin_roles.findOne({
      where: { bpcl_admin_role_id },
      attributes: ["role_name"],
    });
  }
  return {
    sub_admin_role_name: subAdminRole?.role_name || "Unknown",
  };
};

module.exports = {
  findUserByEmail,
  findUserByMobile,
  findUserByIdAndRole,
  updateLastLogin,
  updatePasswordByEmail,
  getSubAdminRole,
  getExtendedAdminData,
};
