const { sendSuccess, sendError } = require('../helpers/responseHelper');
const dmaSupervisorRepository = require('../repository/dma-repository');

exports.getDashboardSummary = async (req, res) => {
  try {
    const contractorId = req.user.admin_id;

    let areaIds = [];

    if (Array.isArray(req.body.area_id)) {
      areaIds = req.body.area_id
        .map((id) => parseInt(id))
        .filter((id) => !isNaN(id));
    } else if (req.body.area_id) {
      const id = parseInt(req.body.area_id);
      if (!isNaN(id)) areaIds = [id];
    }

    const summaryData = await dmaSupervisorRepository.getContractorSummary(
      contractorId,
      areaIds
    );

    const response = {
      contractor_id: contractorId,
      ...summaryData,
    };

    sendSuccess(req, res, response, 'Dashboard summary retrieved successfully');
  } catch (error) {
    console.error('Error in getDashboardSummary:', error);
    sendError(req, res, null, error.message || 'Failed to retrieve dashboard summary');
  }
};

exports.getSupervisorsPerformance = async (req, res) => {
  try {
    const contractorId = req.user.admin_id;

    // Pass pagination params from req.body or query, with defaults
    const page = parseInt(req.body.page) || 1;
    const limit = parseInt(req.body.limit) || 10;
    const search = req.body.search || '';
    const sortBy = req.body.sortBy || 'supervisor_name';
    const sortOrder = req.body.sortOrder || 'ASC';

    const performanceData = await dmaSupervisorRepository.getSupervisorPerformance(
      contractorId,
      page,
      limit,
      search,
      sortBy,
      sortOrder
    );

    // Construct response data object properly
    const response = {
      contractor_id: contractorId,
      supervisors_performance: performanceData.supervisors_performance, // <-- add this!
      totalRecords: performanceData.totalRecords,
      totalPages: performanceData.totalPages,
      currentPage: performanceData.currentPage,
    };

    sendSuccess(req, res, response, 'Supervisors performance data retrieved successfully');
  } catch (error) {
    console.error('Error in getSupervisorsPerformance:', error);
    sendError(req, res, null, error.message || 'Failed to retrieve supervisors performance data');
  }
};



exports.assignCustomersToSupervisor = async (req, res) => {
    try {
        const contractorId = req.user.admin_id;
        const { supervisor_id, customer_ids } = req.body;

        const supervisor = await dmaSupervisorRepository.getDmaSupervisorById(supervisor_id);
        
        if (!supervisor) {
            return sendError(
                req, 
                res, 
                null, 
                'Supervisor not found',
                404
            );
        }

        if (!supervisor.is_active) {
            return sendError(
                req, 
                res, 
                null, 
                'Cannot assign customers to inactive supervisor',
                400
            );
        }

        if (supervisor.created_by !== contractorId) {
            return sendError(
                req, 
                res, 
                null, 
                'Unauthorized: Supervisor does not belong to your organization',
                403
            );
        }

        const result = await dmaSupervisorRepository.assignCustomersToSupervisor(
            supervisor_id,
            customer_ids
        );

        if (result.unassigned.length > 0 || result.alreadyAssigned.length > 0) {
            return sendError(
                req,
                res,
                { 
                    assigned: result.assigned,
                    unassigned: result.unassigned,
                    alreadyAssigned: result.alreadyAssigned,
                    messages: result.messages
                },
                'Some customers could not be assigned',
                400
            );
        }

        sendSuccess(
            req,
            res,
            { 
                assigned: result.assigned,
                messages: result.messages 
            },
            'Customers assigned successfully'
        );
    } catch (error) {
        console.error('Error in assignCustomersToSupervisor:', error);
        sendError(req, res, null, error.message || 'Failed to assign customers');
    }
};

exports.reassignCustomer = async (req, res) => {
    try {
        const contractorId = req.user.admin_id;
        const { customer_id, new_supervisor_id } = req.body;

        // Check if new supervisor exists and belongs to the contractor
        const newSupervisor = await dmaSupervisorRepository.getDmaSupervisorById(new_supervisor_id);
        
        if (!newSupervisor) {
            return sendError(req, res, null, 'New supervisor not found', 404);
        }

        if (!newSupervisor.is_active) {
            return sendError(req, res, null, 'Cannot assign customer to inactive supervisor', 400);
        }

        if (newSupervisor.created_by !== contractorId) {
            return sendError(req, res, null, 'Unauthorized: New supervisor does not belong to your organization', 403);
        }

        // Get current assignment to verify contractor ownership
        const currentAssignment = await dmaSupervisorRepository.getCurrentAssignment(customer_id);
        
        if (!currentAssignment) {
            return sendError(req, res, null, 'Customer assignment not found', 404);
        }

        // Verify current supervisor belongs to the contractor
        const currentSupervisor = await dmaSupervisorRepository.getDmaSupervisorById(
            currentAssignment.dma_supervisor_assigned_id
        );

        if (currentSupervisor.created_by !== contractorId) {
            return sendError(req, res, null, 'Unauthorized: Current assignment does not belong to your organization', 403);
        }

        // Check if new supervisor is same as current supervisor
        if (currentAssignment.dma_supervisor_assigned_id === new_supervisor_id) {
            return sendError(req, res, null, 'Customer is already assigned to this supervisor', 400);
        }

        // Perform the reassignment
        const result = await dmaSupervisorRepository.reassignCustomer(
            customer_id,
            currentAssignment.dma_supervisor_assigned_id,
            new_supervisor_id
        );

        sendSuccess(
            req,
            res,
            result,
            'Customer reassigned successfully'
        );
    } catch (error) {
        console.error('Error in reassignCustomer:', error);
        sendError(req, res, null, error.message || 'Failed to reassign customer', 400);
    }
};

module.exports = {
    getDashboardSummary: exports.getDashboardSummary,
    getSupervisorsPerformance: exports.getSupervisorsPerformance,
    assignCustomersToSupervisor: exports.assignCustomersToSupervisor,
    reassignCustomer: exports.reassignCustomer
};
