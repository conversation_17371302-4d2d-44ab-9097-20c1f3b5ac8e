const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_customer_connection_address",
    {
      customer_connection_address_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
        unique:
          "UK_trxcustomerconnectionaddress_connection_permanent_id_pincode_area_house_no",
      },
      connection_type: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "original",
      },
      connection_pngareas_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_available_pngareas",
          key: "available_pngareas_id",
        },
        unique:
          "UK_trxcustomerconnectionaddress_connection_permanent_id_pincode_area_house_no",
      },
      connection_address: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      connection_pincode: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique:
          "UK_trxcustomerconnectionaddress_connection_permanent_id_pincode_area_house_no",
      },
      connection_landmark: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      connection_block_no: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      connection_floor_no: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      connection_house_no: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique:
          "UK_trxcustomerconnectionaddress_connection_permanent_id_pincode_area_house_no",
      },

      connection_building_house_name: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique:
          "UK_trxcustomerconnectionaddress_connection_permanent_id_pincode_area_house_no",
      },
      connection_state: {
        type: DataTypes.STRING(30),
        allowNull: false,
      },
      connection_city: {
        type: DataTypes.STRING(30),
        allowNull: false,
      },
      connection_district: {
        type: DataTypes.STRING(30),
        allowNull: false,
      },
      permanent_pngareas_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_available_pngareas",
          key: "available_pngareas_id",
        },
        unique:
          "UK_trxcustomerconnectionaddress_connection_permanent_id_pincode_area_house_no",
      },
      permanent_address: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      permanent_pincode: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique:
          "UK_trxcustomerconnectionaddress_connection_permanent_id_pincode_area_house_no",
      },
      permanent_landmark: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      permanent_block_no: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      permanent_floor_no: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      permanent_house_no: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique:
          "UK_trxcustomerconnectionaddress_connection_permanent_id_pincode_area_house_no",
      },
      permanent_building_house_name: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique:
          "UK_trxcustomerconnectionaddress_connection_permanent_id_pincode_area_house_no",
      },
      permanent_state: {
        type: DataTypes.STRING(30),
        allowNull: false,
      },
      permanent_city: {
        type: DataTypes.STRING(30),
        allowNull: false,
      },
      permanent_district: {
        type: DataTypes.STRING(30),
        allowNull: false,
      },
      is_latest: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      same_as_connection: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_customer_connection_address",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_customer_connection_address_id",
          unique: true,
          fields: [{ name: "customer_connection_address_id" }],
        },
        {
          name: "UK_trxcustomerconnectionaddress_connection_permanent_id_pincode_area_house_no",
          unique: true,
          fields: [
            { name: "customer_connection_id" },
            { name: "connection_pincode" },
            { name: "connection_pngareas_id" },
            { name: "connection_house_no" },
            { name: "connection_building_house_name" },
            { name: "permanent_pincode" },
            { name: "permanent_pngareas_id" },
            { name: "permanent_house_no" },
            { name: "permanent_building_house_name" },
            { name: "is_latest" },
            { name: "is_active" },
          ],
        },
      ],
    }
  );
};
