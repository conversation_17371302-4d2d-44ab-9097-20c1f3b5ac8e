const { Joi } = require('express-validation');

// Mock service for database checks
const govtSchemeService = {
  isNameDuplicate: async (name, id = null) => {
    // Replace this with actual DB query logic.
    // Example: Check if the name exists in the DB for a different ID.
    return false; // return true if duplicate exists, otherwise false.
  },
  isActiveSchemeExists: async (id) => {
    // Replace this with actual DB query logic to check if the scheme is active.
    return true; // return true if active scheme exists for the given ID.
  },
};

// Custom validation for duplicate name check
const validateDuplicateName = async (value, helpers, id = null) => {
  const isDuplicate = await govtSchemeService.isNameDuplicate(value, id);
  if (isDuplicate) {
    return helpers.error('any.custom', { message: 'Name already exists' });
  }
  return value;
};

// Custom validation for checking active scheme during delete
const validateActiveScheme = async (value, helpers) => {
  const exists = await govtSchemeService.isActiveSchemeExists(value);
  if (!exists) {
    return helpers.error('any.custom', { message: 'Record not found or is inactive' });
  }
  return value;
};

const createGovtSchemeValidation = {
  body: Joi.object({
      scheme_name: Joi.string()
        .min(3)
        .max(200)
        .required(),
      upfront_amt: Joi.number().integer().min(0).required().messages({
        'number.base': 'Upfront Amount must be a number',
        'number.min': 'Upfront Amount cannot be negative',
      }),
      month_amt: Joi.number().integer().min(0).optional().default(0).messages({
        'number.base': 'Monthly Amount must be a number',
        'number.min': 'Monthly Amount cannot be negative',
      }),
      for_year: Joi.number().integer().min(0).optional().default(0).messages({
        'number.base': 'For Year must be a number',
        'number.min': 'For Year cannot be negative',
      }),
      rental_amt: Joi.number().integer().min(0).optional().default(0).messages({
        'number.base': 'Rental Amount must be a number',
        'number.min': 'Rental Amount cannot be negative',
      }),
      is_active: Joi.number().optional().default(true).messages({
        'boolean.base': 'isActive must be a boolean value',
      }),
  }),
};


const updateGovtSchemeValidation = {
  body: Joi.object({

      govt_scheme_id: Joi.number().integer().optional().messages({
        'number.base': 'govtScheme_id must be a number',
      }),
      scheme_name: Joi.string()
        .max(200)
        .optional()
        .messages({
          'string.max': 'Name should not exceed 200 characters',
          'any.custom': '{{#message}}',
        }),
      upfront_amt: Joi.number()
        .integer()
        .min(0)
        .optional()
        .messages({
          'number.base': 'Upfront Amount must be a number',
          'number.min': 'Upfront Amount cannot be negative',
        }),
      month_amt: Joi.number()
        .integer()
        .min(0)
        .optional()
        .messages({
          'number.base': 'Monthly Amount must be a number',
          'number.min': 'Monthly Amount cannot be negative',
        }),
      for_year: Joi.number()
        .integer()
        .min(0)
        .optional()
        .messages({
          'number.base': 'For Year must be a number',
          'number.min': 'For Year cannot be negative',
        }),
      rental_amt: Joi.number()
        .integer()
        .min(0)
        .optional()
        .messages({
          'number.base': 'Rental Amount must be a number',
          'number.min': 'Rental Amount cannot be negative',
        }),
      is_active: Joi.number()
        .optional()
        .messages({
          'boolean.base': 'isActive must be a boolean value',
        }),
    }),
};

const deleteGovtSchemeValidation = {
  body: Joi.object({
    govt_scheme_id: Joi.number().integer().optional().messages({
      'number.base': 'govtScheme_id must be a number',
    }),
  }),
};

const getGovtSchemeValidation = {
  body: Joi.object({
    page: Joi.number().integer().min(1).allow(null, '').default(null).messages({
      'number.base': 'Page must be a number',
      'number.min': 'Page must be greater than 0',
    }),
    limit: Joi.number().integer().min(1).allow(null, '').default(null).messages({
      'number.base': 'Limit must be a number',
      'number.min': 'Limit must be greater than 0',
    }),
    search: Joi.string().max(200).allow(null, '').messages({
      'string.max': 'Search query should not exceed 200 characters',
    }),
    orderBy: Joi.string().valid('asc', 'desc').allow(null, '').messages({
      'string.valid': 'OrderBy must be either "asc" or "desc"',
    }),
    orderName: Joi.string().allow(null, '').messages({
      'string.base': 'OrderName must be a string',
    }),
  }),
};

module.exports = {
  createGovtSchemeValidation,
  updateGovtSchemeValidation,
  deleteGovtSchemeValidation,
  getGovtSchemeValidation,
};
