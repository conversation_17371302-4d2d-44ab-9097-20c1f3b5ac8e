require("dotenv").config();
const axios = require("axios");
const { mailConfig } = require("../../config");

const emailOTPNotification = async (to, subject, body) => {
  try {
    console.log("mailConfig", mailConfig, to, subject, body);
  
    
    const response = await axios.post(
      `https://urjachatbot.hellobpcl.in/api/engagements/notifications/v2/push?bot=${process.env.BOT_ID}`,
      {
        userDetails: {
          email: to,
        },
        notification: {
          cc: mailConfig.cc,
          bcc: mailConfig.bcc,
          type: "email",
          sender: "<EMAIL>", // Replace with your sender email
          freeTextContent: `${body}`,
          subject,
        },
      },
      {
        headers: {
          "x-api-key": process.env.X_API_KEY, // Store API Key in .env
          "Content-Type": "application/json",
        },
      }
    );

    console.log("OTP Email Sent Successfully:", response.data);
    return { success: true, data: response.data };
  } catch (error) {
    console.error(
      "Error sending OTP Email:",
      error.response ? error.response.data : error.message
    );
    return { success: false, error: error.message };
  }
};

const smsOTPNotification = async (phone, otp) => {
  console.log("2", phone, otp);
  console.log("botId",process.env.BoT_ID);
  
  try {
    const response = await axios.post(
      `https://urjachatbot.hellobpcl.in/api/engagements/notifications/v2/push?bot=${process.env.BOT_ID}`,
      {
        userDetails: {
          number: phone,
        },
        notification: {
          type: "sms",
          templateId: "1107900000676100009",
          params: {
            1: otp,
            2: "BP123",
            3: "A/C",
          },
        },
      },
      {
        headers: {
          "x-api-key": process.env.X_API_KEY, // Store API Key in .env
          "Content-Type": "application/json",
        },
      }
    );

    console.log("OTP  SMS Sent Successfully:", response.data);
    return { success: true, data: response.data };
  } catch (error) {
    console.error(
      "Error sending OTP sms:",
      error.response ? error.response.data : error.message
    );
    return { success: false, error: error.message };
  }
};
const sendEmailOTP = async (to, subject, body) => {
  try {
 
    const response = await axios.post(
      `https://urjachatbot.hellobpcl.in/api/engagements/notifications/v2/push?bot=${process.env.BOT_ID}`,
      {
        userDetails: {
          email: to,
        },
        notification: {
          cc: [],
          bcc: [],
          type: "email",
          sender: "<EMAIL>", // Replace with your sender email
          freeTextContent: `${body}`,
          subject,
        },
      },
      {
        headers: {
          "x-api-key": process.env.X_API_KEY, // Store API Key in .env
          "Content-Type": "application/json",
        },
      }
    );

    console.log("OTP Email Sent Successfully:", response.data);
    return { success: true, data: response.data };
  } catch (error) {
    console.error(
      "Error sending OTP Email:",
      error.response ? error.response.data : error.message
    );
    return { success: false, error: error.message };
  }
};

const sendSmsOTP = async (phone, otp) => {
  console.log("2", phone, otp);
  console.log("botId",process.env.BoT_ID);
  
  try {
    const response = await axios.post(
      `https://urjachatbot.hellobpcl.in/api/engagements/notifications/v2/push?bot=${process.env.BOT_ID}`,
      {
        userDetails: {
          number: phone,
        },
        notification: {
          type: "sms",
          templateId: "1107900000676100009",
          params: {
            1: otp,
            2: "BP123",
            3: "A/C",
          },
        },
      },
      {
        headers: {
          "x-api-key": process.env.X_API_KEY, 
          "Content-Type": "application/json",
        },
      }
    );

    console.log("OTP  SMS Sent Successfully:", response.data);
    return { success: true, data: response.data };
  } catch (error) {
    console.error(
      "Error sending OTP sms:",
      error.response ? error.response.data : error.message
    );
    return { success: false, error: error.message };
  }
};

const sendEmailNotificationWithoutCc = async (to, subject, body) => {
  try {
    const response = await axios.post(
      `https://urjachatbot.hellobpcl.in/api/engagements/notifications/v2/push?bot=${process.env.BOT_ID}`,
      {
        userDetails: {
          email: to,
        },
        notification: {
          cc: [],
          bcc: [],
          type: "email",
          sender: "<EMAIL>", // Replace with your sender email
          freeTextContent: `${body}`,
          subject,
        },
      },
      {
        headers: {
          "x-api-key": process.env.X_API_KEY, // Store API Key in .env
          "Content-Type": "application/json",
        },
      }
    );

    console.log("Notification Sent Successfully:", response.data);
    return { success: true, data: response.data };
  } catch (error) {
    console.error(
      "Error sending OTP Email:",
      error.response ? error.response.data : error.message
    );
    return { success: false, error: error.message };
  }
};

module.exports = { emailOTPNotification, smsOTPNotification,sendEmailOTP,sendSmsOTP,sendEmailNotificationWithoutCc };
