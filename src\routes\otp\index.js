const { validate } = require('express-validation');
const express = require('express');
const otpRoutes = express.Router();
const otpController = require('../../controllers/otp-controller');
const {
  verifyPngToken,
} = require('../../middleware/customer-auth');
const {
  sendOtpValidation,
  emailAndSmsOtpVerifyValidation,
} = require('../../validation/otp-validation');

otpRoutes.post(
  '/send-otp',
  verifyPngToken('guest'),
  validate(sendOtpValidation),
  otpController.sendOtp
);
otpRoutes.post(
  '/email-sms-otp-verify',
  verifyPngToken('guest'),
  validate(emailAndSmsOtpVerifyValidation),
  otpController.emailAndSmsOtpVerify
);

module.exports = { otpRoutes };
