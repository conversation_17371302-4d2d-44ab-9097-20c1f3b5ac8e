const { sendSuccess, sendError } = require("../helpers/responseHelper");
const messages = require("../constant/messages");
const customerService = require("../services/customer-service");
const message = require("../utils/constants");

exports.customerLogin = async (req, res) => {
  try {
    const result = await customerService.customerLogin(req.bpclMobile);
    sendSuccess(req, res, result, "Logged in successfully");
  } catch (error) {
    sendError(req, res, null, error.message || "Login failed");
  }
};
exports.customerRefreshToken = async (req, res) => {
  try {
    const result = await customerService.customerRefreshToken(req.customer);
    sendSuccess(req, res, result, "Token created successfully");
  } catch (error) {
    sendError(req, res, null, error.message || "Login failed");
  }
};
exports.customerDetailsToCache = async (req, res) => {
  try {
    const requestBody = req.body;
    if (req.personaType == "customer") {
      requestBody.bpcl_id = req.bpclId;
    }
    if (!requestBody.bpcl_id) {
      sendError(req, res, req, "Bpcl Id is required");
      return;
    }
    const userData = req.personaType == "customer" ? req.customer : req.dma;

    if(req.body.from_dma){
      await customerService.updateHelpWithRegistrationCustomer(requestBody, userData);
    }
    const result = await customerService.submitDetailsToCache(
      req.body,
      req.personaType,
      userData
    );

    sendSuccess(req, res, result, "Details saved successfully");
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};

exports.customerUploadDocuments = async (req, res) => {
  console.log("req.files", req.files, req.body);

  try {
    const requestBody = req.body;
    if (req.personaType == "customer") {
      requestBody.bpcl_id = req.bpclId;
    }
    if (!requestBody.bpcl_id) {
      sendError(req, res, req, "Bpcl Id is required");
      return;
    }

    const userData = req.personaType == "customer" ? req.customer : req.dma;
    if (req?.body?.customer_type) {
      userData.customer_type = req?.body?.customer_type;
    }
    // Call service layer with req.body and req.files
    console.log("debug", req.personaType);

    const result = await customerService.uploadDocuments(
      requestBody,
      req.files,
      req.personaType,
      userData
    );
    sendSuccess(
      req,
      res,
      result,
      "Documents uploaded and Redis cache updated successfully."
    );
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};
exports.customerList = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    // Ensure required fields are provided
    if (!req.body.bpcl_id) {
      return sendError(req, res, {}, "bpcl_id is required.");
    }

    // Call service layer with req.body and req.files
    const result = await customerService.getCustomerListService(requestBody);
    console.log("result", result);

    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};
exports.getCustomerDetailsById = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId || req.body.bpcl_id;

    const userData = req.personaType == "customer" ? req.customer : req.dma;

    const result = await customerService.CustomerDetailById(
      requestBody,
      req.personaType,
      userData
    );
    if (result) {
      sendSuccess(req, res, result, "Customer Details Fetched successfully.");
    } else {
      sendError(req, res, null, "Customer Details not found.");
    }
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};
exports.registrationHelp = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const data =
      await customerService.createRegistrationHelpService(requestBody);
    if (data.respStatus) {
      sendSuccess(
        req,
        res,
        data,
        message.HELP_WITH_REGISTARION_CONNECTION_EXIST
      );
    } else {
      sendSuccess(req, res, data, message.CREATED);
    }
  } catch (error) {
    sendError(req, res, error, message.BAD_REQUEST);
  }
};

exports.customerDetailsToDB = async (req, res) => {
  try {
    console.log("req.file =", req.file);

    const requestBody = req.body;
    if (req.personaType == "customer") {
      requestBody.bpcl_id = req.bpclId;
    }
    if (!requestBody.bpcl_id) {
      sendError(req, res, req, "Bpcl Id is required");
      return;
    }

    const userData = req.personaType == "customer" ? req.customer : req.dma;

    // Call service layer with req.body and req.files
    console.log("debug", req.personaType);
    const result = await customerService.submitDetailToDB(
      requestBody,
      req.file,
      req.personaType,
      userData
    );
    sendSuccess(req, res, result, "Details Saved Successfully.");
  } catch (error) {
    console.log("error in  customerDetailsToDB Controller", error);

    sendError(req, res, null, error.message);
  }
};

exports.customerDocumentDelete = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    if (req.personaType != "customer") {
      requestBody.bpcl_id = req.body.bpcl_id;
    }
    const userData = req.personaType == "customer" ? req.customer : req.dma;

    const result = await customerService.customerDocumentDeleteService(
      requestBody,
      req.personaType,
      userData
    );
    sendSuccess(req, res, null, result);
  } catch (error) {
    console.error("Error in deletePngDocument:", error);
    sendError(req, res, null, error.message);
  }
};

exports.pincodeConnectionCheck = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result =
      await customerService.pincodeConnectionCheckService(requestBody);
    // return res.status(200).json({ message: result.message, data: result.data });
    sendSuccess(req, res, null, result);
  } catch (error) {
    console.error("Error in pincodeConnectionCheck:", error);
    sendError(req, res, null, error.message);
    // return res.status(500).json({ message: "Internal Server Error" });
  }
};
exports.paymentApproval = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    let result;
    switch (requestBody.payment_type) {
      case "security-deposit":
        result =
          await customerService.securityDepositApprovalService(requestBody);
        console.log("result", result);

        break;
      case "extra-pipeline":
        result = await customerService.extraChargeApprovalService(requestBody);

        break;
    }

    sendSuccess(req, res, result, "Approval Updated successfully.");
  } catch (error) {
    console.error("Error in securityDepositApproval:", error);
    sendError(req, res, null, error.message);
  }
};

exports.customerApprovalDecline = async (req, res) => {
  try {
    const result = await customerService.customerApprovalDeclineService(
      req.body
    );
    // console.log("re",result);
    // sendSuccess(req, res, result, result.message);
    if (!result) {
      return sendError(req, res, null, "No result returned from service.");
    }
    sendSuccess(req, res, result, result.message || "Success");
    // return res.status(200).json({ message: result.message, data: result.data });
  } catch (error) {
    console.error("Error in customerApproval/Decline:", error);
    sendError(req, res, null, error.message);
  }
};

exports.connectionPaymentList = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result =
      await customerService.connectionPaymentDetailService(requestBody);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    console.error("Error in connectionPaymentList:", error);
    sendError(req, res, error, error.message);
  }
};
exports.customerLogout = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await customerService.customerLogout(req.pngToken);
    sendSuccess(req, res, result, "Logged out successfully");
  } catch (error) {
    sendError(req, res, null, error.message || "Logout failed");
  }
};

exports.getCustomerDetailsByMobile = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result =
      await customerService.getCustomerDetailsByMobile(requestBody);
    if (result) {
      sendSuccess(req, res, result, "Customer Details Fetched successfully.");
    } else {
      sendError(req, res, null, "Customer Details not found.");
    }
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};
exports.checkReworkExpiry = async (req, res) => {
  try {
    const result = await customerService.checkReworkExpiryService();
    if (result) {
      sendSuccess(req, res, result, "Rework Expiry Update Successfully.");
    } else {
      sendError(req, res, null, "Rework Expiry not found.");
    }
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};
