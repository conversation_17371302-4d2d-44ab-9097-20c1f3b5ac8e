const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_customer_connection",
    {
      customer_connection_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      prefix: {
        type: DataTypes.STRING(5),
        allowNull: false,
      },
      bpcl_id: {
        type: DataTypes.STRING(10),
        allowNull: false,
        defaultValue: "0",
      },
      available_pngareas_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_available_pngareas",
          key: "available_pngareas_id",
        },
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "trx_customer",
          key: "customer_id",
        },
      },
      first_name: {
        type: DataTypes.STRING(25),
        allowNull: false,
      },
      middle_name: {
        type: DataTypes.STRING(25),
        allowNull: true,
      },
      last_name: {
        type: DataTypes.STRING(25),
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      mobile: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      alternate_mobile: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      spouse_name: {
        type: DataTypes.STRING(75),
        allowNull: true,
      },
      parents_name: {
        type: DataTypes.STRING(75),
        allowNull: true,
      },
      dob: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      no_of_family_members: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      ca_no: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      bp_id: {
        type: DataTypes.STRING(12),
        allowNull: true,
      },
      connection_address_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
      },
      is_extra_connection: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      extra_points: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      govt_scheme_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_govt_scheme",
          key: "govt_scheme_id",
        },
      },
      is_security_deposit: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      security_deposit_category: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      account_type: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING(40),
        allowNull: false,
        defaultValue: "application-pending",
      },
      internal_status: {
        type: DataTypes.STRING(40),
        allowNull: false,
        defaultValue: "application-pending",
      },
      approved_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      onboarding_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      installation_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      disconnected_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      rental_owner_name: {
        type: DataTypes.STRING(75),
        allowNull: true,
      },
      rental_owner_mobile: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      created_by_type: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      isu_synced: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      isu_status: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      isu_error: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      connection_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: "original",
      },
      reason_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_masters",
          key: "master_id",
        },
      },
      is_lpg: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      lpg_consumer_no: {
        type: DataTypes.STRING(30),
        allowNull: true,
      },
      lpg_distributor_name: {
        type: DataTypes.STRING(75),
        allowNull: true,
      },
      lpg_omc_name: {
        type: DataTypes.STRING(75),
        allowNull: true,
      },
      lpg_consent: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
    },
    {
      sequelize,
      tableName: "trx_customer_connection",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "IX_FK_trxcustomerconnection_available_pngareas_id",
          fields: [{ name: "available_pngareas_id" }],
        },
        {
          name: "IX_FK_trxcustomerconnection_customer_id",
          fields: [{ name: "customer_id" }],
        },
        {
          name: "IX_FK_trxcustomerconnection_govt_scheme_id",
          fields: [{ name: "govt_scheme_id" }],
        },
        {
          name: "PK_customer_connection_id",
          unique: true,
          fields: [{ name: "customer_connection_id" }],
        },
      ],
    }
  );
};
