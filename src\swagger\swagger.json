{"openapi": "3.0.0", "info": {"title": "Proof Master and Government Scheme API", "version": "1.0.0", "description": "API documentation for managing Proof Master and Government Schemes."}, "servers": [{"url": "https://dev.api.png.hellobpcl.in", "description": "Development server"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Enter your JWT token here"}}}, "security": [{"bearerAuth": []}], "paths": {"/master/proof-master-list": {"get": {"tags": ["Master"], "summary": "Get a list of proof master entries", "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "example": 1}}, {"name": "limit", "in": "query", "description": "Number of entries per page", "required": false, "schema": {"type": "integer", "example": 2}}, {"name": "proof_type", "in": "query", "description": "Filter by proof type", "required": false, "schema": {"type": "string", "example": "proof_of_ownership"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "proof_type": {"type": "string", "example": "proof_of_ownership"}, "description": {"type": "string", "example": "Proof of ownership for property"}}}}}}}}}}, "/master/govt-scheme-list": {"get": {"tags": ["Master"], "summary": "Get List of Government Schemes", "description": "Retrieve a paginated list of government schemes.", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1}, "description": "Page number for pagination."}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 10}, "description": "Number of records per page."}], "responses": {"200": {"description": "List of government schemes retrieved successfully."}, "400": {"description": "Bad request."}}}}, "/master/master-list": {"post": {"tags": ["Master"], "summary": "Fetch master list data based on master type", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"master_type": {"type": "string", "example": "connection-transfer-reason"}}}}}}, "responses": {"200": {"description": "Successfully fetched master list data", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object"}, "example": []}, "message": {"type": "string", "example": "Data fetched successfully"}}}}}}}}}, "/master/issue-type-list": {"post": {"tags": ["Master"], "summary": "Fetch issue type list based on master type and customer type", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"master_type": {"type": "string", "example": "issue-category"}, "customer_type": {"type": "string", "example": "registrationId"}}}}}}, "responses": {"200": {"description": "Successfully fetched issue type list data", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object"}, "example": []}, "message": {"type": "string", "example": "Fetch Data successfully."}}}}}}}}}, "/application-status/list": {"get": {"tags": ["Application Status"], "summary": "Retrieve application status", "description": "Get the current status of an application by providing the application ID.", "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": true, "schema": {"type": "integer", "example": 1}}, {"name": "limit", "in": "query", "description": "Number of records per page", "required": true, "schema": {"type": "integer", "example": 10}}], "responses": {"200": {"description": "List of application statuses", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": [{"application_status_id": 1, "application_status_value": "application-pending", "application_status_label": "Application Pending", "is_active": 1, "is_delete": 0, "created_by": 0, "updated_by": 0}], "meta": {"totalRecords": 20, "totalPages": 2, "currentPage": 1}, "message": "Data fetched Successfully"}}}}}}}, "/payment/current-bill": {"get": {"tags": ["Payment"], "summary": "Fetch the current bill details", "description": "Retrieves the current bill for a specific customer based on the provided BP ID.", "parameters": [{"name": "bp_id", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Business Partner ID of the customer"}], "responses": {"200": {"description": "Successful response with bill details", "content": {"application/json": {"example": {"meter_bill_id": 8, "customer_meter_reading_id": 35, "customer_connection_id": 63, "customer_meter_id": 4, "transaction_id": "TXN1008", "bill_amount": 500, "due_amount": 50, "gst_type": "sgst", "gst_amount": 25, "total_amount": 575, "payment_status": "completed", "reason": null, "payment_method": "cash", "paid_on": "2025-03-28T07:54:04.437Z", "is_active": true, "created_by": 0, "updated_by": 0, "createdAt": "2025-03-28T07:54:04.437Z", "updatedAt": null, "customer_meter_reading": {"customer_meter_reading_id": 35, "customer_connection_id": 54, "customer_meter_id": 4, "cycle_id": 3, "meter_reading_file": "/public/uploads/customers/defaultUser/meterdetails.png", "previous_reading": 180, "current_reading": 220, "reading_units": 40, "bill_amount": 400, "due_date": "2024-06-10T00:00:00.000Z", "is_paid": 0, "reason": null, "meter_reading_status": "inprogress", "submitted_by": 3, "declared_by": "averaged_out", "mro_id": null, "isu_synced": false, "is_active": true, "created_by": 1, "updated_by": null, "createdAt": "2025-03-24T08:36:03.700Z", "updatedAt": null, "cycle": {"cycle_id": 3, "start_date": "2025-03-01", "end_date": "2025-03-31", "is_active": true, "created_by": 1, "updated_by": null, "createdAt": "2025-03-24T08:32:13.457Z", "updatedAt": null}, "customer_meter": {"customer_meter_id": 4, "customer_connection_id": 63, "mapped_lmc_customer_connection_id": 5, "meter_type": "mechanical", "meter_no": "34343", "meter_installation_datetime": "2025-03-24T12:32:18.667Z", "reason": null, "meter_status": "completed"}}}}}}, "400": {"description": "Bad Request - Invalid input parameters", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "message": "Invalid request parameters"}}}}}}}, "/payment/payment-history": {"post": {"tags": ["Payment"], "summary": "Fetch customer payment history", "description": "Retrieves paginated payment history for a given customer connection ID with an optional time filter.", "requestBody": {"required": true, "content": {"application/json": {"example": {"customer_connection_id": 63, "page": 1, "limit": 1, "timeFilter": 6}}}}, "responses": {"200": {"description": "Successful response with customer payment history", "content": {"application/json": {"example": {"totalRecords": 3, "totalPages": 1, "currentPage": 1, "updatedPayments": [{"connection_payments_id": 5, "transaction_id": "TXN005", "base_amount": 300, "gst_type": "cgst", "gst_amount": 15, "total_amount": 315, "payment_status": "failed", "section_type": "extra-pipeline", "meter_bill_id": null, "due_date": null, "reason": "Extra pipeline setup", "payment_method": "cash", "paid_on": null, "created_at": "2025-03-24T08:21:22.230Z", "updated_at": null, "customer_connection": {"first_name": "<PERSON><PERSON><PERSON>", "middle_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "mobile": "7047670143", "trx_customer_connection_addresses": [{"connection_address": null, "connection_pincode": 560001, "connection_landmark": "anand kunj", "connection_block_no": "", "connection_floor_no": "1st floor", "connection_house_no": "1030", "connection_state": "<PERSON>hya pradesh", "connection_city": "jabalpur", "connection_district": "jabalpur"}], "trx_customer_meters": [{"meter_type": "mechanical"}]}, "meter_bill": null, "type": "Postpaid"}, {"connection_payments_id": 6, "transaction_id": "TXN006", "base_amount": 350.25, "gst_type": "sgst", "gst_amount": 17.5, "total_amount": 367.75, "payment_status": "completed", "section_type": "meter-readings", "meter_bill_id": null, "due_date": null, "reason": "Monthly meter reading", "payment_method": "online", "paid_on": "2024-03-07T18:00:00.000Z", "created_at": "2025-03-24T08:21:22.230Z", "updated_at": null, "customer_connection": {"first_name": "<PERSON><PERSON><PERSON>", "middle_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "mobile": "7047670143", "trx_customer_connection_addresses": [{"connection_address": null, "connection_pincode": 560001, "connection_landmark": "anand kunj", "connection_block_no": "", "connection_floor_no": "1st floor", "connection_house_no": "1030", "connection_state": "<PERSON>hya pradesh", "connection_city": "jabalpur", "connection_district": "jabalpur"}], "trx_customer_meters": [{"meter_type": "mechanical"}]}, "meter_bill": null, "type": "Postpaid"}, {"connection_payments_id": 7, "transaction_id": "TXN007", "base_amount": 400, "gst_type": "cgst", "gst_amount": 20, "total_amount": 420, "payment_status": "pending", "section_type": "security-deposit", "meter_bill_id": null, "due_date": null, "reason": "Deposit update", "payment_method": "cash", "paid_on": null, "created_at": "2025-03-24T08:21:22.230Z", "updated_at": null, "customer_connection": {"first_name": "<PERSON><PERSON><PERSON>", "middle_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "mobile": "7047670143", "trx_customer_connection_addresses": [{"connection_address": null, "connection_pincode": 560001, "connection_landmark": "anand kunj", "connection_block_no": "", "connection_floor_no": "1st floor", "connection_house_no": "1030", "connection_state": "<PERSON>hya pradesh", "connection_city": "jabalpur", "connection_district": "jabalpur"}], "trx_customer_meters": [{"meter_type": "mechanical"}]}, "meter_bill": null, "type": "Postpaid"}]}}}}, "400": {"description": "Bad Request - Invalid input", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "message": "Invalid request body"}}}}}}}, "/payment/download-pdf": {"post": {"tags": ["Payment"], "summary": "Generate and download a payment invoice as a PDF", "requestBody": {"required": true, "content": {"application/json": {"example": {"connection_payments_id": 1}}}}, "responses": {"200": {"description": "PDF successfully generated and returned", "content": {"application/pdf": {"example": "Binary PDF file"}}}, "400": {"description": "Invalid request parameters"}, "500": {"description": "Internal server error"}}}}, "/payment/check-number": {"post": {"tags": ["Payment"], "summary": "Check if a number is linked to a customer connection", "requestBody": {"required": true, "content": {"application/json": {"example": {"bp_id": "942187530196", "number": "7047670143"}}}}, "responses": {"200": {"description": "Number verification successful", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "Number is linked to the customer connection"}}}}, "400": {"description": "Invalid request parameters"}, "404": {"description": "Number not linked to any customer connection"}}}}, "/payment/verify-otp": {"post": {"tags": ["Payment"], "summary": "Verify OTP for a given number and customer connection", "requestBody": {"required": true, "content": {"application/json": {"example": {"bp_id": 3, "number": "BP2109", "OTP": 707694}}}}, "responses": {"200": {"description": "OTP verification successful", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "OTP verified successfully"}}}}, "400": {"description": "Invalid OTP or request parameters"}, "401": {"description": "OTP verification failed"}}}}, "/dash-board/graph-data": {"post": {"tags": ["Dashboard"], "summary": "Fetch graph data for billing trends", "description": "Retrieves billing data for a specific duration (e.g., 6 months).", "requestBody": {"required": true, "content": {"application/json": {"example": {"customer_connection_id": 3, "customer_meter_id": 7, "duration": "6_months"}}}}, "responses": {"200": {"description": "Successful response with graph data", "content": {"application/json": {"example": {"status": true, "statusCode": 200, "data": [{"month": "October", "bill_amount": 400}, {"month": "November", "bill_amount": 450}, {"month": "December", "bill_amount": 500}], "message": "Graph data fetched successfully"}}}}, "400": {"description": "Bad Request - Invalid request parameters", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "message": "Invalid request parameters"}}}}}}}, "/dash-board/notification": {"get": {"tags": ["Dashboard"], "summary": "Fetch notifications for a specific BPCL ID", "description": "Retrieves all notifications related to a specific BPCL ID.", "parameters": [{"name": "bpcl_id", "in": "query", "required": true, "schema": {"type": "integer"}, "example": 8977}], "responses": {"200": {"description": "Successful response with notifications", "content": {"application/json": {"example": {"status": true, "statusCode": 200, "data": [{"message": "Payment due on 2024-03-25", "date": "2024-03-10"}, {"message": "Meter reading scheduled for 2024-03-20", "date": "2024-03-15"}], "message": "Notifications fetched successfully"}}}}, "400": {"description": "Bad Request - Invalid BPCL ID", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "message": "Invalid BPCL ID"}}}}}}}, "/dash-board/banner": {"get": {"tags": ["Dashboard"], "summary": "Retrieve banner details for a specific page type", "parameters": [{"name": "page_type", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Page type to fetch banner details (e.g., Dashboard)"}], "responses": {"200": {"description": "Banner details retrieved successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"banner_id": 1, "image_url": "https://example.com/banner.jpg", "page_type": "Dashboard"}, "message": "Banner details fetched successfully"}}}}, "400": {"description": "Invalid request parameters"}, "404": {"description": "Banner not found for the given page type"}}}}, "/customer-profile/add-update-email": {"post": {"tags": ["Customer Profile"], "summary": "Add or update customer's email", "requestBody": {"required": true, "content": {"application/json": {"example": {"customer_connection_id": 129, "email": "<EMAIL>", "type": "next"}}}}, "responses": {"200": {"description": "Email updated successfully"}}}}, "/customer-profile/email-otp-verify": {"post": {"tags": ["Customer Profile"], "summary": "Verify email OTP", "requestBody": {"required": true, "content": {"application/json": {"example": {"customer_connection_id": 3, "number": "<EMAIL>", "OTP": 45}}}}, "responses": {"200": {"description": "OTP verification successful"}}}}, "/customer-profile/send-otp": {"post": {"tags": ["Customer Profile"], "summary": "Send OTP to email or number", "requestBody": {"required": true, "content": {"application/json": {"example": {"email": "<EMAIL>", "number": "7047670143"}}}}, "responses": {"200": {"description": "OTP sent successfully"}}}}, "/customer-profile/email-sms-otp-verify": {"post": {"tags": ["Customer Profile"], "summary": "Verify OTP for email and SMS", "requestBody": {"required": true, "content": {"application/json": {"example": {"email": "<EMAIL>", "number": "7047670143", "otp": 344556}}}}, "responses": {"200": {"description": "Email and SMS OTP verification successful"}, "400": {"description": "Invalid request parameters or OTP"}}}}, "/customer-profile/add-update-number": {"post": {"tags": ["Customer Profile"], "summary": "Add or update customer number", "requestBody": {"required": true, "content": {"application/json": {"example": {"bpcl_id": 9002, "number": "7047671340", "old_number": "7047670143"}}}}, "responses": {"200": {"description": "Customer number updated successfully"}, "400": {"description": "Invalid request parameters"}}}}, "/customer-profile/add-update-alt-number": {"post": {"tags": ["Customer Profile"], "summary": "Add or update alternative customer number", "requestBody": {"required": true, "content": {"application/json": {"example": {"customer_connection_id": 129, "alt_number": "7896007744", "type": "next"}}}}, "responses": {"200": {"description": "Alternative number updated successfully"}, "400": {"description": "Invalid request parameters"}}}}, "/customer-profile/alt-number-otp-verify": {"post": {"tags": ["Customer Profile"], "summary": "Verify OTP for alternative number", "requestBody": {"required": true, "content": {"application/json": {"example": {"customer_connection_id": 129, "alt_number": "7896007744", "OTP": 67}}}}, "responses": {"200": {"description": "Alternative number OTP verification successful"}, "400": {"description": "Invalid request parameters or OTP"}}}}, "/customer/get-token": {"post": {"tags": ["Customer"], "summary": "Customer Login To Get Access Token", "description": "This api will validate bpcl token and create and return png token", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"request": {"type": "string", "example": "S+bAZUkbGz9M66jRJVZVOnrq1FoSZ9qh3JicVjYrcbE="}}, "required": ["request"]}, "example": {"request": "S+bAZUkbGz9M66jRJVZVOnrq1FoSZ9qh3JicVjYrcbE="}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"token": "", "customerExists": false}, "message": "Logged in successfully"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"example": {"status": false, "message": "Invalid request parameters"}}}}}}}, "/customer/refresh-token": {"get": {"tags": ["Customer"], "summary": "Get New Access Token using Refresh Token", "description": "This api will validate refresh tokena and provide new Access token", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJtb2JpbGUiOiI3MDEwODY3NTc1IiwiaWF0IjoxNzQyNDY4OTIyLCJleHAiOjE3NDI0NzYxMjJ9.9wa_rkRIdAIE3XyKB-8omfG7LOLeJQYmT-oOTxgQc_8"}, "message": "Logged in successfully"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"example": {"status": false, "message": "Invalid request parameters"}}}}}}}, "/customer/logout": {"post": {"tags": ["Customer"], "summary": "Customer <PERSON><PERSON><PERSON>", "description": "This api will validate png token and logout", "requestBody": {"required": false, "content": {}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Logged out successfully"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"example": {"status": false, "message": "Invalid request parameters"}}}}}}}, "/pincode-check/check-pincode": {"post": {"tags": ["Customer"], "summary": "Customer <PERSON> Pincode Exists", "description": "This api will check if pincode exists in the database for the given pincode", "requestBody": {"required": true, "content": {"application/json": {"example": {"pincode": 560001}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"0": {"available_pngareas_id": 1, "pincode_id": 1, "pincode": 560001, "is_active": true, "pincodeAreasAlias": {"pincode": 560001, "area": {"area_id": 1, "area_name": "Area 1", "geographical_area": {"geographical_area_id": 1, "ga_area_name": "north gg area 1", "city": {"city_id": 513, "city_title": "Mumbai"}, "state": {"state_id": 21, "state_title": "Maharashtra"}}}}, "state": "Maharashtra", "city": "Mumbai", "area": {"area_name": "Area 1"}, "geographical_area": {"state": "Maharashtra", "city": "Mumbai"}}, "pincode": 560001, "available": "Yes"}, "message": "Pincode details found"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "data": null, "message": "Unserviceable PIN code"}}}}}}}, "/customer/check-pincode-connection": {"post": {"tags": ["Customer"], "summary": "Check if a pincode has PNG connection", "description": "Validates if the given pincode is eligible for PNG connection.", "requestBody": {"required": true, "content": {"application/json": {"example": {"bpcl_id": 456, "connection_pincode": "560001", "connection_house_no": "1030", "connection_floor_no": "1st floor", "connection_block_no": "", "connection_landmark": "anand kunj", "connection_state": "<PERSON>hya pradesh", "connection_city": "jabalpur", "available_pngareas_id": 2, "house_flat_no": "1212"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"example": {"status": true, "message": "Connection Exist"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "data": null, "message": "Connection not exists."}}}}}}}, "/customer/save-details": {"post": {"tags": ["Customer"], "summary": "Save customer details in multiple steps", "description": "Saves customer details based on the step number.", "requestBody": {"required": true, "content": {"application/json": {"example": {"submit_type": "save", "bpcl_id": 8888888888, "step_no": 1, "available_pngareas_id": 6, "house_flat_no": "1212", "personal_details": {"prefix": "mr", "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle_name": "", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "mobile": "8899007711", "alternate_mobile": "", "parent_spouse": "parent", "parents_name": "onkar prasad", "spouse_name": ""}}}}}, "responses": {"200": {"description": "Successfully saved details", "content": {"application/json": {"example": {"status": true, "message": "Customer Details saved successfully"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"example": {"status": false, "message": "Missing required fields"}}}}}}}, "/customer/upload": {"post": {"tags": ["Customer"], "summary": "Upload documents for customer", "description": "Uploads required proof documents in form-data format.", "requestBody": {"required": true, "content": {"multipart/form-data": {"example": {"bpcl_id": 8777777777, "house_flat_no": "1212", "png_available_address": "building2", "proof_type": "photo_proof", "proof_master_id": 1, "file": "(binary file)"}}}}, "responses": {"200": {"description": "File uploaded successfully", "content": {"application/json": {"example": {"status": true, "message": "File uploaded successfully"}}}}}}}, "/customer/delete": {"post": {"tags": ["Customer"], "summary": "Delete Uploaded documents ", "description": "Delete Uploaded documents in form-data format.", "requestBody": {"required": true, "content": {"multipart/form-data": {"example": {"bpcl_id": 8777777777, "house_flat_no": "1212", "available_pngareas_id": "building2", "proof_type": "photo_proof", "proof_master_id": 1}}}}, "responses": {"200": {"description": "File Deleted successfully", "content": {"application/json": {"example": {"status": true, "message": "File Deleted successfully"}}}}}}}, "/customer/list": {"post": {"tags": ["Customer"], "summary": "Fetch customer details list", "requestBody": {"required": true, "content": {"application/json": {"example": {"active_status": "false"}}}}, "responses": {"200": {"description": "Successfully fetched customer list", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": [{"registrationId": "TEMP31270416657125", "customerConnectionDetails": {"bpcl_id": 2111, "available_pngareas_id": 5, "prefix": "mr", "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle_name": "", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "mobile": "8899007711", "alternate_mobile": "", "parents_name": "", "spouse_name": "test", "is_extra_connection": 1, "is_security_deposit": 1, "is_active": 1, "status": "application-pending", "isu_synced": 0, "is_delete": 0, "created_by": 0, "createdAt": "2025-03-23T18:30:02.523Z", "updatedAt": "2025-03-23T18:30:02.523Z", "updated_by": 0}, "customerConnectionAddress": {"created_by": 0, "updated_by": 0}, "customerAdditionalInfo": {}}]}}}}}}}, "/customer/get-details": {"post": {"tags": ["Customer"], "summary": "Get customer details", "requestBody": {"required": true, "content": {"application/json": {"example": {"customer_connection_id": 50, "bpcl_id": "9798788783", "available_pngareas_id": 1, "house_flat_no": "5003"}}}}, "responses": {"200": {"description": "Customer Details Fetched successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": [{"registrationId": "TEMP31270416657125", "customerConnectionDetails": {"bpcl_id": 2111, "available_pngareas_id": 5, "prefix": "mr", "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle_name": "", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "mobile": "8899007711", "alternate_mobile": "", "parents_name": "", "spouse_name": "test", "is_extra_connection": 1, "is_security_deposit": 1, "is_active": 1, "status": "application-pending", "isu_synced": 0, "is_delete": 0, "created_by": 0, "createdAt": "2025-03-23T18:30:02.523Z", "updatedAt": "2025-03-23T18:30:02.523Z", "updated_by": 0}, "customerConnectionAddress": {"created_by": 0, "updated_by": 0}, "customerAdditionalInfo": {}}]}}}}}}}, "/customer/extra-charge-approval": {"post": {"tags": ["Customer"], "summary": "Approve/Decline the extra charges", "description": "In Onboarding user need to approve/decline the extra charges", "requestBody": {"required": true, "content": {"application/json": {"example": {"customer_connection_id": 18, "approval_status": 1}}}}, "responses": {"200": {"description": "Request submitted successfully", "content": {"application/json": {"example": {"status": true, "message": "Support request submitted"}}}}}}}, "/customer/registration-help": {"post": {"tags": ["Customer"], "summary": "Request help for registration", "description": "Allows users to request support during the registration process.", "requestBody": {"required": true, "content": {"application/json": {"example": {"fullname": "sharad pandey", "mobile": "9874125630", "email": "<EMAIL>", "pincode": 560001, "area": "demo area", "flat_no": "h203", "floor_no": "2nd", "block_no": "wing B", "available_pngareas_id": 5, "building_house_name": "Krishna residency Indore", "landmark": "Near Hotel Blue"}}}}, "responses": {"200": {"description": "Request submitted successfully", "content": {"application/json": {"example": {"status": true, "message": "Support request submitted"}}}}}}}, "/lmc/cases": {"get": {"tags": ["LMC"], "summary": "Fetch active LMC cases", "description": "Retrieves a list of active cases for the given LMC ID.", "parameters": [{"name": "lmc_id", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "The ID of the LMC"}, {"name": "caseType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["active", "closed"]}, "description": "Filter cases by type"}], "responses": {"200": {"description": "Fetch Data successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"cases": [{"mapped_lmc_customer_connection_id": 5, "lmc_stage": "feasibility-check", "visit_status": "completed", "visit_datetime": "2025-03-23T05:30:00.000Z", "tpi_status": "inprogress", "customer_connection_id": 75, "full_name": "<PERSON><PERSON>", "email": "<EMAIL>", "mobile": "9823456789", "pincode": 201301}], "totalRecords": 99, "totalPages": 1, "currentPage": 1}, "message": "Fetch Data successfully."}}}}}}}, "/lmc/schedule-visit": {"post": {"tags": ["LMC"], "summary": "Schedule an installation visit", "description": "Schedules an installation visit for a customer.", "requestBody": {"required": true, "content": {"application/json": {"example": {"schedule_date": "2025-04-15 00:02:12", "mapped_lmc_connection_stage_id": 1}}}}, "responses": {"200": {"description": "Visit scheduled successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"success": true, "message": "installation scheduled", "details": "You have successfully scheduled visit on 15 Apr 2025 for customer with BP ID 161"}, "message": "Fetch Data successfully."}}}}}}}, "/lmc/pipeline-submit-details": {"post": {"tags": ["LMC"], "summary": "Submit Pipeline Drawing Details", "description": "Submits pipeline drawing details for a customer connection.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "description": "The ID of the customer connection"}, "file_type": {"type": "string", "description": "The type of drawing (e.g., isometric-drawing)"}, "pipeline_size": {"type": "integer", "description": "The size of the pipeline"}, "customer_consent_request": {"type": "boolean", "description": "Whether customer consent is requested"}, "lmc_stage": {"type": "string", "description": "The current stage of the LMC process"}}, "required": ["customer_connection_id", "file_type", "pipeline_size", "customer_consent_request", "lmc_stage"]}, "example": {"customer_connection_id": 161, "file_type": "isometric-drawing", "pipeline_size": 15, "customer_consent_request": false, "lmc_stage": "installation"}}}}, "responses": {"200": {"description": "Pipeline drawing details submitted successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "Pipeline drawing details submitted successfully."}}}}}}}, "/lmc/commissioning-tickets": {"get": {"tags": ["LMC"], "summary": "Get LMC Commissioning Tickets", "description": "Retrieves a list of LMC commissioning tickets based on filters.", "parameters": [{"name": "lmc_id", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "The ID of the LMC"}, {"name": "ticketType", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Filter tickets by type"}], "responses": {"200": {"description": "Successfully fetched LMC commissioning tickets", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": "Sample commissioning tickets data here", "message": "Fetch Data successfully."}}}}}}}, "/lmc/feasibility-tickets": {"get": {"tags": ["LMC"], "summary": "Get LMC Feasibility Tickets", "description": "Retrieves a list of LMC feasibility tickets based on filters.", "parameters": [{"name": "lmc_id", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "The ID of the LMC"}, {"name": "ticketType", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Filter tickets by type"}], "responses": {"200": {"description": "Successfully fetched LMC feasibility tickets", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": "Sample feasibility tickets data here", "message": "Fetch Data successfully."}}}}}}}, "/lmc/installation-tickets": {"get": {"tags": ["LMC"], "summary": "Get LMC Installation Tickets", "description": "Retrieves a list of LMC installation tickets based on filters.", "parameters": [{"name": "lmc_id", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "The ID of the LMC"}, {"name": "ticketType", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Filter tickets by type"}], "responses": {"200": {"description": "Successfully fetched LMC installation tickets", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": "Sample installation tickets data here", "message": "Fetch Data successfully."}}}}}}}, "/lmc/submit-jmr": {"post": {"tags": ["LMC"], "summary": "Submit LMC JMR", "description": "Submits JMR details for LMC.", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer"}, "file": {"type": "string", "format": "binary"}, "meter_type": {"type": "string"}, "meter_no": {"type": "string"}, "lmc_id": {"type": "integer"}, "meter_reading": {"type": "integer"}}, "required": ["customer_connection_id", "file", "meter_type", "meter_no", "lmc_id", "meter_reading"]}}}}, "responses": {"200": {"description": "Successfully submitted the JMR", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "JMR submitted successfully."}}}}}}}, "/lmc/jmr-submission": {"get": {"tags": ["LMC"], "summary": "Get LMC JMR Submission Details", "description": "Retrieves details of JMR submissions based on customer connection ID.", "parameters": [{"name": "customer_connection_id", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "The ID of the customer connection"}], "responses": {"200": {"description": "Successfully fetched JMR submission details", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": "Sample JMR submission data here", "message": "Fetch Data successfully."}}}}}}}, "/lmc/feasibility-visit": {"get": {"tags": ["LMC"], "summary": "Get Feasibility Visit Details", "description": "Retrieves feasibility visit details based on customer connection ID.", "parameters": [{"name": "mapped_lmc_connection_stage_id", "in": "query", "required": false, "schema": {"type": "integer"}, "example": 1}], "responses": {"200": {"description": "Successfully fetched feasibility visit details", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": "Sample feasibility visit data here", "message": "Fetch Data successfully."}}}}}}}, "/lmc/pipeline-drawing-upload": {"post": {"tags": ["LMC"], "summary": "Upload Pipeline Drawing", "description": "Uploads a pipeline drawing for a customer connection.", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "description": "The ID of the customer connection"}, "file": {"type": "string", "format": "binary", "description": "The pipeline drawing file"}, "file_type": {"type": "string", "description": "The type of drawing (e.g., isometric-drawing)"}, "lmc_stage": {"type": "string", "description": "The current stage of the LMC process"}}, "required": ["customer_connection_id", "file", "file_type", "lmc_stage"]}}}}, "responses": {"200": {"description": "File uploaded successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "File uploaded successfully."}}}}}}}, "/meter-reading/history-list": {"get": {"tags": ["Meter Reading"], "summary": "Get Meter Reading History List", "description": "Retrieves meter reading history based on customer connection ID and time filter.", "parameters": [{"name": "customer_connection_id", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "The customer connection ID"}, {"name": "timeFilter", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Time filter for meter reading history"}], "responses": {"200": {"description": "Successfully fetched meter reading history list", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": "Sample meter reading history data here", "message": "Fetch Data successfully."}}}}}}}, "/meter-reading/upload-details": {"get": {"tags": ["Meter Reading"], "summary": "Get Meter Reading Upload Details", "description": "Retrieves details of uploaded meter readings.", "parameters": [{"name": "customer_connection_id", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "The customer connection ID"}], "responses": {"200": {"description": "Successfully fetched meter reading upload details", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": "Sample upload details data here", "message": "Fetch Data successfully."}}}}}}}, "/meter-reading/details": {"get": {"tags": ["Meter Reading"], "summary": "Get Meter Reading Details", "description": "Retrieves meter reading details based on customer connection ID and cycle ID.", "parameters": [{"name": "customer_connection_id", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "The customer connection ID"}], "responses": {"200": {"description": "Successfully fetched meter reading details", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": "Sample meter reading details here", "message": "Fetch Data successfully."}}}}}}}, "/meter-reading/verify": {"post": {"tags": ["Meter Reading"], "summary": "Verify Meter Uploaded Reading", "description": "Verifies a meter reading uploaded by the customer.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer"}, "current_reading": {"type": "integer"}}, "required": ["customer_connection_id", "current_reading"]}}}}, "responses": {"200": {"description": "Successfully verified meter reading", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "Meter reading verified successfully."}}}}}}}, "/meter-reading/upload": {"post": {"tags": ["Meter Reading"], "summary": "Upload meter reading", "description": "Uploads a meter reading file for a customer connection.", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer"}, "file": {"type": "string", "format": "binary"}, "declared_by": {"type": "string"}, "cycle_id": {"type": "integer"}}, "required": ["customer_connection_id", "file", "declared_by", "cycle_id"]}}}}, "responses": {"200": {"description": "Successfully uploaded the meter reading", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "Meter reading uploaded successfully."}}}}}}}, "/meter-reading/submit-details": {"post": {"tags": ["Meter Reading"], "summary": "Submit meter reading details", "description": "Submits meter reading details for a customer connection.", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer"}}, "required": ["customer_connection_id"]}}}}, "responses": {"200": {"description": "Successfully submitted meter reading details", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "Meter reading details submitted successfully."}}}}}}}, "/auth/login": {"post": {"tags": ["DMA"], "summary": "User login", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "admin@123"}}, "required": ["email", "password"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"admin_id": {"type": "integer", "example": 3}, "bpcl_admin_id": {"type": "integer", "example": 1}, "persona_role_id": {"type": "integer", "example": 1}, "user_role": {"type": "string", "example": "contractor"}, "agency_id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "DMA Contractor 1"}, "last_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mobile": {"type": "string", "example": "8908908901"}, "alternate_mobile": {"type": "string", "example": null}, "email": {"type": "string", "example": "<EMAIL>"}, "city": {"type": "string", "example": "mumbai"}, "state": {"type": "string", "example": "maharastra"}, "last_logged_in": {"type": "string", "example": null}, "is_active": {"type": "boolean", "example": true}, "is_locked": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 0}, "createdAt": {"type": "string", "example": "2025-03-20T05:10:36.070Z"}, "updatedAt": {"type": "string", "example": null}}}, "token": {"type": "string", "example": "xyz"}}}, "message": {"type": "string", "example": "Logged in successfully"}}}}}}}}}, "/profile": {"get": {"tags": ["DMA"], "summary": "Get user profile", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"admin_id": {"type": "integer", "example": 3}, "bpcl_admin_id": {"type": "integer", "example": 1}, "persona_role_id": {"type": "integer", "example": 1}, "user_role": {"type": "string", "example": "contractor"}, "agency_id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "DMA Contractor 1"}, "last_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mobile": {"type": "string", "example": "8908908901"}, "alternate_mobile": {"type": "string", "example": null}, "email": {"type": "string", "example": "<EMAIL>"}, "city": {"type": "string", "example": "mumbai"}, "state": {"type": "string", "example": "maharastra"}, "last_logged_in": {"type": "string", "example": null}, "is_active": {"type": "boolean", "example": true}, "is_locked": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 0}, "createdAt": {"type": "string", "example": "2025-03-20T05:10:36.070Z"}, "updatedAt": {"type": "string", "example": null}}}, "message": {"type": "string", "example": "Profile retrieved successfully"}}}}}}}}}, "/auth/logout": {"get": {"tags": ["DMA"], "summary": "User logout", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "null", "example": null}, "message": {"type": "string", "example": "Logged out successfully"}}}}}}}}}, "/fetch-dma-customers": {"get": {"tags": ["DMA"], "summary": "Fetch DMA customers for contractors", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"help_with_registration_id": {"type": "integer", "example": 1}, "bpcl_id": {"type": "string", "example": "236"}, "available_pngareas_id": {"type": "integer", "example": 5}, "fullname": {"type": "string", "example": "<PERSON><PERSON>"}, "address": {"type": "string", "example": " "}, "pincode": {"type": "integer", "example": 560001}, "area": {"type": "string", "example": "demo area"}, "mobile": {"type": "string", "example": "9876543210"}, "email": {"type": "string", "example": "<EMAIL>"}, "status": {"type": "string", "example": "not-started"}, "flat_no": {"type": "string", "example": "h202"}, "customer_type": {"type": "string", "example": "assigned"}, "created_at": {"type": "string", "example": "2025-03-24T05:34:29.643Z"}, "updated_at": {"type": "string", "example": null}, "trx_dma_help_with_reg_mappings": {"type": "array", "items": {"type": "object", "properties": {"dma_help_with_reg_mapping_id": {"type": "integer", "example": 1}, "hwrm_status": {"type": "string", "example": "not-started"}, "help_with_registration_id": {"type": "integer", "example": 1}, "dma_supervisor_assigned_id": {"type": "integer", "example": 15}, "is_active": {"type": "boolean", "example": false}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 0}, "created_at": {"type": "string", "example": "2025-03-24T16:13:30.953Z"}, "dma_supervisor_assigned": {"type": "object", "properties": {"supervisor_name": {"type": "string", "example": "Sup3 Visor"}}}}}}}}}, "message": {"type": "string", "example": "DMA customers retrieved successfully"}}}}}}}}}, "/fetch-dma-customers/assigned": {"get": {"tags": ["DMA"], "summary": "Fetch assigned DMA customers for supervisors", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"dma_help_with_reg_mapping_id": {"type": "integer", "example": 5}, "help_with_registration_id": {"type": "integer", "example": 5}, "dma_supervisor_assigned_id": {"type": "integer", "example": 15}, "hwrm_status": {"type": "string", "example": "approved"}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 0}, "createdAt": {"type": "string", "example": "2025-03-25T18:42:37.517Z"}, "updatedAt": {"type": "string", "example": "2025-03-25T18:43:34.537Z"}, "help_with_registration": {"type": "object", "properties": {"help_with_registration_id": {"type": "integer", "example": 5}, "bpcl_id": {"type": "string", "example": "456"}, "available_pngareas_id": {"type": "integer", "example": 1}, "customer_connection_id": {"type": "string", "example": null}, "temp_registration_id": {"type": "string", "example": "TEMP31271001671320"}, "flat_no": {"type": "string", "example": "121"}, "fullname": {"type": "string", "example": "abc"}, "address": {"type": "string", "example": "49/C Sch. no. 71"}, "pincode": {"type": "integer", "example": 560001}, "area": {"type": "string", "example": "Area 1"}, "mobile": {"type": "string", "example": "1234567890"}, "email": {"type": "string", "example": "<EMAIL>"}, "status": {"type": "string", "example": "approved"}, "customer_type": {"type": "string", "example": "assigned"}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 0}, "createdAt": {"type": "string", "example": "2025-03-24T17:44:15.047Z"}, "updatedAt": {"type": "string", "example": "2025-03-25T18:43:34.507Z"}}}}}}, "message": {"type": "string", "example": "Assigned customers retrieved successfully"}}}}}}}}}, "/fetch-dma-customers/status-count-supervisor": {"get": {"tags": ["DMA"], "summary": "Fetch status counts for supervisor", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"not-started": {"type": "integer", "example": 2}, "inprogress": {"type": "integer", "example": 0}, "pending-approval": {"type": "integer", "example": 0}, "rework": {"type": "integer", "example": 0}, "approved": {"type": "integer", "example": 1}, "rejected": {"type": "integer", "example": 0}}}, "message": {"type": "string", "example": "Status counts retrieved successfully"}}}}}}}}}, "/fetch-dma-customers/update-status": {"put": {"tags": ["DMA"], "summary": "Update customer status", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"helpWithRegistrationId": {"type": "integer", "example": 13}, "status": {"type": "string", "example": "inprogress"}}, "required": ["helpWithRegistrationId", "status"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"help_with_registration_id": {"type": "integer", "example": 5}, "bpcl_id": {"type": "string", "example": "456"}, "available_pngareas_id": {"type": "integer", "example": 1}, "customer_connection_id": {"type": "string", "example": null}, "temp_registration_id": {"type": "string", "example": "TEMP31271001671320"}, "flat_no": {"type": "string", "example": "121"}, "fullname": {"type": "string", "example": "abc"}, "address": {"type": "string", "example": "49/C Sch. no. 71"}, "pincode": {"type": "integer", "example": 560001}, "area": {"type": "string", "example": "Area 1"}, "mobile": {"type": "string", "example": "1234567890"}, "email": {"type": "string", "example": "<EMAIL>"}, "status": {"type": "string", "example": "inprogress"}, "customer_type": {"type": "string", "example": "assigned"}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 0}, "createdAt": {"type": "string", "example": "2025-03-24T17:44:15.047Z"}, "updatedAt": {"type": "string", "example": "2025-03-27T14:09:12.173Z"}, "trx_dma_help_with_reg_mappings": {"type": "array", "items": {"type": "object", "properties": {"dma_help_with_reg_mapping_id": {"type": "integer", "example": 5}, "help_with_registration_id": {"type": "integer", "example": 5}, "dma_supervisor_assigned_id": {"type": "integer", "example": 15}, "hwrm_status": {"type": "string", "example": "inprogress"}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 0}, "createdAt": {"type": "string", "example": "2025-03-25T18:42:37.517Z"}, "updatedAt": {"type": "string", "example": "2025-03-27T14:09:12.207Z"}}}}}}, "message": {"type": "string", "example": "Customer status updated successfully"}}}}}}}}}, "/dma-supervisor/create": {"post": {"tags": ["DMA"], "summary": "Onboard a new supervisor", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": "Sup6"}, "last_name": {"type": "string", "example": "Visor"}, "mobile": {"type": "string", "example": "2345678906"}, "alternate_mobile": {"type": "string", "example": "1234567896"}, "email": {"type": "string", "example": "<EMAIL>"}, "persona_role_id": {"type": "integer", "example": 1}, "agency_id": {"type": "integer", "example": 1}}, "required": ["first_name", "last_name", "mobile", "email", "persona_role_id", "agency_id"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"is_locked": {"type": "boolean", "example": true}, "updated_by": {"type": "integer", "example": 0}, "admin_id": {"type": "integer", "example": 22}, "first_name": {"type": "string", "example": "Sup7"}, "last_name": {"type": "string", "example": "Visor"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "2345678907"}, "alternate_mobile": {"type": "string", "example": "1234567896"}, "user_role": {"type": "string", "example": "supervisor"}, "persona_role_id": {"type": "integer", "example": 1}, "bpcl_admin_id": {"type": "integer", "example": 1}, "agency_id": {"type": "integer", "example": 1}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 3}, "updatedAt": {"type": "string", "example": "2025-03-27T13:59:02.387Z"}, "createdAt": {"type": "string", "example": "2025-03-27T13:59:02.387Z"}}}, "message": {"type": "string", "example": "Supervisor created successfully"}}}}}}}}}, "/dma-supervisor/list/all": {"get": {"tags": ["DMA"], "summary": "Get list of all supervisors", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"admin_id": {"type": "integer", "example": 22}, "first_name": {"type": "string", "example": "Sup7"}, "last_name": {"type": "string", "example": "Visor"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "2345678907"}, "alternate_mobile": {"type": "string", "example": "1234567896"}, "persona_role_id": {"type": "integer", "example": 1}, "agency_id": {"type": "integer", "example": 1}, "is_active": {"type": "boolean", "example": true}, "created_at": {"type": "string", "example": "2025-03-27T13:59:02.387Z"}, "updated_at": {"type": "string", "example": "----"}, "persona_role": {"type": "object", "properties": {"persona_role_id": {"type": "integer", "example": 1}, "role_name": {"type": "string", "example": "DMA"}}}, "agency": {"type": "object", "properties": {"agency_id": {"type": "integer", "example": 1}, "agency_name": {"type": "string", "example": "Agency 1"}}}}}}, "message": {"type": "string", "example": "Supervisors retrieved successfully"}}}}}}}}}, "/dma-supervisor/delete-multiple": {"post": {"tags": ["DMA"], "summary": "Delete multiple supervisors", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"supervisor_ids": {"type": "array", "items": {"type": "integer", "example": 24}}}, "required": ["supervisor_ids"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"deleted": {"type": "array", "items": {"type": "integer", "example": 24}}}}, "message": {"type": "string", "example": "Supervisors deleted successfully"}}}}}}}}}, "/dma-contractor/dashboard-summary": {"get": {"tags": ["DMA"], "summary": "Get contractor dashboard summary", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"contractor_id": {"type": "integer", "example": 3}, "dma_supervisor_summary": {"type": "object", "properties": {"total_supervisors": {"type": "integer", "example": 41}}}, "dma_customer_summary": {"type": "object", "properties": {"total_not_started": {"type": "integer", "example": 54}, "total_in_progress": {"type": "integer", "example": 1}, "total_pending_approval": {"type": "integer", "example": 0}, "total_rework": {"type": "integer", "example": 0}, "total_approved": {"type": "integer", "example": 0}, "total_rejected": {"type": "integer", "example": 0}, "total_unassigned": {"type": "integer", "example": 11}, "total_assigned": {"type": "integer", "example": 42}, "total_dma_assigned": {"type": "integer", "example": 13}}}}}, "message": {"type": "string", "example": "Dashboard summary retrieved successfully"}}}}}}}}}, "/dma-contractor/supervisors-performance": {"get": {"tags": ["DMA"], "summary": "Get supervisors performance data", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"contractor_id": {"type": "integer", "example": 3}, "supervisors_performance": {"type": "array", "items": {"type": "object", "properties": {"supervisor_id": {"type": "integer", "example": 16}, "supervisor_name": {"type": "string", "example": "Sup4 Visor"}, "total_assigned": {"type": "integer", "example": 3}, "approved": {"type": "integer", "example": 0}, "inprogress": {"type": "integer", "example": 0}, "pending-approval": {"type": "integer", "example": 0}, "rework": {"type": "integer", "example": 0}, "rejected": {"type": "integer", "example": 0}, "assigned-customers": {"type": "integer", "example": 0}, "dma-registered": {"type": "integer", "example": 0}}}}}}, "message": {"type": "string", "example": "Supervisors performance data retrieved successfully"}}}}}}}}}, "/dma-contractor/assign-customers": {"post": {"tags": ["DMA"], "summary": "Assign customers to supervisor", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"supervisor_id": {"type": "integer", "example": 16}, "customer_ids": {"type": "array", "items": {"type": "integer", "example": 4}}}, "required": ["supervisor_id", "customer_ids"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"assigned": {"type": "array", "items": {"type": "integer", "example": 6}}, "messages": {"type": "object", "example": {}}}}, "message": {"type": "string", "example": "Customers assigned successfully"}}}}}}}}}, "/dma-contractor/reassign-customer": {"put": {"tags": ["DMA"], "summary": "Reassign customer to a new supervisor", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"customer_id": {"type": "integer", "example": 4}, "new_supervisor_id": {"type": "integer", "example": 16}}, "required": ["customer_id", "new_supervisor_id"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"dma_help_with_reg_mapping_id": {"type": "integer", "example": 15}, "help_with_registration_id": {"type": "integer", "example": 6}, "dma_supervisor_assigned_id": {"type": "integer", "example": 15}, "hwrm_status": {"type": "string", "example": "not-started"}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 0}, "createdAt": {"type": "string", "example": "2025-03-27T14:05:27.763Z"}, "updatedAt": {"type": "string", "example": "2025-03-27T14:05:27.763Z"}, "dma_supervisor_assigned": {"type": "object", "properties": {"first_name": {"type": "string", "example": "Sup3"}, "last_name": {"type": "string", "example": "Visor"}, "created_by": {"type": "integer", "example": 3}}}}}, "message": {"type": "string", "example": "Customer reassigned successfully"}}}}}}}}}, "/service-request/transfer-connection": {"post": {"tags": ["Service Request"], "summary": "Raised Connection transfer request", "requestBody": {"required": true, "content": {"application/json": {"example": {"existing_connection_id_connection_id": 198, "connection_transfer_reason": 4, "connection_transfer_date": "2025-12-01"}}}}, "responses": {"200": {"description": "Transfer Connection request raised Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"isu_synced": 0, "is_active": true, "created_by": 0, "updated_by": 0, "transfer_connection_request_id": 4, "existing_connection_id_connection_id": 192, "connection_transfer_reason": 4, "connection_transfer_date": "2025-12-01", "connection_transfer_status": "pending", "updatedAt": "2025-03-27T14:57:47.470Z", "createdAt": "2025-03-27T14:57:47.470Z"}, "message": "Transfer Connection request raised Successfully"}}}}}}}, "/master/service-rates": {"get": {"tags": ["Master"], "summary": "Retrieve service rates", "description": "Get the list of service rates.", "parameters": [], "responses": {"200": {"description": "List of service rates", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": [{"service_rate_id": 1, "connection_service_id": 1, "area_id": 1, "rates": 2500, "is_active": true, "created_by": 0, "updated_by": 0, "createdAt": "2025-03-31T11:14:41.750Z", "updatedAt": null}, {"service_rate_id": 2, "connection_service_id": 1, "area_id": 3, "rates": 2000, "is_active": true, "created_by": 0, "updated_by": 0, "createdAt": "2025-03-31T11:14:53.957Z", "updatedAt": null}], "message": "Data fetched successfully"}}}}}}}, "/service-request/name-transfer": {"post": {"tags": ["Service Request"], "summary": "Raised Connection transfer request", "requestBody": {"required": true, "content": {"application/json": {"example": {"customer_connection_id": 19, "name_transfer_reason_id": 4, "title": "Mr", "first_name": "jone", "middle_name": "doe", "last_name": "Frenk", "mobile": "7845698741", "alt_mobile": ""}}}}, "responses": {"200": {"description": "Transfer name request raised Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"area_id": 1, "base_charges": 200, "handling_fees": 50, "gst_percentage": 5, "gst_amount": 150, "total_amount": 1300}, "message": "Application submitted successfully"}}}}}}}, "/tpi/status-count": {"post": {"tags": ["TPI"], "summary": "Status count", "requestBody": {"required": true, "content": {"application/json": {"example": {"lmc_stage": "feasibility-check"}}}}, "responses": {"200": {"description": "Status count", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"status": "success", "statusCode": 200, "data": {"total_feasibility_count": 39, "feasibility_review_pending": 13, "feasibility_review_inprogress": 7, "feasibility_sent_resubmission": 5, "feasibility_checks_approved": 7, "feasibility_rejected_checked": 7}, "message": "Fetch Data successfully."}, "message": "Fetch Data successfully."}}}}}}}, "/service-request/name-transfer-save-details": {"post": {"tags": ["Service Request"], "summary": "Raised Connection transfer request", "requestBody": {"required": true, "content": {"application/json": {"example": {"customer_connection_id": 148}}}}, "responses": {"200": {"description": "Transfer name request raised Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"status": "success", "statusCode": 200, "data": {"name_transfer_status": "application-pending", "isu_synced": 0, "is_active": true, "created_by": 0, "updated_by": 0, "name_transfer_request_id": 2, "existing_connection_id": 148, "new_connection_id": null, "name_transfer_reason": 3, "prefix": "mr", "first_name": "<PERSON><PERSON>", "middle_name": "dou", "last_name": "canel", "mobile": "7845698741", "updatedAt": "2025-04-02T13:34:56.917Z", "createdAt": "2025-04-02T13:34:56.917Z", "email": null, "alternate_mobile": null, "approved_date": null}, "message": "Name transfer application submitted successfully"}, "message": "Application submitted successfully"}}}}}}}, "/lmc-contractor/customer-assignments": {"get": {"tags": ["LMC Contractor"], "summary": "Get LMC Contractor customer assignments", "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"cases": {"type": "array", "items": {"type": "object", "properties": {"mapped_lmc_customer_connection_id": {"type": "integer", "example": 4}, "customer_connection_id": {"type": "integer", "example": 55}, "full_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "7047670143"}, "pincode": {"type": "integer", "example": 400001}, "assignedLMCName": {"type": "string", "example": "Sup1 Visor"}, "assignedLMCID": {"type": "integer", "example": 1}, "visit_datetime": {"type": "string", "format": "date-time", "example": "2024-03-01T09:00:00.000Z"}}}}, "totalRecords": {"type": "integer", "example": 100}, "totalPages": {"type": "integer", "example": 1}, "currentPage": {"type": "integer", "example": 1}}}, "message": {"type": "string", "example": "Dashboard summary retrieved successfully."}}}}}}}}}, "/lmc-contractor/supervisor-onboarding": {"get": {"tags": ["LMC Contractor"], "summary": "Get LMC Contractor supervisor onboarding details with optional approval status filter", "parameters": [{"name": "approve_status", "in": "query", "description": "Filter supervisors based on approval status (e.g., 'rejected', 'approved')", "required": false, "schema": {"type": "string", "example": "rejected"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"supervisor_id": {"type": "integer", "example": 54}, "name": {"type": "string", "example": "<PERSON><PERSON>"}, "mobile": {"type": "string", "example": "9887654321"}, "email": {"type": "string", "example": "<EMAIL>"}, "remark": {"type": "string", "example": null}, "onboarded_on": {"type": "string", "format": "date", "example": "01 Apr 2025"}, "rejected_date": {"type": "string", "format": "date", "example": null}}}}, "message": {"type": "string", "example": "Supervisors retrieved successfully"}}}}}}}}}, "/lmc-contractor/my-supervisor": {"get": {"tags": ["LMC Contractor"], "summary": "Get LMC Contractor's supervisors based on activity status", "parameters": [{"name": "is_active", "in": "query", "description": "Filter supervisors based on their active status (true/false)", "required": false, "schema": {"type": "boolean", "example": false}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"supervisor_id": {"type": "integer", "example": 53}, "name": {"type": "string", "example": "<PERSON><PERSON>"}, "mobile": {"type": "string", "example": "9876543210"}, "email": {"type": "string", "example": "<EMAIL>"}, "onboarded_on": {"type": "string", "format": "date", "example": "01 Apr 2025"}, "inactive_from": {"type": "string", "format": "date", "example": "02 Apr 2025"}}}}, "message": {"type": "string", "example": "Fetch Data successfully."}}}}}}}}}, "/lmc-contractor/geographical-areas": {"get": {"tags": ["LMC Contractor"], "summary": "Retrieve geographical areas", "responses": {"200": {"description": "Successfully retrieved geographical areas", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"geographical_area_id": {"type": "integer", "example": 1}, "region_name": {"type": "string", "example": "north"}, "state_id": {"type": "integer", "example": 21}, "city_id": {"type": "integer", "example": 513}, "ga_area_name": {"type": "string", "example": "north gg area 1"}, "is_active": {"type": "boolean", "example": true}, "created_at": {"type": "string", "format": "date-time", "example": "2025-03-20T05:10:35.910Z"}, "created_by": {"type": "integer", "example": 0}, "updated_at": {"type": "string", "format": "date-time", "nullable": true, "example": null}, "updated_by": {"type": "integer", "example": 0}}}}, "message": {"type": "string", "example": "geographicalAreas retrieved successfully"}}}}}}}}}, "/lmc-contractor/dashboard-metrics": {"get": {"tags": ["LMC Contractor"], "summary": "Retrieve dashboard metrics for a given geographical area", "parameters": [{"name": "geographical_area_id", "in": "query", "description": "ID of the geographical area to retrieve metrics for", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Successfully retrieved dashboard metrics", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"totalGeographicalAreas": {"type": "integer", "example": 1}, "totalAreasCovered": {"type": "integer", "example": 4}, "totalSupervisors": {"type": "integer", "example": 26}, "totalCustomers": {"type": "integer", "example": 99}, "totalConnectionsFeasibility": {"type": "integer", "example": 38}, "totalConnectionsInstallation": {"type": "integer", "example": 31}, "totalConnectionsCommissioning": {"type": "integer", "example": 30}, "totalActiveConnections": {"type": "integer", "example": 99}, "totalRejectedTickets": {"type": "integer", "example": 18}}}, "message": {"type": "string", "example": "dashboardMetrics retrieved successfully"}}}}}}}}}, "/lmc-contractor/supervisor-tracking": {"get": {"tags": ["LMC Contractor"], "summary": "Retrieve supervisor tracking data for geographical areas", "responses": {"200": {"description": "Successfully retrieved supervisor tracking data", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"geographicalAreas": {"type": "array", "items": {"type": "object", "properties": {"geographicalAreaName": {"type": "string", "example": "north"}, "areas": {"type": "array", "items": {"type": "object", "properties": {"areaName": {"type": "string", "example": "Area 1"}, "lmcSupervisors": {"type": "array", "items": {"type": "object", "properties": {"lmcSupervisorName": {"type": "string", "example": "TPI Sales 1 vishwakarma"}, "lmcId": {"type": "integer", "example": 1}, "counts": {"type": "object", "properties": {"totalCustomers": {"type": "integer", "example": 0}, "feasibilityStage": {"type": "integer", "example": 0}, "installationStage": {"type": "integer", "example": 0}, "commissioningStage": {"type": "integer", "example": 0}, "rejectedTickets": {"type": "integer", "example": 0}, "successfulActivations": {"type": "integer", "example": 0}}}}}}}}}}}}}}, "message": {"type": "string", "example": "Fetch Data successfully."}}}}}}}}}, "/lmc-contractor/create-supervisor": {"post": {"tags": ["LMC Contractor"], "summary": "Create a new supervisor", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": "Test"}, "last_name": {"type": "string", "example": "User"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "7654567891"}, "alternate_mobile": {"type": "string", "example": ""}, "bpclAdminId": {"type": "integer", "example": 1}, "password": {"type": "string", "example": "12345"}, "agency_id": {"type": "integer", "example": 1}, "file": {"type": "string", "example": "img.jpg"}}}}}}, "responses": {"200": {"description": "Supervisor created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"is_locked": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 0}, "admin_id": {"type": "integer", "example": 92}, "first_name": {"type": "string", "example": "Test1"}, "last_name": {"type": "string", "example": "User1"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "7654567891"}, "alternate_mobile": {"type": "string", "example": ""}, "user_role": {"type": "string", "example": "supervisor"}, "persona_role_id": {"type": "integer", "nullable": true, "example": null}, "bpcl_admin_id": {"type": "integer", "example": 1}, "agency_id": {"type": "integer", "example": 1}, "is_active": {"type": "boolean", "example": true}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-04-02T13:43:52.163Z"}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-04-02T13:43:52.163Z"}}}, "message": {"type": "string", "example": "Supervisor created successfully"}}}}}}}}}, "/tpi/application-status-summary": {"get": {"tags": ["TPI"], "summary": "Fetch the summary of application statuses for a given TPI ID", "parameters": [{"name": "tpi_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 2}}], "responses": {"200": {"description": "Successfully fetched application status summary", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"total_applications_assigned": {"type": "integer", "example": 103}, "applications_pending_review": {"type": "integer", "example": 0}, "applications_sent_resubmission": {"type": "integer", "example": 0}, "approved_applications": {"type": "integer", "example": 0}, "rejected_applications": {"type": "integer", "example": 19}}}, "message": {"type": "string", "example": "Fetch Data successfully."}}}}}}}}}, "/tpi/application-status": {"get": {"tags": ["TPI"], "summary": "Fetch the application status details for a given TPI ID", "parameters": [{"name": "tpi_id", "in": "query", "required": true, "schema": {"type": "integer", "example": 5}}], "responses": {"200": {"description": "Successfully fetched application status data", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"cases": {"type": "array", "items": {"type": "object"}, "example": []}, "totalRecords": {"type": "integer", "example": 0}, "totalPages": {"type": "integer", "example": 0}, "currentPage": {"type": "integer", "example": 1}}}, "message": {"type": "string", "example": "Fetch Data successfully."}}}}}}}}}, "/tpi/customer-status-history": {"post": {"tags": ["TPI"], "summary": "Fetch the customer status history based on search parameters", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"search": {"type": "string", "example": ""}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 100}, "lmc_stage": {"type": "string", "example": "installation"}, "startDate": {"type": "string", "example": ""}, "endDate": {"type": "string", "example": ""}}}}}}, "responses": {"200": {"description": "Successfully retrieved customer status history data", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"totalRecords": {"type": "integer", "example": 40}, "totalPages": {"type": "integer", "example": 1}, "currentPage": {"type": "integer", "example": 1}, "rows": {"type": "array", "items": {"type": "object", "properties": {"mapped_lmc_customer_connection_id": {"type": "integer", "example": 14}, "customer_connection_id": {"type": "integer", "example": 147}, "lmc_id": {"type": "integer", "example": 6}, "lmc_stage": {"type": "string", "example": "installation"}, "visit_datetime": {"type": "string", "example": "2025-03-28T05:30:00.000Z"}, "reason": {"type": "string", "example": "Routine check"}, "visit_status": {"type": "string", "example": "completed"}, "tpi_id": {"type": "integer", "example": 2}, "tpi_status": {"type": "string", "example": "no-submission"}, "customer_approval": {"type": "integer", "example": 1}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 1}, "updated_by": {"type": "integer", "example": 1}, "createdAt": {"type": "string", "example": "2025-03-23T10:52:47.227Z"}, "updatedAt": {"type": "string", "example": "2025-03-28T12:54:07.117Z"}, "customer_connection": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 147}, "first_name": {"type": "string", "example": "<PERSON><PERSON>"}, "last_name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "987650022"}, "bp_id": {"type": "string", "example": null}, "trx_customer_connection_addresses": {"type": "array", "items": {"type": "object", "properties": {"connection_address": {"type": "string", "example": "Sample Address 147"}, "connection_pincode": {"type": "integer", "example": 110047}, "permanent_address": {"type": "string", "example": "Permanent Address 147"}, "permanent_pincode": {"type": "integer", "example": 120047}}}}}}}}}}}, "message": {"type": "string", "example": "Customer Status History data retrieved successfully"}}}}}}}}}, "/tpi/lmc-status-data-list": {"post": {"tags": ["TPI"], "summary": "Fetch the LMC status data list based on search parameters", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"search": {"type": "string", "example": ""}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "startDate": {"type": "string", "example": ""}, "endDate": {"type": "string", "example": ""}, "admin_type": {"type": "string", "example": "LMC"}}}}}}, "responses": {"200": {"description": "Successfully retrieved LMC status data", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"totalRecords": {"type": "integer", "example": 10}, "totalPages": {"type": "integer", "example": 10}, "currentPage": {"type": "integer", "example": 1}, "rows": {"type": "array", "items": {"type": "object", "properties": {"tpi_onboard_approve_id": {"type": "integer", "example": 14}, "tpi_id": {"type": "integer", "example": 13}, "supervisor_id": {"type": "integer", "example": 34}, "admin_type": {"type": "string", "example": "LMC"}, "approve_status": {"type": "string", "example": "pending"}, "remark": {"type": "string", "example": "This is a sample remark."}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 5}, "updated_by": {"type": "integer", "example": 0}, "createdAt": {"type": "string", "example": "2025-04-04T12:05:43.220Z"}, "updatedAt": {"type": "string", "example": "2025-04-04T12:10:05.490Z"}, "lmc_created_by": {"type": "object", "properties": {"first_name": {"type": "string", "example": "LMC Contractor 1"}, "last_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "8908908912"}, "is_active": {"type": "boolean", "example": true}}}}}}}}, "message": {"type": "string", "example": "Get TPI LMC Approved Rejected Pending Data successfully"}}}}}}}}}, "/tpi/feasibility-installation-customer-details": {"post": {"tags": ["TPI"], "summary": "Fetch feasibility installation customer details based on customer connection ID and LMC stage", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 177}, "lmc_stage": {"type": "string", "example": "feasibility-check"}}}}}}, "responses": {"200": {"description": "Successfully fetched feasibility installation customer details", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"customerDetails": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 177}, "connection_type": {"type": "string", "example": "original"}, "prefix": {"type": "string", "example": "other"}, "bpcl_id": {"type": "string", "example": "1032"}, "available_pngareas_id": {"type": "integer", "example": 8}, "customer_id": {"type": "integer", "example": 52}, "first_name": {"type": "string", "example": "<PERSON><PERSON>"}, "middle_name": {"type": "string", "example": null}, "last_name": {"type": "string", "example": "LastName52"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "*********"}, "alternate_mobile": {"type": "string", "example": null}, "spouse_name": {"type": "string", "example": null}, "parents_name": {"type": "string", "example": null}, "ca_no": {"type": "string", "example": null}, "bp_id": {"type": "string", "example": null}, "connection_address_type": {"type": "string", "example": "company_owned"}, "is_extra_connection": {"type": "boolean", "example": false}, "extra_points": {"type": "integer", "example": 1}, "govt_scheme_id": {"type": "string", "example": null}, "account_type": {"type": "string", "example": "postpaid"}, "status": {"type": "string", "example": "approved"}, "internal_status": {"type": "string", "example": "approved"}, "approved_date": {"type": "string", "example": null}, "onboarding_date": {"type": "string", "example": null}, "installation_date": {"type": "string", "example": null}, "disconnected_date": {"type": "string", "example": null}, "created_by_type": {"type": "string", "example": "customer"}, "isu_synced": {"type": "integer", "example": 0}, "isu_status": {"type": "string", "example": "pending"}, "isu_error": {"type": "string", "example": null}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 1}, "updated_by": {"type": "integer", "example": 0}, "createdAt": {"type": "string", "example": "2025-03-23T10:25:19.520Z"}, "updatedAt": {"type": "string", "example": "2025-04-07T02:05:25.177Z"}, "trx_customer_connection_addresses": {"type": "array", "items": {"type": "object", "properties": {"customer_connection_address_id": {"type": "integer", "example": 275}, "customer_connection_id": {"type": "integer", "example": 177}, "connection_type": {"type": "string", "example": "original"}, "connection_pngareas_id": {"type": "integer", "example": 8}, "connection_address": {"type": "string", "example": "Sample Address 177"}, "connection_pincode": {"type": "integer", "example": 110027}, "connection_landmark": {"type": "string", "example": "Landmark 177"}, "connection_block_no": {"type": "string", "example": "Block 7"}, "connection_floor_no": {"type": "string", "example": "Floor 2"}, "connection_house_no": {"type": "string", "example": "House 177"}, "connection_state": {"type": "string", "example": "State 2"}, "connection_city": {"type": "string", "example": "City 7"}, "connection_district": {"type": "string", "example": "District 1"}, "permanent_pngareas_id": {"type": "integer", "example": 8}, "permanent_address": {"type": "string", "example": "Permanent Address 177"}, "permanent_pincode": {"type": "integer", "example": 120027}, "permanent_landmark": {"type": "string", "example": "177"}, "permanent_block_no": {"type": "string", "example": "PBlock 7"}, "permanent_floor_no": {"type": "string", "example": "PFloor 2"}, "permanent_house_no": {"type": "string", "example": "PHouse 177"}, "permanent_state": {"type": "string", "example": "PState 2"}, "permanent_city": {"type": "string", "example": "PCity 7"}, "permanent_district": {"type": "string", "example": "PDistrict 1"}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 1}, "updated_by": {"type": "integer", "example": 1}, "createdAt": {"type": "string", "example": "2025-03-23T10:44:23.087Z"}, "updatedAt": {"type": "string", "example": null}}}}}}, "lmcDetails": {"type": "object", "properties": {"first_name": {"type": "string", "example": "LMC supervisor"}, "last_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "user_role": {"type": "string", "example": "supervisor"}, "bpcl_admin_id": {"type": "integer", "example": 1}, "mobile": {"type": "string", "example": "8908908913"}}}, "feasibilityStatus": {"type": "array", "items": {}}, "feasibilitypipelineDetails": {"type": "array", "items": {}}, "pipelineDetails": {"type": "array", "items": {}}}}, "message": {"type": "string", "example": "Fetch Data successfully."}}}}}}}}}, "/tpi/lmc-work-approval-status-update": {"post": {"tags": ["TPI"], "summary": "Update the LMC work approval status", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 152}, "tpi_status": {"type": "string", "example": "completed"}, "lmc_stage": {"type": "string", "example": "feasibility-check"}, "reason": {"type": "string", "example": "feasibility-check completed"}}}}}}, "responses": {"200": {"description": "Successfully updated the LMC work approval status", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "integer", "example": 1}}, "message": {"type": "string", "example": "Updated Data successfully."}}}}}}}}}, "/tpi/lmc-status-update": {"post": {"tags": ["TPI"], "summary": "Update the LMC status", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"tpi_onboard_approve_id": {"type": "integer", "example": 1}, "approve_status": {"type": "string", "example": "rejected"}, "remark": {"type": "string", "example": "testing rejected"}}}}}}, "responses": {"200": {"description": "Successfully updated the LMC status", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "integer", "example": 1}}, "message": {"type": "string", "example": "LMC Status Data Updated Successfully."}}}}}}}}}, "/tpi/customer-review-list": {"post": {"tags": ["TPI"], "summary": "Fetch the list of customer reviews", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "search": {"type": "string", "example": ""}, "startDate": {"type": "string", "format": "date", "example": ""}, "endDate": {"type": "string", "format": "date", "example": ""}}}}}}, "responses": {"200": {"description": "Successfully fetched the customer review list", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"totalRecords": {"type": "integer", "example": 53}, "totalPages": {"type": "integer", "example": 53}, "currentPage": {"type": "integer", "example": 1}, "rows": {"type": "array", "items": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 48}, "first_name": {"type": "string", "example": "<PERSON><PERSON>"}, "last_name": {"type": "string", "example": "gupta"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "1234567890"}, "created_at": {"type": "string", "format": "date-time", "example": "2025-04-02T18:16:17.337Z"}, "status": {"type": "string", "example": "application-pending"}, "internal_status": {"type": "string", "example": "application-rejected"}, "created_by_type": {"type": "string", "example": "null"}, "trx_customer_connection_addresses": {"type": "object", "properties": {"connection_pincode": {"type": "integer", "example": 560001}}}}}}}}, "message": {"type": "string", "example": "Fetch Data successfully."}}}}}}}}}, "/tpi/customer-status-update": {"post": {"tags": ["TPI"], "summary": "Update the status of a customer", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 317}, "status": {"type": "string", "example": "application-resubmit"}, "internal_status": {"type": "string", "example": "application-resubmit"}, "resubmit_reasons": {"type": "array", "items": {"type": "object", "properties": {"master_id": {"type": "integer", "example": 59}, "master_value": {"type": "string", "example": "additional-info"}, "description": {"type": "string", "example": "some description"}}}}}}}}}, "responses": {"200": {"description": "Successfully updated the customer status", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "integer", "example": 1}}, "message": {"type": "string", "example": "customer Status Data Updated Successfully."}}}}}}}}}, "/tpi/customer-application-details": {"post": {"tags": ["TPI"], "summary": "Fetch customer application details", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 65}}}}}}, "responses": {"200": {"description": "Successfully fetched customer application details", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 65}, "connection_type": {"type": "string", "example": "original"}, "prefix": {"type": "string", "example": "mr"}, "bpcl_id": {"type": "string", "example": "9890909090"}, "available_pngareas_id": {"type": "integer", "example": 1}, "customer_id": {"type": "integer", "example": 2}, "first_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "example": ""}, "mobile": {"type": "string", "example": "8899007711"}, "status": {"type": "string", "example": "installation-rescheduled"}, "internal_status": {"type": "string", "example": "application-completed"}, "trx_customer_connection_addresses": {"type": "array", "items": {"type": "object", "properties": {"connection_pincode": {"type": "integer", "example": 400001}, "connection_state": {"type": "string", "example": "Maharashtra"}, "connection_city": {"type": "string", "example": "Mumbai"}, "connection_district": {"type": "string", "example": "Mumbai"}, "connection_house_no": {"type": "string", "example": "789"}}}}, "trx_customer_proof_documents": {"type": "array", "items": {"type": "object", "properties": {"proof_type": {"type": "string", "example": "proof_of_ownership"}, "file_name": {"type": "string", "example": "Screenshot 2025-03-12 151620.png"}, "file_path": {"type": "string", "example": "/home/<USER>/wwwroot/public/uploads/customers/456/Screenshot 2025-03-12 151620.png"}, "verification_status": {"type": "string", "example": "pending"}}}}}}, "message": {"type": "string", "example": "Customer application details fetched successfully."}}}}}}}}}, "/tpi/onboarding-approval": {"post": {"tags": ["TPI"], "summary": "Update onboarding approval status", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"persona_type": {"type": "string", "example": "dma"}, "supervisor_id": {"type": "integer", "example": 17}, "approve_status": {"type": "string", "enum": ["approved", "rejected"], "example": "approved"}, "remark": {"type": "string", "example": "All documents rejected"}}}}}}, "responses": {"200": {"description": "Onboarding status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "integer"}, "example": [1]}, "message": {"type": "string", "example": "Onboarding status updated successfully."}}}}}}}}}, "/tpi/onboard-status-count": {"post": {"tags": ["TPI"], "summary": "Fetch onboarding status count", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"admin_type": {"type": "string", "example": "DMA"}}}}}}, "responses": {"200": {"description": "Successfully fetched onboarding status count", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"pending": {"type": "integer", "example": 5}, "approved": {"type": "integer", "example": 8}, "rejected": {"type": "integer", "example": 3}}}, "example": [{"pending": 5, "approved": 8, "rejected": 3}]}, "message": {"type": "string", "example": "Fetch Data successfully."}}}}}}}}}, "/tpi/pending-onboarding-list": {"post": {"tags": ["TPI"], "summary": "Fetch pending onboarding list", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"persona_type": {"type": "string", "example": "dma"}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 50}}}}}}, "responses": {"200": {"description": "Successfully fetched pending onboarding list", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"supervisor_id": {"type": "integer", "example": 121}, "first_name": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "last_name": {"type": "string", "example": "Bairag<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "9560485977"}, "alternate_mobile": {"type": "string", "example": ""}, "persona_role": {"type": "object", "properties": {"persona_role_id": {"type": "integer", "example": 1}, "role_name": {"type": "string", "example": "DMA"}}}, "onboard_status": {"type": "string", "example": "approved"}, "remark": {"type": "string", "example": "onboarding approved"}, "onboard_updated_at": {"type": "string", "example": "2025-04-08T20:30:09.630Z"}, "created_by": {"type": "object", "properties": {"id": {"type": "integer", "example": 3}, "name": {"type": "string", "example": "DMA Contractor 1 vishwakarma"}}}, "created_at": {"type": "string", "example": "2025-04-08T20:11:08.337Z"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer", "example": 16}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 1}, "total_pages": {"type": "integer", "example": 16}}}}}, "message": {"type": "string", "example": "Pending onboarding list fetched successfully"}}}}}}}}}, "/lmc/connection-list": {"post": {"tags": ["LMC"], "summary": "Fetch connection list for a customer", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 48}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/service-request/address-change": {"post": {"tags": ["Service Request"], "summary": "Submit address change request", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"address_request_type": {"type": "string", "example": "permanent-address"}, "customer_connection_id": {"type": "integer", "example": 49}, "available_pngareas_id": {"type": "integer", "example": 6}, "house_flat_no": {"type": "string", "example": "1030"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/service-request/save-permanent-address-change": {"post": {"tags": ["Service Request"], "summary": "Save permanent address change request", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 49}, "customer_connection_address_id": {"type": "integer", "example": 341}, "address_request_type": {"type": "string", "example": "permanent-address", "enum": ["permanent-address", "transfer-connection"]}, "reason_id": {"type": "integer", "example": 3}, "permanent_address": {"type": "object", "properties": {"same_as_connetion_address": {"type": "boolean", "example": true}, "permanent_pngareas_id": {"type": "integer", "example": 6}, "permanent_pincode": {"type": "string", "example": "482003"}, "permanent_landmark": {"type": "string", "example": "anand kunj"}, "permanent_block_no": {"type": "string", "example": ""}, "permanent_floor_no": {"type": "string", "example": "1st floor"}, "permanent_house_no": {"type": "string", "example": "1030"}, "permanent_state": {"type": "string", "example": "<PERSON>hya pradesh"}, "permanent_city": {"type": "string", "example": "jabalpur"}, "permanent_district": {"type": "string", "example": "jabalpur"}}}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/service-request/permanent-address-change-document": {"post": {"tags": ["Service Request"], "summary": "Upload document for permanent address change", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "string", "example": "49"}, "house_flat_no": {"type": "string", "example": "1030"}, "available_pngareas_id": {"type": "string", "example": "6"}, "proof_type": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "proof_master_id": {"type": "string", "example": "22"}, "files": {"type": "string", "format": "binary"}}, "required": ["customer_connection_id", "house_flat_no", "available_pngareas_id", "proof_type", "proof_master_id", "files"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/service-request/get-address-change-detail": {"post": {"tags": ["Service Request"], "summary": "Get address change detail", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 49}, "available_pngareas_id": {"type": "integer", "example": 6}, "house_flat_no": {"type": "string", "example": "1030"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/service-request/update-connection": {"post": {"tags": ["Service Request"], "summary": "Update connection request (temporary disconnection, etc.)", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"update_request_type": {"type": "string", "example": "temporary-disconnection"}, "request_type": {"type": "string", "example": "disconnection"}, "request_sub_type": {"type": "string", "example": "temporary"}, "customer_connection_id": {"type": "integer", "example": 50}, "customer_connection_address_id": {"type": "integer", "example": 341}, "request_reason_id": {"type": "integer", "example": 13}, "estimated_duration_id": {"type": "integer", "example": 17}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/service-request/extra-points": {"post": {"tags": ["Service Request"], "summary": "Submit extra point service request", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 77}, "customer_connection_address_id": {"type": "integer", "example": 30}, "extra_point_type": {"type": "array", "items": {"type": "string", "enum": ["extra-kitchen-point", "gas-geyser-point", "other-point"]}, "example": ["extra-kitchen-point", "gas-geyser-point"]}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/service-request/alteration-modification": {"post": {"tags": ["Service Request"], "summary": "Submit alteration/modification request", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 77}, "customer_connection_address_id": {"type": "integer", "example": 30}, "area_id": {"type": "integer", "example": 22}, "description": {"type": "string", "example": "description for the modification."}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/service-request/save-new-address-details": {"post": {"tags": ["Service Request"], "summary": "Save new address details for a customer connection", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 48}, "old_available_pngareas_id": {"type": "integer", "example": 6}, "old_house_flat_no": {"type": "string", "example": "66"}, "available_pngareas_id": {"type": "integer", "example": 6}, "house_flat_no": {"type": "string", "example": "66"}, "step_no": {"type": "integer", "example": 2}, "connection_address": {"type": "object", "properties": {"connection_pngareas_id": {"type": "integer", "example": 6}, "connection_pincode": {"type": "string", "example": "560002"}, "connection_landmark": {"type": "string", "example": "anand kunj"}, "connection_block_no": {"type": "string", "example": ""}, "connection_floor_no": {"type": "string", "example": "1st floor"}, "connection_house_no": {"type": "string", "example": "66"}, "connection_state": {"type": "string", "example": "<PERSON>hya pradesh"}, "connection_city": {"type": "string", "example": "jabalpur"}, "connection_district": {"type": "string", "example": "jabalpur"}, "permanent_pngareas_id": {"type": "integer", "example": 6}, "permanent_pincode": {"type": "string", "example": "482003"}, "permanent_landmark": {"type": "string", "example": "anand kunj"}, "permanent_block_no": {"type": "string", "example": ""}, "permanent_floor_no": {"type": "string", "example": "1st floor"}, "permanent_house_no": {"type": "string", "example": "1030"}, "permanent_state": {"type": "string", "example": "<PERSON>hya pradesh"}, "permanent_city": {"type": "string", "example": "jabalpur"}, "permanent_district": {"type": "string", "example": "jabalpur"}, "connection_type": {"type": "string", "example": "transfer-connection"}}}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/service-request/new-address-document": {"post": {"tags": ["Service Request"], "summary": "Upload new address document for a service request", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "string", "example": "48"}, "house_flat_no": {"type": "string", "example": "66"}, "available_pngareas_id": {"type": "string", "example": "6"}, "proof_type": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "proof_master_id": {"type": "string", "example": "22"}, "files": {"type": "string", "format": "binary"}}, "required": ["customer_connection_id", "house_flat_no", "available_pngareas_id", "proof_type", "proof_master_id", "files"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/service-request/submit-enable-connection-request": {"post": {"tags": ["Service Request"], "summary": "Submit a request to enable a connection (e.g., transfer connection)", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 48}, "customer_connection_address_id": {"type": "integer", "example": 341}, "address_request_type": {"type": "string", "example": "transfer-connection"}, "available_pngareas_id": {"type": "integer", "example": 6}, "house_flat_no": {"type": "string", "example": "66"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/auth/verify-otp": {"post": {"tags": ["DMA"], "summary": "User login", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "otp": {"type": "string", "example": "123456"}}, "required": ["email", "otp"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"admin_id": {"type": "integer", "example": 3}, "bpcl_admin_id": {"type": "integer", "example": 1}, "persona_role_id": {"type": "integer", "example": 1}, "user_role": {"type": "string", "example": "contractor"}, "agency_id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "DMA Contractor 1"}, "last_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mobile": {"type": "string", "example": "8908908901"}, "alternate_mobile": {"type": "string", "example": null}, "email": {"type": "string", "example": "<EMAIL>"}, "city": {"type": "string", "example": "mumbai"}, "state": {"type": "string", "example": "maharastra"}, "last_logged_in": {"type": "string", "example": null}, "is_active": {"type": "boolean", "example": true}, "is_locked": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 0}, "createdAt": {"type": "string", "example": "2025-03-20T05:10:36.070Z"}, "updatedAt": {"type": "string", "example": null}}}, "token": {"type": "string", "example": "xyz"}}}, "message": {"type": "string", "example": "Logged in successfully"}}}}}}}}}, "/auth/reset-password": {"post": {"tags": ["DMA"], "summary": "User reset password", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "current_password": {"type": "string", "example": "admin@123"}, "new_password": {"type": "string", "example": "admin@12345"}, "re_enter_password": {"type": "string", "example": "admin@12345"}}, "required": ["email", "current_password", "new_password", "re_enter_password"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}}}, "token": {"type": "string", "example": "xyz"}}}, "message": {"type": "string", "example": "Logged in successfully"}}}}}}}}}, "/media/image": {"post": {"tags": ["Media"], "summary": "Get media", "requestBody": {"required": true, "content": {"application/json": {"example": {"foldername": "Registration", "media": "test.png"}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Application submitted successfully"}}}}}}}, "/media/download": {"post": {"tags": ["Media"], "summary": "Download media", "requestBody": {"required": true, "content": {"application/json": {"example": {"foldername": "Registration", "media": "test.png"}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Application submitted successfully"}}}}}}}, "/media/delete": {"post": {"tags": ["Media"], "summary": "Delete media", "requestBody": {"required": true, "content": {"application/json": {"example": {"foldername": "Registration", "media": "test.png"}}}}, "responses": {"200": {"description": "Data deleted Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data deleted successfully"}}}}}}}, "/bpcl-admin/area-list": {"post": {"tags": ["BPCL Admin"], "summary": "Fetch area list", "description": "Retrieves a list of area filtered by Geographical area.", "requestBody": {"required": true, "content": {"application/json": {"example": {"ga_id": 1}}}}, "responses": {"200": {"description": "Successful response with area listing", "content": {"application/json": {"example": [{"area_id": 1, "geographical_area_id": 1, "area_name": "Area 1", "is_active": true, "created_by": 0, "updated_by": 0, "createdAt": "2025-03-20T05:10:35.923Z", "updatedAt": null}]}}}, "400": {"description": "Bad Request - Invalid input", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "message": "Invalid status provided"}}}}}}}, "/bpcl-admin/contractor-onboarding": {"post": {"tags": ["BPCL Admin"], "summary": "Contractor Onboarding", "description": "Onboards a new contractor with personal details, area assignment, and supporting document upload (PNG, JPG, PDF).", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"ga_id": {"type": "integer", "example": 1}, "contractor_type": {"type": "string", "example": "TPI Sales"}, "mobile": {"type": "string", "example": "7734611432"}, "alt_mobile": {"type": "string", "example": "7862156456"}, "email": {"type": "string", "example": "<EMAIL>"}, "first_name": {"type": "string", "example": "john"}, "middle_name": {"type": "string", "example": ""}, "last_name": {"type": "string", "example": "test"}, "agency": {"type": "string", "example": "agency"}, "area_id": {"type": "string", "example": "1"}, "document_type": {"type": "string", "example": "contract"}, "file": {"type": "string", "format": "binary", "description": "Contract document (PNG, JPG, PDF)"}}, "required": ["ga_id", "contractor_type", "mobile", "email", "first_name", "last_name", "agency", "area_id", "document_type", "file"]}}}}, "responses": {"200": {"description": "Contractor onboarded successfully", "content": {"application/json": {"example": {"user": {"is_locked": true, "updated_by": 0, "admin_id": 196, "persona_role_id": 2, "user_role": "sales", "first_name": "jone", "last_name": "test", "mobile": "7734611432", "alternate_mobile": "7862156456", "email": "<EMAIL>", "is_active": false, "created_by": 153, "updatedAt": "2025-04-18T19:26:27.737Z", "createdAt": "2025-04-18T19:26:27.737Z"}, "area": {"is_active": true, "updated_by": 0, "amga_id": 19, "admin_id": 196, "ga_area_id": 1, "created_by": 153, "updated_at": "2025-04-18T19:26:27.963Z", "created_at": "2025-04-18T19:26:27.963Z"}, "mappedArea": [{"admin_mapped_area_id": null, "amga_id": 19, "area_id": "1", "is_active": true, "created_by": 153, "updated_by": 0, "createdAt": "2025-04-18T13:56:28.056Z", "updatedAt": "2025-04-18T13:56:28.056Z"}]}}}}, "400": {"description": "Bad Request - Missing or invalid input", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "message": "Invalid contractor details or missing document"}}}}}}}, "/bpcl-admin/tpi-sales-areas-wise": {"post": {"tags": ["BPCL Admin"], "summary": "Tpi Sales Area Wise", "description": "Get areas list for an geographical area for Tpi Sales", "requestBody": {"required": true, "content": {"application/json": {"example": {"ga_area_id": 1, "page": 1, "limit": 10, "area_id": [], "search": ""}}}}, "responses": {"200": {"description": "Tpi sales areas fetched successfully", "content": {"application/json": {"example": {"records": [{"area_name": "Area 1", "area_id": 1, "agency_name": "agency", "admin_id": 1, "first_name": "TPI Sales 1", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobile": "8908908903", "email": "<EMAIL>", "totalAssigned": 5, "totalPending": 0, "totalRework": 0, "totalApproved": 5, "totalRejected": 0}, {"area_name": "Area 2", "area_id": 2, "agency_name": null, "admin_id": 183, "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "last_name": "<PERSON>", "mobile": "7734611931", "email": "<EMAIL>", "totalAssigned": 0, "totalPending": 0, "totalRework": 0, "totalApproved": 0, "totalRejected": 0}, {"area_name": "Area 3", "area_id": 3, "agency_name": "agency", "admin_id": 1, "first_name": "TPI Sales 1", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobile": "8908908903", "email": "<EMAIL>", "totalAssigned": 0, "totalPending": 0, "totalRework": 0, "totalApproved": 0, "totalRejected": 0}, {"area_name": "Area 4", "area_id": 4, "agency_name": "agency", "admin_id": 1, "first_name": "TPI Sales 1", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobile": "8908908903", "email": "<EMAIL>", "totalAssigned": 0, "totalPending": 0, "totalRework": 0, "totalApproved": 0, "totalRejected": 0}], "count": 4}}}}, "400": {"description": "Bad Request - Missing or invalid input", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "message": "Invalid contractor details or missing document"}}}}}}}, "/bpcl-admin/tpi-engineer-areas-wise": {"post": {"tags": ["BPCL Admin"], "summary": "Tpi Engineer Area Wise", "description": "Get areas list for an geographical area for Tpi Engineer", "requestBody": {"required": true, "content": {"application/json": {"example": {"ga_area_id": 1, "page": 1, "limit": 10, "area_id": [], "search": ""}}}}, "responses": {"200": {"description": "Tpi engineer areas fetched successfully", "content": {"application/json": {"example": {"records": [{"area_name": "Area 1", "area_id": 1, "agency_name": "agency", "admin_id": 1, "first_name": "TPI Engineer 1", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobile": "8908908903", "email": "<EMAIL>", "totalAssigned": 5, "totalPending": 0, "totalRework": 0, "totalApproved": 5, "totalRejected": 0}, {"area_name": "Area 2", "area_id": 2, "agency_name": null, "admin_id": 183, "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "last_name": "<PERSON>", "mobile": "7734611931", "email": "<EMAIL>", "totalAssigned": 0, "totalPending": 0, "totalRework": 0, "totalApproved": 0, "totalRejected": 0}, {"area_name": "Area 3", "area_id": 3, "agency_name": "agency", "admin_id": 1, "first_name": "TPI Sales 1", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobile": "8908908903", "email": "<EMAIL>", "totalAssigned": 0, "totalPending": 0, "totalRework": 0, "totalApproved": 0, "totalRejected": 0}, {"area_name": "Area 4", "area_id": 4, "agency_name": "agency", "admin_id": 1, "first_name": "TPI Sales 1", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobile": "8908908903", "email": "<EMAIL>", "totalAssigned": 0, "totalPending": 0, "totalRework": 0, "totalApproved": 0, "totalRejected": 0}], "count": 4}}}}, "400": {"description": "Bad Request - Missing or invalid input", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "message": "Invalid contractor details or missing document"}}}}}}}, "/ga-customer/ongoing-areas-list": {"post": {"tags": ["BPCL Admin"], "summary": "Ongoing Area list", "requestBody": {"required": true, "content": {"application/json": {"example": {"geographical_area": 1, "area_id": [1, 2, 3], "page": 1, "limit": 10, "search": ""}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/ga-customer/completed-areas-list": {"post": {"tags": ["BPCL Admin"], "summary": "Completed Area list", "requestBody": {"required": true, "content": {"application/json": {"example": {"geographical_area": 1, "area_id": [1, 2, 3], "page": 1, "limit": 10, "search": ""}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/ga-customer/ongoing-summary-counts": {"post": {"tags": ["BPCL Admin"], "summary": "Ongoing Summary Counts", "requestBody": {"required": true, "content": {"application/json": {"example": {"geographical_area": 1, "area_id": [1, 2, 3, 4]}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/ga-customer/completed-summary-counts": {"post": {"tags": ["BPCL Admin"], "summary": "Comleted Summary Counts", "requestBody": {"required": true, "content": {"application/json": {"example": {"geographical_area": 1, "area_id": [1, 2, 3, 4]}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/fo-supervisor/mro-listing": {"post": {"tags": ["FO Supervisor"], "summary": "Get MRO Listing for FO Supervisor", "description": "Retrieves paginated MRO tickets assigned within a specified geographical area for the logged-in FO Supervisor.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}, "geographical_area_id": {"type": "integer", "example": 1}, "area_ids": {"type": "string", "example": "1", "description": "Filter listing as per the area id"}}, "required": ["page", "limit", "geographical_area_id"]}}}}, "responses": {"200": {"description": "Successfully fetched MRO listing", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/fo-supervisor/mro-details": {"post": {"tags": ["FO Supervisor"], "summary": "Get detailed MRO information", "description": "Fetches detailed information about a specific MRO ticket for a Field Officer Supervisor.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mro_ticket": {"type": "string", "description": "Unique MRO ticket number", "example": "MRO 001A"}}, "required": ["mro_ticket"]}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/fo-supervisor/meter-reading-upload": {"post": {"tags": ["FO Supervisor"], "summary": "Upload meter reading with image", "description": "Allows a supervisor to upload a meter reading along with an image file and related metadata.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "Image file of the meter reading"}, "current_reading": {"type": "string", "description": "The meter's current reading", "example": "654"}, "mro_id": {"type": "string", "description": "Meter Reading Order ID", "example": "50"}, "meter_no": {"type": "string", "description": "Meter number", "example": "12221"}, "remarks": {"type": "string", "description": "Any remarks regarding the meter reading", "example": "testeee3243"}}, "required": ["file", "current_reading", "mro_id", "meter_no"]}}}}, "responses": {"200": {"description": "Meter reading uploaded successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/fo-supervisor/mro-customer-details": {"post": {"tags": ["FO Supervisor"], "summary": "Get MRO customer details", "description": "Fetches detailed MRO customer information for a given customer connection ID.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 57}}, "required": ["customer_connection_id"]}}}}, "responses": {"200": {"description": "Customer MRO details fetched successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "Customer details retrieved", "data": {}}}}}}}}, "/fo-supervisor/geographical-areas": {"post": {"tags": ["FO Supervisor"], "summary": "Get mapped geographical areas", "description": "Returns a list of geographical areas mapped to the currently logged-in FO Supervisor.", "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "description": "This endpoint does not require any request body. Authorization token is used to identify the supervisor."}}}}, "responses": {"200": {"description": "List of geographical areas retrieved successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "Geographical areas fetched", "data": [{"geographical_area_id": 1, "geographical_area_name": "Zone A", "district": "Mumbai", "state": "Maharashtra"}, {"geographical_area_id": 2, "geographical_area_name": "Zone B", "district": "Pune", "state": "Maharashtra"}]}}}}}}}, "/fo-supervisor/dashboard-metics": {"post": {"tags": ["FO Supervisor"], "summary": "Get FO Dashboard Metrics", "description": "Fetches dashboard metrics such as assigned MROs, completed readings, pending readings, and performance stats based on a geographical area.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"geographical_area_id": {"type": "integer", "example": 3, "description": "ID of the geographical area for which metrics are requested"}}, "required": ["geographical_area_id"]}}}}, "responses": {"200": {"description": "Dashboard metrics fetched successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "Dashboard metrics retrieved", "data": {"total_mros_assigned": 120, "completed_meter_readings": 85, "pending_meter_readings": 35, "fo_performance_score": 92}}}}}}}}, "/fo-admin/create-supervisor": {"post": {"tags": ["FO Admin"], "summary": "Create a new FO Supervisor", "description": "Creates a new FO Supervisor with profile details and an optional profile image.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": "Test"}, "middle_name": {"type": "string", "example": ""}, "last_name": {"type": "string", "example": "User"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "7659560011"}, "file": {"type": "string", "format": "binary", "description": "Optional profile image of the supervisor"}, "area_ids": {"type": "string", "example": "1"}}, "required": ["first_name", "last_name", "email", "mobile", "file"]}}}}, "responses": {"200": {"description": "Supervisor created successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "FO Supervisor created successfully", "data": {"id": 101, "first_name": "Test", "last_name": "User", "email": "<EMAIL>", "mobile": "7659560011", "profile_image_url": "http://example.com/uploads/supervisor_image.jpg"}}}}}, "400": {"description": "Bad Request - Validation failed", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Missing required fields or invalid input"}}}}, "401": {"description": "Unauthorized - Invalid or expired token", "content": {"application/json": {"example": {"status": "error", "statusCode": 401, "message": "Unauthorized"}}}}}}}, "/fo-admin/revoke-supervisor": {"post": {"tags": ["FO Admin"], "summary": "Revoke an FO Supervisor", "description": "Revokes (deactivates or removes) an existing FO Supervisor by their ID.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"supervisorId": {"type": "integer", "example": 56, "description": "ID of the supervisor to be revoked"}}, "required": ["supervisorId"]}}}}, "responses": {"200": {"description": "Supervisor revoked successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "FO Supervisor revoked successfully"}}}}, "400": {"description": "Bad Request - Invalid supervisor ID", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Invalid supervisorId provided"}}}}, "401": {"description": "Unauthorized - Missing or invalid token", "content": {"application/json": {"example": {"status": "error", "statusCode": 401, "message": "Unauthorized"}}}}, "404": {"description": "Supervisor not found", "content": {"application/json": {"example": {"status": "error", "statusCode": 404, "message": "Supervisor not found"}}}}}}}, "/fo-admin/assign-supervisor": {"post": {"tags": ["FO Admin"], "summary": "Assign FO Supervisor to MRO Ticket", "description": "Assigns a Field Officer Supervisor to a specific MRO (Meter Reading Order) ticket.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"fo_supervisor_id": {"type": "integer", "example": 125, "description": "ID of the FO Supervisor to be assigned"}, "mro_ticket": {"type": "integer", "example": 153, "description": "ID of the MRO ticket to assign to the supervisor"}}, "required": ["fo_supervisor_id", "mro_ticket"]}}}}, "responses": {"200": {"description": "Supervisor assigned successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "FO Supervisor assigned successfully to MRO ticket"}}}}, "400": {"description": "Bad Request - Missing or invalid parameters", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Invalid fo_supervisor_id or mro_ticket"}}}}, "401": {"description": "Unauthorized - Missing or invalid token", "content": {"application/json": {"example": {"status": "error", "statusCode": 401, "message": "Unauthorized"}}}}, "404": {"description": "Not Found - Supervisor or MRO ticket doesn't exist", "content": {"application/json": {"example": {"status": "error", "statusCode": 404, "message": "Supervisor or MRO ticket not found"}}}}}}}, "/fo-admin/mro-listing": {"post": {"tags": ["FO Admin"], "summary": "Get MRO Listing (Admin)", "description": "Retrieves a paginated list of Meter Reading Orders (MROs) filtered by status. Accessible to FO Admins.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "assigned", "description": "Status of the MROs to filter by (e.g., assigned, unassigned, completed)"}, "page": {"type": "integer", "example": 1, "description": "Page number for pagination"}, "limit": {"type": "integer", "example": 10, "description": "Number of MROs to return per page"}, "area_ids": {"type": "string", "example": "1", "description": "Filter listing as per the area id"}}, "required": ["status", "page", "limit"]}}}}, "responses": {"200": {"description": "MRO listing retrieved successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "MRO listing fetched successfully", "data": {"rows": [{"mro_ticket": 153, "status": "assigned", "assigned_to": {"fo_supervisor_id": 125, "name": "Test User"}, "cycle_month": "April", "created_at": "2025-04-21T10:00:00Z"}], "count": 1}}}}}, "400": {"description": "Bad Request - Missing or invalid parameters", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Invalid pagination or status"}}}}}}}, "/fo-admin/area-metrics": {"post": {"tags": ["FO Admin"], "summary": "Get Area-Level KPI Metrics", "description": "Retrieves KPI metrics grouped by geographical areas (GA) within the provided date range. Intended for FO Admin users.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"start_date": {"type": "string", "format": "date", "example": "2025-04-01", "description": "Start date for filtering metrics (inclusive)"}, "end_date": {"type": "string", "format": "date", "example": "2025-05-01", "description": "End date for filtering metrics (exclusive)"}}, "required": ["start_date", "end_date"]}}}}, "responses": {"200": {"description": "Area-level KPI metrics retrieved successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "Area metrics fetched successfully", "data": [{"geographical_area_id": 3, "geographical_area_name": "South Zone", "total_mros": 120, "assigned_mros": 110, "completed_mros": 105, "pending_mros": 5, "average_completion_time": "1.5 days"}]}}}}, "400": {"description": "Bad Request - Invalid date range or missing parameters", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Invalid or missing date range"}}}}, "401": {"description": "Unauthorized - Missing or invalid token", "content": {"application/json": {"example": {"status": "error", "statusCode": 401, "message": "Unauthorized access"}}}}}}}, "/fo-admin/mro-metrics": {"post": {"tags": ["FO Admin"], "summary": "Get MRO Metrics for Date Range", "description": "Retrieves MRO (Meter Reading Order) metrics for the given date range. Intended for FO Admin users.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"start_date": {"type": "string", "format": "date", "example": "2023-04-01", "description": "Start date for the MRO metrics"}, "end_date": {"type": "string", "format": "date", "example": "2023-05-01", "description": "End date for the MRO metrics"}}, "required": ["start_date", "end_date"]}}}}, "responses": {"200": {"description": "MRO metrics retrieved successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "MRO metrics fetched successfully", "data": {"total_mros": 150, "completed_mros": 140, "pending_mros": 10, "average_completion_time": "2 days"}}}}}, "400": {"description": "Bad Request - Invalid date range or missing parameters", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Invalid or missing date range"}}}}, "401": {"description": "Unauthorized - Missing or invalid token", "content": {"application/json": {"example": {"status": "error", "statusCode": 401, "message": "Unauthorized access"}}}}}}}, "/fo-admin/supervisor-list": {"post": {"tags": ["FO Admin"], "summary": "Get Supervisor List", "description": "Retrieves the list of supervisors based on the approval status. Intended for FO Admin users.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"approve_status": {"type": "string", "enum": ["active", "inactive", "pending"], "example": "active", "description": "Approval status of the supervisor"}, "area_ids": {"type": "string", "example": "1", "description": "Filter listing as per the area id"}}, "required": ["approve_status"]}}}}, "responses": {"200": {"description": "Supervisor list retrieved successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "Supervisor list fetched successfully", "data": [{"supervisor_id": 1, "supervisor_name": "<PERSON>", "status": "active"}, {"supervisor_id": 2, "supervisor_name": "<PERSON>", "status": "active"}]}}}}, "400": {"description": "Bad Request - Invalid approval status", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Invalid approval status"}}}}, "401": {"description": "Unauthorized - Missing or invalid token", "content": {"application/json": {"example": {"status": "error", "statusCode": 401, "message": "Unauthorized access"}}}}}}}, "/fo-admin/mro-details": {"post": {"tags": ["FO Admin"], "summary": "Get MRO Details", "description": "Retrieves the details of a specific MRO (Meter Reading Order) by its ticket ID. Intended for FO Admin users.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mro_ticket": {"type": "string", "example": "MRO 001A", "description": "The unique ticket ID for the MRO"}}, "required": ["mro_ticket"]}}}}, "responses": {"200": {"description": "MRO details retrieved successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "MRO details fetched successfully", "data": {"mro_ticket": "MRO 001A", "assigned_date": "28 Apr 2023", "scheduled_mr_date": "5 Feb 2025", "total_customers": 148, "completed_customers": 140, "pending_customers": 8, "remarks": "Some remarks regarding this MRO"}}}}}, "400": {"description": "Bad Request - Missing or invalid MRO ticket", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Invalid or missing MRO ticket"}}}}, "401": {"description": "Unauthorized - Missing or invalid token", "content": {"application/json": {"example": {"status": "error", "statusCode": 401, "message": "Unauthorized access"}}}}}}}, "/fo-admin/mro-customer-details": {"post": {"tags": ["FO Admin"], "summary": "Get MRO Customer Details", "description": "Retrieves detailed information of a specific customer based on their connection ID. Intended for FO Admin users.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 48, "description": "The unique connection ID for the customer"}}, "required": ["customer_connection_id"]}}}}, "responses": {"200": {"description": "MRO customer details retrieved successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "message": "MRO customer details fetched successfully", "data": {"customer_connection_id": 48, "customer_name": "<PERSON>", "address": "123 Main Street, Springfield", "assigned_mro": "MRO 001A", "total_readings": 5, "completed_readings": 4, "pending_readings": 1, "remarks": "Customer details retrieved successfully"}}}}}, "400": {"description": "Bad Request - Missing or invalid customer connection ID", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Invalid or missing customer connection ID"}}}}, "401": {"description": "Unauthorized - Missing or invalid token", "content": {"application/json": {"example": {"status": "error", "statusCode": 401, "message": "Unauthorized access"}}}}}}}, "/fo-admin/supervisor-onboarding": {"post": {"tags": ["FO Admin"], "summary": "Update the onboarding status of a supervisor", "description": "Allows the FO Admin to update the onboarding status of a supervisor, either approving or rejecting the request.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"approve_status": {"type": "string", "enum": ["approved", "rejected"], "description": "The status of the supervisor onboarding request", "example": "rejected"}}}}}}, "responses": {"200": {"description": "Supervisor onboarding status updated successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/bpcl-admin/contractor-approval-list": {"post": {"tags": ["BPCL Admin"], "summary": "Contractor Approval List", "description": "Fetch a list of contractors based on approval status (pending, approved, rejected).", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["ga_id", "type"], "properties": {"ga_id": {"type": "integer", "example": 1}, "type": {"type": "string", "enum": ["pending", "approved", "rejected"], "example": "pending"}}}}}}, "responses": {"200": {"description": "Contractor approval list fetched successfully", "content": {"application/json": {"example": {"contractor_type": "tpi", "created_at": "2025-04-21T18:42:32.047Z", "contractor": {"admin_id": 313, "first_name": "accenture", "last_name": "<PERSON>uma<PERSON>", "mobile": "7794611939", "user_role": "sales", "updated_by": 0, "updated_at": "2025-04-21T18:42:31.630Z", "trx_admin_contractor_other_details": [{"document_type": "contract", "file_name": "1745241152352-Screenshot 2025-02-13 143517.png", "file_path": "admin/adm_313/ContractorOnboarding/documents", "file_ext": "png", "agency": {"agency_name": "agency"}}]}, "onboardedBy": {"first_name": "v<PERSON><PERSON><PERSON><PERSON>", "last_name": "singh"}}}}}, "400": {"description": "Bad Request - Invalid GA ID or status type", "content": {"application/json": {"example": {"status": false, "statusCode": 400, "message": "Invalid GA ID or status type"}}}}}}}, "/ga-customer/contractor-area-list": {"post": {"tags": ["BPCL Admin"], "summary": "Dashboard Contractor Area List", "requestBody": {"required": true, "content": {"application/json": {"example": {"geographical_area": 1, "area_id": [1, 2, 3], "page": 1, "limit": 10, "search": ""}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/ga-customer/lmc-area-list": {"post": {"tags": ["BPCL Admin"], "summary": "LMC Area List", "requestBody": {"required": true, "content": {"application/json": {"example": {"geographical_area": 1, "area_id": [1, 2, 3], "page": 1, "limit": 10, "search": ""}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/ga-customer/area-wise-list": {"post": {"tags": ["BPCL Admin"], "summary": "Area wise customer List", "requestBody": {"required": true, "content": {"application/json": {"example": {"tab_type": "active", "geographical_area": 1, "area_id": [1, 3]}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/ga-customer/area-count": {"post": {"tags": ["BPCL Admin"], "summary": "Area wise customer count for registartion the result will differnet and for active the result will different", "requestBody": {"required": true, "content": {"application/json": {"example": {"tab_type": "active"}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/ga-customer/dma-area-list": {"post": {"tags": ["BPCL Admin"], "summary": "DMA Area List", "requestBody": {"required": true, "content": {"application/json": {"example": {"geographical_area": 1, "area_id": [1, 2, 3], "page": 1, "limit": 10, "search": ""}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/ga-customer/fo-area-list": {"post": {"tags": ["BPCL Admin"], "summary": "FO Area List", "requestBody": {"required": true, "content": {"application/json": {"example": {"geographical_area": 1, "area_id": [1, 2, 3], "page": 1, "limit": 10, "search": ""}}}}, "responses": {"200": {"description": "Data fetched Successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/bpcl-admin/contractor-management-area-update": {"post": {"tags": ["FO Admin"], "summary": "Update the onboarding status of a supervisor", "description": "Contractor Management Area Update.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"admin_id": {"type": "integer", "example": 1}, "area_id": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "ga_id": {"type": "integer", "example": 1}, "contractor_type": {"type": "string", "example": "TPI Sales"}}, "required": ["admin_id", "area_id", "ga_id", "contractor_type"]}}}}, "responses": {"200": {"description": "Contractor management area updated successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {}, "message": "Data fetched successfully"}}}}}}}, "/lmc/get-installation-form-data": {"post": {"tags": ["LMC"], "summary": "Get installation form data", "description": "Fetches installation form data for a given connection stage and customer connection ID.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mapped_lmc_connection_stage_id": {"type": "integer"}, "mapped_lmc_customer_connection_id": {"type": "integer"}}}, "example": {"mapped_lmc_connection_stage_id": 1, "mapped_lmc_customer_connection_id": 221}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/lmc/upload-reading": {"post": {"tags": ["LMC"], "summary": "Upload meter reading file", "description": "Uploads a meter reading file along with associated connection and cycle details.", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "string", "format": "text"}, "file": {"type": "string", "format": "binary"}, "declared_by": {"type": "string", "format": "text"}, "cycle_id": {"type": "string", "format": "text"}}}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {}}}}}}, "/lmc/feasibility-submit-details": {"post": {"tags": ["LMC"], "summary": "Submit feasibility details for LMC connection", "description": "Submits feasibility details including pipeline lengths, meter type, and customer consent for the LMC connection.", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"mapped_lmc_connection_stage_id": {"type": "string", "format": "text", "example": "5"}, "house_type": {"type": "string", "format": "text", "example": "bungalow"}, "mdpe_pipeline_length": {"type": "number", "format": "float", "example": 12.5}, "riser_length": {"type": "number", "format": "float", "example": 3.2}, "gi_pipeline_length": {"type": "number", "format": "float", "example": 8.4}, "meter_type": {"type": "string", "format": "text", "example": "mechanical"}, "customer-consent": {"type": "string", "format": "binary"}, "isometric-drawing": {"type": "string", "format": "binary"}, "actual_visit_date": {"type": "string", "format": "date", "example": "2025-04-01"}, "customer_connection_id": {"type": "string", "format": "text", "example": "223"}}}}}}, "responses": {"200": {"description": "Feasibility details submitted successfully", "content": {"application/json": {}}}}}}, "/lmc/commissioning-submit-details": {"post": {"tags": ["LMC"], "summary": "Submit commissioning details for LMC connection", "description": "Submits commissioning details including meter type, customer consent, isometric drawing, and other related fields for the LMC connection.", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"mapped_lmc_connection_stage_id": {"type": "string", "format": "text", "example": "64"}, "meter_type": {"type": "string", "format": "text", "example": "mechanical"}, "natural-gas-consent": {"type": "string", "format": "binary"}, "isometric-drawing": {"type": "string", "format": "binary"}, "actual_visit_date": {"type": "string", "format": "date", "example": "2025-04-01"}, "ready_for_conversion": {"type": "string", "format": "text", "example": "1"}, "meter_reading": {"type": "string", "format": "text", "example": "00.2"}, "jmr-customer-approval": {"type": "string", "format": "binary"}, "customer_connection_id": {"type": "string", "format": "text", "example": "225"}, "jmr": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "Commissioning details submitted successfully", "content": {"application/json": {}}}}}}, "/lmc/get-feasibility-details": {"post": {"tags": ["LMC"], "summary": "Get feasibility details for LMC connection", "description": "Retrieves the feasibility details for a given LMC connection stage.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mapped_lmc_connection_stage_id": {"type": "integer", "example": 64}}, "required": ["mapped_lmc_connection_stage_id"]}}}}, "responses": {"200": {"description": "Feasibility details retrieved successfully", "content": {"application/json": {}}}}}}, "/lmc/get-commissioning-details": {"post": {"tags": ["LMC"], "summary": "Get commissioning details for LMC connection", "description": "Retrieves the commissioning details for a given LMC connection stage.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mapped_lmc_connection_stage_id": {"type": "integer", "example": 64}}, "required": ["mapped_lmc_connection_stage_id"]}}}}, "responses": {"200": {"description": "Commissioning details retrieved successfully", "content": {"application/json": {}}}}}}, "/lmc/form-details": {"post": {"tags": ["LMC"], "summary": "Submit form details for LMC connection", "description": "Submits the form details associated with a specific LMC connection using the customer connection ID.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 219}}, "required": ["customer_connection_id"]}}}}, "responses": {"200": {"description": "Form details submitted successfully", "content": {"application/json": {}}}}}}, "/lmc/get-pipeline-checklist": {"post": {"tags": ["LMC"], "summary": "Get pipeline checklist for LMC connection", "description": "Retrieves the pipeline checklist details based on the connection stage and customer connection ID.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mapped_lmc_connection_stage_id": {"type": "integer", "example": 1}, "mapped_lmc_customer_connection_id": {"type": "integer", "example": 232}}, "required": ["mapped_lmc_connection_stage_id", "mapped_lmc_customer_connection_id"]}}}}, "responses": {"200": {"description": "Pipeline checklist retrieved successfully", "content": {"application/json": {}}}}}}, "/lmc/installation-submit-details": {"post": {"tags": ["LMC"], "summary": "Submit installation details for LMC connection", "description": "Submits the installation details such as visit date, installation, testing, and other related data for LMC connection.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"step": {"type": "string", "example": "technicalDetails"}, "mapped_lmc_connection_stage_id": {"type": "integer", "example": 1}, "mapped_lmc_customer_connection_id": {"type": "integer", "example": 232}, "detailsData": {"type": "object", "properties": {"actual_visit_date": {"type": "string", "format": "date", "example": "2025-04-27"}, "regulator_number": {"type": "string", "example": "REG-123456"}, "installation_date": {"type": "string", "format": "date", "example": "2025-04-28"}, "testing_date": {"type": "string", "format": "date", "example": "2025-04-29"}, "rfc_date": {"type": "string", "format": "date", "example": "2025-04-30"}, "house_type_floor": {"type": "string", "example": "Ground Floor"}, "area": {"type": "string", "example": "<PERSON> Residency"}, "city": {"type": "string", "example": "New Delhi"}, "house_address": {"type": "string", "example": "B-12, <PERSON> Residency, Sector 45"}, "house_type": {"type": "string", "example": "Independent"}, "mdpe_pipeline_length": {"type": "number", "format": "float", "example": 15.5}, "riser_length": {"type": "number", "format": "float", "example": 5.2}, "gi_pipeline_length": {"type": "number", "format": "float", "example": 10.0}, "meter_no": {"type": "string", "example": "9908098989089"}}}}}}}}, "responses": {"200": {"description": "Installation details submitted successfully", "content": {"application/json": {}}}}}}, "/bpcl-admin/sub-admin-type-list": {"get": {"tags": ["BPCL Admin"], "summary": "Retrieve a list of sub-admin types", "description": "Fetches a list of sub-admin types available in the BPCL Admin system.", "responses": {"200": {"description": "Successfully retrieved sub-admin types", "content": {"application/json": {}}}}}}, "/bpcl-admin/sub-admin-onboarding": {"post": {"tags": ["BPCL Admin"], "summary": "Submit sub-admin onboarding details", "description": "Submits onboarding details for a sub-admin in the BPCL system.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"ga_id": {"type": "integer", "example": 1}, "sub_admin_type": {"type": "integer", "example": 1}, "title": {"type": "string", "enum": ["mr", "mrs", "ms", "other"], "example": "mr"}, "emp_name": {"type": "string", "example": "sub admin2"}, "bpcl_id": {"type": "string", "example": "456454BP"}, "position_id": {"type": "string", "example": "4545SA"}, "mobile": {"type": "string", "example": "785360421"}, "alt_mobile": {"type": "string", "example": ""}, "email": {"type": "string", "example": "<EMAIL>"}, "period_from": {"type": "string", "format": "date-time", "example": "2025-04-01T18:30:00.000Z"}, "period_to": {"type": "string", "format": "date-time", "example": "2025-04-24T18:30:00.000Z"}}, "required": ["ga_id", "sub_admin_type", "title", "emp_name", "bpcl_id", "position_id", "mobile", "email", "period_from", "period_to"]}}}}, "responses": {"200": {"description": "Sub-admin onboarding details submitted successfully", "content": {"application/json": {}}}}}}, "/bpcl-admin/contractor-accept-reject": {"post": {"tags": ["BPCL Admin"], "summary": "Accept or reject contractor", "description": "Accepts or rejects a contractor's request with a remark in the BPCL system.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"ga_id": {"type": "integer", "example": 1}, "contractor_id": {"type": "integer", "example": 340}, "type": {"type": "string", "enum": ["accept", "reject"], "example": "reject"}, "remark": {"type": "string", "example": "this is remark."}}, "required": ["ga_id", "contractor_id", "type", "remark"]}}}}, "responses": {"200": {"description": "Contractor accept/reject action completed successfully", "content": {"application/json": {}}}}}}, "/otp/send-otp": {"post": {"tags": ["OTP"], "summary": "Send OTP to mobile number", "description": "Sends a One-Time Password (OTP) to the provided mobile number.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"number": {"type": "string", "example": "8196037673"}}, "required": ["number"]}}}}, "responses": {"200": {"description": "OTP sent successfully", "content": {"application/json": {}}}}}}, "/otp/email-sms-otp-verify": {"post": {"tags": ["OTP"], "summary": "Verify OTP sent via email or SMS", "description": "Verifies the One-Time Password (OTP) received by the user through SMS or email.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"number": {"type": "string", "example": "9654321098"}, "OTP": {"type": "integer", "example": 636888}}, "required": ["number", "OTP"]}}}}, "responses": {"200": {"description": "OTP verified successfully", "content": {"application/json": {}}}}}}, "/customer/payment-approval": {"post": {"tags": ["Customer"], "summary": "Approve customer payment", "description": "Approves a specific type of customer payment such as security deposit, extra pipeline, or name transfer.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"payment_type": {"type": "string", "enum": ["security-deposit", "extra-pipeline", "name-transfer"], "example": "security-deposit"}, "customer_connection_id": {"type": "integer", "example": 55}, "approval_status": {"type": "integer", "example": 1}}, "required": ["payment_type", "customer_connection_id", "approval_status"]}}}}, "responses": {"200": {"description": "Payment approval processed successfully", "content": {"application/json": {}}}}}}, "/service-request/transfer-approval-status-update": {"post": {"tags": ["Service Request"], "summary": "Update name transfer approval status", "description": "Approves or rejects a name transfer request based on the provided ID and status.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name_transfer_request_id": {"type": "integer", "example": 2}, "approval_status": {"type": "integer", "enum": [0, 1], "example": 1, "description": "1 = approved, 0 = rejected"}}, "required": ["name_transfer_request_id", "approval_status"]}}}}, "responses": {"200": {"description": "Name transfer approval status updated successfully", "content": {"application/json": {}}}}}}, "/service-request/document-delete": {"post": {"tags": ["Service Request"], "summary": "Delete uploaded document", "description": "Deletes a specific document uploaded for a service request based on customer and proof details.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 48}, "house_flat_no": {"type": "string", "example": "666"}, "available_pngareas_id": {"type": "integer", "example": 6}, "proof_type": {"type": "string", "example": "photo_proof"}, "proof_master_id": {"type": "integer", "example": 1}}, "required": ["customer_connection_id", "house_flat_no", "available_pngareas_id", "proof_type", "proof_master_id"]}}}}, "responses": {"200": {"description": "Document deleted successfully", "content": {"application/json": {}}}}}}, "/service-request/get-new-address-detail": {"post": {"tags": ["Service Request"], "summary": "Get new address detail", "description": "Fetches the updated address details based on customer connection and flat number.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 48}, "available_pngareas_id": {"type": "integer", "example": 6}, "house_flat_no": {"type": "string", "example": "66"}}, "required": ["customer_connection_id", "available_pngareas_id", "house_flat_no"]}}}}, "responses": {"200": {"description": "Address details fetched successfully", "content": {"application/json": {}}}}}}, "/bpcl-admin/get-geographical-areas": {"get": {"tags": ["FO Admin"], "summary": "Fetch geographical areas", "description": "Retrieves the list of geographical areas managed by BPCL Admin.", "responses": {"200": {"description": "Geographical areas fetched successfully", "content": {"application/json": {}}}}}}, "/bpcl-admin/channel-partners/count": {"get": {"tags": ["FO Admin"], "summary": "Fetch channel partner count", "description": "Retrieves the total count of channel partners managed by BPCL Admin.", "responses": {"200": {"description": "Channel partner count fetched successfully", "content": {"application/json": {}}}}}}, "/bpcl-admin/geographical-area-mapping": {"post": {"tags": ["FO Admin"], "summary": "Map geographical area", "description": "Maps a given geographical area by ID in the BPCL Admin system.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"geographical_area_id": {"type": "integer", "example": 1}}, "required": ["geographical_area_id"]}}}}, "responses": {"200": {"description": "Geographical area mapped successfully", "content": {"application/json": {}}}}}}, "/bpcl-admin/contractor-management-tpi": {"post": {"tags": ["FO Admin"], "summary": "Fetch contractor management data for TPI", "description": "Retrieves contractor management records for TPI users based on geographical area and role.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"geographical_area_id": {"type": "integer", "example": 1}, "user_role": {"type": "string", "example": "engineer"}, "page": {"type": "integer", "example": 1}, "limit": {"type": "integer", "example": 10}}, "required": ["geographical_area_id", "user_role", "page", "limit"]}}}}, "responses": {"200": {"description": "Contractor management data fetched successfully", "content": {"application/json": {}}}}}}, "/bpcl-admin/contractor-details-by-persona-role": {"post": {"tags": ["FO Admin"], "summary": "Fetch contractor details by persona role", "description": "Retrieves contractor details filtered by the specified persona role (must be one of [dma, fo, lmc]).", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"persona_role": {"type": "string", "example": "fo"}}, "required": ["persona_role"]}}}}, "responses": {"200": {"description": "Contractor details fetched successfully", "content": {"application/json": {}}}}}}, "/customer/get-details-by-mobile": {"post": {"tags": ["Customer"], "summary": "Fetch customer details by mobile number", "description": "Retrieves customer details using the provided mobile number.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mobile": {"type": "string", "example": "9597481229"}}, "required": ["mobile"]}}}}, "responses": {"200": {"description": "Customer details fetched successfully", "content": {"application/json": {}}}}}}, "/customer/customer-approval-decline": {"post": {"tags": ["Customer"], "summary": "Approve or decline customer", "description": "Handles customer approval or decline based on the provided reason and connection ID.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"master_value": {"type": "string", "example": "contact-engineer"}, "master_type": {"type": "string", "example": "decline_reason"}, "customer_connection_id": {"type": "string", "example": "245"}}, "required": ["master_value", "master_type", "customer_connection_id"]}}}}, "responses": {"200": {"description": "Customer approval or decline processed successfully", "content": {"application/json": {}}}}}}, "/dash-board/view-notification": {"post": {"tags": ["Dashboard"], "summary": "View notification", "description": "Fetches the details of a notification based on the provided alert request ID.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"alert_request_id": {"type": "integer", "example": 2}}, "required": ["alert_request_id"]}}}}, "responses": {"200": {"description": "Notification details fetched successfully", "content": {"application/json": {}}}}}}, "/customer/connection-payment-list": {"post": {"tags": ["Customer"], "summary": "Fetch connection payment list", "description": "Retrieves the connection payment list for a given customer connection ID.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 18}, "imc_pipeline_id": {"type": "integer", "example": 3}, "approval_status": {"type": "integer", "example": 0}}, "required": ["customer_connection_id"]}}}}, "responses": {"200": {"description": "Connection payment list fetched successfully", "content": {"application/json": {}}}}}}, "/bpcl-admin/admin-profile": {"post": {"tags": ["BPCL Admin"], "summary": "Fetch admin profile", "description": "Retrieves the admin profile information.", "requestBody": {"content": {"application/json": {}}}, "responses": {"200": {"description": "Admin profile data fetched successfully", "content": {"application/json": {}}}}}}, "/lmc/get-customer-address": {"post": {"tags": ["LMC"], "summary": "Fetch customer address", "description": "Retrieves the address details for a given customer connection ID.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 221}}, "required": ["customer_connection_id"]}}}}, "responses": {"200": {"description": "Customer address fetched successfully", "content": {"application/json": {}}}}}}, "/lmc/installation-submit": {"post": {"tags": ["LMC"], "summary": "Submit installation data", "description": "Submits installation information for a given LMC customer connection and stage.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"mapped_lmc_connection_stage_id": {"type": "integer", "example": 1}, "mapped_lmc_customer_connection_id": {"type": "integer", "example": 232}}, "required": ["mapped_lmc_connection_stage_id", "mapped_lmc_customer_connection_id"]}}}}, "responses": {"200": {"description": "Installation submitted successfully", "content": {"application/json": {}}}}}}, "/auth/microsoft": {"post": {"tags": ["BPCL Admin"], "summary": "Microsoft SSO Login", "description": "Authenticates a user using a Microsoft Single Sign-On (SSO) token.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string", "description": "Microsoft OAuth2 access token", "example": "eyJ0eXAiOiJKV1QiLCJub25jZSI6ImR1S1RrR29Eaz..."}}, "required": ["token"]}}}}, "responses": {"200": {"description": "User logged in successfully with SSO", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": {"user": {"admin_id": 437, "persona_role_id": 5, "bpcl_admin_id": null, "user_role": "bpcl-sub-admin", "prefix": "mr", "first_name": "<PERSON>", "last_name": "", "mobile": "8536941236", "alternate_mobile": "", "email": "<EMAIL>", "bpcl_id": "456454BP", "position_id": "4545SA", "from_date": "2025-07-02", "to_date": "2025-05-25", "city": null, "state": null, "last_logged_in": "2025-05-12T12:59:49.917Z", "is_active": true, "is_locked": true, "created_by": 168, "updated_by": 0, "createdAt": "2025-05-12T11:32:34.093Z", "updatedAt": "2025-05-12T12:59:49.917Z", "persona_role": {"role_name": "BPCL-GA"}, "loginType": "sso"}, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "message": "User loggedIn successfully with SSO."}}}}, "400": {"description": "Bad Request - Invalid or missing token", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Invalid Microsoft SSO token"}}}}, "401": {"description": "Unauthorized - Token verification failed", "content": {"application/json": {"example": {"status": "error", "statusCode": 401, "message": "Unauthorized access"}}}}}}}, "/bpcl-admin/customer-approval-list": {"post": {"tags": ["BPCL Admin"], "summary": "Get Customer Approval List", "description": "Fetches a list of customer connections based on geographical area and approval status (pending, resubmission, or closed).", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"geographical_area_id": {"type": "integer", "description": "ID of the geographical area to filter customers", "example": 1}, "status": {"type": "string", "description": "Status of the customer approval", "enum": ["pending", "resubmission", "closed"], "example": "pending"}}, "required": ["geographical_area_id", "status"]}}}}, "responses": {"200": {"description": "Customer approval list fetched successfully", "content": {"application/json": {"example": {"status": "success", "statusCode": 200, "data": [{"customer_connection_id": 296, "bp_id": "100000000162", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON>", "internal_status": "application-pending", "created_on": "2025-04-11T12:08:55.210Z", "updated_on": "2025-04-11T12:32:37.307Z", "available_pngarea": {"pincode": 560001}, "trx_help_with_registrations": [{"customer_type": "self-registered"}], "trx_customer_connection_tpi_mappings": [{"submitted_on": "2025-05-14T12:34:16.183Z", "tpi": {"admin_id": 1, "tpi_first_name": "TPI Sales 1", "tpi_last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tpi_mobile": "8908908903"}}], "reviewer": {"reviewer_id": 1, "reviewer_first_name": "TPI Sales 1", "reviewer_last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}], "message": "Customer approval list retrieved successfully."}}}}, "400": {"description": "Bad Request - Invalid or missing parameters", "content": {"application/json": {"example": {"status": "error", "statusCode": 400, "message": "Invalid request payload"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"example": {"status": "error", "statusCode": 500, "message": "An unexpected error occurred while fetching the customer approval list"}}}}}}}, "/bpcl-admin/customer-personal-details": {"post": {"tags": ["BPCL Admin"], "summary": "Fetch customer details", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 65}}}}}}, "responses": {"200": {"description": "Successfully fetched customer application details", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 65}, "connection_type": {"type": "string", "example": "original"}, "prefix": {"type": "string", "example": "mr"}, "bpcl_id": {"type": "string", "example": "9890909090"}, "available_pngareas_id": {"type": "integer", "example": 1}, "customer_id": {"type": "integer", "example": 2}, "first_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "example": ""}, "mobile": {"type": "string", "example": "8899007711"}, "status": {"type": "string", "example": "installation-rescheduled"}, "internal_status": {"type": "string", "example": "application-completed"}, "trx_customer_connection_addresses": {"type": "array", "items": {"type": "object", "properties": {"connection_pincode": {"type": "integer", "example": 400001}, "connection_state": {"type": "string", "example": "Maharashtra"}, "connection_city": {"type": "string", "example": "Mumbai"}, "connection_district": {"type": "string", "example": "Mumbai"}, "connection_house_no": {"type": "string", "example": "789"}}}}, "trx_customer_proof_documents": {"type": "array", "items": {"type": "object", "properties": {"proof_type": {"type": "string", "example": "proof_of_ownership"}, "file_name": {"type": "string", "example": "Screenshot 2025-03-12 151620.png"}, "file_path": {"type": "string", "example": "/home/<USER>/wwwroot/public/uploads/customers/456/Screenshot 2025-03-12 151620.png"}, "verification_status": {"type": "string", "example": "pending"}}}}}}, "message": {"type": "string", "example": "Customer application details fetched successfully."}}}}}}}}}, "/bpcl-admin/comment-details": {"post": {"tags": ["BPCL Admin"], "summary": "Fetch comment details", "description": "Retrieves comment details for a specific customer connection and mapping type.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 414}, "mapping_type": {"type": "string", "example": "customer-application"}}, "required": ["customer_connection_id", "mapping_type"]}}}}, "responses": {"200": {"description": "Comment details retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Comment details retrieved successfully"}}}}}}}}}, "/bpcl-admin/eo-jmr-update": {"post": {"tags": ["BPCL Admin"], "summary": "Update EO JMR Status", "description": "Updates the EO JMR status for a given TMMA ID with a specific action and reason.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"tmma_id": {"type": "integer", "example": 21}, "action": {"type": "string", "example": "resubmission"}, "reason": {"type": "string", "example": "rework"}}, "required": ["tmma_id", "action", "reason"]}}}}, "responses": {"200": {"description": "EO JMR status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"data": {"type": "object", "properties": {"message": {"type": "string", "example": "Status updated to 'resubmission' successfully."}, "updatedRecord": {"type": "object", "properties": {"tmma_id": {"type": "integer", "example": 21}, "customer_meter_reading_id": {"type": "integer", "example": 205}, "meter_reading_file": {"type": "string", "example": "customer/cus_227/lmc_supervisor/commissioning/isometricDrawing.png"}, "meter_reading": {"type": "number", "example": 67998.908}, "tpi_id": {"type": "integer", "example": 53}, "tpi_status": {"type": "string", "example": "resubmit-jmr-eo"}, "reason": {"type": "string", "example": "rework"}, "is_latest": {"type": "boolean", "example": true}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 53}, "updated_by": {"type": "integer", "example": 171}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-04-24T11:55:43.660Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-06-03T11:30:23.353Z"}}}}}}}, "message": {"type": "string", "example": ""}}}}}}}}}, "/bpcl-admin/so-status-update": {"post": {"tags": ["BPCL Admin"], "summary": "Update SO Status", "description": "Updates the SO status for a given customer connection ID with a specified action and reason.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 411}, "action": {"type": "string", "example": "resubmission"}, "reason": {"type": "string", "example": "rework"}}, "required": ["customer_connection_id", "action", "reason"]}}}}, "responses": {"200": {"description": "SO status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"data": {"type": "object", "properties": {"message": {"type": "string", "example": "Status updated to 'resubmission' successfully."}, "updatedRecord": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 411}, "prefix": {"type": "string", "example": "mr"}, "bpcl_id": {"type": "string", "example": "9926597431"}, "available_pngareas_id": {"type": "integer", "example": 10}, "customer_id": {"type": "integer", "example": 141}, "first_name": {"type": "string", "example": "Sienna"}, "middle_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "9926597431"}, "alternate_mobile": {"type": "string", "example": "9337632811"}, "spouse_name": {"type": "string", "example": ""}, "parents_name": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "dob": {"type": "string", "example": "2025-05-01"}, "no_of_family_members": {"type": "integer", "example": 5}, "ca_no": {"type": ["string", "null"], "example": null}, "bp_id": {"type": "string", "example": "************"}, "connection_address_type": {"type": "string", "example": "owned"}, "is_extra_connection": {"type": "boolean", "example": false}, "extra_points": {"type": "integer", "example": 0}, "govt_scheme_id": {"type": "integer", "example": 2}, "is_security_deposit": {"type": "boolean", "example": false}, "security_deposit_category": {"type": ["string", "null"], "example": null}, "account_type": {"type": "string", "example": "postpaid"}, "status": {"type": "string", "example": "application-inreview"}, "internal_status": {"type": "string", "example": "application-resubmit-so"}, "approved_date": {"type": ["string", "null"], "example": null}, "onboarding_date": {"type": ["string", "null"], "example": null}, "installation_date": {"type": ["string", "null"], "example": null}, "disconnected_date": {"type": ["string", "null"], "example": null}, "rental_owner_name": {"type": ["string", "null"], "example": null}, "rental_owner_mobile": {"type": ["string", "null"], "example": null}, "created_by_type": {"type": "string", "example": "customer"}, "isu_synced": {"type": "boolean", "example": false}, "isu_status": {"type": ["string", "null"], "example": null}, "isu_error": {"type": ["string", "null"], "example": null}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 171}, "connection_type": {"type": "string", "example": "original"}, "reason_id": {"type": "integer", "example": 56}, "is_lpg": {"type": "boolean", "example": false}, "lpg_consumer_no": {"type": ["string", "null"], "example": null}, "lpg_distributor_name": {"type": ["string", "null"], "example": null}, "lpg_omc_name": {"type": ["string", "null"], "example": null}, "lpg_consent": {"type": "boolean", "example": false}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-05-09T19:29:31.333Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-06-03T11:29:37.897Z"}}}}}}}, "message": {"type": "string", "example": ""}}}}}}}}}, "/bpcl-admin/eo-status-update": {"post": {"tags": ["BPCL Admin"], "summary": "Update EO Status", "description": "Processes an EO action such as 'approved' or 'reject' for a given customer connection.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 411}, "action": {"type": "string", "example": "approved"}, "reason": {"type": "string", "example": "approved"}}, "required": ["customer_connection_id", "action", "reason"]}}}}, "responses": {"200": {"description": "EO status action processed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "statusCode": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"data": {"type": "object", "properties": {"message": {"type": "string", "example": "EO action 'reject' processed successfully."}, "updatedRecord": {"type": "object", "properties": {"updatedCustomer": {"type": "object", "properties": {"customer_connection_id": {"type": "integer", "example": 411}, "prefix": {"type": "string", "example": "mr"}, "bpcl_id": {"type": "string", "example": "9926597431"}, "available_pngareas_id": {"type": "integer", "example": 10}, "customer_id": {"type": "integer", "example": 141}, "first_name": {"type": "string", "example": "Sienna"}, "middle_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "9926597431"}, "alternate_mobile": {"type": "string", "example": "9337632811"}, "spouse_name": {"type": "string", "example": ""}, "parents_name": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "dob": {"type": "string", "example": "2025-05-01"}, "no_of_family_members": {"type": "integer", "example": 5}, "ca_no": {"type": ["string", "null"], "example": null}, "bp_id": {"type": "string", "example": "************"}, "connection_address_type": {"type": "string", "example": "owned"}, "is_extra_connection": {"type": "boolean", "example": false}, "extra_points": {"type": "integer", "example": 0}, "govt_scheme_id": {"type": "integer", "example": 2}, "is_security_deposit": {"type": "boolean", "example": false}, "security_deposit_category": {"type": ["string", "null"], "example": null}, "account_type": {"type": "string", "example": "postpaid"}, "status": {"type": "string", "example": "application-rejected"}, "internal_status": {"type": "string", "example": "application-rejected"}, "approved_date": {"type": ["string", "null"], "example": null}, "onboarding_date": {"type": ["string", "null"], "example": null}, "installation_date": {"type": ["string", "null"], "example": null}, "disconnected_date": {"type": ["string", "null"], "example": null}, "rental_owner_name": {"type": ["string", "null"], "example": null}, "rental_owner_mobile": {"type": ["string", "null"], "example": null}, "created_by_type": {"type": "string", "example": "customer"}, "isu_synced": {"type": "boolean", "example": false}, "isu_status": {"type": ["string", "null"], "example": null}, "isu_error": {"type": ["string", "null"], "example": null}, "is_active": {"type": "boolean", "example": true}, "created_by": {"type": "integer", "example": 0}, "updated_by": {"type": "integer", "example": 171}, "connection_type": {"type": "string", "example": "original"}, "reason_id": {"type": "integer", "example": 80}, "is_lpg": {"type": "boolean", "example": false}, "lpg_consumer_no": {"type": ["string", "null"], "example": null}, "lpg_distributor_name": {"type": ["string", "null"], "example": null}, "lpg_omc_name": {"type": ["string", "null"], "example": null}, "lpg_consent": {"type": "boolean", "example": false}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-05-09T19:29:31.333Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-06-03T11:28:51.467Z"}}}, "message": {"type": "string", "example": "Installation reject processed successfully."}}}}}}}, "message": {"type": "string", "example": ""}}}}}}}}}}}