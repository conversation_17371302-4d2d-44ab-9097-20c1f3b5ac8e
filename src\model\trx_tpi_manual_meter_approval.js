const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_tpi_manual_meter_approval', {
    tmma_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    customer_meter_reading_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer_meter_readings',
        key: 'customer_meter_reading_id'
      }
    },
    meter_reading_file: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    meter_reading: {
      type: DataTypes.DECIMAL(15,3),
      allowNull: false
    },
    tpi_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_admin',
        key: 'admin_id'
      }
    },
    tpi_status: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    is_latest: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_tpi_manual_meter_approval',
    schema: 'dbo',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        name: "PK_tmma_id",
        unique: true,
        fields: [
          { name: "tmma_id" },
        ]
      },
    ]
  });
};
