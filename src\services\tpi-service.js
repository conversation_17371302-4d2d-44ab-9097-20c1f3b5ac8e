const tpiRepository = require("../repository/tpi-repository");
const message = require("../utils/constants");
const { emailOTPNotification,sendEmailNotificationWithoutCc } = require('../helpers/smsEmailHelper');
const { decryptPassword } = require('../utils/encrypt-decrypt');
const { generateCode, formatDate } = require('../utils/commonFn');
const lmcRepository = require("../repository/lmc-repository");

const getTPICasesSummery = async (params) => {
  try {
    const result = tpiRepository.getTPICasesSummery(params.tpi_id);
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};

const tpiApplicationStatus = async (params) => {
  try {
    const result = await tpiRepository.tpiApplicationStatus(params);
    return result;
  } catch (error) {
    console.error("Error in getLmcCasesWithFilters:", error);
    throw error;
  }
};

const getStatusCount = async (payload) => {
  try {
    const tpi_id = payload.user.admin_id;
    const lmc_stage = payload.body.lmc_stage;

    // Normalize and validate area_id input
    let areaIds = [];

    if (Array.isArray(payload.body.area_id)) {
      areaIds = payload.body.area_id
        .map((id) => parseInt(id))
        .filter((id) => !isNaN(id));
    } else if (payload.body.area_id) {
      const id = parseInt(payload.body.area_id);
      if (!isNaN(id)) areaIds = [id];
    }

    if (!areaIds.length) {
      throw { status: 400, message: "area_id must be a non-empty array or valid number." };
    }

    const result = await tpiRepository.getStatusCountRepo(tpi_id, lmc_stage, areaIds);
    return result;

  } catch (error) {
    console.error("Error in getStatusCount:", error.message || error);
    throw {
      status: error.status || 500,
      message: error.message || "An error occurred while fetching status count.",
    };
  }
};


const feasibilityInstallationCustomerDetailService = async (payload) => {
  try {
    //const tpi_id = payload.user.admin_id;
    const tpi_id = 2;
    const {customer_connection_id, lmc_stage} = payload;
    const result = tpiRepository.feasibilityInstallationCustomerDetailRepo(tpi_id,customer_connection_id, lmc_stage);
    return result;
  } catch (error) {
    console.error("Error in getLmcConnectionDetails:", error);
    throw error;
  }
};
const customerStatusHistory = async (params) => {
  try {
    const result = await tpiRepository.customerStatusHistory(params);
    return result;
  } catch (error) {
    console.error("Error in customerStatusHistory:", error);
    throw error;
  }
};

const tpiCustomeReview = async (params) => {
  try {
    const result = await tpiRepository.tpiCustomeReview(params);
    return result;
  } catch (error) {
    console.error("Error in tpiCustomeReview:", error);
    throw error;
  }
};


const lmcWorkApprovalStatusUpdate = async (params) => {
  try {
    const {tpi_id, customer_connection_id, tpi_status, lmc_stage, reason, lmc_connection_mapping_id } = params;

    // Validate required fields
    if (!customer_connection_id || !tpi_status || !lmc_stage || !lmc_connection_mapping_id) {
      throw new Error("Missing required parameters.");
    }

    // Define valid statuses
    const validStatuses = ['no-submission', 'pending', 'resubmit', 'completed', 'rejected', 'pending-eo', 'resubmit-eo', 'completed-eo', 'rejected-eo'];
    
    // Validate the status
    if (!validStatuses.includes(tpi_status)) {
      throw new Error("Invalid status. Allowed values: 'no-submission', 'pending', 'resubmit', 'completed', 'rejected', 'pending-eo', 'resubmit-eo', 'completed-eo', 'rejected-eo'.");
    }

    // Construct update payload
    let updateData = {
      tpi_status,
      lmc_stage,
      tpi_engineer_id: tpi_id,
      updated_by: tpi_id,
      updated_at: new Date(),
    };

    // If status is 'resubmit' or 'rejected', reason is required
    if ((tpi_status === "resubmit" || tpi_status === "rejected") && !reason) {
      throw new Error(`Reason is required for status: ${tpi_status}`);
    }

    if(tpi_status === "resubmit"){
      updateData.visit_status = 'to-be-rescheduled';
    }

    if (reason) {
      updateData.reason = reason;
    }

    const whereCondition = {
      lmc_connection_mapping_id,
      lmc_stage
    }

    // Update customer connection status based on tpi_status
    let customerStatus;
    switch(tpi_status) {
      case 'completed':
        if(lmc_stage === "feasibility-check"){
          customerStatus = 'onboarding-schedule-pending';
          await tpiRepository.fetchAndUpdateReasonId(customer_connection_id, "Team-visit-schedule-is-in-progress");
        }else if(lmc_stage === "installation"){
          customerStatus = 'onboarding-approved';
        }
        break;
      case 'rejected':
        customerStatus = 'application-rejected';
        await tpiRepository.fetchAndUpdateReasonId(customer_connection_id, "onboarding-not-possible");
        break;
      case 'resubmit':
        customerStatus = 'onboarding-inprogress';
        break;
      case 'pending-eo':
        customerStatus = 'onboarding-inprogress';
        break;
    }

    if (customerStatus) {
      await tpiRepository.updateCustomerConnectionStatus(customer_connection_id, customerStatus);
    }

    const result = await tpiRepository.lmcWorkApprovalStatusUpdate(
      whereCondition,
      updateData
    );

    if(tpi_status === "completed" && lmc_stage === "feasibility-check"){
      const whereGetRecord = {
        lmc_connection_mapping_id,
        lmc_stage
      }
      const previousData = await tpiRepository.getSingleDataByConnectionID(
        whereGetRecord,
      );

      // const prevDataExistCheck = await tpiRepository.getSingleDataByConnectionID(
      //   {
      //     lmc_stage : 'installation',
      //     customer_connection_id
      //   },
      // );

      const dataForCreate = {
        lmc_connection_mapping_id,
        lmc_stage : 'installation',
        form_no: generateCode('FORM'),
        service_order_no: generateCode('SO'),
        service_request_no: generateCode('SR', 5),
        visit_datetime : null,
        actual_visit_date: null,
        reason : null,
        visit_status : 'not-scheduled',
        tpi_engineer_id: tpi_id,
        tpi_status : 'no-submission',
        is_active : 1,
        is_latest: 1,
        created_at : new Date(),
        created_by : tpi_id,
        updated_at : null,
        updated_by : null,
      }
      
      await tpiRepository.insertInlmcCustomerConnection(
        dataForCreate,
        previousData
      );
    }

    if(tpi_status === "completed" && lmc_stage === "installation"){
      // const whereGetRecord = {
      //   lmc_connection_mapping_id,
      //   lmc_stage
      // }
      // const previousData = await tpiRepository.getSingleDataByConnectionID(
      //   whereGetRecord,
      // );

      // const dataForCreate = {
      //   lmc_connection_mapping_id,
      //   lmc_stage : 'commissioning',
      //   form_no: generateCode('FORM'),
      //   service_order_no: generateCode('SO'),
      //   service_request_no: generateCode('SR', 5),
      //   visit_datetime : null,
      //   actual_visit_date: null,
      //   reason : null,
      //   visit_status : 'not-scheduled',
      //   tpi_engineer_id: tpi_id,
      //   tpi_status : 'no-submission',
      //   is_active : 1,
      //   is_latest: 1,
      //   created_at : new Date(),
      //   created_by : tpi_id,
      //   updated_at : null,
      //   updated_by : null,
      // }

      // await tpiRepository.createPaymentEntry(customer_connection_id, tpi_id);

      // await tpiRepository.insertInlmcCustomerConnection(
      //   dataForCreate,
      // );
    }

    return result;
  } catch (error) {
    console.error("Error in lmcWorkApprovalStatusUpdate:", error);
    throw error;
  }
};

const customerStatusUpdate = async (params) => {
  try {
    const { 
      customer_connection_id, 
      internal_status, 
      master_id, 
      master_value, 
      description,
      resubmit_reasons,
      status
    } = params;

    // Get TPI ID from the token/session
    const tpi_id = params.tpi_id;

    const result = await tpiRepository.customerStatusUpdate({
      customer_connection_id,
      internal_status,
      master_id,
      master_value,
      status,
      description,
      tpi_id
    });

    // If status is rejected, create TPI mapping entry
    if (internal_status === 'application-rejected') {
      for (const reason of resubmit_reasons) {
        await tpiRepository.createTpiMapping({
          customer_connection_id,
          tpi_id,
          tpi_status: 'rejected',
          reason_id: reason.master_id || null,
          remark: reason.description || null
        });
        await tpiRepository.updateSupervisorCustomerStatus({
          customer_connection_id,
          tpi_status: 'rejected',
        });
      }
      await tpiRepository.fetchAndUpdateReasonId(customer_connection_id, "document-mismatch");

    }
    if (internal_status === 'application-completed' || internal_status === 'application-pending-so') {
      for (const reason of resubmit_reasons) {
        await tpiRepository.createTpiMapping({
        customer_connection_id,
        tpi_id,
        tpi_status: 'pending-so',
        reason_id: reason.master_id || null,
        remark: reason.description || null
        });
        await tpiRepository.updateSupervisorCustomerStatus({
          customer_connection_id,
          tpi_status: 'approved',
        });
      }
    }
    if (internal_status === 'application-resubmit') {
      const tpiMappingResult = await tpiRepository.createTpiMapping({
        customer_connection_id,
        tpi_id,
        tpi_status: 'rework',
        reason_id: master_id || null,
        remark: description || null
      });

      if (resubmit_reasons && resubmit_reasons.length > 0) {
        for (const reason of resubmit_reasons) {
          await tpiRepository.createTpiRevokeEntry({
            customer_connection_tpi_mapping_id: tpiMappingResult.customer_connection_tpi_mapping_id,
            tpi_id,
            reason_id: reason.master_id || null,
            remark: reason.description || null
          });
        }
      }
      
      await tpiRepository.fetchAndUpdateReasonId(customer_connection_id, "update-registration-form");
    }

    return result;
  } catch (error) {
    console.error("Error in customerStatusUpdate:", error);
    throw error;
  }
};


const lmcStatusDataList = async (params) => {
  try {
    const result = await tpiRepository.lmcStatusDataList(params);
    return result;
  } catch (error) {
    console.error("Error in lmcStatusDataList:", error);
    throw error;
  }
};

const lmcStatusUpdate = async (params) => {
  try {
    const {tpi_id, tpi_onboard_approve_id, approve_status, remark} = params;


    // Construct update payload
    let updateData = {
      approve_status,
      remark : remark ? remark : null,
      updated_by: tpi_id, 
      updated_at: new Date(), // Set the current timestamp
    };

    const whereCondition = {
      tpi_onboard_approve_id,
    }
    const result = await tpiRepository.lmcStatusUpdate(
      whereCondition,
      updateData
    );

    return result;
  } catch (error) {
    console.error("Error in lmcStatusUpdate:", error);
    throw error;
  }
};


const onboardStatusCount = async (payload) => {
  try {
    const admin_type  = payload.admin_type;
    const whereCondition = {
      admin_type,
    }
    const result = tpiRepository.onboardStatusCount(admin_type,whereCondition);
    return result;
  } catch (error) {
    console.error("Error in onboardStatusCount:", error);
    throw error;
  }
};

const getCustomerApplicationDetailsService = async (params) => {
  try {
    const { customer_connection_id } = params;
    const result = await tpiRepository.getCustomerApplicationDetailsRepo(customer_connection_id);
    return result;
  } catch (error) {
    console.error("Error in getCustomerApplicationDetailsService:", error);
    throw error;
  }
};

const getPendingOnboardingList = async (params) => {
  try {
    const { persona_type, page = 1, limit = 10 } = params;
    const offset = (page - 1) * limit;
    
    const result = await tpiRepository.getPendingOnboardingList(persona_type, offset, limit);
    return {
      data: result.rows,
      pagination: {
        total: result.count,
        page: page,
        limit: limit,
        total_pages: Math.ceil(result.count / limit)
      }
    };
  } catch (error) {
    console.error("Error in getPendingOnboardingList service:", error);
    throw error;
  }
};

const handleOnboardingApproval = async (params) => {
  try {
    const { persona_type, supervisor_id, approve_status, remark, admin_id } = params;

    const updateData = {
      persona_type,
      approve_status,
      remark,
      updated_by: admin_id,
      updated_at: new Date(),
      tpi_id: admin_id
    };

    let result = await tpiRepository.updateTpiOnboardApproval({
      supervisor_id,
      updateData
    });

    if (!result || result[0] === 0) {
      throw new Error(`No records found for ${persona_type.toUpperCase()} supervisor with ID ${supervisor_id}`);
    }


    // If approval status is 'approved' and the personaType is 'DMA, send welcome email
    if (approve_status === 'approved' && persona_type === "dma") {
      // Get supervisor details
      const supervisorDetails = await tpiRepository.getSupervisorDetails(supervisor_id);
      // Get the respective contractor details of the supervisor
      const contractorDetails = await tpiRepository.getSupervisorDetails(
        supervisorDetails.contractor_id
      );
      const areaDetails =
        await tpiRepository.getSupervisorAreaDetails(supervisor_id);
      // Extract all area names
      const areaNames = Array.isArray(areaDetails)
        ? areaDetails.map((a) => a.area_name).join(", ")
        : "";
      if (supervisorDetails && supervisorDetails.email) {
        const emailSubject = "BPCL - DMA Supervisor Onboarding";
        let pwd = decryptPassword(supervisorDetails.password.toString());
        const emailBody = `
      Dear ${supervisorDetails.first_name} ${supervisorDetails.last_name},<br><br>

      <b>Congratulations!</b> You’ve been onboarded as DMA Supervisor for area - ${areaNames}.<br><br>

      Please find below onboarding details:<br><br>

      <b>DMA Contractor details:</b><br>
      Name: ${contractorDetails.first_name} ${contractorDetails.last_name}<br>
      Contact: ${contractorDetails.mobile}<br><br>

      <b>Login details:</b><br>
      DMA Application Link: ${process.env.NODE_ENV === "local" ? "http://localhost:3004" : process.env.NODE_ENV === "dev" ? "https://dev.pngcp.hellobpcl.in/" : "https://qa.pngcp.hellobpcl.in/"}<br>
      Login ID: ${supervisorDetails.email}<br>
      Login Password: ${pwd} <br><br>

      Regards,<br>
      BPCL DMA Onboarding Team
        `;

        try {
          await sendEmailNotificationWithoutCc(
            supervisorDetails.email,
            emailSubject,
            emailBody
          );
          console.log(`Approval email sent to supervisor: ${supervisorDetails.email}`);
        } catch (emailError) {
          console.error('Error sending approval email:', emailError);
        }
      }
    }

    if (approve_status === 'rejected' && persona_type === "dma"){
      const supervisorDetails =
        await tpiRepository.getSupervisorDetails(supervisor_id);
        await tpiRepository.getContractorDetails(supervisor_id);
        if (supervisorDetails && supervisorDetails.email) {
          const emailSubject = "BPCL - DMA Supervisor Onboarding Rejected";
          const emailBody = `
          Dear ${supervisorDetails.first_name} ${supervisorDetails.last_name},<br><br>
          Your DMA Supervisor Onboarding has been rejected. Please contact the support team for further details.
          `;
          try {
            await sendEmailNotificationWithoutCc(
              supervisorDetails.email,
              emailSubject,
              emailBody
            );
            console.log(
              `Rejection email sent to supervisor: ${supervisorDetails.email}`
            );
          } catch (emailError) {
            console.error('Error sending rejection email:', emailError);
          }
        }
    }


    // If approval status is 'approved' and the personaType is 'LMC', send welcome email
    if (approve_status === "approved" && persona_type === "lmc") {
      // Get supervisor details
      const supervisorDetails =
        await tpiRepository.getSupervisorDetails(supervisor_id);
        // Get the respective contractor details of the supervisor
      const contractorDetails = await tpiRepository.getSupervisorDetails(
        supervisorDetails.contractor_id
      );
      // Extract all area names
      const areaDetails =
        await tpiRepository.getSupervisorAreaDetails(supervisor_id);
      const areaNames = Array.isArray(areaDetails)
        ? areaDetails.map((a) => a.area_name).join(", ")
        : "";
      console.log("Area Name:", areaNames);
      if (supervisorDetails && supervisorDetails.email) {
        const emailSubject = "BPCL - LMC Supervisor Onboarding";
        let pwd = decryptPassword(supervisorDetails.password.toString());
        const emailBody = ` 
        Dear ${supervisorDetails.first_name} ${supervisorDetails.last_name},<br><br>

        Congratulations! You have been successfully onboarded as a <b>LMC Supervisor</b> for area - <b>${areaNames}</b>.<br><br>

        <b>Onboarding Details:</b><br>
        - LMC Contractor Name: ${contractorDetails.first_name} ${contractorDetails.last_name} <br>
        - LMC Contractor Contact: ${contractorDetails.mobile}<br><br>
        <b>Login Details:</b><br>
        - LMC Application Link: ${process.env.NODE_ENV === "local" ? "http://localhost:3004" : process.env.NODE_ENV === "dev" ? "https://dev.pngcp.hellobpcl.in/" : "https://qa.pngcp.hellobpcl.in/"}<br>
        - User ID: ${supervisorDetails.email}<br>
        - Temporary Password: ${pwd}<br><br>
        For any queries, please contact [Support Team Contact].<br><br>
        Best regards,<br>
        BPCL LMC Onboarding Team
        `;
        try {
          await sendEmailNotificationWithoutCc(
            supervisorDetails.email,
            emailSubject,
            emailBody
          );
          console.log(
            `Approval email sent to supervisor: ${supervisorDetails.email}`
          );
        } catch (emailError) {
          console.error("Error sending approval email:", emailError);
        }
      }
    }

    return result;
  } catch (error) {
    console.error(`Error in handleOnboardingApproval service: ${error.message}`);
    throw error;
  }
};

const getCustomerApplicationStatusCount = async () => {
  try {
    const result = await tpiRepository.getCustomerApplicationStatusCount();
    return result;
  } catch (error) {
    console.error("Error in getCustomerApplicationStatusCount:", error);
    throw error;
  }
};

const getJmrApprovalList = async (requestBody) => {
  try {
    const { page = 1, limit = 10, tpi_status } = requestBody;
    const areaIds = requestBody?.area_id;
    
    const params = {
      where: { is_deleted: 0 },
      limit: limit,
      offset: (page - 1) * limit,
      order: [['created_at', 'DESC']]
    };

    if (tpi_status) {
      params.where.tpi_status = tpi_status;
    }

    return await tpiRepository.getJmrApprovalList(params, areaIds);
  } catch (error) {
    throw error;
  }
};

const getJmrApprovalStatusCount = async (payload) => {
  try {
    const areaIds = payload?.body?.area_id;
    if (!Array.isArray(areaIds) || areaIds.length === 0) {
      throw { status: 400, message: "area_id must be a non-empty array." };
    }
    return await tpiRepository.getJmrApprovalStatusCount(areaIds);
  } catch (error) {
    console.error("Error in getJmrApprovalStatusCount service:", error);
    throw error;
  }
};

const getJmrSubmissionDetails = async (tmma_id) => {
  try {
    const result = await tpiRepository.getJmrSubmissionDetails(tmma_id);
    if (!result) {
      throw new Error('JMR submission details not found');
    }
    return result;
  } catch (error) {
    throw error;
  }
};

const updateJmrApprovalStatus = async (params) => {
  try {
    const { tmma_id, status, reason, tpi_id, customer_connection_id, lmc_connection_stage_id } = params;

    // Validate if record exists
    const existingRecord = await tpiRepository.getJmrSubmissionDetails(tmma_id);
    if (!existingRecord) {
      throw new Error('JMR submission record not found');
    }

    const updateData = {
      status,
      reason: reason || null,
      updated_by: tpi_id
    };

    switch(status){
      case 'rejected':
        await tpiRepository.updateCustomerConnectionStatus(existingRecord.customer.customer_connection_id, 'application-rejected-jmr');
        await tpiRepository.fetchAndUpdateReasonId(existingRecord.customer.customer_connection_id, "installation-not-possible");
        break;
      case 'resubmit':
        await tpiRepository.fetchAndUpdateReasonId(existingRecord.customer.customer_connection_id, "contact-engineering-team-for-further-details");
        break;
    }

    const result = await tpiRepository.updateJmrApprovalStatus(tmma_id, updateData, tpi_id, existingRecord.customer.customer_connection_id, lmc_connection_stage_id);
    return result;
  } catch (error) {
    console.error("Error in updateJmrApprovalStatus:", error);
    throw error;
  }
};

const getTpiCategories = async (params) => {
  try {
    const result = await tpiRepository.getTpiCategories(params.master_type);
    return result;
  } catch (error) {
    console.error("Error in getTpiCategories:", error);
    throw error;
  }
};

const updateGaApproval = async (params) => {
  try {
    const { customer_connection_id, action, reason, review_type, tpi_id } = params;


    // Construct update payload
    let updateData = {
      updated_by: tpi_id,
      updated_at: new Date(),
      reason: reason
    };

    // Set internal_status based on action
    if (action === 'send-back-to-so') {
      if (review_type === 'customer-application') {
        updateData.internal_status = 'application-pending-so';
        updateData.status = 'application-inreview';
        
        // Create TPI mapping entry for tracking the action
        await tpiRepository.createTpiMapping({
          customer_connection_id,
          tpi_id,
          tpi_status: 'pending-so',
          remark: reason || null
        });

        // Update customer connection status using the new repository function
        const result = await tpiRepository.updateGaApprovalStatus(
          customer_connection_id,
          updateData
        );
      } else if (review_type === 'lmc-installation') {
        updateData.internal_status = 'onboarding-inreview';
        updateData.status = 'onboarding-inreview';

      } else if (review_type === 'lmc-jmr') {
        updateData.internal_status = 'jmr-inreview';
        updateData.status = 'jmr-inreview';
      }
      updateData.internal_status = 'application-pending-so';
      updateData.status = 'application-inreview';
    } else if (action === 'send-for-resubmission') {
      updateData.internal_status = 'application-resubmit';
      updateData.status = 'application-rework';
    }



    return result;
  } catch (error) {
    console.error("Error in updateGaApproval:", error);
    throw error;
  }
};

const getCommentDetailsTpi = async (req) => {
  try {
    if (!req || !req.body) {
      throw new Error("Request body is missing.");
    }

    const { customer_connection_id, mapping_type } = req.body;

    if (!customer_connection_id) {
      throw new Error("customer_connection_id is required.");
    }

    if (!mapping_type) {
      throw new Error("mapping_type is required.");
    }

    let commentDetails;
    switch (mapping_type) {
      case 'customer-application':
        commentDetails = await tpiRepository.getSoCommentDetailsTpi(customer_connection_id);
        break;
      case 'lmc-installation':
        commentDetails = await tpiRepository.getEoCommentDetailsTpi(customer_connection_id);
        break;
      case 'jmr-manual':
        commentDetails = await tpiRepository.getEoJmrCommentDetailsTpi(customer_connection_id);
        break;
      default:
        throw new Error(`Unsupported mapping_type: ${mapping_type}`);
    }
    if (!commentDetails || commentDetails.length === 0) {
      throw new Error("No comment details found for the given customer_connection_id and mapping_type.");
    }
    return {
      data: {
        message: `${mapping_type.toUpperCase()} comment details fetched successfully.`,
        commentDetails,
      },
    };
  } catch (error) {
    console.error("Error in getCommentDetails service:", error.message);
    throw error;
  }
};

const getFeasibilityFormDetailsTpi = async (req) => {
  try {
    const { lmc_connection_mapping_id } = req.body;

    const mapped_lmc_connection_stage_id = await tpiRepository.getFeasibilityStageId(lmc_connection_mapping_id);

    const feasibilityData = await tpiRepository.getFeasibilityFormDetailsTpi(mapped_lmc_connection_stage_id);

    if (!feasibilityData) {
      return {
        success: false,
        message: "No feasibility data found",
      };
    }

    const mappedStage = feasibilityData.mapped_lmc_connection_stage || {};

    const lmcSupervisorFullName = await tpiRepository.getSupervisorFullName(mappedStage.lmc_connection_mapping_id);

    const response = {
      visit_status: mappedStage.visit_status || 'NA',
      actual_visit_date: mappedStage.actual_visit_date ? formatDate(mappedStage.actual_visit_date) : 'NA',
      visit_datetime: mappedStage.visit_datetime ? formatDate(mappedStage.visit_datetime) : 'NA',
      date_of_creation: feasibilityData.createdAt ? formatDate(feasibilityData.createdAt) : 'NA',
      house_type: feasibilityData.house_type || 'NA',
      mdpe_pipeline_length: Number(feasibilityData.mdpe_pipeline_length || 0),
      riser_length: Number(feasibilityData.riser_length || 0),
      gi_pipeline_length: Number(feasibilityData.gi_pipeline_length || 0),
      meter_type: feasibilityData.meter_type || 'NA',
      documents: mappedStage.trx_lmc_pipeline_documents?.map(doc => ({
        file_name: doc.file_name,
        file_path: doc.file_path,
        file_type: doc.file_type,
      })) || [],
      lmc_supervisor_name: lmcSupervisorFullName || 'NA',
    };

    return {
      success: true,
      message: "Feasibility data fetched successfully",
      data: response,
    };

  } catch (error) {
    console.error("Service error in getFeasibilityFormDetailsTpi:", error);
    return {
      success: false,
      message: "Error processing the request",
      error: error.message,
    };
  }
};

const getInstallationFormDataTpi = async (lmc_connection_mapping_id) => {
  try {

    const mapped_lmc_connection_stage_id = await tpiRepository.getInstallationStageId(lmc_connection_mapping_id);

    // Fetch stage details
    const stage = await lmcRepository.getStageDetails(mapped_lmc_connection_stage_id);
    if (!stage) {
      throw new Error("Mapped LMC Connection Stage ID not found.");
    }
    const customer_connection_id = stage["lmc_connection_mapping.customer_connection_id"];

    // Fetch installation and documents
    const installationDetails = await lmcRepository.getInstallationDetails(mapped_lmc_connection_stage_id);
    const uploadedDocuments = await lmcRepository.getUploadedDocuments(mapped_lmc_connection_stage_id);

    // Fetch address from customer connection


    const meterDetails = await lmcRepository.getMeterDetails(customer_connection_id);

    // Fetch house type from latest feasibility/commissioning details
    const latestCommissioning = await lmcRepository.getLatestFeasibilityCommissioning(mapped_lmc_connection_stage_id);

    const customerAddress = await lmcRepository.getCustomerAddressDetails(customer_connection_id);

    // Build response
    return {
      technicalDetails: {
        actual_visit_date: stage.actual_visit_date,
        regulator_number: installationDetails?.regulator_no || 'NA',
        installation_date: installationDetails?.installation_date || 'NA',
        testing_date: installationDetails?.testing_date || 'NA',
        rfc_date: installationDetails?.rfc_date || 'NA',
        house_type_floor: installationDetails?.house_type || 'NA',
        house_type: latestCommissioning?.house_type || installationDetails?.house_type || 'NA',
        mdpe_pipeline_length: latestCommissioning?.mdpe_pipeline_length || installationDetails?.mdpe_pipeline_length || 'NA',
        riser_length: latestCommissioning?.riser_length || installationDetails?.riser_length || 'NA',
        gi_pipeline_length: latestCommissioning?.gi_pipeline_length || installationDetails?.gi_pipeline_length || 'NA',
        meter_no: meterDetails?.meter_no || 'NA',
        area: customerAddress?.connection_address_pngarea?.pincodeAreasAlias?.area?.area_name || null,
        city: customerAddress?.connection_city || 'NA',
        house_no: customerAddress?.connection_house_no || customerAddress?.connection_floor_no || 'NA',
        house_address: customerAddress?.connection_address || 'NA'
      },
      billOfMaterial: {
        gi_length: installationDetails?.gi_length || 0,
        gi_length_tapping: installationDetails?.gi_length_for_tapping || 0,
        copper_length: installationDetails?.copper_length || 0,
        appliance_valve_half_inch: installationDetails?.appliance_valve || 0,
        isolation_ball_valve: installationDetails?.isolation_ball_valve || 0,
        brass_fitting_set: installationDetails?.brass_fitting_set || 0,
        gi_plug_half_inch: installationDetails?.gi_plug || 0,
        half_inch_2_inch_nipple: installationDetails?.half_two_nipple || 0,
        half_inch_3_inch_nipple: installationDetails?.half_three_nipple || 0,
        half_inch_4_inch_nipple: installationDetails?.half_four_nipple || 0,
        elbow: installationDetails?.elbow || 0,
        equal_tee: installationDetails?.equal_tee || 0,
        socket: installationDetails?.socket || 0,
        union: installationDetails?.union_value || 0,
        anaconda: installationDetails?.anaconda || 0,
        elbow_mf: installationDetails?.elbow_mf || 0,
      },
      checklist: installationDetails?.checklist || {},
      uploadDocuments: uploadedDocuments.map(doc => ({
        mapped_lmc_connection_stage_id: doc.mapped_lmc_connection_stage_id,
        file_name: doc.file_name,
        file_path: doc.file_path,
        file_ext: doc.file_ext,
        verification_status: doc.verification_status,
        file_status: doc.file_status,
        file_type: doc.file_type,
      })),
      mapped_lmc_connection_stage_id,
    };
  } catch (error) {
    console.error("Service error in getInstallationFormDataTpi:", error);
    throw new Error(`Error fetching installation form data tpi: ${error.message}`);
  }
};

module.exports = {
  getTPICasesSummery,
  tpiApplicationStatus,
  getStatusCount,
  feasibilityInstallationCustomerDetailService,
  customerStatusHistory,
  tpiCustomeReview,
  lmcWorkApprovalStatusUpdate,
  customerStatusUpdate,
  lmcStatusDataList,
  lmcStatusUpdate,
  onboardStatusCount,
  getCustomerApplicationDetailsService,
  getPendingOnboardingList,
  handleOnboardingApproval,
  getCustomerApplicationStatusCount,
  getJmrApprovalList,
  getJmrApprovalStatusCount,
  getJmrSubmissionDetails,
  updateJmrApprovalStatus,
  getTpiCategories,
  updateGaApproval,
  getCommentDetailsTpi,
  getFeasibilityFormDetailsTpi,
  getInstallationFormDataTpi
};
