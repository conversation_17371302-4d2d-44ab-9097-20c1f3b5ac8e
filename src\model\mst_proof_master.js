const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_proof_master",
    {
      proof_master_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      proof_type: {
        type: DataTypes.STRING(30),
        allowNull: true,
        unique: "UK_mstproofmaster_prooftype_proofname",
      },
      master_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_masters",
          key: "master_id",
        },
      },
      proof_name: {
        type: DataTypes.STRING(200),
        allowNull: false,
        unique: "UK_mstproofmaster_prooftype_proofname",
      },
      display_order: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "mst_proof_master",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "UK_mstproofmaster_prooftype_proofname",
          unique: true,
          fields: [{ name: "proof_type" }, { name: "proof_name" }],
        },
        {
          name: "UK_proof_master_id",
          unique: true,
          fields: [{ name: "proof_master_id" }],
        },
      ],
    }
  );
};
