const { validate } = require("express-validation");
const { restrictToRoles } = require("../../middleware/auth");
const express = require("express");
const foSupervisor = express.Router();
const decrypt = require("../../middleware/encrypt-decrypt");
const foSupervisorController = require("../../controllers/fo-supervisor-controller");
const {
  dashboardMetricsValidation,
} = require("../../validation/fo-admin-validation");
const verifyToken = require("../../middleware/auth");
const { getMROListingValidation, meterReadingUploadValidation, getMROCustomerDetailsValidation, getMRODetailsValidation, getGeographicalAreasValidator } = require("../../validation/fo-supervisor-validation");
const { uploadSingle } = require("../../middleware/newMulter");

foSupervisor.post(
  "/meter-reading-upload",
  verifyToken,
  restrictToRoles("supervisor"),
  uploadSingle,
  validate(meterReadingUploadValidation),
  foSupervisorController.meterReadingUploads
);

foSupervisor.post(
  "/mro-listing",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(getMROListingValidation),
  foSupervisorController.getMROListing
);
foSupervisor.post(
  "/mro-details",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(getMRODetailsValidation),
  foSupervisorController.getMROTicketDetails
);
foSupervisor.post(
  "/mro-customer-details",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(getMROCustomerDetailsValidation),
  foSupervisorController.getDetailedMROCustomerInfo
);
foSupervisor.post(
  "/geographical-areas",
  verifyToken,
  restrictToRoles("supervisor"),
  // validate(getGeographicalAreasValidator),
  foSupervisorController.getGeographicalAreas
);
foSupervisor.post(
  "/dashboard-metics",
  verifyToken,
  restrictToRoles("supervisor"),
  validate(dashboardMetricsValidation),
  foSupervisorController.getFOMetricsGA
);

module.exports = { foSupervisor };
