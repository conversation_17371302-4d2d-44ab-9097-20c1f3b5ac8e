const { Router } = require("express");
const masterJson = require("../dummy.json");
// const { thirdPartyRoutes } = require('./third-party');
const govtSchemeRoutes = require("./govtscheme");
// const proofOfOwnershipRoutes = require('./proofofownership');
const proofMasterRoutes = require("./proofmaster/index.js");
const applicationStatusRoutes = require("./application-status.js");
const pinCodeCheckRoutes = require("./pincodecheck");
const { customerRoute } = require("./customer");
const { lmcRoute } = require("./lmc");
const { tpiRoute } = require("./TPI");
const { meterReadingRoute } = require("./meterReading");
const { dashBoardRoutes } = require("./dashboard");
const authRoutes = require("./auth");
const profileRoutes = require("./profile");
const { paymentRoutes } = require("./payment");
const helpSupportRoutes = require("./help-support");
const masterRoute = require("./master");
const { customerProfileRoutes } = require("./customerprofile");
const dmaCustomerRoutes = require("./dma-customer");
const dmaSupervisorRoutes = require("./dma-supervisor");
const dmaContractorRoutes = require("./dma-contractor");
const { serviceRequestRoutes } = require("./service-request");
const { lmcContractor } = require("./lmc-contractor");
const { foAdmin } = require("./fo-admin");
const { mediaRoute } = require("./media");
const { gaCustomerRoute } = require("./ga-admin-customer");
const bpclAdminRoutes = require("./bpcl-admin");
const { foSupervisor } = require("./fo-supervisor");
const { otpRoutes } = require("./otp");
const { dbInsertRoutes } = require("./db-insert");
const sapRoute = require("./sap/index.js");

const routes = Router();

// routes.use('/third-party', thirdPartyRoutes);
routes.use("/govt-scheme", govtSchemeRoutes);
routes.use("/proof-Master", proofMasterRoutes);
// routes.use('/proofOfOwnership', proofOfOwnershipRoutes);
routes.use("/customer", customerRoute);
routes.use("/meter-reading", meterReadingRoute);
routes.use("/application-status", applicationStatusRoutes);
routes.use("/pincode-check", pinCodeCheckRoutes);
routes.use("/dash-board", dashBoardRoutes);
routes.use("/lmc", lmcRoute);
routes.use("/auth", authRoutes);
routes.use("/profile", profileRoutes);
routes.use("/payment", paymentRoutes);
routes.use("/customer-profile", customerProfileRoutes);
routes.use("/fetch-dma-customers", dmaCustomerRoutes);
routes.use("/help-support", helpSupportRoutes);
routes.use("/master", masterRoute);

routes.use("/sap", sapRoute);
routes.use("/dma-supervisor", dmaSupervisorRoutes);
routes.use("/dma-contractor", dmaContractorRoutes);
routes.use("/service-request", serviceRequestRoutes);
routes.use("/tpi", tpiRoute);
routes.use("/lmc-contractor", lmcContractor);
routes.use("/fo-admin", foAdmin);
routes.use("/media", mediaRoute);
routes.use("/ga-customer", gaCustomerRoute);
routes.use("/bpcl-admin", bpclAdminRoutes);
routes.use("/fo-supervisor", foSupervisor);
routes.use("/otp", otpRoutes);
routes.use("/db-insert", dbInsertRoutes);

routes.post("/master/list", (req, res, next) => {
  const info = masterJson;
  res.json({
    status: "success",
    statusCode: 200,
    data: info,
    message: "Data Found",
  });
});

module.exports = { routes };
