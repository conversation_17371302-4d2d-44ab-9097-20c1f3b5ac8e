{"name": "png", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "start-local": "nodemon --env-file=.env index.js", "test": "jest", "testcoverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint --fix --ext .js,.jsx .", "webpack-build": "webpack --mode production", "dev": "webpack --watch --mode development", "webpack-start": "node dist/server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure-rest/ai-document-intelligence": "^1.1.0", "@azure/data-tables": "^13.3.0", "@azure/storage-blob": "^12.25.0", "archiver": "^7.0.1", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "express": "^4.18.2", "express-validation": "^4.1.0", "firebase-admin": "^12.6.0", "fs": "^0.0.1-security", "handlebars": "^4.7.8", "jsonwebtoken": "^9.0.1", "md5": "^2.3.0", "moment": "^2.30.1", "msnodesqlv8": "^4.4.0", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "nodemon": "^3.1.7", "path": "^0.12.7", "pdfkit": "^0.16.0", "pg": "^8.13.0", "pg-hstore": "^2.3.4", "puppeteer": "^24.4.0", "redis": "^4.7.0", "sequelize": "^6.37.5", "sequelize-auto": "^0.8.8", "sharp": "^0.33.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tedious": "^18.6.1", "tesseract.js": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@eslint/js": "^9.10.0", "babel-loader": "^10.0.0", "eslint": "^9.10.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.9.0", "jest": "^29.6.2", "supertest": "^6.3.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}}