const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_areas",
    {
      area_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      geographical_area_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_geographical_areas",
          key: "geographical_area_id",
        },
        unique: "UK_mstareas_geographical_area_id_area_name",
      },

      isu_code: {
        type: DataTypes.STRING(18),
        allowNull: true,
      },
      area_name: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: "UK_mstareas_geographical_area_id_area_name",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "mst_areas",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_mstareas_area_id",
          unique: true,
          fields: [{ name: "area_id" }],
        },
        {
          name: "UK_mstareas_geographical_area_id_area_name",
          unique: true,
          fields: [{ name: "geographical_area_id" }, { name: "area_name" }],
        },
      ],
    }
  );
};
