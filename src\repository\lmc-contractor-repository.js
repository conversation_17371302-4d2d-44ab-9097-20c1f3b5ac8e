const { Op, Sequelize } = require('sequelize');
const { dbModels, sequelize } = require('../database/connection');
const { generateCode } = require('../utils/commonFn');
const lmcCustomerConnectionModel = dbModels.trx_mapped_lmc_customer_connection;
const lmcPipelineModel = dbModels.trx_lmc_pipeline;

const getLmcCustomerAssignments = async (params, area_ids, contractorId) => {
  try {
    const {
      lmc_id,
      search,
      page = 1,
      limit = 1000,
      lmc_stage,
      visit_status,
      tpi_status,
      startDate,
      endDate,
      assigned_status,
    } = params;

    const offset = (page - 1) * limit;
    const customerFilter = { is_active: 1 };
    const lmcMappingFilter = { is_latest: 1 };

    if (search) {
      customerFilter[Op.or] = [
        { customer_connection_id: { [Op.like]: `%${search}%` } },
        { first_name: { [Op.like]: `%${search}%` } },
        { last_name: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } },
        { mobile: { [Op.like]: `%${search}%` } },
      ];
    }

    if (lmc_id) lmcMappingFilter.lmc_id = lmc_id;
    if (lmc_stage) lmcMappingFilter.lmc_stage = lmc_stage;
    if (visit_status) lmcMappingFilter.visit_status = visit_status;
    if (tpi_status) lmcMappingFilter.tpi_status = tpi_status;

    if (startDate && endDate) {
      lmcMappingFilter.visit_datetime = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    const areaWhereCondition = area_ids?.length
      ? { area_id: { [Op.in]: area_ids } }
      : {};

    if (assigned_status === 'false') {
      const assignedCustomers = await dbModels.trx_lmc_connection_mapping.findAll({
        attributes: ['customer_connection_id'],
        where: { customer_connection_id: { [Op.ne]: null } },
        raw: true,
      });

      const assignedIds = assignedCustomers.map(row => row.customer_connection_id);
      customerFilter.customer_connection_id = { [Op.notIn]: assignedIds };
    }

    const include = [
      {
        model: dbModels.trx_customer_connection_tpi_mapping,
        as: 'trx_customer_connection_tpi_mappings',
        attributes: ['tpi_status',"updated_at"],
        required: true,
        where: { tpi_status: 'approved-so' }
      },
      {
        model: dbModels.trx_customer_connection_address,
        as: 'trx_customer_connection_addresses',
        attributes: ['connection_pincode'],
        required: false,
      },
      {
        model: dbModels.mst_available_pngareas,
        as: 'available_pngarea',
        attributes: ['pincode_id'],
        required: true,
        include: [
          {
            model: dbModels.mst_pincodes,
            as: 'pincodeAreasAlias',
            attributes: ['pincode_id', 'area_id'],
            required: true,
            where: areaWhereCondition,
            include: [
              {
                model: dbModels.mst_areas,
                as: 'area',
                attributes: ['area_name'],
              },
            ],
          },
        ],
      },
    ];

    if (assigned_status === 'true') {
      include.push({
        model: dbModels.trx_lmc_connection_mapping,
        as: 'trx_lmc_connection_mappings',
        attributes: ['lmc_connection_mapping_id', 'customer_connection_id', 'lmc_id', 'updated_at', 'created_at'],
        required: true,
        include: [
          {
            model: dbModels.mst_admin,
            as: 'lmc',
            attributes: ['bpcl_admin_id', 'first_name', 'last_name', 'email', 'mobile'],
            where: { created_by: contractorId },
          },
          {
            model: dbModels.trx_mapped_lmc_connection_stages,
            as: 'trx_mapped_lmc_connection_stages',
            where: Object.keys(lmcMappingFilter).length ? lmcMappingFilter : undefined,
            attributes: [
              'mapped_lmc_connection_stage_id',
              'lmc_stage',
              'visit_status',
              'visit_datetime',
              'actual_visit_date',
              'tpi_status',
            ],
          },
        ],
      });
    }

    const { count, rows } = await dbModels.trx_customer_connection.findAndCountAll({
      where: customerFilter,
      attributes: ['customer_connection_id', 'first_name', 'last_name', 'email', 'mobile'],
      include,
      limit,
      offset,
      distinct: true,
    });

    const formattedCases = rows.map(customer => {
      const address = customer.trx_customer_connection_addresses?.[0] || {};
      const mapping = customer.trx_lmc_connection_mappings?.[0] || {};
      const tpiMapping = customer.trx_customer_connection_tpi_mappings?.[0]?.dataValues || {};
      const lmc = mapping.lmc || {};
      const stage = mapping.trx_mapped_lmc_connection_stages?.[0] || {};
      const area = customer.available_pngarea?.pincodeAreasAlias?.area || {};

      return {
        mapped_lmc_customer_connection_id: mapping.lmc_connection_mapping_id || 'N/A',
        customer_connection_id: customer.customer_connection_id,
        full_name: `${customer.first_name || ''} ${customer.last_name || ''}`.trim(),
        email: customer.email || '',
        mobile: customer.mobile || '',
        pincode: address.connection_pincode || 'N/A',

        assignedLMCID: mapping.lmc_id || 'N/A',
        assignedLMCName: `${lmc.first_name || ''} ${lmc.last_name || ''}`.trim() || 'N/A',
        AssignmentStatus: mapping.lmc_id ? 'Assigned' : 'To be assigned',

        lmc_stage: stage.lmc_stage || 'N/A',
        visit_status: stage.visit_status || 'N/A',
        visit_datetime: stage.visit_datetime || 'N/A',
        actual_visit_date: stage.actual_visit_date || 'N/A',
        tpi_status: stage.tpi_status || 'N/A',

        assignedLMCDate: mapping.updated_at || 'N/A',
        bpApprovalDate: tpiMapping.updated_at || 'N/A',

        area: area.area_name || 'N/A',
      };
    });

    return {
      cases: formattedCases,
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    };
  } catch (error) {
    console.error('Error fetching LMC customer assignments:', error);
    throw new Error('Failed to fetch LMC customer assignments.');
  }
};


const getLMCSupervisorById = async (email,mobile) => {
  try {

      return await dbModels.mst_admin.findOne({
        where: {
          [Op.or]: [
            { email: email },
            { mobile: mobile }
          ],
          user_role: 'supervisor'
        },
          include: [
              {
                  model: dbModels.mst_persona_role,
                  as: 'persona_role',
                  attributes: ['persona_role_id', 'role_name']
              }
          ]
      });
  } catch (error) {
      console.error('Error in getDmaSupervisorById:', error);
      throw error;
  }
};

const createLMCSupervisor = async (supervisorData, contractorId, file) => {
  const transaction = await sequelize.transaction();
  try {
    // Step 1: Create supervisor in mst_admin
    const supervisor = await dbModels.mst_admin.create({
      first_name: supervisorData.first_name,
      last_name: supervisorData.last_name,
      email: supervisorData.email,
      mobile: supervisorData.mobile,
      alternate_mobile: supervisorData.alternate_mobile,
      password: supervisorData.password,
      user_role: 'supervisor',
      persona_role_id: supervisorData.persona_role_id,
      bpcl_admin_id: supervisorData.bpclAdminId,
      is_active: true,
      created_by: contractorId
    }, { transaction });

    // Step 2: Optional document upload
    if (file) {
      await dbModels.mst_admin_documents.create({
        admin_id: supervisor.admin_id,
        document_type: 'supporting',
        file_name: file.originalname,
        file_path: file.path,
        file_ext: file.originalname.split('.').pop(),
        created_by: contractorId,
      }, { transaction });
    }

    // Step 3: Add to approval queue
    await dbModels.trx_tpi_onboard_approve.create({
      tpi_id: contractorId,
      supervisor_id: supervisor.admin_id,
      admin_type: 'lmc',
      approve_status: 'pending',
      remark: null,
      is_active: 1,
      created_by: contractorId
    }, { transaction });

    // Step 4: Insert area mappings
    if (Array.isArray(supervisorData.area_ids) && supervisorData.area_ids.length > 0) {
      const areaMappings = supervisorData.area_ids.map(area_id => ({
        admin_id: supervisor.admin_id,
        area_id,
        is_active: true,
        created_by: contractorId
      }));

      await dbModels.trx_admin_mapped_areas.bulkCreate(areaMappings, { transaction });
    }

    await transaction.commit();
    return supervisor;
  } catch (error) {
    await transaction.rollback();
    console.error('Error in repository createLMCSupervisor:', error);
    throw error;
  }
};

const getAllLMCSupervisors = async (approve_status, areaIds, contractorId) => {
  try {
    let whereCondition = {};

    if (approve_status === 'rejected') {
      whereCondition = { approve_status: 'rejected' };
    } else {
      whereCondition = { approve_status: 'pending' };
    }

    const includeMappedAreas = {
      model: dbModels.trx_admin_mapped_areas,
      as: 'trx_admin_mapped_areas',
      attributes: ['area_id'],
      required: Array.isArray(areaIds) && areaIds.length > 0,
      ...(Array.isArray(areaIds) && areaIds.length > 0
        ? { where: { area_id: { [Op.in]: areaIds } } }
        : {})
    };

    const supervisors = await dbModels.mst_admin.findAll({
      where: {
        created_by: contractorId,
        user_role: 'supervisor'
      },
      attributes: [
        'admin_id',
        'first_name',
        'last_name',
        'email',
        'mobile',
        'alternate_mobile',
        'persona_role_id',
        'is_active',
        'created_at',
        'updated_at'
      ],
      include: [
        {
          model: dbModels.mst_persona_role,
          as: 'persona_role',
          attributes: ['persona_role_id', 'role_name'],
          required: true
        },
        includeMappedAreas,
        {
          model: dbModels.trx_tpi_onboard_approve,
          as: 'trx_tpi_onboard_approves',
          attributes: [
            'tpi_onboard_approve_id',
            'approve_status',
            'is_active',
            'created_at',
            'updated_at',
            'remark'
          ],
          where: whereCondition,
          required: true
        }
      ],
      order: [['created_at', 'DESC']],
      distinct: true 
    });

    return supervisors;
  } catch (error) {
    console.error('Error in getAllLMCSupervisors:', error);
    throw error;
  }
};

const revokeLMCSupervisor = async (supervisorId, remark) => {
  try {
    // Find the supervisor by admin_id
    let supervisor = await dbModels.mst_admin.findOne({
      where: { admin_id: supervisorId },
      attributes: ['admin_id', 'first_name', 'last_name']
    });

    if (!supervisor) {
      return { success: false, message: "Supervisor not found." };
    }

    // Update the supervisor's status to inactive and set updated_at timestamp
    await dbModels.mst_admin.update(
      { 
        is_active: false,
        updated_at: new Date() // Ensure updated_at is refreshed
      },
      { where: { admin_id: supervisorId } }
    );

    return {
      success: true,
      message: `You have successfully revoked access of LMC supervisor ${supervisor.first_name} ${supervisor.last_name} (LMC${supervisor.admin_id}), they will now be visible under the inactive tab.`,
      supervisor
    };
  } catch (error) {
    console.error("Error in revokeLMCSupervisor:", error);
    return { success: false, message: "Failed to revoke supervisor access.", error: error.message };
  }
};

const getLMCSupervisor = async (supervisorId, remark) => {
  try {
    // Find the supervisor by admin_id
    let supervisor = await dbModels.mst_admin.findOne({
      where: { admin_id: supervisorId },
      attributes: ['admin_id', 'first_name', 'last_name','email']
    });

    return supervisor
  } catch (error) {
    console.error("Error in revokeLMCSupervisor:", error);
    return { success: false, message: "Failed to revoke supervisor access.", error: error.message };
  }
};


const getMyLMCSupervisors = async (contractorId, is_active, areaIds) => {
  try {
    // Normalize is_active to boolean
    if (typeof is_active === 'string') {
      is_active = is_active === 'true' || is_active === '1';
    }

    const includeMappedAreas = {
      model: dbModels.trx_admin_mapped_areas,
      as: 'trx_admin_mapped_areas',
      attributes: ['area_id'],
      required: Array.isArray(areaIds) && areaIds.length > 0,
      ...(Array.isArray(areaIds) && areaIds.length > 0
        ? { where: { area_id: { [Op.in]: areaIds } } }
        : {})
    };
    const supervisors = await dbModels.mst_admin.findAll({
      where: {
        created_by: contractorId,
        user_role: 'supervisor',
        ...(typeof is_active === 'boolean' && { is_active }),
      },
      attributes: [
        'admin_id',
        'first_name',
        'last_name',
        'email',
        'mobile',
        'alternate_mobile',
        'persona_role_id',
        'is_active',
        'created_at',
        'updated_at'
      ],
      include: [
        {
          model: dbModels.mst_persona_role,
          as: 'persona_role',
          attributes: ['persona_role_id', 'role_name'],
          required: false
        },
        includeMappedAreas,
        {
          model: dbModels.trx_tpi_onboard_approve,
          as: 'trx_tpi_onboard_approves',
          attributes: ['tpi_onboard_approve_id', 'approve_status', 'is_active', 'updated_at'],
          where: { approve_status: 'approved' },
          required: true
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return supervisors;
  } catch (error) {
    console.error('Error in getMyLMCSupervisors (repository):', error);
    throw error;
  }
};


const getGeographicalAreas = async () => {
  try {
    const geographicalAreas = await dbModels.mst_geographical_areas.findAll({
      attributes: [
        'geographical_area_id',
        'region_name',
        'state_id',
        'city_id',
        'ga_area_name',
        'is_active',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by'
      ],
      order: [['region_name', 'ASC']] // Order by region_name, ascending
    });

    return geographicalAreas;
  } catch (error) {
    console.error('Error in getGeographicalAreas:', error);
    throw error;
  }
};
const getSupervisorsTracking = async (area_ids, contractorId) => {
  try {
    const areaWhereCondition = area_ids.length
    ? { area_id: { [Op.in]: area_ids } }
    : {};

    const supervisors = await dbModels.mst_admin.findAll({
      attributes: ['admin_id', 'first_name', 'last_name'],
      where: { created_by: contractorId },
          include: [
            {
              model: dbModels.trx_admin_mapped_areas,
              as: 'trx_admin_mapped_areas',
              attributes: ['admin_id', 'area_id'],
          required: true,
          where: areaWhereCondition,
              include: [
                {
              model: dbModels.mst_areas,
              as: 'area',
              attributes: ['area_name', 'geographical_area_id', 'area_id'],
              required: true,
                  include: [
                    {
                  model: dbModels.mst_geographical_areas,
                  as: 'geographical_area',
                  attributes: ['geographical_area_id'],
                  required: true,
                }
              ]
              
            }
          ]
        },
        {
          model: dbModels.trx_lmc_connection_mapping,
          as: 'trx_lmc_connection_mappings',
          attributes: ['customer_connection_id'],
          required: false,
          include: [
            {
              model: dbModels.trx_mapped_lmc_connection_stages,
              as: 'trx_mapped_lmc_connection_stages',
              attributes: ['lmc_stage', 'tpi_status', 'is_active'],
              required: false,
              where : { is_latest : 1}
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return supervisors;
  } catch (error) {
    console.error('Error in getSupervisorsTracking:', error);
    throw error;
  }
};

const getDashboardMetrics = async (area_ids) => {
  try {
    const areaWhereCondition = area_ids.length
    ? { area_id: { [Op.in]: area_ids } }
    : {};
    const supervisors = await dbModels.mst_geographical_areas.findAll({
      attributes: ['region_name', 'ga_area_name', 'geographical_area_id'],
      include: [
        {
          model: dbModels.mst_areas,
          as: 'mst_areas',
          attributes: ['area_name'],
          where: areaWhereCondition,
          include: [
            {
              model: dbModels.trx_admin_mapped_areas,
              as: 'trx_admin_mapped_areas',
              attributes: ['admin_id', 'area_id'],
              include: [
                {
                  model: dbModels.mst_admin,
                  as: 'admin',
                  attributes: [
                    'admin_id',
                    'first_name',
                    'last_name',
                    'email',
                    'mobile',
                    'alternate_mobile',
                    'persona_role_id',
                    'is_active'
                  ],
                  required : true,
                  include: [
                    {
                      model: dbModels.trx_lmc_connection_mapping,
                      as: 'trx_lmc_connection_mappings',
                      attributes: [
                        'lmc_connection_mapping_id',
                        'customer_connection_id',
                        'lmc_id',
                        'created_at',
                        'updated_at'
                      ],
                      include: [
                        {
                          model: dbModels.trx_mapped_lmc_connection_stages,
                          as: 'trx_mapped_lmc_connection_stages',
                          attributes: [
                            'mapped_lmc_connection_stage_id',
                        'lmc_stage',
                        'visit_status',
                        'tpi_status',
                            'visit_datetime',
                            'actual_visit_date',
                            'created_at'
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return supervisors;
  } catch (error) {
    console.error('Error in getDashboardMetrics:', error);
    throw error;
  }
};

const getDashboardMetricsCounts = async (area_ids = [], contractorId) => {
  const areaWhereCondition = area_ids.length
    ? { area_id: { [Op.in]: area_ids } }
    : {};
    areaWhereCondition.admin_id = contractorId;
  // Total geographical areas linked to contractor via area mappings
  const totalGeographicalAreas = await dbModels.mst_geographical_areas.count({
    include: [
      {
        model: dbModels.mst_areas,
        as: 'mst_areas',
        include: [
          {
            model: dbModels.trx_admin_mapped_areas,
            as: 'trx_admin_mapped_areas',
            where: areaWhereCondition,
            include: [
              {
                model: dbModels.mst_admin,
                as: 'admin',
                where: { admin_id: contractorId }
              }
            ]
          }
        ]
      }
    ]
  });

  // Total areas covered (filtered by area_ids)
  const totalAreasCovered = await dbModels.mst_areas.count({
    where: areaWhereCondition
  });

  // Total supervisors mapped to areas
  const totalSupervisors = await dbModels.mst_admin.count({
    where: { user_role: 'supervisor' ,created_by: contractorId },
    include: [
      {
        model: dbModels.trx_admin_mapped_areas,
        as: "trx_admin_mapped_areas",
        required: true,
        where: areaWhereCondition
      }
    ],
    distinct: true
  });

  // Total customer connections
  const totalCustomers = await countConnections({ area_ids });

  const totalConnectionsFeasibility = await countConnections({
    area_ids,
    stage: "feasibility-check",
  });

  const totalConnectionsInstallation = await countConnections({
    area_ids,
    stage: "installation",
  });

  const totalConnectionsCommissioning = await countConnections({
    area_ids,
    stage: "commissioning",
  });

  const totalActiveConnections = await countConnections({
    area_ids,
    isActive: true,
  });

  const totalRejectedTickets = await countConnections({
    area_ids,
    rejectionStatus: "rejected",
  });

  return {
    totalGeographicalAreas,
    totalAreasCovered,
    totalSupervisors,
    totalCustomers,
    totalConnectionsFeasibility,
    totalConnectionsInstallation,
    totalConnectionsCommissioning,
    totalActiveConnections,
    totalRejectedTickets,
  };
};



// Helper function to count connections with common logic
async function countConnections({
  stage,
  isActive = 1,
  rejectionStatus,
  area_ids,
}) {
  const stageWhere = {};
  if (stage) stageWhere.lmc_stage = stage;
  if (rejectionStatus) stageWhere.tpi_status = rejectionStatus;

  const areaWhereCondition = area_ids?.length
    ? { area_id: { [Op.in]: area_ids } }
    : {};

  return await dbModels.trx_lmc_connection_mapping.count({
    where: { is_active: isActive },
    include: [
      {
        model: dbModels.trx_mapped_lmc_connection_stages,
        as: "trx_mapped_lmc_connection_stages",
        required: true,
        where: stageWhere,
      },
      {
        model: dbModels.mst_admin,
        as: "lmc",
        required: true,
        include: [
          {
            model: dbModels.trx_admin_mapped_areas,
            as: "trx_admin_mapped_areas",
            required: true,
            where: areaWhereCondition, 
          },
        ],
      },
    ],
    distinct: true,
  });
}


const assignMCSupervisor = async (supervisorId, customerConnectionId) => {
  const transaction = await sequelize.transaction();
  try {
    // Validate supervisor exists
    const supervisor = await dbModels.mst_admin.findOne({
      where: { admin_id: supervisorId },
      transaction,
    });

    if (!supervisor) {
      await transaction.rollback();
      return { success: false, message: "Supervisor not found." };
    }

    // Check if mapping already exists
    let mapping = await dbModels.trx_lmc_connection_mapping.findOne({
      where: { customer_connection_id: customerConnectionId },
      transaction,
    });

    if (!mapping) {
      // Create new mapping record
      const newMapping = await dbModels.trx_lmc_connection_mapping.create({
        customer_connection_id: customerConnectionId,
        lmc_id: supervisorId,
        created_by: supervisorId,
        updated_by: supervisorId,
        updated_at: new Date(),
      }, { transaction });

      // Create corresponding stage entry
      await dbModels.trx_mapped_lmc_connection_stages.create({
        lmc_connection_mapping_id: newMapping.lmc_connection_mapping_id,
        lmc_stage: 'feasibility-check',
        form_no: generateCode('FORM'),
        service_order_no: generateCode('SO'),
        service_request_no: generateCode('SR', 5),
        visit_status: 'not-scheduled',
        tpi_status: 'no-submission',
        created_by: supervisorId,
        updated_by: supervisorId,
        updated_at: new Date(),
      }, { transaction });

    } else {
      // Update existing mapping
      await dbModels.trx_lmc_connection_mapping.update(
        {
          lmc_id: supervisorId,
          updated_by: supervisorId,
          updated_at: new Date(),
        },
        {
          where: { customer_connection_id: customerConnectionId },
          transaction,
        }
      );
    }

    // Update customer connection status
    await updateCustomerConnectionStatus(customerConnectionId, 'onboarding-schedule-pending', 'Team-visit-schedule-is-in-progress');

    await transaction.commit();
    return {
      success: true,
      message: `Supervisor ${supervisor.first_name} ${supervisor.last_name} (LMC${supervisor.admin_id}) ${mapping ? "successfully reassigned" : "assigned with new mapping"}.`,
    };

  } catch (error) {
    await transaction.rollback();
    console.error("Error in assignMCSupervisor:", error);
    return { success: false, message: "Failed to assign supervisor.", error: error.message };
  }
};

const getLmcCustomerAssignmentsTicket = async (customer_connection_id) => {
  try {
    const row = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id },
      attributes: [
        "customer_connection_id",
        "first_name",
        "last_name",
        "email",
        "mobile",
        "bp_id"
      ],
      include: [
        {
          model: dbModels.trx_customer_meter,
          as: "trx_customer_meters",
          attributes: ["meter_type", "meter_no"],
          required: false,
          include: [
            {
              model: dbModels.trx_customer_meter_readings,
              as: "trx_customer_meter_readings",
              attributes: ["meter_reading_file"],
              required: false,
            },
          ],
        },
        {
          model: dbModels.trx_lmc_connection_mapping,
          as: "trx_lmc_connection_mappings",
          required: false,
          attributes: ["lmc_id", "updated_at"],
          include: [
            {
              model: dbModels.trx_mapped_lmc_connection_stages,
              as: "trx_mapped_lmc_connection_stages",
              attributes: [
                "mapped_lmc_connection_stage_id",
                "lmc_stage",
                "visit_status",
                "tpi_status",
                "created_at",
                "visit_datetime",
              ],
              required: false,
              include: [
                {
                  model: dbModels.trx_lmc_pipeline_documents,
                  as: "trx_lmc_pipeline_documents",
                  where: { file_type: "isometric-drawing" },
                  required: false,
                  attributes: [
                    "lmc_pipeline_document_id",
                    "file_type",
                    "file_name",
                    "file_path",
                    "file_status",
                    "verification_status",
                    "is_active",
                  ],
                },
              ],
            },
            {
              model: dbModels.mst_admin,
              as: "lmc",
              attributes: [
                "admin_id",
                "first_name",
                "last_name",
                "email",
                "mobile",
              ],
              required: false,
            },
          ],
        },
      ],
      raw: false,
    });

    if (!row) return { case: null };

    const customer = row;
    const meter = customer?.trx_customer_meters?.[0] || {};
    const meterReading = meter?.trx_customer_meter_readings?.[0] || {};

    // Get the latest mapping based on updated_at (optional: sort if multiple)
    const mapping = (customer?.trx_lmc_connection_mappings || [])[0] || {};
    const stage = mapping?.trx_mapped_lmc_connection_stages || {};
    const drawing = stage?.trx_lmc_pipeline_documents?.[0] || {};
    const lmc = mapping?.lmc || {};

    return {
      case: {
        mapped_lmc_customer_connection_id: stage.mapped_lmc_connection_stage_id || "N/A",
        customer_connection_id: customer.customer_connection_id,
        bp_id: customer.bp_id,
        customer: {
          name: `${customer.first_name || ""} ${customer.last_name || ""}`.trim(),
          email: customer.email || "N/A",
          phone: customer.mobile || "N/A",
        },
        lmc_stage: stage.lmc_stage || "N/A",
        visit_status: stage.visit_status || "Unknown",
        visit_datetime: stage.visit_datetime || "N/A",
        tpi_status: stage.tpi_status || "Not submitted",
        pipeline_details: {
          isometric_drawing: {
            file_id: drawing?.lmc_pipeline_document_id || "N/A",
            file_name: drawing?.file_name || "N/A",
            file_type: drawing?.file_type || "N/A",
            file_path: drawing?.file_path || meterReading?.meter_reading_file || "N/A",
            file_status: drawing?.file_status || "N/A",
            verification_status: drawing?.verification_status || "N/A",
            is_active: drawing?.is_active || false,
          },
        },
        meter_details: {
          meter_type: meter?.meter_type || "N/A",
          meter_no: meter?.meter_no || "N/A",
          meter_file_path: meterReading?.meter_reading_file || "N/A",
        },
        assigned_lmc_details: {
          assignedLMCDate: mapping?.updated_at || "N/A",
          AssignmentStatus: lmc?.admin_id ? "Assigned" : "To be assigned",
          assignedLMCName: `${lmc?.first_name || ""} ${lmc?.last_name || ""}`.trim(),
          assignedLMCID: lmc?.admin_id || "N/A",
        },
        created_at: stage.created_at || "N/A",
      },
    };
  } catch (error) {
    console.error("Error fetching LMC assignment ticket:", error);
    return { error: "Internal server error" };
  }
};


const updateCustomerConnectionStatus = async (
  customer_connection_id,
  newStatus,
  masterValue
) => {
  const transaction = await sequelize.transaction();

  try {
    console.log("customer_connection_id:", customer_connection_id);
    console.log("newStatus:", newStatus);

    if (!customer_connection_id || !newStatus) {
      return {
        success: false,
        message: "Missing customer_connection_id or status.",
      };
    }

    let reason = null;

    // 1. Check for reason in mst_masters if masterValue is provided
    if (masterValue) {
      reason = await dbModels.mst_masters.findOne({
        where: { master_value: masterValue },
        raw:true,
        transaction,
      });
    }

    // 2. Build update object dynamically
    const updateData = { status: newStatus };
    if (reason) {
      updateData.reason_id = reason.master_id;
    }

    // 3. Perform update
    const [updatedRows] = await dbModels.trx_customer_connection.update(
      updateData,
      {
        where: { customer_connection_id },
        transaction,
      }
    );

    console.log("Updated rows:", updatedRows);

    if (updatedRows === 0) {
      await transaction.rollback();
      return {
        success: false,
        message: "No matching customer found or status unchanged.",
      };
    }

    await transaction.commit();
    return {
      success: true,
      message: "Status updated successfully.",
    };

  } catch (error) {
    await transaction.rollback();
    console.error("Error updating customer connection status:", error);
    return {
      success: false,
      message: "Failed to update status.",
      error: error.message,
    };
  }
};

const getGeoAreaByUser = async (contractorId,area_ids) => {
  try {
    const areaWhereCondition = area_ids?.length
    ? { area_id: { [Op.in]: area_ids } }
    : {};
    const supervisors = await dbModels.mst_geographical_areas.findAll({
      attributes: ['region_name', 'ga_area_name', 'geographical_area_id'],
      include: [
        {
          model: dbModels.mst_areas,
          as: 'mst_areas',
          attributes: ['area_name','geographical_area_id','area_id'],
          required: true,
          include: [
            {
              model: dbModels.trx_admin_mapped_areas,
              as: 'trx_admin_mapped_areas',
              attributes: ['admin_id', 'area_id'],
              required: true,
              where: areaWhereCondition, 
              include: [
                {
                  model: dbModels.mst_admin,
                  as: 'admin',
                  attributes: [
                    'admin_id',
                    'first_name',
                    'last_name'
                  ],
                  required: true,
                  where : {admin_id : contractorId}
                }
              ]
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return supervisors;

  } catch (error) {
    console.error('Error in getSupervisorsTracking:', error);
    throw error;
  }
};

const getAreaIdFromCustomerConnection = async (customer_connection_id) => {
  try {
    const customerConnection = await dbModels.trx_customer_connection.findOne({
      attributes: ['customer_connection_id'],
      where: { customer_connection_id },
      include: [
        {
          model: dbModels.mst_available_pngareas,
          attributes: ['pincode_id'],
          as: 'available_pngarea',
          include: [
            {
              model: dbModels.mst_pincodes,
              as: 'pincodeAreasAlias',
              attributes: ['pincode_id', 'area_id'],
            },
          ],
        },
      ],
      raw : true
    });

    const areaIds = customerConnection || [];

    

    return areaIds;
  } catch (error) {
    console.error('Error in getAreaIdFromCustomerConnection:', error);
    throw error;
  }
};




module.exports = {
  getLmcCustomerAssignments,
  getLMCSupervisorById,
  createLMCSupervisor,
  getAllLMCSupervisors,
  revokeLMCSupervisor,
  getMyLMCSupervisors,
  getGeographicalAreas,
  getSupervisorsTracking,
  getDashboardMetrics,
  getDashboardMetricsCounts,
  assignMCSupervisor,
  getLmcCustomerAssignmentsTicket,
  getGeoAreaByUser,
  getAreaIdFromCustomerConnection,
  getLMCSupervisor
};
