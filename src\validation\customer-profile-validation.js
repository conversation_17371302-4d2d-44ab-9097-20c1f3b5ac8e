const { Joi } = require('express-validation');
const { PHONE_NO_INVALID, PINCODE_INVALID } = require('../constant/messages');
const {
  MOBILE_REGEX,
  ALPHASPACE_REGEX,
  PINCODE_REGEX,
} = require('../constant/regex');

const addUpdateEmailValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    email: Joi.string().email().required(),
    type: Joi.string().valid("next", "done").required(),
  }).required(),
};

const emailOtpVerifyValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    email: Joi.string().email().required(),
    OTP: Joi.number().integer().required(),
  }).required(),
};

const sendOtpValidation = {
  body: Joi.object({
    email: Joi.string().email().allow('', null).optional(),
    number: Joi.string().regex(MOBILE_REGEX).length(10).messages({
      'string.pattern.base': 'Mobile number must be exactly 10 digits.',
      'string.length': 'Mobile number must be exactly 10 digits.'
    }).required(),
  }).required(),
};

const emailAndSmsOtpVerifyValidation = {
  body: Joi.object({
    email: Joi.string().email().required(),
    number: Joi.string().regex(MOBILE_REGEX).length(10).messages({
      'string.pattern.base': 'Mobile number must be exactly 10 digits.',
      'string.length': 'Mobile number must be exactly 10 digits.'
    }).required(),
    OTP: Joi.number().integer().required(),
  }).required(),
};

const addUpdateNumberValidation = {
  body: Joi.object({
  
    number: Joi.string().regex(MOBILE_REGEX).length(10).messages({
      'string.pattern.base': 'Mobile number must be exactly 10 digits.',
      'string.length': 'Mobile number must be exactly 10 digits.'
    }).required(),
    old_number: Joi.string().regex(MOBILE_REGEX).length(10).messages({
      'string.pattern.base': 'Mobile number must be exactly 10 digits.',
      'string.length': 'Mobile number must be exactly 10 digits.'
    }).required(),
  }).required(),
};

const addUpdateAltNumberValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    alt_number: Joi.string().regex(MOBILE_REGEX).length(10).messages({
      'string.pattern.base': 'Mobile number must be exactly 10 digits.',
      'string.length': 'Mobile number must be exactly 10 digits.'
    }).required(),
    type: Joi.string().valid("next", "done").required(),
  }).required(),
};

const altNumberOtpVerifyValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    alt_number: Joi.string().regex(MOBILE_REGEX).length(10).messages({
      'string.pattern.base': 'Mobile number must be exactly 10 digits.',
      'string.length': 'Mobile number must be exactly 10 digits.'
    }).required(),
    OTP: Joi.number().integer().required(),
  }).required(),
};

module.exports = {
  addUpdateEmailValidation,
  emailOtpVerifyValidation,
  sendOtpValidation,
  emailAndSmsOtpVerifyValidation,
  addUpdateNumberValidation,
  addUpdateAltNumberValidation,
  altNumberOtpVerifyValidation,
};
