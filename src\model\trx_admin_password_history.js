const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_admin_password_history', {
    admin_password_history_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    admin_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_admin',
        key: 'admin_id'
      }
    },
    password: {
      type: DataTypes.BLOB,
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_admin_password_history',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_admin_password_history_id",
        unique: true,
        fields: [
          { name: "admin_password_history_id" },
        ]
      },
    ]
  });
};
