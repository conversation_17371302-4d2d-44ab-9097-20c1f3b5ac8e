const reviewRepository = require('../repository/review-repository');
const redisHelper = require('../helpers/redisHelper');

exports.processReviewDetails = async (reviewData, files) => {
  try {
    // Save file details and metadata in database
    const savedData = await reviewRepository.storeReviewDetails(reviewData, files);

    // Store the review data in Redis cache
    const redisKey = `review_${reviewData.user_id}_${Date.now()}`;
    await redisHelper.setData(redisKey, savedData, 3600); // Cache for 1 hour

    return savedData;
  } catch (error) {
    throw new Error('Error processing review details: ' + error.message);
  }
};
