const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('mst_persona_role', {
    persona_role_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    role_name: {
      type: DataTypes.STRING(10),
      allowNull: false,
      unique: "UK_mstpersona_role_role_name"
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'mst_persona_role',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_persona_role_id",
        unique: true,
        fields: [
          { name: "persona_role_id" },
        ]
      },
      {
        name: "UK_mstpersona_role_role_name",
        unique: true,
        fields: [
          { name: "role_name" },
        ]
      },
    ]
  });
};
