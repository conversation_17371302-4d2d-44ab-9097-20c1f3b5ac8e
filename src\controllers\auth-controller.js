const { sendSuccess, sendError } = require("../helpers/responseHelper");
const authService = require("../services/auth-service");
const { blacklist } = require("../middleware/auth");

exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;

    const { user } = await authService.login(username, password);
    sendSuccess(req, res, { user }, "OTP has been sent successfully");
  } catch (error) {
    const statusCode = error.message.includes("pending approval") ? 403 : 401;
    sendError(req, res, null, error.message || "Login failed", statusCode);
  }
};

exports.verifyOTP = async (req, res) => {
  try {
    const { user, token } = await authService.verifyOTP(req);
    sendSuccess(req, res, { user, token }, "Login Successfully.");
  } catch (error) {
    sendError(req, res, error, error.message || "Error in verify otp.");
  }
};
exports.resetPassword = async (req, res) => {
  try {
    const data = await authService.resetPassword(req);
    sendSuccess(req, res, data, "Password Change Successfully.");
  } catch (error) {
    sendError(req, res, error, error.message || "Error in verify otp.");
  }
};

exports.logout = async (req, res) => {
  try {
    const token = (
      req.headers["authorization"] || req.headers["Authorization"]
    )?.replace("Bearer ", "");
    if (!token) {
      return sendError(req, res, null, "No token provided");
    }

    blacklist.add(token);
    sendSuccess(req, res, null, "Logged out successfully");
  } catch (error) {
    console.error("Logout error:", error);
    sendError(req, res, null, "Failed to logout");
  }
};

exports.handleMicrosoftLogin = async (req, res) => {
  try {
    console.log("req body",req.body)
    const  accessTokentoken  = req.body.token;
    const { user, token } = await authService.verifyMicrosoftToken(accessTokentoken);
    
    sendSuccess(req, res, { user, token }, "User loggedIn successfully with SSO.");
  } catch (error) {
    console.error("SSO login error:", error);
    sendError(req, res, null, error.message || "Failed to SSO Login");
  }
};

module.exports = {
  login: exports.login,
  logout: exports.logout,
  verifyOTP: exports.verifyOTP,
  resetPassword: exports.resetPassword,
  handleMicrosoftLogin:exports.handleMicrosoftLogin
};
