const { Joi } = require('express-validation');

const getMROListingValidation = {
  body: Joi.object({
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).default(10).optional(),
    search: Joi.string().optional(),
    geographical_area_id: Joi.number().integer().positive().optional(),
    area_ids: Joi.string()
      .pattern(/^\d+(,\d+)*$/)
      .optional()
      .messages({
        "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
      })
  }),
};

const meterReadingUploadValidation = {
  body: Joi.object({
    current_reading: Joi.number().required().messages({
      'any.required': 'Current reading is required.',
      'number.base': 'Current reading must be a number.',
    }),
    mro_id: Joi.number().integer().optional().messages({
      'any.optional': 'MRO ID is required.',
      'number.base': 'MRO ID must be a number.',
    }),
    mru_id: Joi.number().integer().optional().messages({
      'any.required': 'MRU  ID is required.',
      'number.base': 'MRU  ID must be a number.',
    }),
    meter_no: Joi.string().required().messages({
      'any.required': 'Meter number is required.',
      'string.base': 'Meter number must be a string.',
    }),
    remarks: Joi.string().optional().allow('').messages({
      'string.base': 'Remark must be a string.',
    }),
    area_ids: Joi.string()
      .pattern(/^\d+(,\d+)*$/)
      .optional()
      .messages({
        "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
      })
  }),
};

const getMROCustomerDetailsValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number()
      .integer()
      .required()
      .messages({
        "any.required": "Customer Connection ID is required.",
        "number.base": "Customer Connection ID must be a valid number.",
        "number.integer": "Customer Connection ID must be an integer.",
      }),
  }),
}

const getMRODetailsValidation = {
  body: Joi.object({
    mro_ticket: Joi.string()
      .trim()
      .required()
      .messages({
        "any.required": "MRO Ticket is required.",
        "string.base": "MRO Ticket must be a valid string.",
        "string.empty": "MRO Ticket cannot be empty.",
      }),
  }),
};
const dashboardMetricsValidator = Joi.object({
  geographical_area_id: Joi.number().integer().positive().optional(),
});



module.exports = {
  getMROListingValidation,
  meterReadingUploadValidation,
  getMROCustomerDetailsValidation,
  getMRODetailsValidation,
  dashboardMetricsValidator
  
};
