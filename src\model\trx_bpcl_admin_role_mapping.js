const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_bpcl_admin_role_mapping', {
    bpcl_admin_role_mapping_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    bpcl_admin_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_bpcl_admin',
        key: 'bpcl_admin_id'
      }
    },
    bpcl_admin_role_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_bpcl_admin_roles',
        key: 'bpcl_admin_role_id'
      }
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_bpcl_admin_role_mapping',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_bpcl_admin_role_mapping_id",
        unique: true,
        fields: [
          { name: "bpcl_admin_role_mapping_id" },
        ]
      },
    ]
  });
};
