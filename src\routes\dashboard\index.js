const { validate } = require('express-validation');
const express = require('express');
const dashBoardRoutes = express.Router();
const dashBoardRoutesController = require('../../controllers/dash-board-controller');
const {
  graphDataValidation,
  notificationValidation,
  viewNotificationValidation,
} = require('../../validation/dash-board-validation');

const {
  verifyBpclToken,
  verifyPngToken,
} = require('../../middleware/customer-auth');

dashBoardRoutes.post(
  '/graph-data',
  verifyPngToken('guest'),
  validate(graphDataValidation),
  dashBoardRoutesController.dashBoarGraphdDetails
);
dashBoardRoutes.get(
  '/notification',
  verifyPngToken('guest'),
  validate(notificationValidation),
  dashBoardRoutesController.notification
);
dashBoardRoutes.get(
  '/banner',
  verifyPngToken('guest'),
  dashBoardRoutesController.banner
);
dashBoardRoutes.post(
  '/view-notification',
  verifyPngToken('guest'),
  validate(viewNotificationValidation),
  dashBoardRoutesController.viewNotification
);
module.exports = { dashBoardRoutes };
