const { sendSuccess, sendError } = require("../helpers/responseHelper");
const FOContractorService = require("../services/fo-admin-service");
const FOContractorRepository = require("../repository/fo-admin-repository");
const crypto = require("crypto");
const { encryptPassword } = require("../utils/encrypt-decrypt");
const { generatePassword } = require("../utils/commonFn");
const SASTokenHelper = require("../helpers/azureSASSDKHelper");

//Create FO Supervisor
exports.createFoSupervisor = async (req, res) => {
  try {
    const existingUser = await FOContractorRepository.getFoSupervisorById(
      req.body.email,
      req.body.mobile
    );

    if (existingUser) {
      return sendError(
        req,
        res,
        null,
        "Supervisor with this email or mobile already exists",
        400
      );
    }

    const plainPassword = generatePassword();
    const encryptedPassword = encryptPassword(plainPassword);

        let filePath = "fo_admin/fo_" + req?.user?.admin_id + "/fo_supervisor/onboarding";
        const permission = 'racwd';
        const sasToken = await SASTokenHelper.generateSASToken(permission);
        const imageUploaded = await SASTokenHelper.azureSingleFileUpload(req, {}, sasToken, filePath);

    if (typeof req.body.area_ids === 'string') {
      req.body.area_ids = req.body.area_ids
        .split(',')
        .map(id => parseInt(id.trim(), 10))
        .filter(id => !isNaN(id));
    }

    const supervisorData = {
      first_name: req.body.first_name,
      last_name: req.body.last_name,
      email: req.body.email,
      mobile: req.body.mobile,
      bpclAdminId: req?.user?.bpcl_admin_id,
      password: encryptedPassword,
      persona_role_id: req?.user?.persona_role_id,
      area_ids: req.body.area_ids
    };

    const supervisor = await FOContractorRepository.createFOSupervisor(
      supervisorData,
      req?.user?.admin_id,
      filePath,
      imageUploaded?.filename
    );

    // TODO: Send Email with credentials

    const supervisorJson = supervisor.toJSON();
    const { password, city, state, last_logged_in, ...supervisorResponse } =
      supervisorJson;

    sendSuccess(
      req,
      res,
      supervisorResponse,
      "Supervisor created successfully",
      201
    );
  } catch (error) {
    console.error("Error in createSupervisor:", error);
    sendError(
      req,
      res,
      null,
      error.message || "Failed to create supervisor",
      500
    );
  }
};

//Get all supervisors
exports.getAllFOSupervisors = async (req, res) => {
  try {
    const supervisors = await FOContractorService.getAllFOSupervisors(req);
    sendSuccess(
      req,
      res,
      supervisors,
      "Supervisors retrieved successfully",
      200
    );
  } catch (error) {
    console.error("Error in getAllSupervisors:", error);
    sendError(
      req,
      res,
      null,
      error.message || "Failed to retrieve supervisors",
      500
    );
  }
};

//Get MRO Ticket Details
exports.getMROTicketDetails = async (req, res) => {
  try {
    const result = await FOContractorService.getMROTicketDetails(req.body);
    sendSuccess(req, res, result, "Data fetched successfully", 200);
  } catch (error) {
    sendError(req, res, null, error.message, 500);
  }
};

//Get Detailed MRO Customer Info
exports.getDetailedMROCustomerInfo = async (req, res) => {
  try {
    const result = await FOContractorService.getDetailedMROCustomerInfo(
      req.body
    );
    sendSuccess(req, res, result, "Data fetched successfully", 200);
  } catch (error) {
    sendError(req, res, null, error.message, 500);
  }
};

//Assign Supervisors to Operation
exports.assignOSupervisors = async (req, res) => {
  try {
    const supervisors = await FOContractorService.assignOSupervisors(req);
    sendSuccess(
      req,
      res,
      supervisors,
      "Supervisors assigned successfully",
      200
    );
  } catch (error) {
    console.error("Error in assignOSupervisors:", error);
    sendError(
      req,
      res,
      null,
      error.message || "Failed to assign supervisors",
      500
    );
  }
};

//Revoke LMC Supervisor
exports.revokeFOSupervisor = async (req, res) => {
  try {
    const result = await FOContractorService.revokeFOSupervisor(req);
    sendSuccess(req, res, result, "Supervisor revoked successfully", 200);
  } catch (error) {
    console.error("Error in revokeFOSupervisor:", error);
    sendError(
      req,
      res,
      null,
      error.message || "Failed to revoke supervisor",
      500
    );
  }
};

//MRO KPIs
exports.getMROKPIs = async (req, res) => {
  try {
    const data = await FOContractorService.getMROKPIs(req);
    sendSuccess(req, res, data, "KPIs retrieved successfully", 200);
  } catch (error) {
    console.error("Error in getMROKPIs:", error);
    sendError(req, res, null, error.message || "Failed to retrieve KPIs", 500);
  }
};

//Upload Meter Reading
exports.meterReadingUploads = async (req, res) => {
  try {
    if (!req.file) {
      return sendError(req, res, {}, "Please upload meter reading image.", 400);
    }

    const supervisorId = req.user.admin_id;
    const result = await FOContractorService.saveMeterReading(
      req.body,
      req.file,
      supervisorId
    );

    sendSuccess(req, res, result, "Meter reading uploaded successfully", 200);
  } catch (error) {
    sendError(req, res, null, error.message || "Upload failed", 500);
  }
};

//Area Metrics
exports.getAreaMetrics = async (req, res) => {
  try {
    const supervisorId = req.user.admin_id;
    const result = await FOContractorService.getAreaMetrics(supervisorId);
    sendSuccess(req, res, result, "Metrics fetched successfully", 200);
  } catch (error) {
    sendError(req, res, null, error.message || "Failed to fetch metrics", 500);
  }
};

//MRO Listing
exports.getMROListing = async (req, res) => {
  try {
    const result = await FOContractorService.getMROListing(req);

    if (result?.notFound) {
      return sendError(
        req,
        res,
        { statusCode: 404 },
        "No MRO records found.",
        404
      );
    }

    sendSuccess(req, res, result, "Fetched data successfully", 200);
  } catch (error) {
    sendError(
      req,
      res,
      null,
      error.message || "Failed to fetch MRO listing",
      500
    );
  }
};
exports.getSupervisorMROListing = async (req, res) => {
  try {
    const result = await FOContractorService.getSupervisorMROListing(req);

    if (result?.notFound) {
      return sendError(
        req,
        res,
        { statusCode: 404 },
        "No MRO records found.",
        404
      );
    }

    sendSuccess(req, res, result, "Fetched data successfully", 200);
  } catch (error) {
    sendError(
      req,
      res,
      null,
      error.message || "Failed to fetch MRO listing",
      500
    );
  }
};
