const express = require('express');
const { validate } = require("express-validation");
const {
  createApplicationStatusValidation,
  updateApplicationStatusValidation,
  deleteApplicationStatusValidation,
  getApplicationStatusValidation,
} = require("../../validation/application-status-validation"); // Assuming validation file

const applicationStatusRoutes = express.Router();
const applicationStatusController = require('../../controllers/application-status-controller');

// Get list of application statuses
applicationStatusRoutes.get('/list', validate(getApplicationStatusValidation), applicationStatusController.getApplicationStatusList);

// Create new application status
applicationStatusRoutes.post('/create', validate(createApplicationStatusValidation), applicationStatusController.createApplicationStatus);

// Update existing application status
applicationStatusRoutes.put('/update', validate(updateApplicationStatusValidation), applicationStatusController.updateApplicationStatus);

// Soft delete application status
applicationStatusRoutes.delete('/delete', validate(deleteApplicationStatusValidation), applicationStatusController.deleteApplicationStatus);

module.exports = applicationStatusRoutes;
