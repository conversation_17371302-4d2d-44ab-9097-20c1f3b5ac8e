const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_mapped_lmc_connection_stages",
    {
      mapped_lmc_connection_stage_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      lmc_connection_mapping_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "trx_lmc_connection_mapping",
          key: "lmc_connection_mapping_id",
        },
        unique: "UK_trx_mlcs_lcmi_ls_fo_son_srn_vs_tei_ts",
      },
      lmc_stage: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique: "UK_trx_mlcs_lcmi_ls_fo_son_srn_vs_tei_ts",
      },
      form_no: {
        type: DataTypes.STRING(10),
        allowNull: false,
        unique: "UK_trx_mlcs_lcmi_ls_fo_son_srn_vs_tei_ts",
      },
      service_order_no: {
        type: DataTypes.STRING(10),
        allowNull: false,
        unique: "UK_trx_mlcs_lcmi_ls_fo_son_srn_vs_tei_ts",
      },
      service_request_no: {
        type: DataTypes.STRING(12),
        allowNull: false,
        unique: "UK_trx_mlcs_lcmi_ls_fo_son_srn_vs_tei_ts",
      },
      visit_datetime: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      actual_visit_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      visit_status: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: "pending",
        unique: "UK_trx_mlcs_lcmi_ls_fo_son_srn_vs_tei_ts",
      },
      tpi_engineer_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_trx_mlcs_lcmi_ls_fo_son_srn_vs_tei_ts",
      },
      tpi_status: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: "pending",
        unique: "UK_trx_mlcs_lcmi_ls_fo_son_srn_vs_tei_ts",
      },
      is_latest: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_mapped_lmc_connection_stages",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_mapped_lmc_connection_stage_id",
          unique: true,
          fields: [{ name: "mapped_lmc_connection_stage_id" }],
        },
        {
          name: "UK_trx_mlcs_lcmi_ls_fo_son_srn_vs_tei_ts",
          unique: true,
          fields: [
            { name: "lmc_connection_mapping_id" },
            { name: "lmc_stage" },
            { name: "form_no" },
            { name: "service_order_no" },
            { name: "service_request_no" },
            { name: "visit_status" },
            { name: "tpi_engineer_id" },
            { name: "tpi_status" },
          ],
        },
      ],
    }
  );
};
