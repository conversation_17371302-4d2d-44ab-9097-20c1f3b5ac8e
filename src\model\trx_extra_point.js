const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_extra_point",
    {
      extra_point_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      customer_connection_address_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection_address",
          key: "customer_connection_address_id",
        },
      },
      extra_point_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
      },
    },
    {
      sequelize,
      tableName: "trx_extra_point",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK__trx_extr__3A4E112797B6BF0B",
          unique: true,
          fields: [{ name: "extra_point_id" }],
        },
      ],
    }
  );
};
