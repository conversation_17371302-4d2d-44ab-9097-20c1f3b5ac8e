const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('mst_agencies', {
    agency_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    agency_name: {
      type: DataTypes.STRING(30),
      allowNull: false,
      unique: "UK_mstagencies_agency_name"
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'mst_agencies',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_agency_id",
        unique: true,
        fields: [
          { name: "agency_id" },
        ]
      },
      {
        name: "UK_mstagencies_agency_name",
        unique: true,
        fields: [
          { name: "agency_name" },
        ]
      },
    ]
  });
};
