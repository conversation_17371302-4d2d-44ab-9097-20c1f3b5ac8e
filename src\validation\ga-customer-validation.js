const { Joi } = require("express-validation");
const moment = require("moment");

const gaCommonValidation = {
  body: Joi.object({
    geographical_area: Joi.number().integer().required(),
    area_id: Joi.array().items(Joi.number()).min(1).required(),
    search: Joi.string().allow('', null).optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).default(10),
  }),
};

const gaCommonCountValidation = {
  body: Joi.object({
    geographical_area: Joi.number().integer().required(),
    area_id: Joi.array().items(Joi.number()).min(1).required(),
  }),
};

module.exports = {
  gaCommonValidation,
  gaCommonCountValidation
};
