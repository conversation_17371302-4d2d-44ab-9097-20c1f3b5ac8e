const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_service_rates",
    {
      service_rate_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      connection_service_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_connection_services",
          key: "connection_service_id",
        },
        unique: "UK_mstservicerates_csi_ai",
      },
      area_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_areas",
          key: "area_id",
        },
        unique: "UK_mstservicerates_csi_ai",
      },
      rates: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0,
      },
      tax: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0,
      },
      upfront: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0,
      },
      handling_fees: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      gst_percentage: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "mst_service_rates",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_service_rate_id",
          unique: true,
          fields: [{ name: "service_rate_id" }],
        },
        {
          name: "UK_mstservicerates_csi_ai",
          unique: true,
          fields: [{ name: "connection_service_id" }, { name: "area_id" }],
        },
      ],
    }
  );
};
