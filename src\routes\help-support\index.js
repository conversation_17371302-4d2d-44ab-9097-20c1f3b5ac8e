const { validate } = require('express-validation');
const verifyToken = require('../../middleware/auth');
const express = require('express');
const helpSupportRoutes = express.Router();
const decrypt = require('../../middleware/encrypt-decrypt');
const helpSupportController = require('../../controllers/help-support-controller');
const { uploadSingle } = require('../../middleware/multer');
const validator = require("../../middleware/validator");
const {
  verifyPngToken,
} = require("../../middleware/customer-auth");

helpSupportRoutes.post('/report-issue',  verifyPngToken("guest"), validator("help-support-validation", "reportIssueValidation"), helpSupportController.reportIssue);
helpSupportRoutes.post('/upload-issue-document',  verifyPngToken("guest"), uploadSingle,helpSupportController.uploadIssueDoc);
helpSupportRoutes.post( "/delete-issue-document", verifyPngToken("guest"), validator("help-support-validation", "deleteDocumentValidation"), helpSupportController.deleteIssueDoc
);
module.exports = helpSupportRoutes;
