const { dbModels, sequelize } = require("../database/connection");
const { Op, Sequelize } = require("sequelize");
const { generateCode, generateTrxID } = require('../utils/commonFn');

const lmcCustomerConnectionModel = dbModels.trx_mapped_lmc_customer_connection;
const trxCustomerConnection = dbModels.trx_customer_connection;
const trxTpiLmcApprove = dbModels.trx_tpi_lmc_approve;
const trxTpiOnboardApprove = dbModels.trx_tpi_onboard_approve;


const getTPICasesSummery = async (tpi_id) => {
  try {
    const results = await lmcCustomerConnectionModel.findAll({
      attributes: [
        [
          Sequelize.fn('COUNT', Sequelize.col('tpi_id')),
          'total_applications_assigned',
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal("CASE WHEN tpi_status = 'inprogress' THEN 1 END")
          ),
          'applications_pending_review',
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal("CASE WHEN tpi_status = 'rework' THEN 1 END")
          ),
          'applications_sent_resubmission',
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal("CASE WHEN tpi_status = 'approved' THEN 1 END")
          ),
          'approved_applications',
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal("CASE WHEN tpi_status = 'rejected' THEN 1 END")
          ),
          'rejected_applications',
        ],
      ],
      where: { tpi_id },
      raw: true,
    });

    if (
      !results.length ||
      Object.values(results[0]).every((value) => value === 0)
    ) {
      throw { status: 404, message: 'No data found for the given tpi_id.' };
    }

    return results[0];
  } catch (error) {
    console.error('Error in getTPICasesSummery:', error.message || error);

    // Re-throw with proper status and message
    throw {
      status: error.status || 500,
      message: error.message || 'An unexpected error occurred.',
    };
  }
};

const tpiApplicationStatus = async (params) => {
  try {
    const {
      tpi_id,
      caseType = 'active',
      search,
      page = 1,
      limit = 100,
      lmc_stage,
      visit_status,
      tpi_status,
    } = params;

    const offset = (page - 1) * limit;
    let whereCondition;
    // Base conditions
    if (caseType === 'active') {
      whereCondition = {
        tpi_id,
        tpi_status: { [Op.not]: ['approved', 'rejected'] },
      };
    } else {
      whereCondition = {
        tpi_id,
        tpi_status: { [Op.in]: ['approved', 'rejected'] },
      };
    }

    // Apply additional filters
    if (lmc_stage) whereCondition.lmc_stage = lmc_stage;
    if (tpi_status) whereCondition.tpi_status = tpi_status;

    // Search condition (customer_connection_id, full_name, email, mobile)
    const searchCondition = search
      ? {
          [Sequelize.Op.or]: [
            {
              '$customer_connection.customer_connection_id$': {
                [Sequelize.Op.like]: `%${search}%`,
              },
            },
            {
              '$customer_connection.first_name$': {
                [Sequelize.Op.like]: `%${search}%`,
              },
            },
            {
              '$customer_connection.last_name$': {
                [Sequelize.Op.like]: `%${search}%`,
              },
            },
            {
              '$customer_connection.email$': {
                [Sequelize.Op.like]: `%${search}%`,
              },
            },
            {
              '$customer_connection.mobile$': {
                [Sequelize.Op.like]: `%${search}%`,
              },
            },
          ],
        }
      : {};

    const { count, rows: cases } =
      await dbModels.trx_mapped_lmc_customer_connection.findAndCountAll({
        attributes: [
          'mapped_lmc_customer_connection_id',
          'lmc_stage',
          'visit_status',
          'tpi_status',
          'visit_datetime',
        ],
        include: [
          {
            model: dbModels.trx_customer_connection,
            as: 'customer_connection',
            attributes: [
              'customer_connection_id',
              'first_name',
              'last_name',
              'email',
              'mobile',
            ],
            include: [
              {
                model: dbModels.trx_customer_connection_address,
                as: 'trx_customer_connection_addresses',
                attributes: ['connection_pincode'],
              },
            ],
          },
          {
            model: dbModels.trx_customer_connection,
            as: 'customer_connection',
            attributes: [
              'customer_connection_id',
              'first_name',
              'last_name',
              'email',
              'mobile',
            ],
            include: [
              {
                model: dbModels.trx_customer_connection_address,
                as: 'trx_customer_connection_addresses',
                attributes: ['connection_pincode'],
              },
            ],
          },
        ],
        where: { ...whereCondition, ...searchCondition },
        limit,
        offset,
        raw: false,
        nest: true,
      });

    const formattedCases = cases.map((caseItem) => ({
      mapped_lmc_customer_connection_id:
        caseItem.mapped_lmc_customer_connection_id,
      lmc_stage: caseItem.lmc_stage,
      visit_status: caseItem.visit_status || 'Unknown',
      visit_datetime: caseItem.visit_datetime || 'N/A',
      tpi_status: caseItem.tpi_status || 'Not submitted',
      customer_connection_id:
        caseItem.customer_connection?.customer_connection_id || 'N/A',
      full_name:
        `${caseItem.customer_connection?.first_name || ''} ${caseItem.customer_connection?.last_name || ''}`.trim(),
      email: caseItem.customer_connection?.email || '',
      mobile: caseItem.customer_connection?.mobile || '',
      pincode:
        caseItem.customer_connection?.trx_customer_connection_addresses[0]
          ?.connection_pincode || 'N/A',
      registration_status: caseItem.tpi_status || 'Unknown',
    }));

    return {
      cases: formattedCases,
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    };
  } catch (error) {
    console.error('Error fetching TPI Application:', error);
    throw error;
  }
};

const getStatusCountRepo = async (tpi_id, lmc_stage, areaIds = []) => {
  try {
    if (!['feasibility-check', 'installation'].includes(lmc_stage)) {
      throw { status: 400, message: 'Invalid lmc_stage provided.' };
    }

    if (!Array.isArray(areaIds) || areaIds.length === 0) {
      throw { status: 400, message: 'areaIds must be a non-empty array.' };
    }

    // let attributes = [];

    // if (lmc_stage === 'feasibility-check') {
    //   attributes = [
    //     // [Sequelize.fn("COUNT", Sequelize.col("trx_mapped_lmc_connection_stages.tpi_engineer_id")), `total_${lmc_stage}_count`],
    //     [Sequelize.fn("COUNT", Sequelize.literal("*")), `total_${lmc_stage}_count`],
    //     [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN tpi_status = 'pending' THEN 1 END")), `${lmc_stage}_review_pending`],
    //     [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN tpi_status = 'completed' THEN 1 END")), `${lmc_stage}_checks_completed`],
    //     [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN tpi_status = 'resubmit' THEN 1 END")), `${lmc_stage}_sent_resubmission`],
    //     [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN tpi_status = 'rejected' THEN 1 END")), `${lmc_stage}_rejected_checked`],
    //   ];
    // } else if (lmc_stage === 'installation') {
    //   attributes = [
    //     [Sequelize.fn("COUNT", Sequelize.col("trx_mapped_lmc_connection_stages.tpi_engineer_id")), `total_${lmc_stage}_count`],
    //     [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN tpi_status IN ('pending', 'pending-eo') THEN 1 END")), `${lmc_stage}_review_pending`],
    //     [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN tpi_status = 'completed-eo' THEN 1 END")), `${lmc_stage}_checks_completed`],
    //     [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN tpi_status IN ('resubmit', 'resubmit-eo') THEN 1 END")), `${lmc_stage}_sent_resubmission`],
    //     [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN tpi_status IN ('rejected', 'rejected-eo') THEN 1 END")), `${lmc_stage}_rejected_checked`],
    //   ];
    // }

    // const whereCondition = {
    //   lmc_stage,
    //   is_active: 1,
    //   is_latest: 1,
    //   // tpi_engineer_id: tpi_id,
    //   tpi_status: { [Sequelize.Op.ne]: 'no-submission' }, 
    //   ...(lmc_stage === 'installation' && { tpi_engineer_id: tpi_id })
    // };

    // const results = await dbModels.trx_mapped_lmc_connection_stages.findAll({
    //   attributes,
    //   where: whereCondition,
    //   include: [
    //     {
    //       model: dbModels.trx_lmc_connection_mapping,
    //       as: 'lmc_connection_mapping',
    //       required: true,
    //       attributes: [],
    //       where: { is_active: 1 },
    //       include: [
    //         {
    //           model: dbModels.trx_customer_connection,
    //           as: 'customer_connection',
    //           required: true,
    //           attributes: [],
    //           include: [
    //             {
    //               model: dbModels.mst_available_pngareas,
    //               as: 'available_pngarea',
    //               required: true,
    //               attributes: [],
    //               include: [
    //                 {
    //                   model: dbModels.mst_pincodes,
    //                   as: 'pincodeAreasAlias',
    //                   required: true,
    //                   attributes: [],
    //                   where: {
    //                     area_id: { [Sequelize.Op.in]: areaIds }
    //                   }
    //                 }
    //               ]
    //             }
    //           ]
    //         }
    //       ]
    //     }
    //   ],
    //   raw: true,
    //   subQuery: false,
    //   group: []
    // });

    // if (
    //   !results.length ||
    //   Object.values(results[0]).every((value) => parseInt(value) === 0)
    // ) {
    //   return {
    //     status: 200,
    //     message: 'No data found for the given parameters.',
    //     data: {}
    //   };
    // }

    const query = `
      SELECT 
    tmlcs.tpi_status,
    COUNT(*) AS status_count
FROM 
    trx_mapped_lmc_connection_stages tmlcs
INNER JOIN 
    trx_lmc_connection_mapping tlcm ON tmlcs.lmc_connection_mapping_id = tlcm.lmc_connection_mapping_id
INNER JOIN 
    trx_customer_connection tcc ON tlcm.customer_connection_id = tcc.customer_connection_id
LEFT JOIN 
    mst_available_pngareas map ON tcc.available_pngareas_id = map.available_pngareas_id
LEFT JOIN 
    mst_pincodes mp ON map.pincode_id = mp.pincode_id
WHERE 
    tmlcs.lmc_stage = :lmc_stage
    AND (tmlcs.tpi_engineer_id IS NULL OR tmlcs.tpi_engineer_id = :tpi_engineer_id)
    AND tmlcs.is_active = 1
	AND tmlcs.is_latest = 1
    AND tlcm.is_active = 1
    AND tcc.is_active = 1
    AND mp.area_id IN (:areaIds) 
	AND tmlcs.tpi_status NOT IN ('no-submission')
GROUP BY 
    tmlcs.tpi_status;
    `;

    const results = await sequelize.query(query, {
      
      replacements: {
        lmc_stage,
        areaIds,
        tpi_engineer_id: tpi_id,
      },
      // logging: console.log,
      type: sequelize.QueryTypes.SELECT,
    });
    const statusSummary = {
      total_pending: 0,
      total_rejected: 0,
      total_resubmit: 0,
      total_completed: 0,
      others: {}
    };

for (const row of results) {
  const status = row.tpi_status;
  const count = parseInt(row.status_count) || 0;

  if (['pending', 'pending-eo'].includes(status)) {
    statusSummary.total_pending += count;
  } else if (['rejected', 'rejected-eo'].includes(status)) {
    statusSummary.total_rejected += count;
  } else if (['resubmit', 'resubmit-eo'].includes(status)) {
    statusSummary.total_resubmit += count;
  } else if (['completed', 'completed-eo'].includes(status)) {
    statusSummary.total_completed += count;
  } else {
    statusSummary.others[status] = (statusSummary.others[status] || 0) + count;
  }
}
    // return results;
    return statusSummary;

  } catch (error) {
    console.error('Error in getStatusCountRepo:', error.message || error);
    throw {
      status: error.status || 500,
      message: error.message || 'An unexpected error occurred.',
    };
  }
};


const feasibilityInstallationCustomerDetailRepo = async (tpi_id, customerConnectionId, lmc_stage) => {
  try {
    let response = {};

    // Fetch LMC Connection Details
    const getLmcConnectionDetails = await dbModels.trx_mapped_lmc_customer_connection.findOne({
      where: { customer_connection_id: customerConnectionId },
    });

    if (!getLmcConnectionDetails) {
      throw { status: 404, message: "LMC Connection Details not found" };
    }

    // Fetch Customer Details with Address
    const customerDetails = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id: customerConnectionId },
      include: [
        {
          model: dbModels.trx_customer_connection_address,
          as: "trx_customer_connection_addresses",
          where: { customer_connection_id: customerConnectionId },
        },
      ],
    });

    // Fetch Feasibility Details
    const feasibilityStatus = await dbModels.trx_mapped_lmc_customer_connection.findAll({
      where: {
        customer_connection_id: customerConnectionId,
        lmc_stage: "feasibility-check",
      },
      attributes: ["lmc_stage", "visit_status", "visit_datetime","tpi_status", "created_at"],
    });

    // Fetch LMC Admin Details
    const lmcDetails = await dbModels.mst_admin.findOne({
      where: { admin_id: getLmcConnectionDetails.lmc_id },
      attributes: ["first_name", "last_name", "user_role", "bpcl_admin_id", "mobile"],
    });


    const pipelineDetails = await dbModels.trx_lmc_pipeline.findOne({
      where: { mapped_lmc_customer_connection_id: customerConnectionId },
      attributes: ["house_type", "pipeline_size"],
      include: [
        {
          model: dbModels.trx_lmc_pipeline_files,
          as: "trx_lmc_pipeline_files",
          attributes: ["file_type", "file_name", "file_path", "file_ext"],
        },
      ],
    });


    // Include Installation Details if lmc_stage is "installation"
    let installationStatus = null;
    let feasibilitypipelineDetails = null
    if (lmc_stage === "installation") {
      installationStatus = await dbModels.trx_mapped_lmc_customer_connection.findOne({
        where: {
          mapped_lmc_customer_connection_id: getLmcConnectionDetails.mapped_lmc_customer_connection_id,
          lmc_stage: lmc_stage,
        },
        attributes: ["lmc_stage", "visit_status","visit_datetime","tpi_status", "created_at"],
      });
      feasibilitypipelineDetails = await dbModels.trx_lmc_pipeline.findOne({
        where: { mapped_lmc_customer_connection_id: getLmcConnectionDetails.mapped_lmc_customer_connection_id },
        attributes: ["house_type", "pipeline_size"],
        include: [
          {
            model: dbModels.trx_lmc_pipeline_files,
            as: "trx_lmc_pipeline_files",
            attributes: ["file_type", "file_name", "file_path", "file_ext"],
          },
        ],
      });
    }

    // Construct Response
    response = {
      customerDetails,
      lmcDetails,
      feasibilityStatus,
      feasibilitypipelineDetails: feasibilitypipelineDetails ? feasibilitypipelineDetails : [],
      pipelineDetails: pipelineDetails ? pipelineDetails : [],
      ...(installationStatus && { installationStatus }),
    };

    return response;

  } catch (error) {
    console.error("Error in fetching data:", error.message || error);

    // Throw with proper status and message
    throw { status: error.status || 500, message: error.message || "An unexpected error occurred." };
  }
};


const customerStatusHistory = async (params) => {
  try {
    const {
      tpi_id = params.user.admin_id,
      search,
      page = 1,
      limit = 100,
      lmc_stage,
      startDate,
      endDate,
      areaIds = [], // Required
    } = params;

    if (!tpi_id) {
      throw { status: 400, message: 'Missing or invalid tpi_id (admin_id).' };
    }

    if (!Array.isArray(areaIds) || areaIds.length === 0) {
      throw { status: 400, message: 'areaIds must be a non-empty array.' };
    }

    const query = `
      SELECT 
          tmlcs.tpi_status,
          tmlcs.visit_status,
          tmlcs.reason,
          tcc.bp_id,
          CONCAT(tcc.first_name, ' ', COALESCE(tcc.middle_name + ' ', ''), tcc.last_name) AS customer_name,
          tcc.mobile,
          map.pincode AS area_pincode,
          map.pincode_id,
          mp.area_id,
          tcc.customer_connection_id,
          tlcm.lmc_connection_mapping_id,
          tmlcs.mapped_lmc_connection_stage_id
      FROM 
          trx_mapped_lmc_connection_stages tmlcs
      INNER JOIN 
          trx_lmc_connection_mapping tlcm ON tmlcs.lmc_connection_mapping_id = tlcm.lmc_connection_mapping_id
      INNER JOIN 
          trx_customer_connection tcc ON tlcm.customer_connection_id = tcc.customer_connection_id
      LEFT JOIN 
          mst_available_pngareas map ON tcc.available_pngareas_id = map.available_pngareas_id
      LEFT JOIN 
          mst_pincodes mp ON map.pincode_id = mp.pincode_id
      WHERE 
          tmlcs.lmc_stage = :lmc_stage
          AND (tmlcs.tpi_engineer_id IS NULL OR tmlcs.tpi_engineer_id = :tpi_engineer_id)
          AND tmlcs.is_active = 1
          AND tmlcs.is_latest = 1
          AND tlcm.is_active = 1
          AND tcc.is_active = 1
          AND tmlcs.tpi_status NOT IN ('no-submission')
          AND mp.area_id IN (:areaIds)
    `;

    const results = await sequelize.query(query, {
      replacements: {
        lmc_stage,
        areaIds,
        tpi_engineer_id: tpi_id,
      },
      // logging: console.log,
      type: sequelize.QueryTypes.SELECT,
    });

    // Deduplicate based on customer_connection_id
    const uniqueCustomerMap = new Map();

    for (const row of results) {
      if (!uniqueCustomerMap.has(row.customer_connection_id)) {
        uniqueCustomerMap.set(row.customer_connection_id, row);
      }
    }

    const finalResult = await Promise.all(
      Array.from(uniqueCustomerMap.values()).map(async (row) => ({
        tpi_status: row.tpi_status,
        visit_status: row.visit_status,
        review_level: await getReviewLevel(row.tpi_status),
        reason: row.reason,
        bp_id: row.bp_id,
        customer_name: row.customer_name,
        mobile: row.mobile,
        area_pincode: row.area_pincode,
        area_id: row.area_id,
        customer_connection_id: row.customer_connection_id,
        lmc_connection_mapping_id: row.lmc_connection_mapping_id,
        mapped_lmc_connection_stage_id: row.mapped_lmc_connection_stage_id,
      }))
    );

    return finalResult;
  } catch (error) {
    console.error("Error in customerStatusHistory:", error);
    throw {
      status: error.status || 500,
      message: error.message || 'Failed to fetch customer status history.',
    };
  }
};

const getReviewLevel = async (tpi_status) => {
  try {
    if(tpi_status === 'pending' || tpi_status === 'completed' || tpi_status === 'resubmit' || tpi_status === 'rejected'){
      return 'tpi';
    }else if(tpi_status === 'pending-eo' || tpi_status === 'resubmit-eo' || tpi_status === 'completed-eo' || tpi_status === 'rejected-eo'){
      return 'eo';
    }else{
      return '';
    }
  } catch (error) {
    console.error("Error in getReviewLevel:", error);
    throw error;
  }
};



const tpiCustomeReview = async (params) => {
  try {
    const { search, page = 1, limit = 100, startDate, endDate } = params;

    const offset = (page - 1) * limit;
    let whereCondition = {
      is_active: 1,
      internal_status: {
        [Sequelize.Op.in]: [
          'application-pending',
          'application-resubmit',
          'application-completed',
          'application-rejected',
          'application-pending-so',
          'application-resubmit-so',
          'application-approved-so',
          'application-rejected-so',
        ],
      },
    };

    // Add date range filtering (for visit_datetime)
    if (startDate && endDate) {
      whereCondition.created_at = {
        [Sequelize.Op.between]: [
          new Date(`${startDate}T00:00:00.000Z`),
          new Date(`${endDate}T23:59:59.999Z`),
        ],
      };
    }

    // Search condition (customer_connection_id, full_name, email, mobile)
    const searchCondition = search
      ? {
          [Sequelize.Op.or]: [
            {
              customer_connection_id: {
                [Sequelize.Op.like]: `%${search}%`,
              },
            },
            {
              first_name: {
                [Sequelize.Op.like]: `%${search}%`,
              },
            },
            {
              last_name: {
                [Sequelize.Op.like]: `%${search}%`,
              },
            },
            {
              email: {
                [Sequelize.Op.like]: `%${search}%`,
              },
            },
            {
              mobile: {
                [Sequelize.Op.like]: `%${search}%`,
              },
            },
          ],
        }
      : {};
      
    const { count, rows } = await trxCustomerConnection.findAndCountAll({
      attributes: [
        'customer_connection_id',
        'first_name',
        'last_name',
        'email',
        'mobile',
        'created_at',
        'status',
        'internal_status',
        'created_by_type',
      ],
      include: [
        {
          model: dbModels.trx_customer_connection_address,
          as: 'trx_customer_connection_addresses',
          attributes: ['connection_pincode'],
        },
      ],
      where: { ...whereCondition, ...searchCondition },
      limit,
      offset,
      raw: true,
      nest: true,
    });

    return {
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      rows: rows,
    };
  } catch (error) {
    console.error('Error fetching TPI Application:', error);
    throw error;
  }
};

const lmcWorkApprovalStatusUpdate = async (whereCondition, updateData) => {
  try {
    const result = await dbModels.trx_mapped_lmc_connection_stages.update(updateData, {
      where: whereCondition,
    });
    return result;
  } catch (error) {
    console.error('Error in lmcWorkApprovalStatusUpdate Repository:', error);
    throw error;
  }
};


const customerStatusUpdate = async ({
  customer_connection_id,
  internal_status,
  status,
  master_id,
  master_value,
  description,
  tpi_id
}) => {
  try {
    // Update both status and internal_status
    const result = await dbModels.trx_customer_connection.update(
      {
        status,
        internal_status,
        updated_at: new Date(),
        updated_by: tpi_id,
      },
      {
        where: {
          customer_connection_id,
          is_active: true
        }
      }
    );

    // If no rows were updated, throw error
    if (!result[0]) {
      throw new Error('Customer connection not found or already inactive');
    }

    // // Get the previous status for history
    // const connection = await dbModels.trx_customer_connection.findOne({
    //   where: { customer_connection_id }
    // });

    // // Create status history entry
    // await dbModels.trx_customer_connection_status_history.create({
    //   customer_connection_id,
    //   previous_status: connection.status,
    //   current_status: internal_status,
    //   reason_id: master_id || null,
    //   reason: description || null,
    //   currently_assigned_to_type: 'tpi',
    //   currently_assigned_to_id: tpi_id,
    //   is_active: true,
    //   created_at: new Date(),
    //   created_by: tpi_id,
    //   created_by_type: 'tpi'
    // });

    return result;
  } catch (error) {
    console.error('Error in customerStatusUpdate:', error);
    throw error;
  }
};

const fetchAndUpdateReasonId = async (customer_connection_id, master_value) => {
  try {
    if (master_value) {
      const reason = await dbModels.mst_masters.findOne({
        where: { master_value },
      });

      if (reason) {
        await dbModels.trx_customer_connection.update(
          { reason_id: reason.master_id },
          { where: { customer_connection_id } }
        );
      }
    }
  } catch (error) {
    console.error('Error in fetchAndUpdateReasonId:', error);
    throw error;
  }
};


const createPaymentEntry = async (customer_connection_id, eo_id) => {
  try {
    const result = await dbModels.trx_connection_payments.create({
      customer_connection_id,
      payment_status: 'pending',
      section_type: 'security-deposit',
      transaction_id: generateTrxID('TEMP'),
      gst_type: 'cgst',
      is_active: true,
      created_at: new Date(),
      created_by: eo_id,
      updated_at: new Date(),
      updated_by: eo_id
    });

    return result;
  } catch (error) {
    console.error('Error in createPaymentEntry:', error);
    throw error;
  }
};


const lmcStatusDataList = async (params) => {
  try {
    const {
      search,
      page = 1,
      limit = 100,
      startDate,
      endDate,
      admin_type
    } = params;
    const offset = (Math.max(page, 1) - 1) * limit; // Ensure page starts from 1

    const whereCondition = {
      is_active : 1,
      admin_type
    };

    const whereConditionForSearch = {};

    // Add date range filtering (for visit_datetime)
    if (startDate && endDate) {
      whereCondition.created_at = {
        [Sequelize.Op.between]: [
          new Date(`${startDate}T00:00:00.000Z`),
          new Date(`${endDate}T23:59:59.999Z`),
        ],
      };
    }

    // Search condition (customer_connection_id, full_name, email, mobile)
    if (search) {
      whereConditionForSearch[Sequelize.Op.or] = [
        {
          '$lmc_created_by.first_name$': {
            [Sequelize.Op.like]: `%${search}%`,
          },
        },
        {
          '$lmc_created_by.last_name$': {
            [Sequelize.Op.like]: `%${search}%`,
          },
        },
        {
          '$lmc_created_by.email$': { [Sequelize.Op.like]: `%${search}%` },
        },
        {
          '$lmc_created_by.mobile$': {
            [Sequelize.Op.like]: `%${search}%`,
          },
        },
      ];
    }

    const { count, rows } = await trxTpiOnboardApprove.findAndCountAll({
      where: whereCondition, // Apply filters
      include: [
        {
          model: dbModels.mst_admin,
          as: 'lmc_created_by',
          attributes: [
            'first_name',
            'last_name',
            'email',
            'mobile',
            'is_active',
          ],
          where: whereConditionForSearch,
        },
      ],
      limit,
      offset,
      raw: false,
      nest: true,
    });

    if (!rows.length) {
      throw { status: 404, message: 'No data found for TPI LMC Approved Rejected Pending Data.' };
    }

    return {
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      rows: rows,
    };
  } catch (error) {
    console.error('Error in lmcStatusDataList:', error.message || error);
    throw {
      status: error.status || 500,
      message: error.message || 'An unexpected error occurred.',
    };
  }
};

const lmcStatusUpdate = async (whereCondition, updateData) => {
  try {
    const result = await trxTpiOnboardApprove.update(updateData, {
      where: whereCondition,
    });
    return result;
  } catch (error) {
    console.error('Error in lmcStatusUpdate Repository:', error);
    throw error;
  }
};


const onboardStatusCount = async (admin_type,whereCondition) => {
  try {
    const results = await trxTpiOnboardApprove.findAll({
      attributes: [
        [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN approve_status = 'pending' THEN 1 END")), `pending`],
        [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN approve_status = 'approved' THEN 1 END")), `approved`],
        [Sequelize.fn("COUNT", Sequelize.literal("CASE WHEN approve_status = 'rejected' THEN 1 END")), `rejected`]
      ],
      where: whereCondition,
      raw: true,
    });
    
    return results;
  } catch (error) {
    console.error('Error in getFeasibility Count:', error.message || error);

    // Re-throw with proper status and message
    throw {
      status: error.status || 500,
      message: error.message || 'An unexpected error occurred.',
    };
  }
};

const getSingleDataByConnectionID = async (whereCondition) => {
  try {
    const result = await  dbModels.trx_mapped_lmc_connection_stages.findOne({
      where: whereCondition,
    });
    return result;
  } catch (error) {
    console.error('Error in getSingleDataByConnectionID Repository:', error);
    throw error;
  }
};


const insertInlmcCustomerConnection = async (dataForCreate, previousData) => {
  try {

    // if(previousData){
    //   await dbModels.trx_mapped_lmc_connection_stages.update(
    //     { is_latest: 0 },
    //     { where: { lmc_connection_mapping_id: previousData.lmc_connection_mapping_id, lmc_stage: previousData.lmc_stage } }
    //   );
    // }

    const result = await dbModels.trx_mapped_lmc_connection_stages.create(dataForCreate);
    return result;
  } catch (error) {
    console.error('Error in insertInlmcCustomerConnection Repository:', error);
    throw error;
  }
};

const getCustomerApplicationDetailsRepo = async (customer_connection_id) => {
  try {
    const customerDetails = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id },
      include: [
        {
          model: dbModels.trx_customer_connection_address,
          as: "trx_customer_connection_addresses",
          required: false
        },
        {
          model: dbModels.trx_customer_proof_documents,
          as: "trx_customer_proof_documents",
          required: false
        },
        {
          model: dbModels.mst_govt_scheme,
          attributes: ['govt_scheme_id','scheme_name'],
          as: "govt_scheme",
          required: false
        },
      ],
      raw: false,
      nest: true
    });

    if (!customerDetails) {
      throw new Error("Customer application details not found");
    }

    return customerDetails;
  } catch (error) {
    console.error("Error in getCustomerApplicationDetailsRepo:", error);
    throw error;
  }
};

const getPendingOnboardingList = async (personaType, offset, limit) => {
  try {
    let personaRoleId;
    switch(personaType.toUpperCase()) {  // Changed to toUpperCase
        case 'DMA':
            personaRoleId = 1;
            break;
        case 'LMC':
            personaRoleId = 3;
            break;
        case 'FO':
            personaRoleId = 4;
            break;
        default:
            throw new Error('Invalid persona type');
    }

    const { count, rows } = await dbModels.mst_admin.findAndCountAll({
        where: {
            persona_role_id: personaRoleId,
        },
        attributes: [
            'admin_id',
            'first_name',
            'last_name',
            'email',
            'mobile',
            'alternate_mobile',
            'created_at',
            'created_by'
        ],
        include: [
            {
                model: dbModels.mst_persona_role,
                as: 'persona_role',
                attributes: ['persona_role_id', 'role_name']
            },
            {
                model: dbModels.trx_tpi_onboard_approve,
                as: 'trx_tpi_onboard_approves',
                attributes: [
                    'approve_status',
                    'remark', 
                    'created_at', 
                    [Sequelize.col('updated_at'), 'updated_at']
                ],
                where: {
                    admin_type: personaType.toUpperCase(), // Changed to toUpperCase
                    is_active: true  // Added is_active check
                },
                required: true
            }
        ],
        offset: offset,
        limit: limit,
        order: [['created_at', 'DESC']],
        raw: false,
        nest: true
    });

    // Fetch creator details separately
    const creatorIds = rows.map(row => row.created_by).filter(id => id != null);
    const creators = creatorIds.length > 0 ? 
        await dbModels.mst_admin.findAll({
            where: {
                admin_id: creatorIds
            },
            attributes: ['admin_id', 'first_name', 'last_name']
        }) : [];

    const creatorsMap = creators.reduce((acc, creator) => {
        acc[creator.admin_id] = creator;
        return acc;
    }, {});

    return {
        count,
        rows: rows.map(row => {
            const creator = creatorsMap[row.created_by] || {};
            const mappedData = {
                supervisor_id: row.admin_id,
                first_name: row.first_name,
                last_name: row.last_name,
                email: row.email,
                mobile: row.mobile,
                alternate_mobile: row.alternate_mobile,
                persona_role: row.persona_role,
                agency: row.agency,
                onboard_status: row.trx_tpi_onboard_approves[0].approve_status,
                remark:row.trx_tpi_onboard_approves[0].remark,
                onboard_updated_at: row.trx_tpi_onboard_approves[0]?.dataValues?.updated_at || row.trx_tpi_onboard_approves[0]?.updated_at || null,
                created_by: {
                    id: row.created_by,
                    name: creator ? `${creator.first_name || ''} ${creator.last_name || ''}`.trim() : 'Unknown'
                },
                created_at: row.dataValues?.created_at || row.created_at
            };
            return mappedData;
        })
    };
  } catch (error) {
      console.error("Error in getPendingOnboardingList repository:", error);
      throw error;
  }
};

const updateTpiOnboardApproval = async ({ supervisor_id, updateData }) => {
  try {
    await dbModels.mst_admin.update({
      is_active: updateData.approve_status === 'approved' ? true : false
    }, {
      where: { 
        admin_id: supervisor_id  // Changed from supervisor_id to admin_id
      }
    });

    return await dbModels.trx_tpi_onboard_approve.update(updateData, {
      where: { 
        supervisor_id,
        admin_type: updateData.persona_type.toUpperCase(),
        is_active: true
      }
    });
  } catch (error) {
    console.error('Error in updateTpiOnboardApproval repository:', error);
    throw error;
  }
};

const getCustomerApplicationStatusCount = async () => {
  try {
    const results = await dbModels.trx_customer_connection.findAll({
      attributes: [
        [
          Sequelize.fn('COUNT', Sequelize.col('customer_connection_id')),
          'total_applications'
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal("CASE WHEN internal_status IN ('application-pending-so', 'application-pending') THEN 1 END")
          ),
          'pending_applications'
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal("CASE WHEN internal_status IN ('application-resubmit', 'application-resubmit-so') THEN 1 END")
          ),
          'resubmit_applications'
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal("CASE WHEN internal_status = 'application-approved-so' THEN 1 END")
          ),
          'completed_applications'
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal("CASE WHEN internal_status IN ('application-rejected', 'application-rejected-so') THEN 1 END")
          ),
          'rejected_applications'
        ]
      ],
      where: {
        internal_status: {
          [Sequelize.Op.in]: [
            'application-pending',
            'application-pending-so',
            'application-resubmit',
            'application-resubmit-so',
            'application-completed',
            'application-rejected',
            'application-rejected-so',
            'application-approved-so'
          ]
        },
        is_active: 1
      },
      raw: true
    });

    // Format the response
    const counts = results[0];
    return {
      total_applications: parseInt(counts.total_applications) || 0,
      pending_applications: parseInt(counts.pending_applications) || 0,
      resubmit_applications: parseInt(counts.resubmit_applications) || 0,
      completed_applications: parseInt(counts.completed_applications) || 0,
      rejected_applications: parseInt(counts.rejected_applications) || 0
    };
  } catch (error) {
    console.error('Error in getCustomerApplicationStatusCount:', error);
    throw error;
  }
};

const getSupervisorDetails = async (supervisorId) => {
  try {
    const supervisor = await dbModels.mst_admin.findOne({
      where: {
        admin_id: supervisorId,
        is_active: true
      },
      attributes: [
        'admin_id',
        'first_name',
        'last_name',
        'email',
        'password',
        'mobile',
        ['created_by', 'contractor_id'],
      ],
      raw: true
    });

    return supervisor;
  } catch (error) {
    console.error('Error in getSupervisorDetails:', error);
    throw error;
  }
};

const getContractorDetails = async (contractorId) => {
  try {
    const contractor = await dbModels.mst_admin.findOne({
      where: {
        admin_id: contractorId,
        is_active: true
      },
      attributes: [
        'admin_id',
        'first_name',
        'last_name',
        'email',
        'password',
        'mobile',
      ],
      raw: true
    });

    return contractor;
  } catch (error) {
    console.error('Error in getContractorDetails:', error);
    throw error;
  }
};


const getCommissioningStageId = async (customer_connection_id) => {
  try {
    const query = `
      SELECT mapped_lmc_connection_stage_id 
      FROM trx_mapped_lmc_connection_stages 
      WHERE lmc_connection_mapping_id = (
        SELECT lmc_connection_mapping_id 
        FROM trx_lmc_connection_mapping 
        WHERE customer_connection_id = :customer_connection_id
        AND is_active = 1
      ) 
      AND lmc_stage = 'commissioning'
      AND is_active = 1
    `;

    const result = await sequelize.query(query, {
      replacements: { customer_connection_id },
      type: sequelize.QueryTypes.SELECT,
      raw: true
    });

    return result.length > 0 ? result[0].mapped_lmc_connection_stage_id : null;
  } catch (error) {
    console.error('Error in getCommissioningStageId:', error);
    return null;
  }
};

const getLmcConnectionMappingId = async (customer_connection_id) => {
  try {
    const result = await dbModels.trx_lmc_connection_mapping.findOne({
      where: {
        customer_connection_id,
        is_active: true
      },
      attributes: ['lmc_connection_mapping_id'],
      raw: true
    });

    return result ? result.lmc_connection_mapping_id : null;
  } catch (error) {
    console.error('Error in getLmcConnectionMappingId:', error);
    return null;
  }
};

const getJmrApprovalList = async (filters = {}, areaIds) => {
  const whereCondition = {
    is_active: true,
    is_latest: true
  };

  if (filters.tpi_status) {
    whereCondition.tpi_status = filters.tpi_status;
  }

  const { rows, count } = await dbModels.trx_tpi_manual_meter_approval.findAndCountAll({
    attributes: [
      'tmma_id',
      'meter_reading_file',
      'meter_reading',
      'tpi_status',
      'reason',
      'created_at',
      'is_latest',
      'is_active'
    ],
    include: [
      {
        model: dbModels.trx_customer_meter_readings,
        as: 'customer_meter_reading',
        attributes: ['customer_meter_reading_id', 'meter_reading_file'],
        required: true,
        include: [
          {
            model: dbModels.trx_customer_connection,
            as: 'customer_connection',
            attributes: ['first_name', 'last_name', 'mobile', 'bp_id', 'customer_connection_id'],
            required: true,
            include: [
              {
                model: dbModels.trx_customer_connection_address,
                as: 'trx_customer_connection_addresses',
                attributes: ['connection_address'],
                required: true
              },
              {
                model: dbModels.mst_available_pngareas,
                as: 'available_pngarea',
                required: true,
                attributes: [],
                include: [
                  {
                    model: dbModels.mst_pincodes,
                    as: 'pincodeAreasAlias',
                    required: true,
                    attributes: [],
                    where: {
                      area_id: {
                        [Sequelize.Op.in]: areaIds
                      }
                    }
                  }
                ]
              }
            ]
          },
          {
            model: dbModels.mst_admin,
            as: 'submitted_admin',
            attributes: ['first_name', 'last_name', 'mobile'],
            required: false
          }
        ]
      }
    ],
    where: whereCondition,
    order: [['created_at', 'DESC']],
    raw: true,
    nest: true
  });

  // Format the result
  const formattedRows = await Promise.all(rows.map(async row => ({
    approval_id: row.tmma_id,
    meter_reading: row.meter_reading,
    approval_status: row.tpi_status,
    reason: row.reason,
    submitted_date: row.created_at,
    meter_reading_file: row.meter_reading_file,
    customer: {
      name: `${row.customer_meter_reading.customer_connection.first_name} ${row.customer_meter_reading.customer_connection.last_name}`,
      bp_id: row.customer_meter_reading.customer_connection.bp_id,
      mobile: row.customer_meter_reading.customer_connection.mobile,
      address: row.customer_meter_reading.customer_connection.trx_customer_connection_addresses?.[0]?.connection_address || null,
      customer_connection_id: row.customer_meter_reading.customer_connection.customer_connection_id,
      lmc_connection_stage_id: await getCommissioningStageId(row.customer_meter_reading.customer_connection.customer_connection_id),
      lmc_connection_mapping_id: await getLmcConnectionMappingId(row.customer_meter_reading.customer_connection.customer_connection_id)
    },
    submitted_by: {
      name: `${row.customer_meter_reading.submitted_admin.first_name} ${row.customer_meter_reading.submitted_admin.last_name}`,
      mobile: row.customer_meter_reading.submitted_admin.mobile
    }
  })));

  return {
    total: count,
    data: formattedRows
  };
};


const getJmrApprovalStatusCount = async (areaIds) => {
  try {
    const results = await dbModels.trx_tpi_manual_meter_approval.findAll({
      attributes: [
        [Sequelize.fn('COUNT', Sequelize.col('tmma_id')), 'total_applications'],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal(`CASE WHEN tpi_status IN ('pending', 'pending-jmr-eo') THEN 1 END`)
          ),
          'pending_applications',
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal(`CASE WHEN tpi_status IN ('completed-jmr-eo') THEN 1 END`)
          ),
          'approved_applications',
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal(`CASE WHEN tpi_status IN ('rejected', 'rejected-jmr-eo') THEN 1 END`)
          ),
          'rejected_applications',
        ],
      ],
      where: {
        is_active: true,
        is_latest: true,
      },
      include: [
        {
          model: dbModels.trx_customer_meter_readings,
          as: 'customer_meter_reading',
          required: true,
          attributes: [], // <-- NO columns here! Just for filtering
          include: [
            {
              model: dbModels.trx_customer_connection,
              as: 'customer_connection',
              required: true,
              attributes: [], // NO columns here either
              include: [
                {
                  model: dbModels.mst_available_pngareas,
                  as: 'available_pngarea',
                  required: true,
                  attributes: [],
                  include: [
                    {
                      model: dbModels.mst_pincodes,
                      as: 'pincodeAreasAlias',
                      required: true,
                      attributes: [],
                      where: {
                        area_id: { [Sequelize.Op.in]: areaIds },
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      raw: true,
    });

    if (!results || results.length === 0) {
      return {
        total_applications: 0,
        pending_applications: 0,
        approved_applications: 0,
        rejected_applications: 0,
      };
    }

    const counts = results[0];
    return {
      total_applications: parseInt(counts.total_applications) || 0,
      pending_applications: parseInt(counts.pending_applications) || 0,
      approved_applications: parseInt(counts.approved_applications) || 0,
      rejected_applications: parseInt(counts.rejected_applications) || 0,
    };
  } catch (error) {
    console.error('Error in getJmrApprovalStatusCount:', error);
    throw error;
  }
};

const getJmrSubmissionDetails = async (tmma_id) => {
  try {
    const result = await dbModels.trx_tpi_manual_meter_approval.findOne({
      attributes: [
        'tmma_id',
        'meter_reading_file',
        'meter_reading',
        'tpi_status',
        'created_at'
      ],
      include: [
        {
          model: dbModels.trx_customer_meter_readings,
          as: 'customer_meter_reading',
          attributes: [
            'customer_connection_id',
            'customer_meter_reading_id',
            'meter_reading_file',
            'created_at'
          ],
          include: [
            {
              model: dbModels.trx_customer_meter,
              as: 'customer_meter',
              attributes: [
                'meter_type',
                'meter_no'
              ]
            },
            {
              model: dbModels.trx_customer_connection,
              as: 'customer_connection',
              attributes: [
                'first_name',
                'last_name',
                'mobile',
                'bp_id'
              ]
            },
            {
              model: dbModels.mst_admin,
              as: 'submitted_admin',
              attributes: [
                'first_name',
                'last_name',
                'mobile'
              ]
            }
          ]
        }
      ],
      where: {
        tmma_id: tmma_id
      },
      raw: true,
      nest: true
    });

    if (!result) {
      return null;
    }

    // Transform the result into desired format
    const formattedResult = {
      submission_id: result.tmma_id,
      customer: {
        name: `${result.customer_meter_reading?.customer_connection?.first_name || ''} ${result.customer_meter_reading?.customer_connection?.last_name || ''}`.trim(),
        mobile: result.customer_meter_reading?.customer_connection?.mobile,
        bp_id: result.customer_meter_reading?.customer_connection?.bp_id,
        customer_connection_id: result.customer_meter_reading?.customer_connection?.customer_connection_id
      },
      lmc_supervisor: {
        name: `${result.customer_meter_reading?.submitted_admin?.first_name || ''} ${result.customer_meter_reading?.submitted_admin?.last_name || ''}`.trim(),
        mobile: result.customer_meter_reading?.submitted_admin?.mobile
      },
      meter_details: {
        type: result.customer_meter_reading?.customer_meter?.meter_type,
        number: result.customer_meter_reading?.customer_meter?.meter_no,
        reading: result.meter_reading,
        jmr_file: result.customer_meter_reading?.meter_reading_file,
        approval_file: result.meter_reading_file
      },
      requested_on: result.created_at, // Changed from result.customer_meter_reading?.created_at to result.created_at
      status: result.tpi_status
    };

    return formattedResult;
  } catch (error) {
    console.error('Error in getJmrSubmissionDetails:', error);
    throw error;
  }
};

const updateJmrApprovalStatus = async (tmma_id, updateData, tpi_id, customer_connection_id, lmc_connection_stage_id) => {
  try {
    const result = await dbModels.trx_tpi_manual_meter_approval.update(
      {
        tpi_status: updateData.status,
        reason: updateData.reason,
        updated_at: new Date(),
        updated_by: updateData.updated_by
      },
      {
        where: {
          tmma_id: tmma_id,
          is_active: true,
          is_latest: true
        }
      }
    );

    if(updateData.status === 'resubmit'){
      await dbModels.trx_mapped_lmc_connection_stages.update(
        {
          tpi_status: updateData.status,
          visit_status: "to-be-rescheduled",
          reason: updateData.reason,
          updated_at: new Date(),
          updated_by: updateData.updated_by
        },
        {
          where: {
            mapped_lmc_connection_stage_id: lmc_connection_stage_id,
            lmc_stage: 'commissioning',
            is_active: true,
            is_latest: true
          }       
        }
      );
    }
    else if(updateData.status === 'rejected'){
      await dbModels.trx_mapped_lmc_connection_stages.update(
        {
          tpi_status: updateData.status,
          reason: updateData.reason,
          updated_at: new Date(),
          updated_by: updateData.updated_by
        },
        {
          where: {
            mapped_lmc_connection_stage_id: lmc_connection_stage_id,
            lmc_stage: 'commissioning',
            is_active: true,
            is_latest: true
          }
        }
      );
    }
    else if(updateData.status === 'pending-jmr-eo'){
      await dbModels.trx_mapped_lmc_connection_stages.update(
        {
          tpi_status: "pending-eo",
          reason: updateData.reason,
          updated_at: new Date(),
          updated_by: updateData.updated_by
        },
        {
          where: {
            mapped_lmc_connection_stage_id: lmc_connection_stage_id,
            lmc_stage: 'commissioning',
            is_active: true,
            is_latest: true
          }
        }
      );
    }
    return result;
  } catch (error) {
    console.error('Error in updateJmrApprovalStatus:', error);
    throw error;
  }
};

const getTpiCategories = async (master_type) => {
  try {
    const categories = await dbModels.mst_masters.findAll({
      where: {
        master_type,
        is_active: 1
      },
      attributes: ['master_id', 'master_name', 'master_value'],
      raw: true
    });
    return categories;
  } catch (error) {
    console.error("Error in getTpiCategories repository:", error);
    throw error;
  }
};

const createTpiMapping = async ({
  customer_connection_id,
  tpi_id,
  tpi_status,
  reason_id,
  remark
}) => {
  try {
    // Get the latest sequence number for this specific customer_connection_id
    const latestEntry = await dbModels.trx_customer_connection_tpi_mapping.findOne({
      where: {
        customer_connection_id,
      },
      order: [
        ['sequence_no', 'DESC']
      ],
      raw: true
    });

    // Calculate new sequence number
    const newSequenceNo = latestEntry ? (parseInt(latestEntry.sequence_no) + 1) : 1;

    // Update is_latest to false for all existing records of this customer
    await dbModels.trx_customer_connection_tpi_mapping.update(
      {
        is_latest: false,
        updated_at: new Date(),
        updated_by: tpi_id
      },
      {
        where: {
          customer_connection_id,
          is_latest: true
        }
      }
    );

    // Create new mapping entry with unique sequence
    const result = await dbModels.trx_customer_connection_tpi_mapping.create({
      customer_connection_id,
      tpi_id,
      tpi_status,
      reason_id,
      remarks: remark,
      sequence_no: newSequenceNo,
      is_latest: true,
      is_active: true,
      created_at: new Date(),
      created_by: tpi_id,
      updated_at: new Date(),
      updated_by: null
    });

    return result;
  } catch (error) {
    console.error('Error in createTpiMapping:', error);
    throw error;
  }
};

const createTpiRevokeEntry = async ({
  customer_connection_tpi_mapping_id,
  tpi_id,
  reason_id,
  remark
}) => {
  try {
    let reasonData = null;
    
    if (reason_id) {
      reasonData = await dbModels.mst_masters.findOne({
        where: {
          master_id: reason_id
        },
        attributes: ['master_id', 'master_name', 'master_value'],
        raw: true
      });
    }

    const result = await dbModels.trx_customer_connection_tpi_rework.create({
      customer_connection_tpi_mapping_id,
      section: reasonData?.master_value || null,
      description: remark || null,
      is_active: true,
      created_at: new Date(),
      created_by: tpi_id,
      updated_at: new Date(),
      updated_by: tpi_id
    }); 

    return result;
  } catch (error) {
    console.error('Error in createTpiRevokeEntry:', error);
    throw error;
  }
};

const updateCustomerConnectionStatus = async (customer_connection_id, status) => {
  var result;
  try {
    switch(status){
      case 'application-rejected-jmr':
        status = 'application-rejected';
        internal_status = 'installation-rejected';
        result = await dbModels.trx_customer_connection.update(
          {
            status,
            internal_status,
            updated_at: new Date()
          },
          {
            where: {
              customer_connection_id,
              is_active: true
            }
          }
        );
        break;
      case 'application-rejected':
        status = "application-rejected";
        internal_status = 'onboarding-rejected';
        result = await dbModels.trx_customer_connection.update(
          {
            status,
            internal_status,
            updated_at: new Date()
          },
          {
            where: {
              customer_connection_id,
              is_active: true
            }
          }
        );
        break;
      default:
        result = await dbModels.trx_customer_connection.update(
          {
            status,
            updated_at: new Date()
          },
          {
            where: {
              customer_connection_id,
              is_active: true
            }
          }
        );
        break;
    }
    
    return result;
  } catch (error) {
    console.error('Error in updateCustomerConnectionStatus:', error);
    throw error;
  }
};

const updateGaApprovalStatus = async (customer_connection_id, updateData) => {
  try {
    // Update the customer connection status
    const result = await dbModels.trx_customer_connection.update(
      updateData,
      {
        where: { 
          customer_connection_id,
          is_active: true 
        }
      }
    );

    // // Get the previous status for history
    // const connection = await dbModels.trx_customer_connection.findOne({
    //   where: { customer_connection_id }
    // });

    // // Create status history entry
    // await dbModels.trx_customer_connection_status_history.create({
    //   customer_connection_id,
    //   previous_status: connection.status,
    //   current_status: updateData.status,
    //   previous_internal_status: connection.internal_status,
    //   current_internal_status: updateData.internal_status,
    //   reason: updateData.reason || null,
    //   currently_assigned_to_type: 'tpi',
    //   currently_assigned_to_id: updateData.updated_by,
    //   is_active: true,
    //   created_by: updateData.updated_by,
    //   created_by_type: 'tpi',
    //   created_at: new Date()
    // });

    return result;
  } catch (error) {
    console.error('Error updating supervisor customer status:', error);
    throw error;
  }
};

const updateSupervisorCustomerStatus = async ({customer_connection_id, tpi_status}) => {
  try {
  if(!customer_connection_id) return
    // Step 1: Get help_with_registration_id using customer_connection_id
    const helpWithReg = await dbModels.trx_help_with_registration.findOne({
      where: { customer_connection_id },
      attributes: ['help_with_registration_id'],
    });

    if (!helpWithReg) {
      return { updated: false, message: 'No registration record found for this customer' };
    }

    // Step 2: Update hwrm_status directly via UPDATE query
    await dbModels.trx_dma_help_with_reg_mapping.update(
      { hwrm_status: tpi_status },
      { where: { help_with_registration_id: helpWithReg.help_with_registration_id } }
    );

    // Step 3: Update status in trx_help_with_registration
    await dbModels.trx_help_with_registration.update(
      { status: tpi_status },
      { where: { help_with_registration_id: helpWithReg.help_with_registration_id } }
    );


    return
  } catch (error) {
    console.error('Error updating supervisor customer status:', error);
    throw error;
  }
};

const getSupervisorAreaDetails = async (supervisorId) => {
  try {
    const query = `
      SELECT area_id, area_name 
      FROM mst_areas 
      WHERE area_id IN (
        SELECT area_id 
        FROM trx_admin_mapped_areas 
        WHERE admin_id = :supervisorId
      )
    `;
    const results = await sequelize.query(query, {
      replacements: { supervisorId },
      type: sequelize.QueryTypes.SELECT,
    });
    console.log("Supervisor Area Details:", results);
    return results;
  } catch (error) {
    console.error("Error in getSupervisorAreaDetails:", error);
    throw error;
  }
};
const getSoCommentDetailsTpi = async (customer_connection_id) => {
  try {
    const approvalEntries = await dbModels.trx_bpcl_subadmin_connection_approval.findAll({
      where: {
        customer_connection_id,
        mapping_type: 'customer-application',
      },
      include: [
        {
          model: dbModels.mst_admin,
          as: 'bpcl_subadmin',
          attributes: ['first_name', 'last_name', 'user_role'],
        }
      ],
      attributes: [
        'updated_by',
        'remarks',
        'updated_at',
        'bpcl_subadmin_status',
        'sequence_no',
      ],
      order: [['sequence_no', 'ASC']],
    });

    const result = approvalEntries.map(entry => {
      const fullName = entry.bpcl_subadmin
        ? `${entry.bpcl_subadmin.first_name || ''} ${entry.bpcl_subadmin.last_name || ''}`.trim()
        : null;

      const formattedDate =
        entry.updated_at instanceof Date
          ? entry.updated_at.toISOString().split('T')[0]
          : (entry.updated_at
            ? new Date(entry.updated_at).toISOString().split('T')[0]
            : null);

      return {
        role: entry.bpcl_subadmin?.user_role || null,
        given_by: fullName,
        reason: entry.remarks,
        status: entry.bpcl_subadmin_status || null,
        date: formattedDate,
      };
    });

    return result;
  } catch (error) {
    console.error("Error in getSoCommentDetails repo:", error);
    throw error;
  }
};

const getEoCommentDetailsTpi = async (customer_connection_id) => {
  try {
    const approvalEntries = await dbModels.trx_bpcl_subadmin_connection_approval.findAll({
      where: {
        customer_connection_id,
        mapping_type: 'lmc-installation',
      },
      include: [
        {
          model: dbModels.mst_admin,
          as: 'bpcl_subadmin',
          attributes: ['first_name', 'last_name', 'user_role'],
        }
      ],
      attributes: [
        'updated_by',
        'remarks',
        'updated_at',
        'bpcl_subadmin_status',
        'sequence_no',
      ],
      order: [['sequence_no', 'ASC']],
    });

    const result = approvalEntries.map(entry => {
      const fullName = entry.bpcl_subadmin
        ? `${entry.bpcl_subadmin.first_name || ''} ${entry.bpcl_subadmin.last_name || ''}`.trim()
        : null;

      const formattedDate =
        entry.updated_at instanceof Date
          ? entry.updated_at.toISOString().split('T')[0]
          : (entry.updated_at
            ? new Date(entry.updated_at).toISOString().split('T')[0]
            : null);

      return {
        role: entry.bpcl_subadmin?.user_role || null,
        given_by: fullName,
        reason: entry.remarks,
        status: entry.bpcl_subadmin_status || null,
        date: formattedDate,
      };
    });

    return result;
  } catch (error) {
    console.error("Error in getEoCommentDetails repo:", error);
    throw error;
  }
};

const getEoJmrCommentDetailsTpi = async (customer_connection_id) => {
  try {
    const approvalEntries = await dbModels.trx_bpcl_subadmin_connection_approval.findAll({
      where: {
        customer_connection_id,
        mapping_type: 'jmr-manual',
      },
      include: [
        {
          model: dbModels.mst_admin,
          as: 'bpcl_subadmin',
          attributes: ['first_name', 'last_name', 'user_role'],
        }
      ],
      attributes: [
        'updated_by',
        'remarks',
        'updated_at',
        'bpcl_subadmin_status',
        'sequence_no',
      ],
      order: [['sequence_no', 'ASC']],
    });

    const result = approvalEntries.map(entry => {
      const fullName = entry.bpcl_subadmin
        ? `${entry.bpcl_subadmin.first_name || ''} ${entry.bpcl_subadmin.last_name || ''}`.trim()
        : null;

      const formattedDate =
        entry.updated_at instanceof Date
          ? entry.updated_at.toISOString().split('T')[0]
          : (entry.updated_at
            ? new Date(entry.updated_at).toISOString().split('T')[0]
            : null);

      return {
        role: entry.bpcl_subadmin?.user_role || null,
        given_by: fullName,
        reason: entry.remarks,
        status: entry.bpcl_subadmin_status || null,
        date: formattedDate,
      };
    });

    return result;
  } catch (error) {
    console.error("Error in getEoJmrCommentDetails repo:", error);
    throw error;
  }
};

const getFeasibilityFormDetailsTpi = async (mapped_lmc_connection_stage_id) => {
  try {
    const feasibilityDetails =
      await dbModels.trx_mapped_lmc_feasibility_commission_details.findOne({
        where: { mapped_lmc_connection_stage_id },
        include: [
          {
            model: dbModels.trx_mapped_lmc_connection_stages,
            as: "mapped_lmc_connection_stage",
            attributes: ["visit_status", "actual_visit_date", "visit_datetime", "lmc_connection_mapping_id"],
            required: false,
            include: [
              {
                model: dbModels.trx_lmc_pipeline_documents,
                as: "trx_lmc_pipeline_documents",
                required: false,
                attributes: ["file_name", "file_path", "file_type"],
              },
            ],
          },
        ],
      });

    return feasibilityDetails;
  } catch (error) {
    console.error("Repository error in getFeasibilityFormDetailsTpi:", error);
    throw error;
  }
};

const getInstallationStageId = async (lmc_connection_mapping_id) => {
  try {
    const stageId = await dbModels.trx_mapped_lmc_connection_stages.findOne({
      where: { lmc_connection_mapping_id, lmc_stage: 'installation' },
      attributes: ["mapped_lmc_connection_stage_id"],
    });

    return stageId?.mapped_lmc_connection_stage_id;
  } catch (error) {
    console.error("Repository error in getInstallationStageId:", error);
    throw error;
  }
};

const getFeasibilityStageId = async (lmc_connection_mapping_id) => {
  try {
    const stageId = await dbModels.trx_mapped_lmc_connection_stages.findOne({
      where: { lmc_connection_mapping_id, lmc_stage: 'feasibility-check' },
      attributes: ["mapped_lmc_connection_stage_id"],
    });

    return stageId?.mapped_lmc_connection_stage_id;
  } catch (error) {
    console.error("Repository error in getInstallationStageId:", error);
    throw error;
  }
};

const getSupervisorFullName = async (lmc_connection_mapping_id) => {
  try {
    const supervisorDetails = await dbModels.trx_lmc_connection_mapping.findOne({
      where: { lmc_connection_mapping_id },
      include: [
        {
          model: dbModels.mst_admin,
          as: "lmc",
          attributes: ["first_name", "last_name"],
        },
      ],
    });


    if (!supervisorDetails || !supervisorDetails.lmc) {
      return null;
    }

    const fullName = `${supervisorDetails.lmc.first_name || ''} ${supervisorDetails.lmc.last_name || ''}`.trim();
    return fullName;
  } catch (error) {
    console.error("Repository error in getSupervisorFullName:", error);
    throw error;
  }
};




module.exports = {
  getTPICasesSummery,
  tpiApplicationStatus,
  getStatusCountRepo,
  feasibilityInstallationCustomerDetailRepo,
  customerStatusHistory,
  tpiCustomeReview,
  lmcWorkApprovalStatusUpdate,
  customerStatusUpdate,
  lmcStatusDataList,
  lmcStatusUpdate,
  onboardStatusCount,
  getSingleDataByConnectionID,
  insertInlmcCustomerConnection,
  getCustomerApplicationDetailsRepo,
  getPendingOnboardingList,
  updateTpiOnboardApproval,
  getCustomerApplicationStatusCount,
  getSupervisorDetails,
  getJmrApprovalList,
  getJmrApprovalStatusCount,
  getJmrSubmissionDetails,
  updateJmrApprovalStatus,
  getTpiCategories,
  createTpiMapping,
  createTpiRevokeEntry,
  updateCustomerConnectionStatus,
  createPaymentEntry,
  updateGaApprovalStatus,
  updateSupervisorCustomerStatus,
  getSupervisorAreaDetails,
  fetchAndUpdateReasonId,
  getSoCommentDetailsTpi,
  getEoCommentDetailsTpi,
  getEoJmrCommentDetailsTpi,
  getFeasibilityFormDetailsTpi,
  getSupervisorFullName,
  getInstallationStageId,
  getFeasibilityStageId,
  getContractorDetails
};
