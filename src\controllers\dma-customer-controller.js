const { sendSuccess, sendError } = require('../helpers/responseHelper');
const dmaCustomerRepository = require('../repository/dma-customer-repository');

exports.getAllDmaCustomers = async (req, res) => {
    try {
        const area_id = req.body.area_id;
        const contractorId = req.user.admin_id;
        const page = req.body.page;
        const limit = req.body.limit;
        const search = req.body.search;
        const sortBy = req.body.sortBy;
        const sortOrder = req.body.sortOrder;
        const customers = await dmaCustomerRepository.getAllDmaCustomers(contractorId, area_id,page,limit,search,sortBy,sortOrder);
      const { data, totalRecords, totalPages, currentPage } = customers;

    res.status(200).json({
      status: 'success',
      statusCode: 200,
      data: data,             
      totalRecords,           
      totalPages,
      currentPage,
      message: 'DMA customers retrieved successfully',
    });

  } catch (error) {
    console.error('Error fetching DMA customers:', error);
    sendError(req, res, null, 'Failed to retrieve DMA customers');
  }
};

exports.getSupervisorCustomers = async (req, res) => {
    try {
        const supervisorId = req.user.admin_id;
        const customers = await dmaCustomerRepository.getSupervisorCustomers(supervisorId);
        sendSuccess(req, res, customers, 'Assigned customers retrieved successfully');
    } catch (error) {
        console.error('Error fetching supervisor customers:', error);
        sendError(req, res, null, 'Failed to retrieve assigned customers');
    }
};

exports.updateCustomerStatus = async (req, res) => {
    try {
        const { helpWithRegistrationId, status } = req.body;
        const supervisorId = req.user.admin_id;

        if (!helpWithRegistrationId || !status) {
            return sendError(req, res, null, 'Missing required fields');
        }

        const customerDetails = await dmaCustomerRepository.getCustomerById(helpWithRegistrationId);
        
        if (!customerDetails) {
            return sendError(req, res, null, 'Customer registration not found');
        }

        // Check if customer is assigned to the supervisor
        const assignment = customerDetails.trx_dma_help_with_reg_mappings?.[0];
        if (!assignment || assignment.dma_supervisor_assigned_id !== supervisorId) {
            return sendError(req, res, null, 'Unauthorized: You can only update status for customers assigned to you');
        }

        const updatedCustomer = await dmaCustomerRepository.updateCustomerStatus(helpWithRegistrationId, status);
        sendSuccess(req, res, updatedCustomer, 'Customer status updated successfully');
    } catch (error) {
        console.error('Error updating customer status:', error);
        sendError(req, res, null, error.message || 'Failed to update customer status');
    }
};

exports.getSupervisorCustomerStatusCount = async (req, res) => {
    try {
        const supervisorId = req.user.admin_id;

        const statusCount = await dmaCustomerRepository.getSupervisorCustomerStatusCount(supervisorId);
        sendSuccess(req, res, statusCount, 'Status counts retrieved successfully');
    } catch (error) {
        console.error('Error fetching status counts:', error);
        sendError(req, res, null, 'Failed to retrieve status counts');
    }
};

module.exports = {
    getAllDmaCustomers: exports.getAllDmaCustomers,
    getSupervisorCustomers: exports.getSupervisorCustomers,
    updateCustomerStatus: exports.updateCustomerStatus,
    getSupervisorCustomerStatusCount: exports.getSupervisorCustomerStatusCount
};
