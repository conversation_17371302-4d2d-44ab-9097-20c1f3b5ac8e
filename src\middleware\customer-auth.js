const { FormatData } = require("../utils");
const message = require("../utils/constants");
const jwtHelper = require("../helpers/jwtHelper");
const { sendError } = require("../helpers/responseHelper");
const { validateBpclToken } = require("../services/hybris-service");
const { MOBILE_REGEX } = require("../constant/regex");
const messages = require("../constant/messages");

const verifyBpclToken = async (req, res, next) => {
  const token = (
    req.headers["authorization"] || req.headers["Authorization"]
  )?.replace("Bearer ", "");
  if (!token) {
    const output = FormatData("", message.TOKEN_REQUIRED);
    sendError(req, res, output, message.TOKEN_REQUIRED, 403);
    return;
  }
  if (!req.body || !req.body.request) {
    sendError(req, res, {}, "Payload is required", 403);
    return;
  }
  try {
    const decoded = await validateBpclToken(token, req.body);
    req.bpclMobile = decoded.customerId;
  } catch (err) {
    const output = FormatData("", message.INVALID_TOKEN, "failed");
    sendError(req, res, err, message.INVALID_TOKEN, 401);
    return;
  }

  next();
};

const verifyPngToken = (
  tokenType = null,
  checkDma = false,
  isContractor = false
) => {
  return async (req, res, next) => {
    const token = (
      req.headers["authorization"] || req.headers["Authorization"]
    )?.replace("Bearer ", "");
    if (!token) {
      const output = FormatData("", message.TOKEN_REQUIRED);
      sendError(req, res, output, message.TOKEN_REQUIRED, 403);
      return;
    }
    try {
      console.log("token", token, checkDma);
      if (checkDma) {
        const decodedDma = await jwtHelper.verifyToken(token);
        console.log("decodedDma", decodedDma, req.body);
        if (
          decodedDma &&
          decodedDma.persona_role &&
          decodedDma.persona_role.role_name == "DMA"
        ) {
          // if (!req.body.customer_type) {
          //   sendError(
          //     req,
          //     res,
          //     { message: "Customer Type Required" },
          //     messages.UNAUTHORIZED_MSG,
          //     401
          //   );
          //   return;
          // }
          if (!isContractor && decodedDma.user_role != "supervisor") {
            sendError(
              req,
              res,
              { message: "Invalid dma" },
              messages.UNAUTHORIZED_MSG,
              401
            );
            return;
          }
          req.body.personaType = "dma";
          req.personaType = "dma";
          decodedDma.customer_type = req?.body?.customer_type;
          req.dma = decodedDma;

          req.dmaId = decodedDma.admin_id;
          req.channelPartnerToken = token;
          next();
          return;
        }
      }
      const decoded = await jwtHelper.verifyPngToken(token);
      if (!decoded || !decoded.mobile || !decoded.mobile.match(MOBILE_REGEX)) {
        sendError(
          req,
          res,
          { message: "bpcl id is invalid" },
          "Unable to process the request",
          401
        );
        return;
      }
      if (tokenType != "guest" && !decoded.customer_id) {
        sendError(req, res, {}, messages.UNAUTHORIZED_MSG, 401);
        return;
      }
      req.personaType = "customer";
      req.customer = decoded;
      req.bpclId = decoded.mobile;
      req.pngToken = token;
    } catch (err) {
      const output = FormatData("", message.INVALID_TOKEN, "failed");
      sendError(req, res, output, err.toString(), 401);
      return;
    }

    next();
  };
};

const verifyRefreshToken = (tokenType = null) => {
  return async (req, res, next) => {
    console.log("REFRESH TOKEN");
    const token = (
      req.headers["authorization"] || req.headers["Authorization"]
    )?.replace("Bearer ", "");
    if (!token) {
      const output = FormatData("", message.REFRESH_TOKEN_REQUIRED);
      sendError(req, res, output, message.REFRESH_TOKEN_REQUIRED, 403);
      return;
    }
    try {
      console.log("token", token);

      const decoded = await jwtHelper.verifyToken(token);
      req.customer = decoded;
      req.refreshToken = token;
    } catch (err) {
      const output = FormatData("", message.INVALID_REFRESH_TOKEN, "failed");
      sendError(req, res, output, err.toString(), 401);
      return;
    }

    next();
  };
};

module.exports = { verifyPngToken, verifyBpclToken, verifyRefreshToken };
