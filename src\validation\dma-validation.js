const { Jo<PERSON> } = require("express-validation");
const { MOBILE_REGEX } = require("../constant/regex");

const createDmaSupervisorValidation = {
  body: Joi.object({
    first_name: Joi.string().max(50).required().messages({
      "string.empty": "First name is required, it cannot be empty",
      "string.max": "First name cannot exceed 50 characters",
      "any.required": "First name field is required",
    }),
    last_name: Joi.string().max(50).required().messages({
      "string.empty": "Last name is required, it cannot be empty",
      "string.max": "Last name cannot exceed 50 characters",
      "any.required": "Last name field is required",
    }),
    email: Joi.string().email().max(100).required().messages({
      "string.email": "Email must be a valid email address",
      "string.empty": "Email is required, it cannot be empty",
      "string.max": "Email cannot exceed 100 characters",
      "any.required": "Email field is required",
    }),
    mobile: Joi.string().length(10).regex(MOBILE_REGEX).required().messages({
      "string.empty": "Mobile number is required, it cannot be empty",
      "string.length": "Mobile number must be exactly 10 digits",
      "string.pattern.base": "Mobile number must be a valid 10-digit number",
      "any.required": "Mobile number field is required",
    }),
    alternate_mobile: Joi.string()
      .length(10)
      .regex(MOBILE_REGEX)
      .allow(null, ""),
    persona_role_id: Joi.number().integer().required(),
    area_id: Joi.alternatives()
      .try(
        Joi.number().integer(),
        Joi.array().items(Joi.number().integer()).min(1)
      )
      .required(),
  }).prefs({ convert: true }),
};

const deleteSupervisorsValidation = {
  body: Joi.object({
    supervisor_ids: Joi.array().items(Joi.number()).min(1).required().messages({
      "array.min": "At least one supervisor must be selected for deletion",
      "array.base": "Supervisor IDs must be provided as an array",
    }),
  }),
};

const assignCustomersValidation = {
  body: Joi.object({
    supervisor_id: Joi.number().integer().required().messages({
      "number.base": "Supervisor ID must be a number",
      "any.required": "Supervisor ID is required",
    }),
    customer_ids: Joi.array().items(Joi.number()).min(1).required().messages({
      "array.min": "At least one customer must be selected for assignment",
      "array.base": "Customer IDs must be provided as an array",
    }),
  }),
};

const reassignCustomerValidation = {
  body: Joi.object({
    customer_id: Joi.number().integer().required().messages({
      "number.base": "Customer ID must be a number",
      "any.required": "Customer ID is required",
    }),
    new_supervisor_id: Joi.number().integer().required().messages({
      "number.base": "New supervisor ID must be a number",
      "any.required": "New supervisor ID is required",
    }),
  }),
};

const getAreaValidation = {
  body: Joi.object({
    geographical_area_id: Joi.number().integer().required(),
  }),
};
const getSupervisorValidation = {
  body: Joi.object({
    area_id: Joi.array().items(Joi.number().integer()).required(),
  }),
};
const getAllCustomerforDmaValidation = {
  body: Joi.object({
    area_id: Joi.array().items(Joi.number().integer()).min(1).required(),
    page: Joi.number().integer().min(1).optional(),
    limit: Joi.number().integer().min(1).optional(),
    search: Joi.string().allow('').optional(),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('ASC', 'DESC').optional(),
  }),
};
const getAllSupervisorsValidation = {
  body: Joi.object({
    area_id: Joi.array().items(Joi.number().integer()).required(),

    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).default(10),
    search: Joi.string().allow("").optional(),
    sortBy: Joi.string()
      .valid(
        "first_name",
        "last_name",
        "email",
        "mobile",
        "created_at",
        "updated_at"
      )
      .optional(),
    sortOrder: Joi.string().valid("ASC", "DESC").optional(),
  }),
};

const getSupervisorPerformanceValidation = {
  body: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).default(10),
    search: Joi.string().allow('').default(''),
    sortBy: Joi.string()
      .valid('supervisor_name', 'supervisor_id')
      .default('supervisor_name'),
    sortOrder: Joi.string().valid('ASC', 'DESC').insensitive().default('ASC'),
  }),
};

module.exports = {
  createDmaSupervisorValidation,
  deleteSupervisorsValidation,
  assignCustomersValidation,
  reassignCustomerValidation,
  getAreaValidation,
  getSupervisorValidation,
  getAllCustomerforDmaValidation,
  getAllSupervisorsValidation,
  getSupervisorPerformanceValidation
};
