const express = require('express');
const { validate } = require("express-validation");
const {
    createGovtSchemeValidation,
    updateGovtSchemeValidation,
    deleteGovtSchemeValidation,
    getGovtSchemeValidation,
} = require("../../validation/govtscheme-validation");

const govtSchemeRoutes = express.Router();
const govtSchemeListController = require('../../controllers/govt-scheme-controller');

// Define RESTful routes
govtSchemeRoutes.get('/list', validate(getGovtSchemeValidation), govtSchemeListController.getGovtSchemeList);
govtSchemeRoutes.post('/create', validate(createGovtSchemeValidation), govtSchemeListController.createGovtSchemeList);
govtSchemeRoutes.put('/update', validate(updateGovtSchemeValidation), govtSchemeListController.updateGovtSchemeList);
govtSchemeRoutes.delete('/delete', validate(deleteGovtSchemeValidation), govtSchemeListController.deleteGovtSchemeList);

module.exports = govtSchemeRoutes;