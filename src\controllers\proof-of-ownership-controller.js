const proofOfOwnershipService = require('../services/proof-of-ownership-service');
const { sendError, sendSuccess } = require('../helpers/responseHelper');
const message = require('../utils/constants');
 
const getProofOfOwnershipList = async function (req, res) {
  try {
      const data = await proofOfOwnershipService.getProofOfOwnershipList(req.body);
      sendSuccess(req, res, data, message.FETCHED);
  } catch (error) {
      sendError(req, res, error, message.BAD_REQUEST);
  }
};


 
const createProofOfOwnershipList = async function (req, res) {
  try {
    const data = await proofOfOwnershipService.createProofOfOwnershipList(req.body);
    if (data && data.message === 'This Name is already exist') {
          // Return a 400 status with the specific message
          return sendError(req, res, data, message.BAD_REQUEST, 400);
        }
    sendSuccess(req, res, data, message.CREATED);
  } catch (error) {
    console.error('Error creating capability:', error);
    sendError(req, res, error, message.BAD_REQUEST);
  }
};
 
const updateProofOfOwnershipList = async function (req, res) {
  try {
    const data = await proofOfOwnershipService.updateProofOfOwnershipList(req.body);
    sendSuccess(req, res, data, message.UPDATED);
  } catch (error) {
    sendError(req, res, error, message.BAD_REQUEST);
  }
};
 
const deleteProofOfOwnershipList = async function (req, res) {
  try {
    const data = await proofOfOwnershipService.deleteProofOfOwnershipList(req.body);
    sendSuccess(req, res, data, message.DELETED);
  } catch (error) {
    sendError(req, res, error, message.BAD_REQUEST);
  }
};
 

module.exports = {
  getProofOfOwnershipList,
  createProofOfOwnershipList,
  updateProofOfOwnershipList,
  deleteProofOfOwnershipList,
};
 

