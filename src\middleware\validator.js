const createHttpError = require("http-errors");
//* Include <PERSON><PERSON> to check error type
const Joi = require("joi");
//* Include all validators
const Validators = require("../validation");
const { sendError } = require("../helpers/responseHelper");

const validator = function (module, validatorKey) {
  //! Check if the module exists
  if (!Validators[module]) {
    throw new Error(`Module '${module}' does not exist in Validators`);
  }

  //! Check if the validator exists inside the module
  if (!Validators[module].hasOwnProperty(validatorKey)) {
    throw new Error(`Validator '${validatorKey}' does not exist in module '${module}'`);
  }

  return async function (req, res, next) {
    try {
      //* Validate request body
      const validated = await Validators[module][validatorKey].validateAsync(req.body);
      req.body = validated;
      next();
    } catch (err) {
      console.log("err", err);
      console.log("err.details", err.details);
      
      //!Joi validation error check
      if (err.isJoi) {
        const formattedErrors = err.details.map((detail) => ({
          field: detail.path.join("."),
          message: detail.message.replace(/"/g, ""),
          value: detail.context.value,
          type: detail.type,
        }));
        return sendError(
          req,
          res,
          {
            errors: formattedErrors,
            validationFailed: true,
          },
          "Validation failed",
          422
        );
      }
      
      //* If validation error occurs, call next with HTTP 422. Otherwise, HTTP 500
      return sendError(req, res, {}, err.details[0].message, 422);
      // if (err.isJoi) return next(createHttpError(422, { message: err.message }));
      // next(createHttpError(500, { message: "Internal Server Error" }));
    }
  };
};

module.exports = validator;
