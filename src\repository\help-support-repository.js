// const { Op } = require('sequelize');
const { dbModels } = require('../database/connection');
const { PROGRESS_STATUS } = require('../utils/constants');
const serviceRequestModel = dbModels.trx_service_request;
const seviceRequestDocumentModel = dbModels.trx_service_request_documents;

const addIssue = async (params,file) => {
  try{
    //save issue details to trx_service_request table
    params.request_status=PROGRESS_STATUS.PENDING;
    const results = await serviceRequestModel.create(params);
    if(file){
      file.service_request_id=results.service_request_id,

      //save issue support document details to trx_service_request_documents table
      await seviceRequestDocumentModel.create(file);
    }
    
    return results;
    
  } catch (error) {
    console.error("Error in addIssue:", error);
    throw error;
  } 
};

const getExitingIssueRequest = async (whereCondition) => {
  try {
    return await serviceRequestModel.findOne({
      attributes: [
        'service_request_id',
        'created_at'
      ],
      where: whereCondition
    });

  } catch (error) {
    console.error("Error in getExitingIssueRequest:", error);
    throw error;
  }
};

module.exports = {
  addIssue,
  getExitingIssueRequest
};
