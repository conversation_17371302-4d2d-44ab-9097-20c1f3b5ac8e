const { dbModels } = require("../database/connection");

const createMeterReadingOrder = async (data) => {
  return await dbModels.mst_meter_reading_orders.create(data);
};

const createMappedGAArea = async (data) => {
  return await dbModels.trx_admin_mapped_ga_areas.create(data);
};

const createAdmin = async (data) => {
  return await dbModels.mst_admin.create(data);
};

const createMeterReadingUnits = async (unitList) => {
  return await dbModels.trx_meter_reading_units.bulkCreate(unitList);
};

module.exports = {
  createMeterReadingOrder,
  createMappedGAArea,
  createAdmin,
  createMeterReadingUnits
};
