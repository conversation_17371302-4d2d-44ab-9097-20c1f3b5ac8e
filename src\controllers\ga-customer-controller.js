const { sendSuccess, sendError } = require("../helpers/responseHelper");
const gaCustomerService = require("../services/ga-customer-service");

exports.ongoingList = async (req, res) => {
  try {
    const result = await gaCustomerService.ongoingList(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.completedList = async (req, res) => {
  try {
    const result = await gaCustomerService.completedList(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.ongoingSummaryCounts = async (req, res) => {
  try {
    const result = await gaCustomerService.ongoingSummaryCounts(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.completedSummaryCounts = async (req, res) => {
  try {
    const result = await gaCustomerService.completedSummaryCounts(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.areaWiseList = async (req, res) => {
  try {
    const result = await gaCustomerService.areaWiseList(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.areaCountData = async (req, res) => {
  try {
    const result = await gaCustomerService.areaCountData(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.contractorAreaList = async (req, res) => {
  try {
    const result = await gaCustomerService.contractorAreaList(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.lmcAreaList = async (req, res) => {
  try {
    const result = await gaCustomerService.lmcAreaList(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.dmaAreaList = async (req, res) => {
  try {
    const result = await gaCustomerService.dmaAreaList(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.foAreaList = async (req, res) => {
  try {
    const result = await gaCustomerService.  foAreaList(req.body);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};