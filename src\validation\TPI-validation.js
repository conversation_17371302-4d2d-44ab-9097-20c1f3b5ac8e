const { Joi } = require("express-validation");
const moment = require("moment");

const getTpiCasesSummaryValidation = {
  query: Joi.object({
    tpi_id: Joi.number().integer().required(),
  }),
};

const getTpiApplicationValidation = {
  query: Joi.object({
    tpi_id: Joi.number().integer().required(),
    caseType: Joi.string().valid("active", "closed").default("active"),
    search: Joi.string().optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).default(10),
    lmc_stage: Joi.string()
      .valid("feasibility-check", "installation", "commissioning")
      .optional(),
    visit_status: Joi.string()
      .valid(
        "schedule",
        "reschedule",
        "to-be-reschedule",
        "pending",
        "inprogress",
        "completed",
        "failed"
      )
      .optional(),
    tpi_status: Joi.string()
      .valid("pending", "inprogress", "approved", "rework", "rejected")
      .optional(),
  }),
};

const getTpiCustomerReviewList = {
  query: Joi.object({
    search: Joi.string().optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).default(10),
    startDate: Joi.string().optional(),
    endDate: Joi.string().optional(),
  }),
};

// const getTpiCustomerReviewUpdate = {
//   query: Joi.object({
//     customer_connection_id: Joi.number().integer().required(),
//     status: Joi.number().integer().min(1).default(1),
//   }),
// };

const getTpiStatusCountValidation = {
  body: Joi.object({
    lmc_stage: Joi.string().valid('feasibility-check', 'installation', 'commissioning').required().max(50),
    area_id: Joi.array().items(Joi.number().integer()).required(), 
  })
};
const getFeasibilityInstallationCustomerDetailsValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    lmc_stage: Joi.string().valid('feasibility-check', 'installation', 'commissioning').required().max(50)
  })
};
const customerStatusHistoryValidation = {
  body: Joi.object({
    search: Joi.string().allow("").optional(),
    page: Joi.number().integer().allow("").optional(),
    limit: Joi.number().integer().allow("").optional(),
    lmc_stage: Joi.string().valid('feasibility-check', 'installation', 'commissioning').required().max(50),
    startDate: Joi.string().allow("").optional(),
    endDate: Joi.string().allow("").optional(),
    areaIds: Joi.array().items(Joi.number().integer()).required(), 
  })
};
 
const lmcWorkApprovalStatusUpdateValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    lmc_connection_mapping_id: Joi.number().integer().required(),
    tpi_status: Joi.string()
      .valid('no-submission', 'pending', 'resubmit', 'completed', 'rejected', 'pending-eo', 'resubmit-eo', 'completed-eo', 'rejected-eo')
      .required(),
    lmc_stage: Joi.string().valid('feasibility-check', 'installation', 'commissioning').required().max(50),
    reason: Joi.string().allow("").optional(),
  })
};

const customerStatusUpdateValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    internal_status: Joi.string()
      .valid('application-pending', 'application-resubmit', 'application-completed', 'application-rejected', 'application-pending-so')
      .required(),
    resubmit_reasons: Joi.array().items(Joi.any()).min(1).required(),
    status: Joi.string().required()
  })
};


const lmcStatusDataListValidation = {
  body: Joi.object({
    search: Joi.string().allow("").optional(),
    page: Joi.number().integer().allow("").optional(),
    limit: Joi.number().integer().allow("").optional(),
    startDate: Joi.string().allow("").optional(),
    endDate: Joi.string().allow("").optional(),
    admin_type : Joi.string().required(),
  })
};


const lmcStatusUpdateValidation = {
  body: Joi.object({
    tpi_onboard_approve_id: Joi.number().integer().required(),
    approve_status: Joi.string()
      .valid('pending','approved', 'rejected')
      .required(),
    remark: Joi.string().allow("").optional(),  
  })
};

const lmcOnboardCountValidation = {
  body: Joi.object({
    admin_type: Joi.string()
      .valid('DMA','LMC', 'FO')
      .required(),
  })
};

const getCustomerApplicationDetailsValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required()
  })
};

const getPendingOnboardingListValidation = {
  body: Joi.object({
    persona_type: Joi.string().required().valid('dma', 'lmc', 'fo'),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).default(10)
  })
};

const onboardingApprovalValidation = {
  body: Joi.object({
    persona_type: Joi.string()
      .valid('dma', 'lmc', 'fo')
      .required(),
    supervisor_id: Joi.number()
      .integer()
      .required(),
    approve_status: Joi.string()
      .valid('approved', 'rejected')
      .required(),
    remark: Joi.string()
      .allow('')
      .optional(),
  })
};

const jmrApprovalStatusUpdateValidation = {
  body: Joi.object({
    tmma_id: Joi.number().integer().required(),
    customer_connection_id: Joi.number().integer().required(),
    lmc_connection_stage_id: Joi.number().integer().required(),
    status: Joi.string().required(),
    reason: Joi.string().required()
  })
};

const getTpiCategoryValidation = {
  body: Joi.object({
    master_type: Joi.string().required()
  })
};

const areaWiseListValidation = {
  body: Joi.object({
    tab_type: Joi.string()
      .valid('registaration', 'active')
      .required(),
    geographical_area: Joi.number().integer().required(),
    area_id: Joi.array().items(Joi.number()).min(1).required(),
  })
};

const gaApprovalStatusUpdateValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    action: Joi.string()
      .valid('send-back-to-so', 'send-for-resubmission')
      .required(),
    reason: Joi.string().required(),
    review_type: Joi.string().valid('customer-application', 'lmc-installation', 'lmc-jmr').required()
  })
};

const commentDetailsTpiValidation = {  
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    mapping_type: Joi.string().required(),
  }),
};

module.exports = {
  getTpiCasesSummaryValidation,
  getTpiApplicationValidation,
  getTpiStatusCountValidation,
  getFeasibilityInstallationCustomerDetailsValidation,
  customerStatusHistoryValidation,
  getTpiCustomerReviewList,
  lmcWorkApprovalStatusUpdateValidation,
  customerStatusUpdateValidation,
  lmcStatusDataListValidation,
  lmcStatusUpdateValidation,
  lmcOnboardCountValidation,
  getCustomerApplicationDetailsValidation,
  getPendingOnboardingListValidation,
  onboardingApprovalValidation,
  jmrApprovalStatusUpdateValidation,
  getTpiCategoryValidation,
  areaWiseListValidation,
  gaApprovalStatusUpdateValidation,
  commentDetailsTpiValidation
};
