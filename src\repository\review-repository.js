const db = require('../database/connection'); // Assuming Sequelize ORM

exports.storeReviewDetails = async (reviewData, files) => {
  try {
    // Prepare file metadata for database storage
    const fileRecords = files.map((file) => ({
      user_id: reviewData.user_id,
      checkbox_values: JSON.stringify(reviewData.checkboxValues),
      file_name: file.filename,
      file_path: file.path,
      file_type: file.mimetype,
    }));

    // Insert data into database (assuming a 'review_uploads' table exists)
    const insertedFiles = await db.ReviewUploads.bulkCreate(fileRecords);

    return insertedFiles;
  } catch (error) {
    throw new Error('Error storing review details in DB: ' + error.message);
  }
};
