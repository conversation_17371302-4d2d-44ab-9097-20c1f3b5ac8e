const router = require("express").Router();
const swaggerUi = require("swagger-ui-express");
const environment = require("../helpers/environmentHelper");
const swaggerDoc = require("./swagger.json");

const { host, basePath } = environment.envSwaggerConfig();
swaggerDoc["x-host"] = host;
swaggerDoc["x-basePath"] = basePath;
swaggerDoc.servers = [{ url: `${host}${basePath}` }];

router.use(
  "/api-docs",
  swaggerUi.serve,
  swaggerUi.setup(swaggerDoc, { explorer: true })
);
router.use("/api-docs.json", (req, res) => {
  res.json(swaggerDoc);
});

module.exports = { swaggerRouter: router };
