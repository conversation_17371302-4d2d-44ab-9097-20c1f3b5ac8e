const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('mst_banners', {
    banner_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    page_type: {
      type: DataTypes.STRING(10),
      allowNull: true
    },
    banner_name: {
      type: DataTypes.STRING(40),
      allowNull: false
    },
    banner_file_name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    banner_alt_text: {
      type: DataTypes.STRING(40),
      allowNull: false
    },
    banner_file_path: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    banner_file_ext: {
      type: DataTypes.STRING(4),
      allowNull: false
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'mst_banners',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_banner_id",
        unique: true,
        fields: [
          { name: "banner_id" },
        ]
      },
    ]
  });
};
