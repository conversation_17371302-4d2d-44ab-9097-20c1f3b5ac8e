const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_customer_meter",
    {
      customer_meter_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      mapped_lmc_customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_lmc_connection_mapping",
          key: "lmc_connection_mapping_id",
        },
      },
      meter_type: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      meter_no: {
        type: DataTypes.STRING(15),
        allowNull: false,
        unique: "UK_customermeter_meter_no",
      },
      jmr_approval_file: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      meter_installation_datetime: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      meter_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      installed_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_customer_meter",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_customer_meter_id",
          unique: true,
          fields: [{ name: "customer_meter_id" }],
        },
        {
          name: "UK_customermeter_meter_no",
          unique: true,
          fields: [{ name: "meter_no" }],
        },
      ],
    }
  );
};
