const { Joi } = require("express-validation");

const createCapabilityListValidation = {
  body: Joi.object({
    capabilities: Joi.array()
      .items(
        Joi.object({
          capabilities: Joi.string().allow(null, "").required(),
          slug: Joi.string().allow(null, "").optional(),
          parent_id: Joi.number().allow(null, "").required(),
          is_active: Joi.any().allow(null, "").optional(),
          ip_address: Joi.any().allow(null, "").optional(),
          browser: Joi.any().allow(null, "").optional(),
        })
      )
      .required(),
    created_by: Joi.number().integer().required(),
  }),
};

const updateCapabilityListValidation = {
  body: Joi.object({
    capability_list_id: Joi.number().required(),
    capabilities: Joi.string().allow(null, "").optional(),
    slug: Joi.string().allow(null, "").optional(),
    parent_id: Joi.number().allow(null, "").optional(),
    is_active: Joi.any().allow(null, "").optional(),
    ip_address: Joi.any().allow(null, "").optional(),
    browser: Joi.any().allow(null, "").optional(),
    updated_by: Joi.number().integer().required(),
  }),
};

const deleteCapabilityListValidation = {
  body: Joi.object({
    capability_list_id: Joi.number().required(),
    updated_by: Joi.number().required(),
  }),
};

module.exports = {
  createCapabilityListValidation,
  updateCapabilityListValidation,
  deleteCapabilityListValidation,
};
