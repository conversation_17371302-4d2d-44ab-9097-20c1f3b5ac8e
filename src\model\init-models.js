var DataTypes = require("sequelize").DataTypes;
var _audit_logs = require("./audit_logs");
var _trx_document_metadata = require("./trx_document_metadata");
var _mst_admin = require("./mst_admin");
var _mst_admin_documents = require("./mst_admin_documents");
var _mst_agencies = require("./mst_agencies");
var _mst_application_internal_status = require("./mst_application_internal_status");
var _mst_application_status = require("./mst_application_status");
var _mst_areas = require("./mst_areas");
var _mst_available_pngareas = require("./mst_available_pngareas");
var _mst_banners = require("./mst_banners");
var _mst_bpcl_admin_roles = require("./mst_bpcl_admin_roles");
var _mst_cities = require("./mst_cities");
var _mst_connection_services = require("./mst_connection_services");
var _mst_cycle = require("./mst_cycle");
var _mst_geographical_areas = require("./mst_geographical_areas");
var _mst_govt_scheme = require("./mst_govt_scheme");
var _mst_issue_type = require("./mst_issue_type");
var _mst_masters = require("./mst_masters");
var _mst_persona_role = require("./mst_persona_role");
var _mst_pincodes = require("./mst_pincodes");
var _mst_pipeline_extra_cost = require("./mst_pipeline_extra_cost");
var _mst_proof_master = require("./mst_proof_master");
var _mst_service_rates = require("./mst_service_rates");
var _mst_states = require("./mst_states");
var _mst_support_charges = require("./mst_support_charges");
var _trx_admin_mapped_areas = require("./trx_admin_mapped_areas");
var _trx_admin_mapped_areas_pincodes = require("./trx_admin_mapped_areas_pincodes");
var _trx_admin_password_history = require("./trx_admin_password_history");
var _trx_alteration_request = require("./trx_alteration_request");
var _trx_bpcl_admin_role_mapping = require("./trx_bpcl_admin_role_mapping");
var _trx_change_address_request = require("./trx_change_address_request");
var _trx_connection_meter_bills = require("./trx_connection_meter_bills");
var _trx_connection_payments = require("./trx_connection_payments");
var _trx_connection_request = require("./trx_connection_request");
var _trx_customer = require("./trx_customer");
var _trx_customer_connection = require("./trx_customer_connection");
var _trx_customer_connection_address = require("./trx_customer_connection_address");
var _trx_customer_connection_field_officer_mapping = require("./trx_customer_connection_field_officer_mapping");
var _trx_customer_connection_status_history = require("./trx_customer_connection_status_history");
var _trx_customer_connection_tpi_mapping = require("./trx_customer_connection_tpi_mapping");
var _trx_customer_connection_tpi_rework = require("./trx_customer_connection_tpi_rework");
var _trx_customer_meter = require("./trx_customer_meter");
var _trx_customer_meter_readings = require("./trx_customer_meter_readings");
var _trx_customer_mobile_history = require("./trx_customer_mobile_history");
var _trx_customer_name_transfer_request = require("./trx_customer_name_transfer_request");
var _trx_customer_notifications = require("./trx_customer_notifications");
var _trx_customer_proof_documents = require("./trx_customer_proof_documents");
var _trx_dma_help_with_reg_mapping = require("./trx_dma_help_with_reg_mapping");
var _trx_extra_point_request = require("./trx_extra_point_request");
var _trx_help_with_registration = require("./trx_help_with_registration");
var _trx_lmc_pipeline = require("./trx_lmc_pipeline");
var _trx_lmc_pipeline_files = require("./trx_lmc_pipeline_files");
var _trx_logs = require("./trx_logs");
var _trx_mapped_lmc_customer_connection = require("./trx_mapped_lmc_customer_connection");
var _trx_service_request = require("./trx_service_request");
var _trx_service_request_documents = require("./trx_service_request_documents");
var _trx_tpi_dma_mapping = require("./trx_tpi_dma_mapping");
var _trx_tpi_lmc_approve = require("./trx_tpi_lmc_approve");
var _trx_tpi_onboard_approve = require("./trx_tpi_onboard_approve");
var _trx_tpi_manual_meter_approval = require("./trx_tpi_manual_meter_approval");
var _trx_admin_status_history = require("./trx_admin_status_history");
var _trx_admin_contractor_other_details = require("./trx_admin_contractor_other_details");
var _trx_meter_reading_orders = require("./trx_meter_reading_orders");
var _trx_admin_mapped_ga_areas = require("./trx_admin_mapped_ga_areas");
var _trx_admin_onboard_approve = require("./trx_admin_onboard_approve");
var _trx_bpcl_admin_mapped_ga_areas = require("./trx_bpcl_admin_mapped_ga_areas");
var _trx_lmc_connection_mapping = require("./trx_lmc_connection_mapping");
var _trx_lmc_pipeline_documents = require("./trx_lmc_pipeline_documents");
var _trx_mapped_lmc_connection_stages = require("./trx_mapped_lmc_connection_stages");
var _trx_mapped_lmc_feasibility_commission_details = require("./trx_mapped_lmc_feasibility_commission_details");
var _trx_mapped_lmc_installation_details = require("./trx_mapped_lmc_installation_details");
var _mst_meter_reading_orders = require("./mst_meter_reading_orders");
var _trx_meter_reading_units = require("./trx_meter_reading_units");
var _trx_bpcl_subadmin_connection_approval = require("./trx_bpcl_subadmin_connection_approval");
var _trx_connection_review_history = require("./trx_connection_review_history");

function initModels(sequelize) {
  var trx_admin_status_history = _trx_admin_status_history(
    sequelize,
    DataTypes
  );
  var trx_bpcl_admin_mapped_ga_areas = _trx_bpcl_admin_mapped_ga_areas(
    sequelize,
    DataTypes
  );

  var trx_tpi_manual_meter_approval = _trx_tpi_manual_meter_approval(
    sequelize,
    DataTypes
  );
  var trx_tpi_onboard_approve = _trx_tpi_onboard_approve(sequelize, DataTypes);
  var audit_logs = _audit_logs(sequelize, DataTypes);
  var mst_admin = _mst_admin(sequelize, DataTypes);
  var mst_admin_documents = _mst_admin_documents(sequelize, DataTypes);
  var mst_agencies = _mst_agencies(sequelize, DataTypes);
  var mst_application_internal_status = _mst_application_internal_status(
    sequelize,
    DataTypes
  );
  var mst_application_status = _mst_application_status(sequelize, DataTypes);
  var mst_areas = _mst_areas(sequelize, DataTypes);
  var mst_available_pngareas = _mst_available_pngareas(sequelize, DataTypes);
  var mst_banners = _mst_banners(sequelize, DataTypes);
  var mst_bpcl_admin_roles = _mst_bpcl_admin_roles(sequelize, DataTypes);
  var mst_cities = _mst_cities(sequelize, DataTypes);
  var mst_connection_services = _mst_connection_services(sequelize, DataTypes);
  var mst_cycle = _mst_cycle(sequelize, DataTypes);
  var mst_geographical_areas = _mst_geographical_areas(sequelize, DataTypes);
  var mst_govt_scheme = _mst_govt_scheme(sequelize, DataTypes);
  var mst_issue_type = _mst_issue_type(sequelize, DataTypes);
  var mst_masters = _mst_masters(sequelize, DataTypes);
  var mst_persona_role = _mst_persona_role(sequelize, DataTypes);
  var mst_pincodes = _mst_pincodes(sequelize, DataTypes);
  var mst_pipeline_extra_cost = _mst_pipeline_extra_cost(sequelize, DataTypes);
  var mst_proof_master = _mst_proof_master(sequelize, DataTypes);
  var mst_service_rates = _mst_service_rates(sequelize, DataTypes);
  var mst_states = _mst_states(sequelize, DataTypes);
  var mst_support_charges = _mst_support_charges(sequelize, DataTypes);
  var trx_admin_mapped_areas = _trx_admin_mapped_areas(sequelize, DataTypes);
  var trx_admin_mapped_areas_pincodes = _trx_admin_mapped_areas_pincodes(
    sequelize,
    DataTypes
  );
  var trx_admin_password_history = _trx_admin_password_history(
    sequelize,
    DataTypes
  );
  var trx_alteration_request = _trx_alteration_request(sequelize, DataTypes);
  var trx_bpcl_admin_role_mapping = _trx_bpcl_admin_role_mapping(
    sequelize,
    DataTypes
  );
  var trx_change_address_request = _trx_change_address_request(
    sequelize,
    DataTypes
  );
  var trx_connection_meter_bills = _trx_connection_meter_bills(
    sequelize,
    DataTypes
  );
  var trx_connection_payments = _trx_connection_payments(sequelize, DataTypes);
  var trx_connection_request = _trx_connection_request(sequelize, DataTypes);
  var trx_customer = _trx_customer(sequelize, DataTypes);
  var trx_customer_connection = _trx_customer_connection(sequelize, DataTypes);
  var trx_customer_connection_address = _trx_customer_connection_address(
    sequelize,
    DataTypes
  );
  var trx_customer_connection_field_officer_mapping =
    _trx_customer_connection_field_officer_mapping(sequelize, DataTypes);
  var trx_customer_connection_status_history =
    _trx_customer_connection_status_history(sequelize, DataTypes);
  var trx_customer_connection_tpi_mapping =
    _trx_customer_connection_tpi_mapping(sequelize, DataTypes);
  var trx_customer_connection_tpi_rework = _trx_customer_connection_tpi_rework(
    sequelize,
    DataTypes
  );
  var trx_customer_meter = _trx_customer_meter(sequelize, DataTypes);
  var trx_customer_meter_readings = _trx_customer_meter_readings(
    sequelize,
    DataTypes
  );
  var trx_customer_mobile_history = _trx_customer_mobile_history(
    sequelize,
    DataTypes
  );
  var trx_customer_name_transfer_request = _trx_customer_name_transfer_request(
    sequelize,
    DataTypes
  );
  var trx_customer_notifications = _trx_customer_notifications(
    sequelize,
    DataTypes
  );
  var trx_customer_proof_documents = _trx_customer_proof_documents(
    sequelize,
    DataTypes
  );
  var trx_dma_help_with_reg_mapping = _trx_dma_help_with_reg_mapping(
    sequelize,
    DataTypes
  );
  var trx_extra_point_request = _trx_extra_point_request(sequelize, DataTypes);
  var trx_help_with_registration = _trx_help_with_registration(
    sequelize,
    DataTypes
  );
  var trx_lmc_pipeline = _trx_lmc_pipeline(sequelize, DataTypes);
  var trx_lmc_pipeline_files = _trx_lmc_pipeline_files(sequelize, DataTypes);
  var trx_logs = _trx_logs(sequelize, DataTypes);
  var trx_mapped_lmc_customer_connection = _trx_mapped_lmc_customer_connection(
    sequelize,
    DataTypes
  );
  var trx_service_request = _trx_service_request(sequelize, DataTypes);
  var trx_service_request_documents = _trx_service_request_documents(
    sequelize,
    DataTypes
  );
  var trx_tpi_dma_mapping = _trx_tpi_dma_mapping(sequelize, DataTypes);
  var trx_tpi_lmc_approve = _trx_tpi_lmc_approve(sequelize, DataTypes);
  var trx_admin_contractor_other_details = _trx_admin_contractor_other_details(
    sequelize,
    DataTypes
  );
  var trx_meter_reading_orders = _trx_meter_reading_orders(
    sequelize,
    DataTypes
  );
  var trx_admin_mapped_ga_areas = _trx_admin_mapped_ga_areas(
    sequelize,
    DataTypes
  );
  var trx_admin_onboard_approve = _trx_admin_onboard_approve(
    sequelize,
    DataTypes
  );
  var trx_lmc_connection_mapping = _trx_lmc_connection_mapping(
    sequelize,
    DataTypes
  );
  var trx_lmc_pipeline_documents = _trx_lmc_pipeline_documents(
    sequelize,
    DataTypes
  );
  var trx_mapped_lmc_connection_stages = _trx_mapped_lmc_connection_stages(
    sequelize,
    DataTypes
  );
  var trx_mapped_lmc_feasibility_commission_details =
    _trx_mapped_lmc_feasibility_commission_details(sequelize, DataTypes);
  var trx_mapped_lmc_installation_details =
    _trx_mapped_lmc_installation_details(sequelize, DataTypes);
  var mst_meter_reading_orders = _mst_meter_reading_orders(
    sequelize,
    DataTypes
  );
  var trx_meter_reading_units = _trx_meter_reading_units(sequelize, DataTypes);
  var trx_document_metadata = _trx_document_metadata(sequelize, DataTypes);
  var trx_bpcl_subadmin_connection_approval =
    _trx_bpcl_subadmin_connection_approval(sequelize, DataTypes);
  var trx_connection_review_history = _trx_connection_review_history(
    sequelize,
    DataTypes
  );

  mst_admin_documents.belongsTo(mst_admin, {
    as: "admin",
    foreignKey: "admin_id",
  });
  mst_admin.hasMany(mst_admin_documents, {
    as: "mst_admin_documents",
    foreignKey: "admin_id",
  });
  trx_admin_mapped_areas.belongsTo(mst_admin, {
    as: "admin",
    foreignKey: "admin_id",
  });
  mst_admin.hasMany(trx_admin_mapped_areas, {
    as: "trx_admin_mapped_areas",
    foreignKey: "admin_id",
  });
  trx_admin_mapped_areas_pincodes.belongsTo(trx_admin_mapped_areas, {
    as: "admin",
    foreignKey: "admin_mapped_area_id",
  });
  trx_admin_mapped_areas.hasMany(trx_admin_mapped_areas_pincodes, {
    as: "trx_admin_mapped_areas_pincodes",
    foreignKey: "admin_mapped_area_id",
  });
  trx_admin_password_history.belongsTo(mst_admin, {
    as: "admin",
    foreignKey: "admin_id",
  });
  mst_admin.hasMany(trx_admin_password_history, {
    as: "trx_admin_password_histories",
    foreignKey: "admin_id",
  });
  trx_customer_connection_field_officer_mapping.belongsTo(mst_admin, {
    as: "field_officer",
    foreignKey: "field_officer_id",
  });
  mst_admin.hasMany(trx_customer_connection_field_officer_mapping, {
    as: "trx_customer_connection_field_officer_mappings",
    foreignKey: "field_officer_id",
  });
  trx_customer_connection_tpi_mapping.belongsTo(mst_admin, {
    as: "tpi",
    foreignKey: "tpi_id",
  });
  mst_admin.hasMany(trx_customer_connection_tpi_mapping, {
    as: "trx_customer_connection_tpi_mappings",
    foreignKey: "tpi_id",
  });
  trx_customer_meter.belongsTo(mst_admin, {
    as: "installed_by_mst_admin",
    foreignKey: "installed_by",
  });
  mst_admin.hasMany(trx_customer_meter, {
    as: "trx_customer_meters",
    foreignKey: "installed_by",
  });
  trx_dma_help_with_reg_mapping.belongsTo(mst_admin, {
    as: "dma_supervisor_assigned",
    foreignKey: "dma_supervisor_assigned_id",
  });
  mst_admin.hasMany(trx_dma_help_with_reg_mapping, {
    as: "trx_dma_help_with_reg_mappings",
    foreignKey: "dma_supervisor_assigned_id",
  });
  trx_mapped_lmc_customer_connection.belongsTo(mst_admin, {
    as: "lmc",
    foreignKey: "lmc_id",
  });
  mst_admin.hasMany(trx_mapped_lmc_customer_connection, {
    as: "trx_mapped_lmc_customer_connections",
    foreignKey: "lmc_id",
  });
  trx_mapped_lmc_customer_connection.belongsTo(mst_admin, {
    as: "tpi",
    foreignKey: "tpi_id",
  });
  mst_admin.hasMany(trx_mapped_lmc_customer_connection, {
    as: "tpi_trx_mapped_lmc_customer_connections",
    foreignKey: "tpi_id",
  });
  trx_tpi_dma_mapping.belongsTo(mst_admin, { as: "dma", foreignKey: "dma_id" });
  mst_admin.hasMany(trx_tpi_dma_mapping, {
    as: "trx_tpi_dma_mappings",
    foreignKey: "dma_id",
  });
  trx_tpi_dma_mapping.belongsTo(mst_admin, { as: "tpi", foreignKey: "tpi_id" });
  mst_admin.hasMany(trx_tpi_dma_mapping, {
    as: "tpi_trx_tpi_dma_mappings",
    foreignKey: "tpi_id",
  });
  trx_tpi_lmc_approve.belongsTo(mst_admin, {
    as: "lmc_supervisor",
    foreignKey: "lmc_supervisor_id",
  });
  mst_admin.hasMany(trx_tpi_lmc_approve, {
    as: "trx_tpi_lmc_approves",
    foreignKey: "lmc_supervisor_id",
  });
  trx_tpi_lmc_approve.belongsTo(mst_admin, { as: "tpi", foreignKey: "tpi_id" });
  mst_admin.hasMany(trx_tpi_lmc_approve, {
    as: "tpi_trx_tpi_lmc_approves",
    foreignKey: "tpi_id",
  });
  mst_pincodes.belongsTo(mst_areas, { as: "area", foreignKey: "area_id" });
  mst_areas.hasMany(mst_pincodes, {
    as: "mst_pincodes",
    foreignKey: "area_id",
  });
  mst_service_rates.belongsTo(mst_areas, { as: "area", foreignKey: "area_id" });
  mst_areas.hasMany(mst_service_rates, {
    as: "mst_service_rates",
    foreignKey: "area_id",
  });
  mst_cycle.belongsTo(mst_areas, { as: "area", foreignKey: "area_id" });
  mst_areas.hasMany(mst_cycle, {
    as: "mst_cycle",
    foreignKey: "area_id",
  });
  mst_support_charges.belongsTo(mst_areas, {
    as: "area",
    foreignKey: "area_id",
  });
  mst_areas.hasOne(mst_support_charges, {
    as: "mst_support_charge",
    foreignKey: "area_id",
  });
  trx_admin_mapped_areas.belongsTo(mst_areas, {
    as: "area",
    foreignKey: "area_id",
  });
  mst_areas.hasMany(trx_admin_mapped_areas, {
    as: "trx_admin_mapped_areas",
    foreignKey: "area_id",
  });
  trx_customer_connection.belongsTo(mst_available_pngareas, {
    as: "available_pngarea",
    foreignKey: "available_pngareas_id",
  });
  mst_available_pngareas.hasMany(trx_customer_connection, {
    as: "trx_customer_connections",
    foreignKey: "available_pngareas_id",
  });
  trx_customer_connection_address.belongsTo(mst_available_pngareas, {
    as: "connection_pngarea",
    foreignKey: "connection_pngareas_id",
  });
  mst_available_pngareas.hasMany(trx_customer_connection_address, {
    as: "trx_customer_connection_addresses",
    foreignKey: "connection_pngareas_id",
  });
  trx_customer_connection_address.belongsTo(mst_available_pngareas, {
    as: "permanent_pngarea",
    foreignKey: "permanent_pngareas_id",
  });
  mst_available_pngareas.hasMany(trx_customer_connection_address, {
    as: "permanent_pngareas_trx_customer_connection_addresses",
    foreignKey: "permanent_pngareas_id",
  });
  trx_help_with_registration.belongsTo(mst_available_pngareas, {
    as: "available_pngarea",
    foreignKey: "available_pngareas_id",
  });
  mst_available_pngareas.hasMany(trx_help_with_registration, {
    as: "trx_help_with_registrations",
    foreignKey: "available_pngareas_id",
  });
  trx_bpcl_admin_role_mapping.belongsTo(mst_bpcl_admin_roles, {
    as: "bpcl_admin_role",
    foreignKey: "bpcl_admin_role_id",
  });
  mst_bpcl_admin_roles.hasMany(trx_bpcl_admin_role_mapping, {
    as: "trx_bpcl_admin_role_mappings",
    foreignKey: "bpcl_admin_role_id",
  });
  mst_geographical_areas.belongsTo(mst_cities, {
    as: "city",
    foreignKey: "city_id",
  });
  mst_cities.hasMany(mst_geographical_areas, {
    as: "mst_geographical_areas",
    foreignKey: "city_id",
  });
  mst_service_rates.belongsTo(mst_connection_services, {
    as: "connection_service",
    foreignKey: "connection_service_id",
  });
  mst_connection_services.hasMany(mst_service_rates, {
    as: "mst_service_rates",
    foreignKey: "connection_service_id",
  });
  trx_customer_connection_field_officer_mapping.belongsTo(mst_cycle, {
    as: "cycle",
    foreignKey: "cycle_id",
  });
  mst_cycle.hasMany(trx_customer_connection_field_officer_mapping, {
    as: "trx_customer_connection_field_officer_mappings",
    foreignKey: "cycle_id",
  });
  trx_customer_meter_readings.belongsTo(mst_cycle, {
    as: "cycle",
    foreignKey: "cycle_id",
  });
  mst_cycle.hasMany(trx_customer_meter_readings, {
    as: "trx_customer_meter_readings",
    foreignKey: "cycle_id",
  });
  mst_areas.belongsTo(mst_geographical_areas, {
    as: "geographical_area",
    foreignKey: "geographical_area_id",
  });
  mst_geographical_areas.hasMany(mst_areas, {
    as: "mst_areas",
    foreignKey: "geographical_area_id",
  });
  mst_pincodes.belongsTo(mst_geographical_areas, {
    as: "geographical_area",
    foreignKey: "geographical_area_id",
  });
  mst_geographical_areas.hasMany(mst_pincodes, {
    as: "mst_pincodes",
    foreignKey: "geographical_area_id",
  });
  trx_customer_connection.belongsTo(mst_govt_scheme, {
    as: "govt_scheme",
    foreignKey: "govt_scheme_id",
  });
  mst_govt_scheme.hasMany(trx_customer_connection, {
    as: "trx_customer_connections",
    foreignKey: "govt_scheme_id",
  });
  trx_service_request.belongsTo(mst_issue_type, {
    as: "issue_type",
    foreignKey: "issue_type_id",
  });
  mst_issue_type.hasMany(trx_service_request, {
    as: "trx_service_requests",
    foreignKey: "issue_type_id",
  });
  mst_issue_type.belongsTo(mst_masters, {
    as: "issue_category",
    foreignKey: "issue_category_id",
  });
  mst_masters.hasMany(mst_issue_type, {
    as: "mst_issue_types",
    foreignKey: "issue_category_id",
  });
  trx_alteration_request.belongsTo(mst_masters, {
    as: "area",
    foreignKey: "area_id",
  });
  mst_masters.hasMany(trx_alteration_request, {
    as: "trx_alteration_requests",
    foreignKey: "area_id",
  });
  trx_connection_request.belongsTo(mst_masters, {
    as: "estimated_duration",
    foreignKey: "estimated_duration_id",
  });
  mst_masters.hasMany(trx_connection_request, {
    as: "trx_connection_requests",
    foreignKey: "estimated_duration_id",
  });
  trx_connection_request.belongsTo(mst_masters, {
    as: "request_reason",
    foreignKey: "request_reason_id",
  });
  mst_masters.hasMany(trx_connection_request, {
    as: "request_reason_trx_connection_requests",
    foreignKey: "request_reason_id",
  });
  trx_customer_connection_status_history.belongsTo(mst_masters, {
    as: "reasones",
    foreignKey: "reason_id",
  });
  mst_masters.hasMany(trx_customer_connection_status_history, {
    as: "trx_customer_connection_status_histories",
    foreignKey: "reason_id",
  });
  trx_customer_connection_tpi_mapping.belongsTo(mst_masters, {
    as: "tpi_mapping_reasons",
    foreignKey: "reason_id",
  });
  mst_masters.hasMany(trx_customer_connection_tpi_mapping, {
    as: "trx_customer_connection_tpi_mapping",
    foreignKey: "reason_id",
  });

  trx_customer_connection.belongsTo(mst_masters, {
    as: "tcc_reason",
    foreignKey: "reason_id",
  });

  mst_masters.hasMany(trx_customer_connection, {
    as: "trx_customer_connection",
    foreignKey: "reason_id",
  });

  trx_customer_name_transfer_request.belongsTo(mst_masters, {
    as: "name_transfer_reason_mst_master",
    foreignKey: "name_transfer_reason",
  });
  mst_masters.hasMany(trx_customer_name_transfer_request, {
    as: "trx_customer_name_transfer_requests",
    foreignKey: "name_transfer_reason",
  });
  trx_service_request.belongsTo(mst_masters, {
    as: "issue_category",
    foreignKey: "issue_category_id",
  });
  mst_masters.hasMany(trx_service_request, {
    as: "trx_service_requests",
    foreignKey: "issue_category_id",
  });
  mst_admin.belongsTo(mst_persona_role, {
    as: "persona_role",
    foreignKey: "persona_role_id",
  });
  mst_persona_role.hasMany(mst_admin, {
    as: "mst_admins",
    foreignKey: "persona_role_id",
  });
  mst_available_pngareas.belongsTo(mst_pincodes, {
    as: "pincodeAreasAlias",
    foreignKey: "pincode_id",
  });
  mst_pincodes.hasMany(mst_available_pngareas, {
    as: "mst_available_pngareas",
    foreignKey: "pincode_id",
  });
  trx_admin_mapped_areas_pincodes.belongsTo(mst_pincodes, {
    as: "pincodes",
    foreignKey: "pincode_id",
  });
  mst_pincodes.hasMany(trx_admin_mapped_areas_pincodes, {
    as: "trx_admin_mapped_areas_pincodes",
    foreignKey: "pincode_id",
  });
  trx_customer_proof_documents.belongsTo(mst_proof_master, {
    as: "proof_master",
    foreignKey: "proof_master_id",
  });
  mst_proof_master.hasMany(trx_customer_proof_documents, {
    as: "trx_customer_proof_documents",
    foreignKey: "proof_master_id",
  });
  mst_cities.belongsTo(mst_states, { as: "state", foreignKey: "state_id" });
  mst_states.hasMany(mst_cities, { as: "mst_cities", foreignKey: "state_id" });
  mst_geographical_areas.belongsTo(mst_states, {
    as: "state",
    foreignKey: "state_id",
  });
  mst_states.hasMany(mst_geographical_areas, {
    as: "mst_geographical_areas",
    foreignKey: "state_id",
  });
  trx_connection_payments.belongsTo(trx_connection_meter_bills, {
    as: "meter_bill",
    foreignKey: "meter_bill_id",
  });
  trx_connection_meter_bills.hasMany(trx_connection_payments, {
    as: "trx_connection_payments",
    foreignKey: "meter_bill_id",
  });
  trx_customer_connection.belongsTo(trx_customer, {
    as: "customer",
    foreignKey: "customer_id",
  });
  trx_customer.hasMany(trx_customer_connection, {
    as: "trx_customer_connections",
    foreignKey: "customer_id",
  });
  trx_customer_mobile_history.belongsTo(trx_customer, {
    as: "new_customer",
    foreignKey: "new_customer_id",
  });
  trx_customer.hasMany(trx_customer_mobile_history, {
    as: "trx_customer_mobile_histories",
    foreignKey: "new_customer_id",
  });
  trx_customer_mobile_history.belongsTo(trx_customer, {
    as: "old_customer",
    foreignKey: "old_customer_id",
  });
  trx_customer.hasMany(trx_customer_mobile_history, {
    as: "old_customer_trx_customer_mobile_histories",
    foreignKey: "old_customer_id",
  });
  trx_alteration_request.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_alteration_request, {
    as: "trx_alteration_requests",
    foreignKey: "customer_connection_id",
  });
  trx_connection_meter_bills.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_connection_meter_bills, {
    as: "trx_connection_meter_bills",
    foreignKey: "customer_connection_id",
  });
  trx_connection_payments.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_connection_payments, {
    as: "trx_connection_payments",
    foreignKey: "customer_connection_id",
  });
  trx_connection_request.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_connection_request, {
    as: "trx_connection_requests",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection_address.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_customer_connection_address, {
    as: "trx_customer_connection_addresses",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection_field_officer_mapping.belongsTo(
    trx_customer_connection,
    { as: "customer_connection", foreignKey: "customer_connection_id" }
  );
  trx_customer_connection.hasMany(
    trx_customer_connection_field_officer_mapping,
    {
      as: "trx_customer_connection_field_officer_mappings",
      foreignKey: "customer_connection_id",
    }
  );
  trx_customer_connection_status_history.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_customer_connection_status_history, {
    as: "trx_customer_connection_status_histories",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection_tpi_mapping.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_customer_connection_tpi_mapping, {
    as: "trx_customer_connection_tpi_mappings",
    foreignKey: "customer_connection_id",
  });
  trx_customer_meter.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_customer_meter, {
    as: "trx_customer_meters",
    foreignKey: "customer_connection_id",
  });
  trx_customer_meter_readings.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_customer_meter_readings, {
    as: "trx_customer_meter_readings",
    foreignKey: "customer_connection_id",
  });
  trx_customer_name_transfer_request.belongsTo(trx_customer_connection, {
    as: "existing_connection",
    foreignKey: "existing_connection_id",
  });
  trx_customer_connection.hasMany(trx_customer_name_transfer_request, {
    as: "trx_customer_name_transfer_requests",
    foreignKey: "existing_connection_id",
  });
  trx_customer_name_transfer_request.belongsTo(trx_customer_connection, {
    as: "new_connection",
    foreignKey: "new_connection_id",
  });
  trx_customer_connection.hasMany(trx_customer_name_transfer_request, {
    as: "new_connection_trx_customer_name_transfer_requests",
    foreignKey: "new_connection_id",
  });
  trx_customer_notifications.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_customer_notifications, {
    as: "trx_customer_notifications",
    foreignKey: "customer_connection_id",
  });
  trx_customer_proof_documents.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_customer_proof_documents, {
    as: "trx_customer_proof_documents",
    foreignKey: "customer_connection_id",
  });
  trx_extra_point_request.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_extra_point_request, {
    as: "trx_extra_point_requests",
    foreignKey: "customer_connection_id",
  });
  trx_help_with_registration.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_help_with_registration, {
    as: "trx_help_with_registrations",
    foreignKey: "customer_connection_id",
  });
  trx_mapped_lmc_customer_connection.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_mapped_lmc_customer_connection, {
    as: "trx_mapped_lmc_customer_connections",
    foreignKey: "customer_connection_id",
  });
  trx_service_request.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_service_request, {
    as: "trx_service_requests",
    foreignKey: "customer_connection_id",
  });
  trx_alteration_request.belongsTo(trx_customer_connection_address, {
    as: "customer_connection_address",
    foreignKey: "customer_connection_address_id",
  });
  trx_customer_connection_address.hasMany(trx_alteration_request, {
    as: "trx_alteration_requests",
    foreignKey: "customer_connection_address_id",
  });
  trx_connection_request.belongsTo(trx_customer_connection_address, {
    as: "customer_connection_address",
    foreignKey: "customer_connection_address_id",
  });
  trx_customer_connection_address.hasMany(trx_connection_request, {
    as: "trx_connection_requests",
    foreignKey: "customer_connection_address_id",
  });
  trx_customer_proof_documents.belongsTo(trx_customer_connection_address, {
    as: "customer_connection_address",
    foreignKey: "customer_connection_address_id",
  });
  trx_customer_connection_address.hasMany(trx_customer_proof_documents, {
    as: "trx_customer_proof_documents",
    foreignKey: "customer_connection_address_id",
  });
  trx_extra_point_request.belongsTo(trx_customer_connection_address, {
    as: "customer_connection_address",
    foreignKey: "customer_connection_address_id",
  });
  trx_customer_connection_address.hasMany(trx_extra_point_request, {
    as: "trx_extra_point_requests",
    foreignKey: "customer_connection_address_id",
  });
  trx_customer_connection_tpi_rework.belongsTo(
    trx_customer_connection_tpi_mapping,
    {
      as: "customer_connection_tpi_mapping",
      foreignKey: "customer_connection_tpi_mapping_id",
    }
  );
  trx_customer_connection_tpi_mapping.hasMany(
    trx_customer_connection_tpi_rework,
    {
      as: "trx_customer_connection_tpi_reworks",
      foreignKey: "customer_connection_tpi_mapping_id",
    }
  );
  trx_connection_meter_bills.belongsTo(trx_customer_meter, {
    as: "customer_meter",
    foreignKey: "customer_meter_id",
  });
  trx_customer_meter.hasMany(trx_connection_meter_bills, {
    as: "trx_connection_meter_bills",
    foreignKey: "customer_meter_id",
  });
  trx_customer_meter_readings.belongsTo(trx_customer_meter, {
    as: "customer_meter",
    foreignKey: "customer_meter_id",
  });
  trx_customer_meter.hasMany(trx_customer_meter_readings, {
    as: "trx_customer_meter_readings",
    foreignKey: "customer_meter_id",
  });
  trx_connection_meter_bills.belongsTo(trx_customer_meter_readings, {
    as: "customer_meter_reading",
    foreignKey: "customer_meter_reading_id",
  });
  trx_customer_meter_readings.hasMany(trx_connection_meter_bills, {
    as: "trx_connection_meter_bills",
    foreignKey: "customer_meter_reading_id",
  });
  trx_dma_help_with_reg_mapping.belongsTo(trx_help_with_registration, {
    as: "help_with_registration",
    foreignKey: "help_with_registration_id",
  });
  trx_help_with_registration.hasMany(trx_dma_help_with_reg_mapping, {
    as: "trx_dma_help_with_reg_mappings",
    foreignKey: "help_with_registration_id",
  });
  trx_lmc_pipeline_files.belongsTo(trx_lmc_pipeline, {
    as: "lmc_pipeline",
    foreignKey: "lmc_pipeline_id",
  });
  trx_lmc_pipeline.hasMany(trx_lmc_pipeline_files, {
    as: "trx_lmc_pipeline_files",
    foreignKey: "lmc_pipeline_id",
  });
  trx_customer_meter.belongsTo(trx_lmc_connection_mapping, {
    as: "lmc_connection_mapping",
    foreignKey: "mapped_lmc_customer_connection_id",
  });
  trx_lmc_connection_mapping.hasMany(trx_customer_meter, {
    as: "trx_customer_meters",
    foreignKey: "mapped_lmc_customer_connection_id",
  });
  // trx_lmc_pipeline.belongsTo(trx_mapped_lmc_customer_connection, {
  //   as: "mapped_lmc_customer_connection",
  //   foreignKey: "mapped_lmc_customer_connection_id",
  // });
  // trx_mapped_lmc_customer_connection.hasMany(trx_lmc_pipeline, {
  //   as: "trx_lmc_pipelines",
  //   foreignKey: "mapped_lmc_customer_connection_id",
  // });
  // trx_lmc_pipeline_files.belongsTo(trx_mapped_lmc_customer_connection, {
  //   as: "mapped_lmc_customer_connection",
  //   foreignKey: "mapped_lmc_customer_connection_id",
  // });
  // trx_mapped_lmc_customer_connection.hasMany(trx_lmc_pipeline_files, {
  //   as: "trx_lmc_pipeline_files",
  //   foreignKey: "mapped_lmc_customer_connection_id",
  // });
  trx_service_request_documents.belongsTo(trx_service_request, {
    as: "service_request",
    foreignKey: "service_request_id",
  });
  trx_service_request.hasMany(trx_service_request_documents, {
    as: "trx_service_request_documents",
    foreignKey: "service_request_id",
  });
  trx_change_address_request.belongsTo(mst_masters, {
    as: "transfer_reason_mst_master",
    foreignKey: "transfer_reason",
  });
  mst_masters.hasMany(trx_change_address_request, {
    as: "trx_change_address_requests",
    foreignKey: "transfer_reason",
  });
  trx_change_address_request.belongsTo(trx_customer_connection, {
    as: "new_customer_connection",
    foreignKey: "new_customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_change_address_request, {
    as: "trx_change_address_requests",
    foreignKey: "new_customer_connection_id",
  });
  trx_change_address_request.belongsTo(trx_customer_connection, {
    as: "old_customer_connection",
    foreignKey: "old_customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_change_address_request, {
    as: "old_customer_connection_trx_change_address_requests",
    foreignKey: "old_customer_connection_id",
  });
  trx_change_address_request.belongsTo(trx_customer_connection_address, {
    as: "new_customer_connection_address",
    foreignKey: "new_customer_connection_address_id",
  });
  trx_customer_connection_address.hasMany(trx_change_address_request, {
    as: "trx_change_address_requests",
    foreignKey: "new_customer_connection_address_id",
  });
  trx_change_address_request.belongsTo(trx_customer_connection_address, {
    as: "old_customer_connection_address",
    foreignKey: "old_customer_connection_address_id",
  });
  trx_customer_connection_address.hasMany(trx_change_address_request, {
    as: "old_customer_connection_address_trx_change_address_requests",
    foreignKey: "old_customer_connection_address_id",
  });

  trx_tpi_onboard_approve.belongsTo(mst_admin, {
    as: "lmc_created_by",
    foreignKey: "created_by",
  });
  mst_admin.hasMany(trx_tpi_onboard_approve, {
    as: "lmc_created_by",
    foreignKey: "created_by",
  });
  trx_tpi_onboard_approve.belongsTo(mst_admin, {
    as: "supervisor",
    foreignKey: "supervisor_id",
  });
  mst_admin.hasMany(trx_tpi_onboard_approve, {
    as: "trx_tpi_onboard_approves",
    foreignKey: "supervisor_id",
  });
  trx_tpi_onboard_approve.belongsTo(mst_admin, {
    as: "tpi",
    foreignKey: "tpi_id",
  });
  mst_admin.hasMany(trx_tpi_onboard_approve, {
    as: "tpi_trx_tpi_onboard_approves",
    foreignKey: "tpi_id",
  });
  trx_tpi_manual_meter_approval.belongsTo(mst_admin, {
    as: "tpi",
    foreignKey: "tpi_id",
  });
  mst_admin.hasMany(trx_tpi_manual_meter_approval, {
    as: "trx_tpi_manual_meter_approvals",
    foreignKey: "tpi_id",
  });
  trx_tpi_manual_meter_approval.belongsTo(trx_customer_meter_readings, {
    as: "customer_meter_reading",
    foreignKey: "customer_meter_reading_id",
  });
  trx_customer_meter_readings.hasMany(trx_tpi_manual_meter_approval, {
    as: "trx_tpi_manual_meter_approvals",
    foreignKey: "customer_meter_reading_id",
  });
  trx_customer_meter_readings.belongsTo(mst_admin, {
    foreignKey: "submitted_by",
    targetKey: "admin_id",
    as: "submitted_admin",
  });
  mst_admin.hasMany(trx_customer_meter_readings, {
    foreignKey: "submitted_by",
    sourceKey: "admin_id",
    as: "declared_readings",
  });
  trx_admin_status_history.belongsTo(mst_admin, {
    as: "admin",
    foreignKey: "admin_id",
  });
  mst_admin.hasMany(trx_admin_status_history, {
    as: "trx_admin_active_inactive_histories",
    foreignKey: "admin_id",
  });
  trx_admin_status_history.belongsTo(mst_admin, {
    as: "created_by_mst_admin",
    foreignKey: "created_by",
  });
  mst_admin.hasMany(trx_admin_status_history, {
    as: "created_by_trx_admin_active_inactive_histories",
    foreignKey: "created_by",
  });
  trx_admin_contractor_other_details.belongsTo(mst_admin, {
    as: "admin",
    foreignKey: "admin_id",
  });
  mst_admin.hasMany(trx_admin_contractor_other_details, {
    as: "trx_admin_contractor_other_details",
    foreignKey: "admin_id",
  });
  trx_admin_contractor_other_details.belongsTo(mst_agencies, {
    as: "agency",
    foreignKey: "agency_id",
  });
  mst_agencies.hasMany(trx_admin_contractor_other_details, {
    as: "trx_admin_contractor_other_details",
    foreignKey: "agency_id",
  });
  trx_admin_contractor_other_details.belongsTo(mst_geographical_areas, {
    as: "ga_area",
    foreignKey: "ga_area_id",
  });
  mst_geographical_areas.hasMany(trx_admin_contractor_other_details, {
    as: "trx_admin_contractor_other_details",
    foreignKey: "ga_area_id",
  });

  trx_meter_reading_orders.belongsTo(mst_admin, {
    as: "fo_supervisor",
    foreignKey: "fo_supervisor_id",
  });
  mst_admin.hasMany(trx_meter_reading_orders, {
    as: "trx_meter_reading_orders",
    foreignKey: "fo_supervisor_id",
  });
  trx_admin_mapped_ga_areas.belongsTo(mst_admin, {
    as: "mst_admin",
    foreignKey: "admin_id",
  });
  mst_admin.hasMany(trx_admin_mapped_ga_areas, {
    as: "trx_admin_mapped_ga_areas",
    foreignKey: "admin_id",
  });
  trx_admin_mapped_ga_areas.belongsTo(mst_geographical_areas, {
    as: "ga_area",
    foreignKey: "ga_area_id",
  });
  mst_geographical_areas.hasMany(trx_admin_mapped_ga_areas, {
    as: "trx_admin_mapped_ga_areas",
    foreignKey: "ga_area_id",
  });
  trx_meter_reading_orders.belongsTo(trx_customer_connection, {
    as: "linked_customer_connection",
    foreignKey: "bp_id",
    targetKey: "bp_id",
  });
  trx_customer_connection.hasMany(trx_meter_reading_orders, {
    as: "related_meter_reading_orders",
    foreignKey: "bp_id",
    sourceKey: "bp_id",
  });
  trx_admin_onboard_approve.belongsTo(mst_admin, {
    as: "approved_by_mst_admin",
    foreignKey: "approved_by",
  });
  mst_admin.hasMany(trx_admin_onboard_approve, {
    as: "trx_admin_onboard_approves",
    foreignKey: "approved_by",
  });
  trx_admin_onboard_approve.belongsTo(mst_admin, {
    as: "contractor",
    foreignKey: "contractor_id",
  });
  mst_admin.hasMany(trx_admin_onboard_approve, {
    as: "contractor_trx_admin_onboard_approves",
    foreignKey: "contractor_id",
  });

  trx_connection_payments.belongsTo(trx_customer_meter_readings, {
    as: "customer_meter_reading",
    foreignKey: "customer_connection_id",
  });
  trx_customer_meter_readings.hasMany(trx_connection_payments, {
    as: "trx_connection_payments",
    foreignKey: "customer_connection_id",
  });

  // Link to onboarded by (creator)
  trx_admin_onboard_approve.belongsTo(mst_admin, {
    foreignKey: "created_by",
    as: "onboardedBy",
  });
  trx_bpcl_admin_mapped_ga_areas.belongsTo(mst_admin, {
    as: "bpcl_admin",
    foreignKey: "bpcl_admin_id",
  });
  mst_admin.hasMany(trx_bpcl_admin_mapped_ga_areas, {
    as: "trx_bpcl_admin_mapped_ga_areas",
    foreignKey: "bpcl_admin_id",
  });
  trx_bpcl_admin_mapped_ga_areas.belongsTo(mst_geographical_areas, {
    as: "ga_area",
    foreignKey: "ga_area_id",
  });
  mst_geographical_areas.hasMany(trx_bpcl_admin_mapped_ga_areas, {
    as: "trx_bpcl_admin_mapped_ga_areas",
    foreignKey: "ga_area_id",
  });

  trx_customer_connection_address.belongsTo(mst_available_pngareas, {
    as: "connection_address_pngarea",
    foreignKey: "connection_pngareas_id",
  });
  mst_available_pngareas.hasMany(trx_customer_connection_address, {
    as: "trx_customer_connection_addresse",
    foreignKey: "connection_pngareas_id",
  });
  trx_lmc_connection_mapping.belongsTo(mst_admin, {
    as: "lmc",
    foreignKey: "lmc_id",
  });
  mst_admin.hasMany(trx_lmc_connection_mapping, {
    as: "trx_lmc_connection_mappings",
    foreignKey: "lmc_id",
  });
  trx_mapped_lmc_connection_stages.belongsTo(mst_admin, {
    as: "tpi_engineer",
    foreignKey: "tpi_engineer_id",
  });
  mst_admin.hasMany(trx_mapped_lmc_connection_stages, {
    as: "trx_mapped_lmc_connection_stages",
    foreignKey: "tpi_engineer_id",
  });
  trx_lmc_connection_mapping.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_lmc_connection_mapping, {
    as: "trx_lmc_connection_mappings",
    foreignKey: "customer_connection_id",
  });
  trx_mapped_lmc_connection_stages.belongsTo(trx_lmc_connection_mapping, {
    as: "lmc_connection_mapping",
    foreignKey: "lmc_connection_mapping_id",
  });
  trx_lmc_connection_mapping.hasMany(trx_mapped_lmc_connection_stages, {
    as: "trx_mapped_lmc_connection_stages",
    foreignKey: "lmc_connection_mapping_id",
  });
  trx_lmc_pipeline_documents.belongsTo(trx_mapped_lmc_connection_stages, {
    as: "mapped_lmc_connection_stage",
    foreignKey: "mapped_lmc_connection_stage_id",
  });
  trx_mapped_lmc_connection_stages.hasMany(trx_lmc_pipeline_documents, {
    as: "trx_lmc_pipeline_documents",
    foreignKey: "mapped_lmc_connection_stage_id",
  });
  trx_mapped_lmc_feasibility_commission_details.belongsTo(
    trx_mapped_lmc_connection_stages,
    {
      as: "mapped_lmc_connection_stage",
      foreignKey: "mapped_lmc_connection_stage_id",
    }
  );
  trx_mapped_lmc_connection_stages.hasMany(
    trx_mapped_lmc_feasibility_commission_details,
    {
      as: "trx_mapped_lmc_feasibility_commission_details",
      foreignKey: "mapped_lmc_connection_stage_id",
    }
  );
  trx_mapped_lmc_installation_details.belongsTo(
    trx_mapped_lmc_connection_stages,
    {
      as: "mapped_lmc_connection_stage",
      foreignKey: "mapped_lmc_connection_stage_id",
    }
  );
  trx_mapped_lmc_connection_stages.hasMany(
    trx_mapped_lmc_installation_details,
    {
      as: "trx_mapped_lmc_installation_details",
      foreignKey: "mapped_lmc_connection_stage_id",
    }
  );
  mst_meter_reading_orders.belongsTo(mst_admin, {
    as: "fo_supervisor",
    foreignKey: "fo_supervisor_id",
  });
  mst_admin.hasOne(mst_meter_reading_orders, {
    as: "mst_meter_reading_order",
    foreignKey: "fo_supervisor_id",
  });
  trx_meter_reading_units.belongsTo(mst_admin, {
    as: "fo_supervisor",
    foreignKey: "fo_supervisor_id",
  });
  mst_admin.hasMany(trx_meter_reading_units, {
    as: "trx_meter_reading_units",
    foreignKey: "fo_supervisor_id",
  });
  trx_meter_reading_units.belongsTo(mst_meter_reading_orders, {
    as: "mro",
    foreignKey: "mro_id",
  });
  mst_meter_reading_orders.hasMany(trx_meter_reading_units, {
    as: "trx_meter_reading_units",
    foreignKey: "mro_id",
  });

  trx_customer_meter_readings.belongsTo(trx_meter_reading_units, {
    as: "CMR",
    foreignKey: "customer_meter_id",
  });

  trx_meter_reading_units.hasMany(trx_customer_meter_readings, {
    as: "MRU",
    foreignKey: "customer_meter_id",
  });
  // Contractor onboarding
  mst_admin.hasOne(trx_admin_onboard_approve, {
    foreignKey: "contractor_id",
    as: "contractorOnboard",
  });
  trx_admin_onboard_approve.belongsTo(mst_admin, {
    foreignKey: "created_by",
    as: "contractorOnboardedBy",
  });
  trx_admin_onboard_approve.belongsTo(mst_admin, {
    foreignKey: "approved_by",
    as: "contractorApprovedBy",
  });
  trx_admin_onboard_approve.belongsTo(mst_admin, {
    foreignKey: "updated_by",
    as: "contractorUpdatedBy",
  });

  // Supervisor onboarding
  mst_admin.hasOne(trx_tpi_onboard_approve, {
    foreignKey: "supervisor_id",
    as: "supervisorOnboard",
  });
  trx_tpi_onboard_approve.belongsTo(mst_admin, {
    foreignKey: "created_by",
    as: "supervisorOnboardedBy",
  });
  trx_tpi_onboard_approve.belongsTo(mst_admin, {
    foreignKey: "tpi_id",
    as: "supervisorApprovedBy",
  });
  trx_tpi_onboard_approve.belongsTo(mst_admin, {
    foreignKey: "updated_by",
    as: "supervisorUpdatedBy",
  });

  // Contractor details
  mst_admin.hasOne(trx_admin_contractor_other_details, {
    foreignKey: "admin_id",
    as: "contractorDetails",
  });
  trx_admin_contractor_other_details.belongsTo(mst_agencies, {
    foreignKey: "agency_id",
    as: "contractorAgency",
  });

  // Areas mapped
  mst_admin.hasMany(trx_admin_mapped_areas, {
    foreignKey: "admin_id",
    as: "mappedAreas",
  });
  trx_admin_mapped_areas.belongsTo(mst_areas, {
    foreignKey: "area_id",
    as: "mappedArea",
  });
  trx_bpcl_subadmin_connection_approval.belongsTo(mst_admin, {
    as: "bpcl_subadmin",
    foreignKey: "bpcl_subadmin_id",
  });
  mst_admin.hasMany(trx_bpcl_subadmin_connection_approval, {
    as: "trx_bpcl_subadmin_connection_approvals",
    foreignKey: "bpcl_subadmin_id",
  });
  mst_admin.hasOne(mst_admin, { as: "createdBy", foreignKey: "created_by" });
  mst_admin.hasMany(trx_tpi_onboard_approve, {
    as: "tpi",
    foreignKey: "supervisor_id",
  });
  trx_meter_reading_units.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "bp_id",
    targetKey: "bp_id",
  });
  trx_customer_connection.hasMany(trx_meter_reading_units, {
    as: "meter_reading_units",
    foreignKey: "bp_id",
    sourceKey: "bp_id",
  });
  trx_customer_connection.belongsTo(mst_admin, {
    foreignKey: "updated_by",
    as: "reviewer",
  });

  trx_meter_reading_units.hasMany(trx_customer_meter_readings, {
    as: "trx_customer_meter_readings",
    foreignKey: "mru_id",
    sourceKey: "mru_id",
  });

  trx_customer_meter_readings.belongsTo(trx_meter_reading_units, {
    as: "trx_meter_reading_units",
    foreignKey: "mru_id",
    targetKey: "mru_id",
  });
  trx_connection_review_history.belongsTo(mst_admin, {
    as: "admin",
    foreignKey: "admin_id",
  });
  mst_admin.hasMany(trx_connection_review_history, {
    as: "trx_connection_review_histories",
    foreignKey: "admin_id",
  });
  trx_connection_review_history.belongsTo(trx_customer_connection, {
    as: "customer_connection",
    foreignKey: "customer_connection_id",
  });
  trx_customer_connection.hasMany(trx_connection_review_history, {
    as: "trx_connection_review_histories",
    foreignKey: "customer_connection_id",
  });

  trx_help_with_registration.belongsTo(mst_pincodes, {
    foreignKey: "pincode",
    targetKey: "pincode",
    as: "pincodes",
  });

  return {
    audit_logs,
    trx_document_metadata,
    mst_admin,
    mst_admin_documents,
    mst_agencies,
    mst_application_internal_status,
    mst_application_status,
    mst_areas,
    mst_available_pngareas,
    mst_banners,
    mst_bpcl_admin_roles,
    mst_cities,
    mst_connection_services,
    mst_cycle,
    mst_geographical_areas,
    mst_govt_scheme,
    mst_issue_type,
    mst_masters,
    mst_persona_role,
    mst_pincodes,
    mst_pipeline_extra_cost,
    mst_proof_master,
    mst_service_rates,
    mst_states,
    mst_support_charges,
    trx_admin_mapped_areas,
    trx_admin_mapped_areas_pincodes,
    trx_admin_password_history,
    trx_alteration_request,
    trx_bpcl_admin_role_mapping,
    trx_change_address_request,
    trx_connection_meter_bills,
    trx_connection_payments,
    trx_connection_request,
    trx_customer,
    trx_customer_connection,
    trx_customer_connection_address,
    trx_customer_connection_field_officer_mapping,
    trx_customer_connection_status_history,
    trx_customer_connection_tpi_mapping,
    trx_customer_connection_tpi_rework,
    trx_customer_meter,
    trx_customer_meter_readings,
    trx_customer_mobile_history,
    trx_customer_name_transfer_request,
    trx_customer_notifications,
    trx_customer_proof_documents,
    trx_dma_help_with_reg_mapping,
    trx_extra_point_request,
    trx_help_with_registration,
    trx_lmc_pipeline,
    trx_lmc_pipeline_files,
    trx_logs,
    trx_mapped_lmc_customer_connection,
    trx_service_request,
    trx_service_request_documents,
    trx_tpi_dma_mapping,
    trx_tpi_lmc_approve,
    trx_tpi_onboard_approve,
    trx_tpi_manual_meter_approval,
    trx_admin_status_history,
    trx_admin_contractor_other_details,
    trx_meter_reading_orders,
    trx_admin_onboard_approve,
    trx_admin_mapped_ga_areas,
    trx_bpcl_admin_mapped_ga_areas,
    trx_lmc_connection_mapping,
    trx_lmc_pipeline_documents,
    trx_mapped_lmc_connection_stages,
    trx_mapped_lmc_feasibility_commission_details,
    trx_mapped_lmc_installation_details,
    mst_meter_reading_orders,
    trx_meter_reading_units,
    trx_bpcl_subadmin_connection_approval,
    trx_connection_review_history,
  };
}

module.exports = initModels;
module.exports.initModels = initModels;
module.exports.default = initModels;
