const govtSchemeService = require('../services/govt-scheme-service');
const { sendError, sendSuccess } = require('../helpers/responseHelper');
const message = require('../utils/constants');

const getGovtSchemeList = async (req, res) => {
    try {
        const { page, limit } = req.query;
        const validatedPage = page && page > 0 ? parseInt(page, 10) : 1;
        const validatedLimit = limit && limit > 0 ? parseInt(limit, 10) : 10;

        const data = await govtSchemeService.getGovtSchemeList({ page: validatedPage, limit: validatedLimit });
        console.log("1",data);
        
        sendSuccess(req, res, data, "Data fetched successfully");
    } catch (error) {
        sendError(req, res, error, "Bad Request");
    }
};

const createGovtSchemeList = async (req, res) => {
    try {
        const data = await govtSchemeService.createGovtSchemeList(req.body);
        if (data && data.message === 'This Name is already exist') {
            return sendError(req, res, data, message.BAD_REQUEST, 400);
        }
        sendSuccess(req, res, data, message.CREATED);
    } catch (error) {
        sendError(req, res, error, message.BAD_REQUEST);
    }
};

const updateGovtSchemeList = async (req, res) => {
  try {
      const { govt_scheme_id } = req.body;
      const data = await govtSchemeService.updateGovtSchemeList(govt_scheme_id, req.body);
      sendSuccess(req, res, data, message.UPDATED);
  } catch (error) {
      sendError(req, res, error, message.BAD_REQUEST);
  }
};

const deleteGovtSchemeList = async (req, res) => {
    try {
        const { govt_scheme_id } = req.body;
        const data = await govtSchemeService.deleteGovtSchemeList(govt_scheme_id);
        sendSuccess(req, res, data, message.DELETED);
    } catch (error) {
        sendError(req, res, error, message.BAD_REQUEST);
    }
};

module.exports = {
    getGovtSchemeList,
    createGovtSchemeList,
    updateGovtSchemeList,
    deleteGovtSchemeList,
};