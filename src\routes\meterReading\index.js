const { validate } = require('express-validation');
const express = require('express');
const meterReadingRoute = express.Router();
const meterReadingController = require('../../controllers/meter-reading-controller');

const {
  getMeterReadingHistoryListValidation,
  getMeterReadingDetailsValidation,
  verifyMeterReadingValidation,
  getMeterReadingUploadDetailsFromCacheValidation,
  submitMeterReadingDetailsValidation,
  getJoinMeterReadingValidation
} = require('../../validation/meter-reading-validation');
const { uploadSingleTemp } = require('../../middleware/newMulter');
const { verifyPngToken } = require('../../middleware/customer-auth');

meterReadingRoute.get('/history-list',  verifyPngToken("guest"),validate(getMeterReadingHistoryListValidation), meterReadingController.meterReadingHistoryList);
meterReadingRoute.get('/details',verifyPngToken("guest"), validate(getMeterReadingDetailsValidation), meterReadingController.meterReadingDetails);
meterReadingRoute.post('/upload',verifyPngToken("guest"), uploadSingleTemp, meterReadingController.meterReadingUploads);
meterReadingRoute.post('/verify',verifyPngToken("guest"), validate(verifyMeterReadingValidation), meterReadingController.verifyMeterReading);
meterReadingRoute.get('/upload-details', verifyPngToken("guest"),validate(getMeterReadingUploadDetailsFromCacheValidation), meterReadingController.meterReadingUploadDetailsFromCache);
meterReadingRoute.post('/submit-details',verifyPngToken("guest"), validate(submitMeterReadingDetailsValidation), meterReadingController.meterReadingSaveDetailsFromCache);
meterReadingRoute.get('/joint-meter',verifyPngToken("guest"), validate(getJoinMeterReadingValidation), meterReadingController.joinMeterReadingDetails);


module.exports = { meterReadingRoute };
