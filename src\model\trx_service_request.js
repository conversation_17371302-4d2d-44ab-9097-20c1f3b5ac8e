const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_service_request",
    {
      service_request_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
        unique: "UK_trxservicerequest_cci_ici_iti_rs",
      },
      issue_category_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_masters",
          key: "master_id",
        },

        unique: "UK_trxservicerequest_cci_ici_iti_rs",
      },
      issue_type_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_issue_type",
          key: "issue_type_id",
        },
        unique: "UK_trxservicerequest_cci_ici_iti_rs",
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      crm_request_id: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },

      request_status: {
        type: DataTypes.STRING(12),
        allowNull: false,
        defaultValue: "pending",
        unique: "UK_trxservicerequest_cci_ici_iti_rs",
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_service_request",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_service_request_id",
          unique: true,
          fields: [{ name: "service_request_id" }],
        },
        {
          name: "UK_trxservicerequest_cci_ici_iti_rs",
          unique: true,
          fields: [
            { name: "customer_connection_id" },
            { name: "issue_category_id" },
            { name: "issue_type_id" },
            { name: "request_status" },
          ],
        },
      ],
    }
  );
};
