const axiosHelper = require("../helpers/axiosHelper");
const { decryptHybrisData } = require("../utils/encrypt-decrypt");
const hybrisApiUrl = process.env.HYBRIS_API_URL + "/bpclservices/v2/bpcl";
module.exports.validateBpclToken = async (token, payload) => {
  try {
    const apiUrl = hybrisApiUrl + "/user/validate/customer";
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const axiosResult = await axiosHelper.apiPost(apiUrl, payload, headers);
    console.log(axiosResult);
    if (axiosResult) {
      const decryptData = JSON.parse(
        decryptHybrisData(
          axiosResult,
          process.env.HYBRIS_DECRYPT_KEY_RESPONSE,
          process.env.HYBRIS_DECRYPT_IV_RESPONSE
        )
      );
      const decryptPayload = JSON.parse(
        decryptHybrisData(
          payload.request,
          process.env.HYBRIS_DECRYPT_KEY_PAYLOAD,
          process.env.HYBRIS_DECRYPT_IV_PAYLOAD
        )
      );

      return decryptPayload;
    }
    return axiosResult;
  } catch (err) {
    // console.log("BYBRIS ERRR", err);
    throw err;
  }
};
