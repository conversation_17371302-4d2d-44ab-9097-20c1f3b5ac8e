const { default: axios } = require("axios");
const jwt = require("jsonwebtoken");
const privateKey = process.env.JWT_PUBLIC_TOKEN_KEY;

module.exports.FormatData = (data, message = "", status = "success") => {
  if (data) {
    return { status: status, message: message, data };
  } else {
    return { status: status, message: message, data: [] };
  }
};

module.exports.signJwt = (object) => {
  return jwt.sign(object, privateKey, {
    expiresIn: "2h",
  });
};

module.exports.PublicItEvent = async (payload) => {
  axios.post("http://localhost:3000/it/app-events", {
    payload,
  });
};

module.exports.PublicTechnologyEvent = async (payload) => {
  axios.post("http://localhost:3000/technology/app-events", {
    payload,
  });
};
