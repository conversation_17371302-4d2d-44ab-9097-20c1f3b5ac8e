const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('mst_support_charges', {
    support_charge_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    area_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_areas',
        key: 'area_id'
      },
      unique: "UK_mstsupportcharges_areaid_chargetype"
    },
    charge_type: {
      type: DataTypes.STRING(10),
      allowNull: false,
      unique: "UK_mstsupportcharges_charge_type"
    },
    charge_value: {
      type: DataTypes.DECIMAL(5,2),
      allowNull: false
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'mst_support_charges',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_support_charge_id",
        unique: true,
        fields: [
          { name: "support_charge_id" },
        ]
      },
      {
        name: "UK_mstsupportcharges_areaid_chargetype",
        unique: true,
        fields: [
          { name: "area_id" },
          { name: "charge_type" },
        ]
      },
      {
        name: "UK_mstsupportcharges_charge_type",
        unique: true,
        fields: [
          { name: "charge_type" },
        ]
      },
    ]
  });
};
