const CryptoJS = require("crypto-js");
const secretKeyConfig = process.env.ENCRYPTION_SECRET_KEY;
const hybrisDecryptIv = process.env.HYBRIS_DECRYPT_IV;
const hybrisDecryptKey = process.env.HYBRIS_DECRYPT_KEY;

if (!secretKeyConfig) {
  throw new Error(
    "ENCRYPTION_SECRET_KEY must be defined in environment variables"
  );
}

function encryptPassword(plainPassword) {
  try {
    const encrypted = CryptoJS.AES.encrypt(plainPassword, secretKeyConfig);

    return encrypted.toString();
    // // Convert to string that can be stored as varbinary
    // return Buffer.from(encrypted.toString(), 'utf8');
  } catch (error) {
    console.error("Password encryption error:", error);
    throw error;
  }
}

function decryptPassword(encryptedPassword) {
  try {
    // // Convert Buffer back to string
    // const encryptedString = encryptedPassword.toString('utf8');
    const decrypted = CryptoJS.AES.decrypt(encryptedPassword, secretKeyConfig);
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error("Password decryption error:", error);
    throw error;
  }
}

function decryptCrypto(encryptedString, secretKey = secretKeyConfig) {
  return CryptoJS.AES.decrypt(encryptedString, secretKey).toString(
    CryptoJS.enc.Utf8
  );
}

function encryptCrypto(plainString, secretKey = secretKeyConfig) {
  return CryptoJS.AES.encrypt(plainString, secretKey).toString();
}

const decryptHybrisData = (cipherText, secret, iv) => {
  // IV is a base64 string
  try {
    const iv1 = CryptoJS.enc.Utf8.parse(iv);
    const key = CryptoJS.enc.Utf8.parse(secret);
    const cipherBytes = CryptoJS.enc.Base64.parse(cipherText);
    // console.log("IVV", cipherBytes);
    const decrypted = CryptoJS.AES.decrypt(cipherText, key, {
      iv: iv1,
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (err) {
    console.trace(err);
    console.log("decryptHybrisData err", err);
    throw err;
  }
};

module.exports = {
  decryptCrypto,
  encryptCrypto,
  decryptHybrisData,
  encryptPassword,
  decryptPassword,
};
