const { Joi } = require('express-validation');
const moment = require('moment');


const getLmcCustomerAssignmentTicketsValidation = {
  query: Joi.object({
    approve_status: Joi.number().integer().optional(),
    assigned_status: Joi.string()
    .valid('true', 'false')
    .optional(),
    area_ids: Joi.string()
    .pattern(/^\d+(,\d+)*$/)
    .optional()
    .messages({
      "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
    })
  }),
  
};


const supervisorOnboardingValidation = {
  query: Joi.object({
    approve_status: Joi.string()
      .valid('pending', 'rejected')
      .required() ,
      area_ids: Joi.string()
      .pattern(/^\d+(,\d+)*$/)
      .optional()
      .messages({
        "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
      })
  }),
   
};

const createSupervisorValidation = {
  body: Joi.object({
    first_name: Joi.string().trim().min(2).max(50).required(),
    middle_name: Joi.string().trim().min(2).max(50).allow('', null).optional(),
    last_name: Joi.string().trim().min(2).max(50).required(),
    email: Joi.string().trim().email().required(),
    mobile: Joi.string()
      .trim()
      .pattern(/^[6-9]\d{9}$/) // Ensures a valid 10-digit Indian mobile number
      .required(),
    alternate_mobile: Joi.string()
      .trim()
      .allow('', null) // Allows empty string or null (optional field)
      .pattern(/^[6-9]\d{9}$/)
      .optional(),
    bpclAdminId: Joi.number().integer().positive().optional(),
    area_ids: Joi.alternatives()
    .try(
      Joi.array().items(Joi.number().integer().positive()).min(1),
      Joi.string().custom((value, helpers) => {
        const parsed = value
          .split(',')
          .map(v => parseInt(v.trim(), 10))
          .filter(n => !isNaN(n) && n > 0);
  
        if (!parsed.length) {
          return helpers.error('any.invalid');
        }
  
        return parsed;
      }, 'Comma-separated parser')
    )
    .required()
    .messages({
      'any.invalid': 'Area IDs must be a comma-separated list of positive integers or an array of them',
    })

  }),
};

const revokeSupervisorValidation = {
  body: Joi.object({
    supervisorId: Joi.required().messages({
      "any.required": "Supervisor ID is required."
    })
  })
};
const mySupervisorValidation = {
  query: Joi.object({
    is_active: Joi.boolean()
      .truthy('true', 'false', 1, 0, '1', '0') // Converts these values to boolean
      .optional()
      .messages({
        "boolean.base": "is_active must be either true or false."
      }),
      customer_connection_id: Joi.optional().messages({
        "any.optional": "customer_connection_id ID is optional."
      }),
      area_ids: Joi.alternatives()
    .try(
      Joi.array().items(Joi.number().integer().positive()).min(1),
      Joi.string().custom((value, helpers) => {
        const parsed = value
          .split(',')
          .map(v => parseInt(v.trim(), 10))
          .filter(n => !isNaN(n) && n > 0);
  
        if (!parsed.length) {
          return helpers.error('any.invalid');
        }
  
        return parsed;
      }, 'Comma-separated parser')
    )
    .optional()
    .messages({
      'any.invalid': 'Area IDs must be a comma-separated list of positive integers or an array of them',
    })
  })
};

const geographicalAreasValidation = {
  query: Joi.object({
    area_ids: Joi.string()
      .pattern(/^\d+(,\d+)*$/)
      .optional()
      .messages({
        "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
      })
  }), // Ensures no query parameters are passed
  body: Joi.object().empty()   // Ensures no request body is sent
};

const dashboardMetricsValidation = {
  query: Joi.object({
    area_ids: Joi.string()
      .pattern(/^\d+(,\d+)*$/)
      .optional()
      .messages({
        "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
      })
  })
};

const supervisorAssignValidation = {
  body: Joi.object({
    supervisorId: Joi.required().messages({
      "any.required": "Supervisor ID is required."
    }),
    customerConnectionId: Joi.required().messages({
      "any.required": "Supervisor ID is required."
    })
  })
};

const supervisorTicketAssignValidation = {
  query: Joi.object({
    customer_connection_id: Joi.number().integer().positive().required()
  })
};

module.exports = dashboardMetricsValidation;


module.exports = {
  getLmcCustomerAssignmentTicketsValidation,
  supervisorOnboardingValidation,
  createSupervisorValidation,
  revokeSupervisorValidation,
  mySupervisorValidation,
  geographicalAreasValidation,
  dashboardMetricsValidation,
  supervisorAssignValidation,
  supervisorTicketAssignValidation
};
