const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_bpcl_roles",
    {
      bpcl_role_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      role_name: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique: "UK_mstbpclroles_role_name",
      },
    },
    {
      sequelize,
      tableName: "mst_bpcl_roles",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_bpcl_role_id",
          unique: true,
          fields: [{ name: "bpcl_role_id" }],
        },
        {
          name: "UK_mstbpclroles_role_name",
          unique: true,
          fields: [{ name: "role_name" }],
        },
      ],
    }
  );
};
