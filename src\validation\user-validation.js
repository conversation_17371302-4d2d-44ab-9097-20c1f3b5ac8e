const { Joi } = require('express-validation');

const createUserValidation = {
  body: Joi.object({
    first_name: Jo<PERSON>.string().required(),
    last_name: Jo<PERSON>.string().required(),
    email: Joi.string().email().required(),
    password: Joi.string()
      .regex(/[a-zA-Z0-9]{3,30}/)
      .required(),
    phone_no: Joi.string()
      .regex(/^[0-9]{10}$/)
      .required()
      .messages({
        'string.pattern.base': `Phone no is not valid`,
      }),
    role_id: Joi.number().integer().min(0).required(),
    created_by: Joi.number().allow(null, '').default(null),
    capabilities_ids: Joi.array().required(),
    ip_address: Joi.string().required(),
    browser: Joi.string().required(),
    // email: Joi.string().required().custom(value => {
    //     return service.CheckExistingUser({email : value}).then(user => {
    //         if (user == undefined || user.length > 0) {
    //             return Promise.reject('Email already exists');
    //         }
    //         return true;
    //     });
    // })
  }),
};

const loginValidation = {
  body: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string()
      .regex(/[a-zA-Z0-9]{3,30}/)
      .required(),
  }),
};

// const loginOTPValidation = {
//   body: Joi.object({
//     email: Joi.string().email().required(),
//   }),
// }

const loginOTPValidation = {
  body: Joi.object({
    mobile: Joi.string()
      .regex(/^[0-9]{10}$/)
      .required()
      .messages({
        'string.pattern.base': `Mobile no is not valid`,
      }),
  }),
};

// const verifyotp = {
//   body: Joi.object({
//     email: Joi.string().email().required(),
//     otp: Joi.string().required(),
//   }),
// }

const verifyotp = {
  body: Joi.object({
    mobile: Joi.string()
      .regex(/^[0-9]{10}$/)
      .required()
      .messages({
        'string.pattern.base': `Mobile no is not valid`,
      }),
    otp: Joi.string().required(),
  }),
};
const getUserValidation = {
  body: Joi.object({
    page: Joi.number().allow(null, '').default(null),
    limit: Joi.number().allow(null, '').default(null),
    search: Joi.string().allow(null, ''),
    orderBy: Joi.string().valid('asc', 'desc').allow(null, ''),
    orderName: Joi.string().allow(null, ''),
    user_id: Joi.number().optional(),
    all_records: Joi.boolean().optional(),
  }),
};
const deleteUserValidation = {
  body: Joi.object({
    user_id: Joi.number().integer().min(0).required(),
    updated_by: Joi.number().integer().min(0).optional(),
    ip_address: Joi.string().required(),
    browser: Joi.string().required(),
  }),
};

const updateUserValidation = {
  body: Joi.object({
    user_id: Joi.number().integer().required(),
    first_name: Joi.string().required(),
    last_name: Joi.string().required(),
    email: Joi.string().email().required(),
    password: Joi.string()
      .regex(/[a-zA-Z0-9]{3,30}/)
      .optional(),
    phone_no: Joi.string()
      .regex(/^[0-9]{10}$/)
      .required()
      .messages({
        'string.pattern.base': `Phone no is not valid`,
      }),
    role_id: Joi.number().integer().min(0).required(),
    updated_by: Joi.number().integer().min(0).optional(),
    capabilities_ids: Joi.array().required(),
    ip_address: Joi.string().required(),
    browser: Joi.string().required(),
  }),
};
const userCapabilitiesValidation = {
  body: Joi.object({
    user_id: Joi.number().integer().min(0).required(),
  }),
};

const resetPassword = {
  body: Joi.object({
    user_id: Joi.number().integer().min(0).required(),
    email: Joi.string().email().required(),
    old_password: Joi.string()
      .regex(/[a-zA-Z0-9]{3,30}/)
      .optional(),
    new_password: Joi.string()
      .regex(/[a-zA-Z0-9]{3,30}/)
      .required(),
  }),
};

const forgotPasswordValidation = {
  body: Joi.object({
    email: Joi.string().email().required(),
  })
};

const forgotPasswordOtpVerificationValidation = {
  body: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string()
      .regex(/[a-zA-Z0-9]{3,30}/)
      .required(),
    otp: Joi.string().pattern(/^\d{6}$/).required()
  })
};
module.exports = {
  createUserValidation,
  getUserValidation,
  loginValidation,
  loginOTPValidation,
  verifyotp,
  deleteUserValidation,
  updateUserValidation,
  userCapabilitiesValidation,
  resetPassword,
  forgotPasswordValidation,
  forgotPasswordOtpVerificationValidation
};
