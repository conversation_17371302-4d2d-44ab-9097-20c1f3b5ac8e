const jwt = require("jsonwebtoken");
const { FormatData } = require("../utils");
const message = require("../utils/constants");
const jwtHelper = require("../helpers/jwtHelper");
const config = process.env;

const blacklist = new Set();

const verifyToken = async (req, res, next) => {
  const token = (
    req.headers["authorization"] || req.headers["Authorization"]
  )?.replace("Bearer ", "");
  if (!token) {
    const output = FormatData("", message.TOKEN_REQUIRED);
    return res.status(403).send(output);
  }
  if (blacklist.has(token)) {
    const output = FormatData("", "Token has been invalidated", "failed");
    return res.status(401).send(output);
  }
  try {
    const decoded = await jwtHelper.verifyToken(token);
    req.user = decoded;
    console.log("decoded", decoded);
  } catch (err) {
    const output = FormatData("", message.INVALID_TOKEN, "failed");
    return res.status(401).send(output);
  }

  return next();
};

const restrictToRoles = (...allowedRoles) => {
  return (req, res, next) => {
    if (!req.user || !allowedRoles.includes(req.user.user_role)) {
      const output = FormatData("", "Unauthorized role", "failed");
      return res.status(403).send(output);
    }
    next();
  };
};

module.exports = verifyToken;
module.exports.restrictToRoles = restrictToRoles;
module.exports.blacklist = blacklist;
