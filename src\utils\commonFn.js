const crypto = require("crypto");
module.exports.slugify = (str) => {
  str = str.replace(/^\s+|\s+$/g, ""); // trim leading/trailing white space
  str = str.toLowerCase(); // convert string to lowercase
  str = str
    .replace(/[^a-z0-9 -]/g, "") // remove any non-alphanumeric characters
    .replace(/\s+/g, "-") // replace spaces with hyphens
    .replace(/-+/g, "-"); // remove consecutive hyphens
  return str;
};

module.exports.generateTempId = (prefix = "TEMP", n = 8) => {
  return prefix + Date.now().toString(n);
};

module.exports.generateRequestId = (prefix = "REQ") => {
  const randomNum = Math.floor(1000 + Math.random() * 9000); // Generates a 10-digit random number
  return prefix + randomNum.toString();
};

module.exports.mergeData = (existingData = {}, newData = {}) => {
  return {
    ...existingData,
    ...Object.fromEntries(
      Object.entries(newData).filter(
        ([_, value]) => value !== null && value !== undefined
      )
    ),
  };
};

module.exports.generateOtp = (otpLength = 6) => {
  const digits = "0123456789";
  let otp = "";

  for (let i = 0; i < otpLength; i++) {
    const randomIndex = Math.floor(Math.random() * digits.length);
    otp += digits[randomIndex];
  }
  return otp;
};

module.exports.addTimeToDate = (date, n, type = "seconds") => {
  const timeTypeValues = {
    seconds: 1000,
    minutes: 60000,
    hours: 3600000,
    days: 1,
  };

  date.setTime(date.getTime() + n * timeTypeValues[type]);

  return date;
};

exports.formatReading = (value) => {
  return parseFloat(value).toFixed(2);
};

exports.paginateQuery = (req) => {
  const page = parseInt(req.body.page) || 1;
  const limit = parseInt(req.body.limit) || 1000;
  const offset = (page - 1) * limit;

  return { page, limit, offset };
};

exports.formatDate = (date) => {
  if (!date) return "-";
  return new Date(date).toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "short",
    year: "numeric",
  });
};

exports.formatAddress = (data) => {
  console.log(data, "data");
  return [
    data?.connection_address,
    data?.connection_city,
    data?.connection_district,
    data?.connection_state,
    data?.connection_pincode,
  ]
    .filter(Boolean)
    .join(", ");
};

exports.generatePassword = () => {
  const charset =
    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  return Array.from(crypto.randomFillSync(new Uint32Array(8)))
    .map((x) => charset[x % charset.length])
    .join("");
};

exports.formatMeterReading = (value) => {
  const number = parseFloat(value);

  if (isNaN(number)) return "00000.000";

  const [intPart, decimalPart = ""] = number.toString().split(".");

  const paddedInt = intPart.padStart(5, "0");
  const paddedDecimal = (decimalPart + "000").substring(0, 3);

  return `${paddedInt}.${paddedDecimal}`;
};

const formatReading = (reading) => {
  return String(reading).padStart(4, "0"); // Ensure it's always 4 digits
};

exports.generateCode = (prefix, length = 3) => {
  const datePart = new Date().toISOString().slice(0, 3).replace(/-/g, ""); // YYYYMMDD
  const randomPart = Math.floor(Math.random() * Math.pow(3, length))
    .toString()
    .padStart(length, "0");
  return `${prefix}${datePart}${randomPart}`;
};

exports.generateTrxID = (prefix) => {
  const now = Date.now().toString(); // milliseconds since epoch
  const timePart = now.slice(-8); // last 8 digits of timestamp
  const randomPart = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0"); // 3 random digits
  return `${prefix}${timePart}${randomPart}`;
};

exports.generatePassword = (length = 9) => {
  const lower = 'abcdefghijklmnopqrstuvwxyz';
  const upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const digits = '0123456789';
  const special = '!@#$%^&*()_+[]{}|;:,.<>?';

  const allChars = lower + upper + digits + special;

  const getRandomChar = (charset) => charset[Math.floor(Math.random() * charset.length)];

  let password = [
    getRandomChar(lower),
    getRandomChar(upper),
    getRandomChar(digits),
    getRandomChar(special)
  ];

  const remainingLength = length - password.length;
  const randomValues = crypto.getRandomValues(new Uint32Array(remainingLength));
  for (let i = 0; i < remainingLength; i++) {
    password.push(allChars[randomValues[i] % allChars.length]);
  }

  for (let i = password.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [password[i], password[j]] = [password[j], password[i]];
  }

  return password.join('');
};

exports.getFutureDateByMonths = (dateStr, months) =>  {
  console.log("getFutureDateByMonths",dateStr,months);
  
  const date = new Date(dateStr);
  date.setMonth(date.getMonth() + months);
  return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
}
