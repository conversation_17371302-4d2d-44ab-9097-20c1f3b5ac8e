const { Op, where } = require('sequelize');
const { dbModels } = require('../database/connection');
const issueTypeeModel = dbModels.mst_issue_type;
const masterModel = dbModels.mst_masters;

const getMasterIssueList = async (whereCond) => {
  try{

    const results = await masterModel.findAll({
      include: [
        {
          model: issueTypeeModel,
          as: 'mst_issue_types',  // ✅ Use the correct alias
          where: {
            customer_type: whereCond.customer_type
          },
          attributes: ['issue_type_id', 'issue_type_name', 'issue_type_value']
        },
      ],
      where: {
        master_type: whereCond.master_type
      },
      attributes: ['master_id', 'master_name', 'master_value'],
      raw: false,  // Ensures Sequelize returns model instances
      nest: true   // Ensures nested objects are properly structured
    });
    console.log("results", results);
    
    return results;
    
  } catch (error) {
    console.error("Error in getPipelineDetails:", error);
    throw error;
  } 
};
const getGovtSchemeList = async (page, limit) => {
  const offset = (page - 1) * limit;
  
  const totalRecords = await dbModels.mst_govt_scheme.count({ where: { is_active: 1 } });
  const totalPages = Math.ceil(totalRecords / limit);
  if (page > totalPages) {
    return { data: [], meta: { totalRecords, totalPages, currentPage: page } };
  }
  const data = await dbModels.mst_govt_scheme.findAll({
    where: { is_active : 1},
    offset,
    limit,
  });
  return { data , meta: { totalRecords, totalPages, currentPage: page } };
}
  
const getGovtSchemeById = async (where=null) => {
  let whereCond={
    is_active: 1,
  }
  if (where) {
    whereCond = {
      ...whereCond,
      ...where
    }
  }

  const totalRecords = await dbModels.mst_govt_scheme.findOne(
    {attributes: ['govt_scheme_id','scheme_name'], where: whereCond , raw: true,
      nest: true});
  return totalRecords;
}

const countProofs = async ({ proof_type }) => {
  return dbModels.mst_proof_master.count({
    where: {
      proof_type,
      is_active: 1, // Adjust this based on schema
    },
  });
};
const getProofsByType = async ({ proof_type, offset, limit }) => {
  return dbModels.mst_proof_master.findAll({
    where: {
      proof_type,
      is_active: 1, // Adjust this based on schema
    },
    offset,
    limit,
  });
};

const getMasterList = async (master_type) => {
  try {
    console.log('masterTyp', master_type)
    const data = await masterModel.findAll({
      where: {master_type, is_active: 1 },
      logging: (sql) => console.log("Generated SQL:", sql), // Log generated SQL
    });

    console.log("data Repo:", data);
    return data;
  } catch (error) {
    console.error("Error fetching master list:", error);
    throw error;
  }
};
  
const getServiceRatesList = async (ServiceTypeslug) => {
  try {
    let queryOptions = {
      where: {
        is_active: 1
      },
      include: []
    };

    if (ServiceTypeslug !== "ALL") {
      queryOptions.include.push({
        model: dbModels.mst_connection_services,
        as: 'connection_service',
        where: {
          service_slug: ServiceTypeslug
        },
        attributes: ['service_name',"service_slug"] 
      });
    } else {
      queryOptions.include.push({
        model: dbModels.mst_connection_services,
        as: 'connection_service',
        attributes: ['service_name',"service_slug"]
      });
    }

    const serviceRates = await dbModels.mst_service_rates.findAll(queryOptions);
    return serviceRates;
  } catch (error) {
    console.error("Error fetching service rates:", error);
    throw error;
  }
};
const getAreaDetailsById = async (areaId) => {
  try {
    const details = await dbModels.mst_available_pngareas.findOne({
      where: { available_pngareas_id: areaId , is_active: 1},
      include: [
        {
          model: dbModels.mst_pincodes,
          as: "pincodeAreasAlias",
          attributes: ["pincode"],
          include: [
            {
              model: dbModels.mst_areas,
              as: "area",
              attributes: ["area_name"],
              include: [
                {
                  model: dbModels.mst_geographical_areas,
                  as: "geographical_area",
                  attributes: ["ga_area_name"],
                  include: [
                    {
                      model: dbModels.mst_cities,
                      as: "city",
                      attributes: ["city_title"],
                    },
                    {
                      model: dbModels.mst_states,
                      as: "state",
                      attributes: ["state_title"],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      raw: true,
      nest: true,
    });

    return details;
  } catch (error) {
    console.error("Error fetching pincode data:", error);
    throw error;
  }
}
const getProofById = async (proof_master_id) => {
  try {
    const details = await dbModels.mst_proof_master.findOne({
      where: { proof_master_id, is_active: 1},
      raw: true,
      nest: true,
    });

    return details;
  } catch (error) {
    console.error("Error fetching Proof by ID data:", error);
    throw error;
  }
}

  

module.exports = {
  getMasterIssueList,
  countProofs,
  getProofsByType,
  getGovtSchemeList,
  getMasterList,
  getServiceRatesList,
  getAreaDetailsById,
  getGovtSchemeById,
  getProofById
};
