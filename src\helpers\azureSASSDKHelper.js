const path = require('path');
const { TableClient, AzureNamedKeyCredential } = require("@azure/data-tables");
const { BlobServiceClient, generateBlobSASQueryParameters, StorageSharedKeyCredential, ContainerSASPermissions } = require('@azure/storage-blob');

async function generateSASToken(permissionsData) {
    const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;
    const accountKey = process.env.AZURE_STORAGE_ACCOUNT_KEY;
    const container_name = process.env.AZURE_CONTAINER_NAME;
    const sharedKeyCredential = new StorageSharedKeyCredential(accountName, accountKey);
    const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net`, sharedKeyCredential);

    const containerClient = blobServiceClient.getContainerClient(container_name);
    const permissions = ContainerSASPermissions.parse(permissionsData); // Read, Add, Create, Write, Delete permissions

    const sasOptions = {
        containerName: containerClient.containerName,
        permissions: permissions,
        startsOn: new Date(),
        expiresOn: new Date(new Date().valueOf() + 15 * 60 * 1000), // 15 minutes expiration
    };

    const sasToken = generateBlobSASQueryParameters(sasOptions, sharedKeyCredential).toString();
    return sasToken;
}

generateSASToken().catch((err) => {
    console.error('Error generating SAS token:', err.message);
});


const azureSingleFileUpload = async (req, res, token, folderName = 'temp') => {
  let result = {
    blob_url: null,
    filename: null,
    folderName: folderName,
    path: null,
    mimetype: null,
    size: null,
    error: null,
  };
  try {
    const sasToken = token;
    console.log("sasToken?????", sasToken);
    const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;
    const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net/?${sasToken}`);
    const containerName = process.env.AZURE_CONTAINER_NAME;
    const containerClient = blobServiceClient.getContainerClient(containerName);

    const fileObj = req.file;
    console.log("zzzzzz", fileObj);
    const buffer = Buffer.from(fileObj.buffer);
    const timestamp = Date.now();
    const fileName = `${timestamp}-${fileObj.originalname}`;
    const blobName = `${folderName}/${timestamp}-${fileObj.originalname}`;
    
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    const blobOptions = {
        blobHTTPHeaders: {
            blobContentType: fileObj.mimetype,
        }
    };
    console.log(">>>>>>>blobName", blobName);
    await blockBlobClient.uploadData(buffer, blobOptions);
    const metadata = {
      "author": "John Doe",
      "description": "Testing image with metadata",
    };
    await blockBlobClient.setMetadata(metadata);
    // storeMetadataInTable();
    console.log(">>>>>>>www", blockBlobClient);
    result.blob_url = blockBlobClient.url;
    result.filename = fileName;
    result.mimetype = fileObj.mimetype;
    result.size = fileObj.size;
    //result.path = fileObj.path;
    
    console.log("fileObj--------->",fileObj)
    
    // console.log("Inside Helper>>>>>>>>>", imageres);
    
    return result;
  } catch (err) {
    console.error('Error uploading file:', err.message);
    result.error = err.message;
    return result;
  }
};



const azureMultipleFileUpload = async (
  req,
  res,
  token,
  folderName = 'test'
) => {
  let result = {
    blob_url: null,
    error: null,
  };
  try {
    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({ error: 'No files were uploaded.' });
    }

    const sasToken = token;
    const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;
    const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net?${sasToken}`);
    const containerName = process.env.AZURE_CONTAINER_NAME;
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobUploadResults = [];

    for (const [fieldname, fileArray] of Object.entries(req.files)) {
      for (const fileObj of fileArray) {
        const buffer = Buffer.from(fileObj.buffer);
        const timestamp = Date.now();
        // Use the custom filename format (timestamp-originalname)
        const fileName = `${timestamp}-${fileObj.originalname}`;
        const blobName = `${folderName}/${fileName}`;
        const blockBlobClient = containerClient.getBlockBlobClient(blobName);
        const ext = path.extname(fileObj.originalname);
        const blobOptions = {
          blobHTTPHeaders: {
            blobContentType: fileObj.mimetype,
          },
        };
        await blockBlobClient.uploadData(buffer, blobOptions);

        // Generate blob URL
        const blobUrl = blockBlobClient.url;
        blobUploadResults.push({
          category: fieldname,
          file: fileObj.originalname,
          url: blobUrl,
          fileName: fileName,
          folderName: folderName,
          error: null
        });
      }
    }
    return blobUploadResults;
  } catch (error) {
    console.error('Error uploading files to Azure:', error.message);
    result.error = error.message;
    return result;
  }
};


const getAzureSingleFile = async (req, token, folderName = 'test') => {console.log("2222", folderName);
  let result = {
    size: null,
    data: null,
    error: null,  
  };
  try {
    const sasToken = token;
    const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;
    const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net?${sasToken}`);
    const containerName = process.env.AZURE_CONTAINER_NAME;
    const containerClient = blobServiceClient.getContainerClient(containerName);

    const blobName = `${folderName}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    const properties = await blockBlobClient.getProperties();
    result.size = properties.contentLength; // Store size with 2 decimal places

    const downloadBlockBlobResponse = await blockBlobClient.download(0);
        const streamToBuffer = async (readableStream) => {
            return new Promise((resolve, reject) => {
                const chunks = [];
                readableStream.on("data", (data) => {
                    chunks.push(data);
                });
                readableStream.on("end", () => {
                    resolve(Buffer.concat(chunks));
                });
                readableStream.on("error", reject);
            });
        };

        const buffer = await streamToBuffer(downloadBlockBlobResponse.readableStreamBody);
        const base64Data = buffer.toString('base64');
        
        // return base64Data;
        result.data = base64Data;
        return result;
  } catch (err) {
    console.error('Error uploading file:', err.message);
    result.error = err.message;
    return result;
  }
};

const getAzureDownload = async (req, token, folderName = 'test') => {console.log("2222", folderName);
  let result = {
    size: null,
    data: null,
    error: null,
  };
  try {
    const sasToken = token;
    const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;
    const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net?${sasToken}`);
    const containerName = process.env.AZURE_CONTAINER_NAME;
    const containerClient = blobServiceClient.getContainerClient(containerName);

    const blobName = `${folderName}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    const properties = await blockBlobClient.getProperties();
    // console.log("aaaaaa", properties);
    // result.size = properties.contentLength; // Store size with 2 decimal places
    const downloadBlockBlobResponse = await blockBlobClient.download(0);
        const streamToBuffer = async (readableStream) => {
            return new Promise((resolve, reject) => {
                const chunks = [];
                readableStream.on("data", (data) => {
                    chunks.push(data);
                });
                readableStream.on("end", () => {
                    resolve(Buffer.concat(chunks));
                });
                readableStream.on("error", reject);
            });
        };

        const buffer = await streamToBuffer(downloadBlockBlobResponse.readableStreamBody);
        // console.log('Buffer:', buffer);
        // console.log("??????", buffer);
        // buffer.type = properties.contentLength
        result.size = properties.contentLength;
        result.data = buffer;
        // result.type = buffer.type;
        return result;
  } catch (err) {
    console.error('Error uploading file:', err.message);
    result.error = err.message;
    return result;
  }
};

async function copyBlobWithinContainer(container, sourceBlob, destinationBlob) {
  const permission = 'racwd';
  const sasToken = await generateSASToken(permission);
  const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;
  const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net?${sasToken}`);

  const containerClient = blobServiceClient.getContainerClient(container);
  const sourceBlobClient = containerClient.getBlobClient(sourceBlob);
  const destinationBlobClient = containerClient.getBlobClient(destinationBlob);
console.log(">>>>>qqqq", sourceBlobClient.url);
  await destinationBlobClient.beginCopyFromURL(sourceBlobClient.url);
  
}

async function deleteBlob(container, blob) {
  const permission = 'racwd';
  const sasToken = await generateSASToken(permission);
  const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;
  const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net?${sasToken}`);

  const blobClient = blobServiceClient.getContainerClient(container).getBlobClient(blob);
  await blobClient.delete();
  console.log(`Deleted blob ${blob}`);
}

async function moveBlobWithinContainer(sourceBlob, destinationBlob) {
  const container = process.env.AZURE_CONTAINER_NAME;
  await copyBlobWithinContainer(container, sourceBlob, destinationBlob);
  await deleteBlob(container, sourceBlob);
  console.log(`Moved blob from ${sourceBlob} to ${destinationBlob} within container ${container}`);
}

async function storeMetadataInTable() {
  let accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;
let accountKey = process.env.AZURE_STORAGE_ACCOUNT_KEY;
let metadataEndpoint = process.env.AZURE_METADATA_END_POINT;
let metadataTable = process.env.AZURE_METADATA_TABLE;
  const tableClient = new TableClient(
    metadataEndpoint,
    metadataTable,
    new AzureNamedKeyCredential(accountName, accountKey)
  );
  const metadata = {
    author: "John Doe",
    description: "Sample image for testing",
    category: "Nature"
  };
  const entity = {
    partitionKey: "blobMetadata",  // Partition key for Table Storage
    rowKey: "test-12345",               // Unique identifier (e.g., blob name or ID)
    author: metadata.author,
    description: metadata.description,
    category: metadata.category,
    newKey: "testinf new key"
  };

  try {
    await tableClient.upsertEntity(entity);
    
    console.log("Metadata stored in Table Storage.?????>>>>>");
  } catch (error) {
    console.error("Error storing metadata in Table Storage:", error);
  }
}

module.exports = { generateSASToken, azureSingleFileUpload, azureMultipleFileUpload, getAzureSingleFile, getAzureDownload, moveBlobWithinContainer, deleteBlob, storeMetadataInTable };