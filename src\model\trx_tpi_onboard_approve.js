const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_tpi_onboard_approve",
    {
      tpi_onboard_approve_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      tpi_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_tpionboardapprove_tpi_id_supervisor_id",
      },
      supervisor_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_tpionboardapprove_tpi_id_supervisor_id",
      },
      admin_type: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      approve_status: {
        type: DataTypes.STRING(10),
        allowNull: true,
        defaultValue: "pending",
      },
      remark: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_tpi_onboard_approve",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_tpi_onboard_approve_id",
          unique: true,
          fields: [{ name: "tpi_onboard_approve_id" }],
        },
        {
          name: "UK_tpionboardapprove_tpi_id_supervisor_id",
          unique: true,
          fields: [{ name: "tpi_id" }, { name: "supervisor_id" }],
        },
      ],
    }
  );
};
