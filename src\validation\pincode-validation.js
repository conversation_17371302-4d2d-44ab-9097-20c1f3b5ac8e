const { Joi } = require("express-validation");

const checkPincodeValidation = Joi.object({
  pincode: Joi.alternatives().try(
    Joi.number().integer().min(100000).max(999999),
    Joi.valid(null),
    Joi.valid('')
  ).optional()
  .messages({
    "number.base": "Pincode must be a number",
    "number.integer": "Pincode must be an integer",
    "number.min": "Pincode must be at least 6 digits",
    "number.max": "<PERSON>nco<PERSON> cannot exceed 6 digits",
  }),
});

module.exports = {
  checkPincodeValidation,
};
