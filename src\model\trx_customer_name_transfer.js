const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_customer_name_transfer",
    {
      name_transfer_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      name_transfer_reason: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_masters",
          key: "master_id",
        },
      },
      prefix: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      first_name: {
        type: DataTypes.STRING(25),
        allowNull: false,
      },
      middle_name: {
        type: DataTypes.STRING(25),
        allowNull: true,
      },
      last_name: {
        type: DataTypes.STRING(25),
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      mobile: {
        type: DataTypes.STRING(12),
        allowNull: false,
      },
      alternate_mobile: {
        type: DataTypes.STRING(12),
        allowNull: true,
      },
      name_transfer_status: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: "application-pending",
      },
      approved_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      isu_synced: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      is_active: {
        type: DataTypes.TINYINT,
        allowNull: true,
        defaultValue: 1,
      },
      is_delete: {
        type: DataTypes.TINYINT,
        allowNull: true,
        defaultValue: 0,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_customer_name_transfer",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK__trx_cust__ADFAFEA7CF02F869",
          unique: true,
          fields: [{ name: "name_transfer_id" }],
        },
      ],
    }
  );
};
