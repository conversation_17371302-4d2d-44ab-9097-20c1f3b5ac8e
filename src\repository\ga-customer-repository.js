const { dbModels, sequelize } = require('../database/connection');
const { Op, Sequelize, fn, col, literal } = require('sequelize');

const lmcCustomerConnectionModel = dbModels.trx_mapped_lmc_customer_connection;
const trxCustomerConnection = dbModels.trx_customer_connection;
const mst_areas = dbModels.mst_areas;
const mst_geographical_areas = dbModels.mst_geographical_areas;

const ongoingList = async (params) => {
  try {
    const { search, page = 1, limit = 100, geographical_area, area_id } = params;
    const offset = (page - 1) * limit;

    let currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 3);
    let threemformattedDate = currentDate.toISOString().split('T')[0];
    let newCurrentDate = new Date();
    newCurrentDate.setMonth(newCurrentDate.getMonth() - 6);
    let sixmformattedDate = newCurrentDate.toISOString().split('T')[0];

    const records = await sequelize.query(
      `
      SELECT mst_geographical_areas.geographical_area_id, mst_areas.area_name, 
      (SELECT count(customer_connection_id) FROM trx_customer_connection where
       (trx_customer_connection.status = 'application-submitted' or 
       trx_customer_connection.status = 'application-rework' or 
       trx_customer_connection.status = 'application-inreview') and available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id ))) as 'applicationInprogress', (SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='feasibility-check' and (tmlcs.tpi_status!='completed' or tmlcs.tpi_status!='rejected') and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id )))) as 'pendingFeasibilityCheck',
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='installation' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id )))) as 'pendingInstallation', 
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='commissioning' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id )))) as 'pendingCommissioning', 
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='installation' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tmlcs.created_at > 
        ` +
      threemformattedDate +
      ` and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id )))) as 'pendingInstallation3m',
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='commissioning' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tmlcs.created_at > 
` +
      sixmformattedDate +
      ` and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id )))) as 'pendingCommissioning6m'
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id  
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area +
      ` and mst_areas.area_id in (` +
      area_id +
      `) 
ORDER BY 
     mst_areas.created_at DESC
OFFSET ` +
      offset +
      ` ROWS FETCH NEXT ` +
      limit +
      ` ROWS ONLY;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const countResult = await sequelize.query(
      `
      Select COUNT(*) AS totalCount
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id  
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area + ` and mst_areas.area_id in (` +
      area_id +
      `) ;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const count = countResult[0]?.totalCount || 0;

    return {
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      rows: records,
    };
  } catch (error) {
    console.error('Error fetching TPI Application:', error);
    throw error;
  }
};

const completedList = async (params) => {
  try {
    const { search, page = 1, limit = 100, geographical_area, area_id } = params;

    const offset = (page - 1) * limit;
    const records = await sequelize.query(
      `
      SELECT mst_geographical_areas.geographical_area_id, mst_areas.area_name, (SELECT COUNT(customer_connection_id) FROM trx_customer_connection where trx_customer_connection.status = 'active-connection' and available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id ))) as 'activeCutomers',
(SELECT COUNT(customer_connection_id) FROM trx_customer_connection where trx_customer_connection.status = 'inactive-connection' and available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id ))) as 'inactiveCutomers',
(SELECT COUNT(customer_connection_id) FROM trx_customer_connection where trx_customer_connection.status = 'suspended-connection' and available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id ))) as 'suspendedCutomers', 
(SELECT COUNT(customer_connection_id) FROM trx_customer_connection where trx_customer_connection.status = 'application-rejected' and available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id ))) as 'rejectedCutomers'
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id  
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area +
      ` and mst_areas.area_id in (` +
      area_id +
      `) 
ORDER BY 
     mst_areas.created_at DESC
OFFSET ` +
      offset +
      ` ROWS FETCH NEXT ` +
      limit +
      ` ROWS ONLY;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const countResult = await sequelize.query(
      `
      SELECT COUNT(*) AS totalCount
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id  
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area + ` and mst_areas.area_id in (` +
      area_id +
      `) ;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const count = countResult[0]?.totalCount || 0;

    return {
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      rows: records,
    };
  } catch (error) {
    console.error('Error fetching TPI Application:', error);
    throw error;
  }
};

const ongoingSummaryCounts = async (params) => {
  try {
    const { geographical_area, area_id } = params;
    let currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 3);
    let threemformattedDate = currentDate.toISOString().split('T')[0];
    let newCurrentDate = new Date();
    newCurrentDate.setMonth(newCurrentDate.getMonth() - 6);
    let sixmformattedDate = newCurrentDate.toISOString().split('T')[0];
    const records = await sequelize.query(
      `
      SELECT mst_geographical_areas.geographical_area_id, (SELECT count(customer_connection_id) FROM trx_customer_connection where
(trx_customer_connection.status = 'application-submitted' or 
       trx_customer_connection.status = 'application-rework' or 
       trx_customer_connection.status = 'application-inreview') and available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where geographical_area_id=mst_geographical_areas.geographical_area_id ))) as 'applicationInprogressCount',
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='feasibility-check' and (tmlcs.tpi_status!='completed' or tmlcs.tpi_status!='rejected') and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where geographical_area_id=mst_geographical_areas.geographical_area_id  )))) as 'pendingFeasibilityCheckCount',
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='installation' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where geographical_area_id=mst_geographical_areas.geographical_area_id )))) as 'pendingInstallationCount', 
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='commissioning' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where geographical_area_id=mst_geographical_areas.geographical_area_id  )))) as 'pendingCommissioningCount', 
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='installation' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tmlcs.created_at > 
        ` +
      threemformattedDate +
      ` and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where geographical_area_id=mst_geographical_areas.geographical_area_id )))) as '3mpendingInstallationCount', 
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='commissioning' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tmlcs.created_at > 
         ` +
      sixmformattedDate +
      ` and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where geographical_area_id=mst_geographical_areas.geographical_area_id  )))) as '6mpendingCommissioningCount'
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id  
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area +
      ` and mst_areas.area_id in (` +
      area_id +
      `) 
        GROUP BY mst_geographical_areas.geographical_area_id;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );


    return {
      rows: records,
    };
  } catch (error) {
    console.error('Error fetching TPI Application:', error);
    throw error;
  }
};

const completedSummaryCounts = async (params) => {
  try {
    const { geographical_area, area_id } = params;

    const records = await sequelize.query(
      `
      SELECT mst_geographical_areas.geographical_area_id, (SELECT COUNT(customer_connection_id) FROM trx_customer_connection where trx_customer_connection.status = 'active-connection' and available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where geographical_area_id=mst_geographical_areas.geographical_area_id ))) as 'activeCutomersCount',
(SELECT COUNT(customer_connection_id) FROM trx_customer_connection where trx_customer_connection.status = 'inactive-connection' and available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where geographical_area_id=mst_geographical_areas.geographical_area_id ))) as 'inactiveCutomersCount',
(SELECT COUNT(customer_connection_id) FROM trx_customer_connection where trx_customer_connection.status = 'suspended-connection' and available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where geographical_area_id=mst_geographical_areas.geographical_area_id ))) as 'suspendedCutomersCount', 
(SELECT COUNT(customer_connection_id) FROM trx_customer_connection where trx_customer_connection.status = 'application-rejected' and available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where geographical_area_id=mst_geographical_areas.geographical_area_id ))) as 'rejectedCutomersCount'
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id  
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area +
      ` and mst_areas.area_id in (` +
      area_id +
      `) 
        GROUP BY mst_geographical_areas.geographical_area_id;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );


    return {
      rows: records,
    };
  } catch (error) {
    console.error('Error fetching TPI Application:', error);
    throw error;
  }
};


const getCustomersAreaWiseforRegistaration = async (params) => {
  const results = await dbModels.trx_customer_connection.findAll({
    logging: console.log,
    attributes: [
      'customer_connection_id',
      'first_name',
      'last_name',
      'email',
      'mobile',
      'status',
      'account_type',
      'connection_type',
      'bpcl_id',
      'bp_id',
      'created_at',
      [
        literal(`
          CASE
            WHEN status IN (
              'application-pending',
              'application-submitted',
              'application-inreview',
              'application-rework',
              'application-resubmitted',
              'application-approved',
              'application-resubmit',
              'application-completed'
            ) THEN 'application'
            WHEN status IN (
              'onboarding-schedule-pending',
              'onboarding-visit-schedule',
              'onboarding-revisit-inreview',
              'onboarding-rework',
              'onboarding-approved',
              'onboarding-visit-inreview',
              'onboarding-rejected',
              'onboarding-inprogress',
              'onboarding-visit-interview',
              'onboarding-visit-rescheduled',
              'onboarding-revisit-interview',
              'onboarding-visit-scheduled'
            ) THEN 'onboarding'            
            WHEN status IN (
              'installation-schedule-pending',
              'installation-visit-inreview',
              'installation-revisit-inreview',
              'installation-inreview',
              'installation-rework',
              'installation-approved',
              'installation-rejected',
              'installation-inprogress',
              'installation-visit-rescheduled',
              'installation-visit-scheduled',
              'activation-in-progress',
              'activation-inprogress'
            ) THEN 'installation'
             WHEN status IN (              
              'application-rejected'
            ) THEN 'Reject'  
            ELSE NULL
          END
        `),
        'stage'
      ],
      [
        literal(`
          CASE
            WHEN created_by_type = 'customer' THEN 'Self Registered'
            WHEN created_by_type = 'dma-supervisor' THEN 'DMA Registered'
            ELSE NULL
          END
        `),
        'registration_type'
      ]
    ],
    include: [
      {
        model: dbModels.mst_available_pngareas,
        as: 'available_pngarea',
        attributes: [],
        include: [
          {
            model: dbModels.mst_pincodes,
            as: 'pincodeAreasAlias',
            attributes: ['pincode'],
            include: [
              {
                model: dbModels.mst_areas,
                as: 'area',
                attributes: ['area_name'],
              },
            ],
          },
        ],
      },
    ],
    where: {
      is_active: 1,
      status: {
        [Op.ne]: 'active-connection'  // Exclude 'active-connection'
      },
      '$available_pngarea.pincodeAreasAlias.area.geographical_area_id$': params.geographical_area,
      '$available_pngarea.pincodeAreasAlias.area.area_id$': {
        [Op.in]: params.area_id,
      },
    },
    order: [[
      { model: dbModels.mst_available_pngareas, as: 'available_pngarea' },
      { model: dbModels.mst_pincodes, as: 'pincodeAreasAlias' },
      { model: dbModels.mst_areas, as: 'area' },
      'area_name', 'ASC'
    ]],
    raw: true,
    nest: true,
  });

  // Clean and reshape the data
  const cleanedResults = results.map(item => ({
    ...item,
    area_name: item.available_pngarea?.pincodeAreasAlias?.area?.area_name || null,
    pincode: item.available_pngarea?.pincodeAreasAlias?.pincode || null,
  }));

  // Remove `available_pngarea` from each record
  cleanedResults.forEach(item => delete item.available_pngarea);

  return cleanedResults;
};


const getCustomersAreaWiseForActive = async (params) => {
  const results = await dbModels.trx_customer_connection.findAll({
    attributes: [
      'customer_connection_id',
      'first_name',
      'last_name',
      'email',
      'mobile',
      'status',
      'account_type',
      'connection_type',
      'bpcl_id',
      'bp_id',
      'updated_at',
      [
        literal(`
          CASE
            WHEN created_by_type = 'customer' THEN 'Self Registered'
            WHEN created_by_type = 'dma-supervisor' THEN 'DMA Registered'
            ELSE NULL
          END
        `),
        'registration_type'
      ]
    ],
    include: [
      {
        model: dbModels.mst_available_pngareas,
        as: 'available_pngarea',
        attributes: [],
        include: [
          {
            model: dbModels.mst_pincodes,
            as: 'pincodeAreasAlias',
            attributes: ['pincode'],
            include: [
              {
                model: dbModels.mst_areas,
                as: 'area',
                attributes: ['area_name'],
              },
            ],
          },
        ],
      },
    ],
    where: {
      is_active: 1,
      status: 'active-connection',  // only 'active-connection'
      '$available_pngarea.pincodeAreasAlias.area.geographical_area_id$': params.geographical_area,
      '$available_pngarea.pincodeAreasAlias.area.area_id$': {
        [Op.in]: params.area_id,
      },
    },
    order: [[
      { model: dbModels.mst_available_pngareas, as: 'available_pngarea' },
      { model: dbModels.mst_pincodes, as: 'pincodeAreasAlias' },
      { model: dbModels.mst_areas, as: 'area' },
      'area_name', 'ASC'
    ]],
    raw: true,
    nest: true,
  });

  // Clean and reshape the data
  const cleanedResults = results.map(item => ({
    ...item,
    area_name: item.available_pngarea?.pincodeAreasAlias?.area?.area_name || null,
    pincode: item.available_pngarea?.pincodeAreasAlias?.pincode || null,
  }));

  // Remove `available_pngarea` from each record
  cleanedResults.forEach(item => delete item.available_pngarea);

  return cleanedResults;
};

const contractorAreaList = async (params) => {
  try {
    const { search, page = 1, limit = 100, geographical_area, area_id } = params;
    const offset = (page - 1) * limit;

    let currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 3);
    let threemformattedDate = currentDate.toISOString().split('T')[0];
    let newCurrentDate = new Date();
    newCurrentDate.setMonth(newCurrentDate.getMonth() - 6);
    let sixmformattedDate = newCurrentDate.toISOString().split('T')[0];

    const records = await sequelize.query(
      `
      SELECT mst_geographical_areas.geographical_area_id, mst_areas.area_name, mst_areas.area_id, 
(select mst_admin.first_name from mst_admin join mst_persona_role 
ON mst_admin.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_mapped_areas ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'TPI' and mst_admin.user_role = 'sales') as tpiSales,
(select mst_admin.first_name from mst_admin join mst_persona_role 
ON mst_admin.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_mapped_areas ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'TPI' and mst_admin.user_role = 'engineer') as tpiEngineer, 
(select COUNT(mst_admin.admin_id) from mst_admin join mst_persona_role 
ON mst_admin.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_mapped_areas ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'LMC' and mst_admin.user_role = 'contractor') as lmcContractorCount, 
(select COUNT(mst_admin.admin_id) from mst_admin join mst_persona_role 
ON mst_admin.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_mapped_areas ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'LMC' and mst_admin.user_role = 'supervisor') as lmcSupervisorCount, 
(select COUNT(mst_admin.admin_id) from mst_admin join mst_persona_role 
ON mst_admin.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_mapped_areas ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'DMA' and mst_admin.user_role = 'contractor') as dmaContractorCount, 
(select COUNT(mst_admin.admin_id) from mst_admin join mst_persona_role 
ON mst_admin.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_mapped_areas ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'DMA' and mst_admin.user_role = 'supervisor') as dmaSupervisorCount, 
(select COUNT(mst_admin.admin_id) from mst_admin join mst_persona_role 
ON mst_admin.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_mapped_areas ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'FO' and mst_admin.user_role = 'contractor') as foContractorCount, 
(select COUNT(mst_admin.admin_id) from mst_admin join mst_persona_role 
ON mst_admin.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_mapped_areas ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'FO' and mst_admin.user_role = 'supervisor') as foSupervisorCount
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id 
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area +
      ` and mst_areas.area_id in (` +
      area_id +
      `) 
ORDER BY 
     mst_areas.created_at DESC
OFFSET ` +
      offset +
      ` ROWS FETCH NEXT ` +
      limit +
      ` ROWS ONLY;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const countResult = await sequelize.query(
      `
      Select COUNT(*) AS totalCount 
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id 
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area + ` and mst_areas.area_id in (` +
      area_id +
      `) ;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const count = countResult[0]?.totalCount || 0;

    return {
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      rows: records,
    };
  } catch (error) {
    console.error('Error fetching TPI Application:', error);
    throw error;
  }
};

const lmcAreaList = async (params) => {
  try {
    const { search, page = 1, limit = 100, geographical_area, area_id } = params;
    const offset = (page - 1) * limit;

    let currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 3);
    let threemformattedDate = currentDate.toISOString().split('T')[0];
    let newCurrentDate = new Date();
    newCurrentDate.setMonth(newCurrentDate.getMonth() - 6);
    let sixmformattedDate = newCurrentDate.toISOString().split('T')[0];

    const records = await sequelize.query(
      `
      SELECT mst_geographical_areas.geographical_area_id, mst_areas.area_name, mst_areas.area_id, 
(select mst_admin.first_name, mst_admin.admin_id, mst_agencies.agency_name,
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_mapped_areas ON ma.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and ma.created_by = mst_admin.admin_id and mst_persona_role.role_name = 'LMC' and ma.user_role = 'supervisor') as 'noOfSupervisors', 
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='feasibility-check' and (tmlcs.tpi_status!='completed' or tmlcs.tpi_status!='rejected') and tlcm.lmc_id = mst_admin.admin_id
and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id )))) as 'feasibilityCheck', 
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='installation' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tlcm.lmc_id = mst_admin.admin_id and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id )))) as 'installation', 
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='commissioning' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tlcm.lmc_id = mst_admin.admin_id and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id )))) as 'commissioning', 
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='installation' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tmlcs.created_at > ` +
      threemformattedDate +
      `
and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id )))) as 'installation3m',
(SELECT COUNT(tlcm.customer_connection_id) from trx_lmc_connection_mapping as tlcm left join trx_mapped_lmc_connection_stages as tmlcs ON 
tlcm.lmc_connection_mapping_id = tmlcs.lmc_connection_mapping_id 
where tmlcs.is_active = 1 and tmlcs.lmc_stage='commissioning' and (tmlcs.tpi_status!='completed-eo' or tmlcs.tpi_status!='rejected-eo' or tmlcs.tpi_status!='rejected') and tmlcs.created_at > ` +
      sixmformattedDate +
      ` and tlcm.customer_connection_id 
in (SELECT customer_connection_id FROM trx_customer_connection where available_pngareas_id 
in (SELECT available_pngareas_id FROM mst_available_pngareas where pincode_id 
in (SELECT pincode_id FROM mst_pincodes where area_id=mst_areas.area_id )))) as 'commissioning6m'
from mst_admin join mst_persona_role 
ON mst_admin.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_contractor_other_details ON mst_admin.admin_id = trx_admin_contractor_other_details.admin_id 
join mst_agencies ON mst_agencies.agency_id = trx_admin_contractor_other_details.agency_id 
join trx_admin_mapped_areas ON mst_admin.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'LMC' and mst_admin.user_role = 'contractor' FOR JSON PATH) AS lmcContractors 
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id 
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area +
      ` and mst_areas.area_id in (` +
      area_id +
      `) 
ORDER BY 
     mst_areas.created_at DESC
OFFSET ` +
      offset +
      ` ROWS FETCH NEXT ` +
      limit +
      ` ROWS ONLY;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const countResult = await sequelize.query(
      `
      Select COUNT(*) AS totalCount  
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id 
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area + ` and mst_areas.area_id in (` +
      area_id +
      `) ;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const count = countResult[0]?.totalCount || 0;

    return {
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      rows: records,
    };
  } catch (error) {
    console.error('Error fetching TPI Application:', error);
    throw error;
  }
};


const getCustomerStageCountsForRegistration = async (params) => {
  const countResult = await sequelize.query(
    `
    
SELECT 
  Count(customer_connection_id) AS total_registration,
  Sum(CASE
        WHEN status IN ( 'application-pending', 'application-submitted',
                         'application-inreview', 'application-rework',
                         'application-resubmitted', 'application-approved',
                         'application-resubmit', 'application-completed' ) THEN 1
        ELSE 0
      END) AS application,
  Sum(CASE
        WHEN status IN ( 'onboarding-schedule-pending', 'onboarding-visit-schedule',
                         'onboarding-revisit-inreview', 'onboarding-rework',
                         'onboarding-approved', 'onboarding-visit-inreview',
                         'onboarding-rejected' ) THEN 1
        ELSE 0
      END) AS onboarding,
  Sum(CASE
        WHEN status IN ( 'installation-schedule-pending', 'installation-visit-inreview',
                         'installation-revisit-inreview', 'installation-inreview',
                         'installation-rework', 'installation-approved',
                         'installation-rejected' ) THEN 1
        ELSE 0
      END) AS installation,
  Sum(CASE
        WHEN status = 'application-rejected' THEN 1
        ELSE 0
      END) AS rejected
FROM 
  dbo.trx_customer_connection AS trx_customer_connection
  LEFT OUTER JOIN dbo.mst_available_pngareas AS available_pngarea
    ON trx_customer_connection.available_pngareas_id = available_pngarea.available_pngareas_id
  LEFT OUTER JOIN dbo.mst_pincodes AS mst_pincodes
    ON available_pngarea.pincode_id = mst_pincodes.pincode_id
  LEFT OUTER JOIN dbo.mst_areas AS mst_areas
    ON mst_pincodes.area_id = mst_areas.area_id
WHERE 
  trx_customer_connection.is_active = 1
  AND trx_customer_connection.status != N'active-connection'
  AND mst_areas.geographical_area_id =` +
    params.geographical_area + ` and mst_areas.area_id in (` +
    params.area_id +
    `) ;
  `,
    {
      type: Sequelize.QueryTypes.SELECT,
    }
  );

  return countResult[0];

};


// const getCustomerStageCountsForRegistration = async () => {
//   const results = await dbModels.trx_customer_connection.findAll({
//     attributes: [
//       [fn('COUNT', col('customer_connection_id')), 'total_registration'],
//       [
//         fn('SUM', literal(`CASE 
//           WHEN status IN (
//             'application-pending',
//             'application-submitted',
//             'application-inreview',
//             'application-rework',
//             'application-resubmitted',
//             'application-approved',
//             'application-resubmit',
//             'application-completed'
//           ) THEN 1 ELSE 0 END`)),
//         'application'
//       ],
//       [
//         fn('SUM', literal(`CASE 
//           WHEN status IN (
//             'onboarding-schedule-pending',
//             'onboarding-visit-schedule',
//             'onboarding-revisit-inreview',
//             'onboarding-rework',
//             'onboarding-approved',
//             'onboarding-visit-inreview',
//             'onboarding-rejected'
//           ) THEN 1 ELSE 0 END`)),
//         'onboarding'
//       ],
//       [
//         fn('SUM', literal(`CASE 
//           WHEN status IN (
//             'installation-schedule-pending',
//             'installation-visit-inreview',
//             'installation-revisit-inreview',
//             'installation-inreview',
//             'installation-rework',
//             'installation-approved',
//             'installation-rejected'
//           ) THEN 1 ELSE 0 END`)),
//         'installation'
//       ],
//       [
//         fn('SUM', literal(`CASE 
//           WHEN status = 'application-rejected' THEN 1 ELSE 0 END`)),
//         'rejected'
//       ]
//     ],
//     where: {
//       is_active: 1,
//       status: {
//         [Op.ne]: 'active-connection'  // Exclude 'active-connection'
//       }
//     },
//     raw: true
//   });

//   return results[0]; // single result row
// };


const getCustomerStageCountsForActive = async () => {
  // Step 1: Get all active-connection customer_connection_ids
  const activeCustomerIds = await dbModels.trx_customer_connection.findAll({
    attributes: ['customer_connection_id'],
    where: {
      status: 'active-connection',
      is_active: 1
    },
    raw: true
  });

  const customerIds = activeCustomerIds.map(c => c.customer_connection_id);
  if (customerIds.length === 0) {
    return {
      billed: 0,
      total_payment_due: 0,
      total_payment_received: 0
    };
  }

  // Step 2: Use payments table with the filtered customer IDs
  const payments = await dbModels.trx_connection_payments.findAll({
    attributes: [
      [fn('COUNT', col('connection_payments_id')), 'billed'],
      [fn('SUM', literal(`CASE WHEN section_type = 'meter-readings' AND payment_status = 'pending' THEN total_amount ELSE 0 END`)), 'total_payment_due'],
      [fn('SUM', literal(`CASE WHEN section_type = 'meter-readings' AND payment_status = 'completed' THEN total_amount ELSE 0 END`)), 'total_payment_received'],
    ],
    where: {
      customer_connection_id: {
        [Op.in]: customerIds
      },
      section_type: 'meter-readings',
      is_active: 1
    },
    raw: true
  });


  return {
    billed: parseInt(payments[0].billed) || 0,
    total_payment_due: parseFloat(payments[0].total_payment_due) || 0,
    total_payment_received: parseFloat(payments[0].total_payment_received) || 0
  };
};

const dmaAreaList = async (params) => {
  try {
    const { search, page = 1, limit = 100, geographical_area, area_id } = params;
    const offset = (page - 1) * limit;

    let currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 3);
    let threemformattedDate = currentDate.toISOString().split('T')[0];
    let newCurrentDate = new Date();
    newCurrentDate.setMonth(newCurrentDate.getMonth() - 6);
    let sixmformattedDate = newCurrentDate.toISOString().split('T')[0];

    const records = await sequelize.query(
      `
      SELECT mst_geographical_areas.geographical_area_id, mst_areas.area_name, mst_areas.area_id, 
(select mstadm.first_name, mstadm.admin_id, mst_agencies.agency_name,
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
where 
ma.created_by = mstadm.admin_id and mst_persona_role.role_name = 'DMA' and ma.user_role = 'supervisor') as 'noOfSupervisors',
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
join trx_dma_help_with_reg_mapping ON ma.admin_id = trx_dma_help_with_reg_mapping.dma_supervisor_assigned_id 
join trx_help_with_registration ON trx_dma_help_with_reg_mapping.help_with_registration_id = trx_help_with_registration.help_with_registration_id 
where ma.created_by = mstadm.admin_id and (trx_dma_help_with_reg_mapping.hwrm_status = 'approved'
 or trx_dma_help_with_reg_mapping.hwrm_status = 'rework'
  or trx_dma_help_with_reg_mapping.hwrm_status = 'pending-approval'
   or trx_dma_help_with_reg_mapping.hwrm_status = 'inprogress'
    or trx_dma_help_with_reg_mapping.hwrm_status = 'not-started'
    or trx_dma_help_with_reg_mapping.hwrm_status = 'rejected') and trx_help_with_registration.customer_type = 'assigned' and mst_persona_role.role_name = 'DMA' and ma.user_role = 'supervisor') as 'assigned', 
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
join trx_dma_help_with_reg_mapping ON ma.admin_id = trx_dma_help_with_reg_mapping.dma_supervisor_assigned_id 
join trx_help_with_registration ON trx_dma_help_with_reg_mapping.help_with_registration_id = trx_help_with_registration.help_with_registration_id 
where ma.created_by = mstadm.admin_id and (trx_dma_help_with_reg_mapping.hwrm_status = 'approved'
 or trx_dma_help_with_reg_mapping.hwrm_status = 'rework'
  or trx_dma_help_with_reg_mapping.hwrm_status = 'pending-approval'
   or trx_dma_help_with_reg_mapping.hwrm_status = 'inprogress'
    or trx_dma_help_with_reg_mapping.hwrm_status = 'not-started'
    or trx_dma_help_with_reg_mapping.hwrm_status = 'rejected') and trx_help_with_registration.customer_type = 'self-registered' and mst_persona_role.role_name = 'DMA' and ma.user_role = 'supervisor') as 'Registred', 
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
join trx_dma_help_with_reg_mapping ON ma.admin_id = trx_dma_help_with_reg_mapping.dma_supervisor_assigned_id 
join trx_help_with_registration ON trx_dma_help_with_reg_mapping.help_with_registration_id = trx_help_with_registration.help_with_registration_id 
where ma.created_by = mstadm.admin_id and trx_dma_help_with_reg_mapping.hwrm_status = 'not-started' and mst_persona_role.role_name = 'DMA' and ma.user_role = 'not-started') as 'notStarted',
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
join trx_dma_help_with_reg_mapping ON ma.admin_id = trx_dma_help_with_reg_mapping.dma_supervisor_assigned_id 
join trx_help_with_registration ON trx_dma_help_with_reg_mapping.help_with_registration_id = trx_help_with_registration.help_with_registration_id 
where ma.created_by = mstadm.admin_id and trx_dma_help_with_reg_mapping.hwrm_status = 'inprogress' and mst_persona_role.role_name = 'DMA' and ma.user_role = 'supervisor') as 'inprogress',
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
join trx_dma_help_with_reg_mapping ON ma.admin_id = trx_dma_help_with_reg_mapping.dma_supervisor_assigned_id 
join trx_help_with_registration ON trx_dma_help_with_reg_mapping.help_with_registration_id = trx_help_with_registration.help_with_registration_id 
where ma.created_by = mstadm.admin_id and trx_dma_help_with_reg_mapping.hwrm_status = 'pending-approval' and mst_persona_role.role_name = 'DMA' and ma.user_role = 'supervisor') as 'pending-approval',
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
join trx_dma_help_with_reg_mapping ON ma.admin_id = trx_dma_help_with_reg_mapping.dma_supervisor_assigned_id 
join trx_help_with_registration ON trx_dma_help_with_reg_mapping.help_with_registration_id = trx_help_with_registration.help_with_registration_id 
where ma.created_by = mstadm.admin_id and trx_dma_help_with_reg_mapping.hwrm_status = 'rework' and mst_persona_role.role_name = 'DMA' and ma.user_role = 'supervisor') as 'rework',
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
join trx_dma_help_with_reg_mapping ON ma.admin_id = trx_dma_help_with_reg_mapping.dma_supervisor_assigned_id 
join trx_help_with_registration ON trx_dma_help_with_reg_mapping.help_with_registration_id = trx_help_with_registration.help_with_registration_id 
where ma.created_by = mstadm.admin_id and trx_dma_help_with_reg_mapping.hwrm_status = 'approved' and mst_persona_role.role_name = 'DMA' and ma.user_role = 'supervisor') as 'approved',
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
join trx_dma_help_with_reg_mapping ON ma.admin_id = trx_dma_help_with_reg_mapping.dma_supervisor_assigned_id 
join trx_help_with_registration ON trx_dma_help_with_reg_mapping.help_with_registration_id = trx_help_with_registration.help_with_registration_id 
where ma.created_by = mstadm.admin_id and trx_dma_help_with_reg_mapping.hwrm_status = 'rejected' and mst_persona_role.role_name = 'DMA' and ma.user_role = 'supervisor') as 'rejected' 
from mst_admin as mstadm join mst_persona_role 
ON mstadm.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_contractor_other_details ON mstadm.admin_id = trx_admin_contractor_other_details.admin_id 
join mst_agencies ON mst_agencies.agency_id = trx_admin_contractor_other_details.agency_id 
join trx_admin_mapped_areas ON mstadm.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'DMA' and mstadm.user_role = 'contractor' FOR JSON PATH) AS lmcContractors 
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id 
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area +
      ` and mst_areas.area_id in (` +
      area_id +
      `) 
ORDER BY 
     mst_areas.created_at DESC
OFFSET ` +
      offset +
      ` ROWS FETCH NEXT ` +
      limit +
      ` ROWS ONLY;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const countResult = await sequelize.query(
      `
      Select COUNT(*) AS totalCount
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id  
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area + ` and mst_areas.area_id in (` +
      area_id +
      `) ;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const count = countResult[0]?.totalCount || 0;

    return {
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      rows: records,
    };
  } catch (error) {
    console.error('Error fetching TPI Application:', error);
    throw error;
  }
};

const foAreaList = async (params) => {
  try {
    const { search, page = 1, limit = 100, geographical_area, area_id } = params;
    const offset = (page - 1) * limit;

    let currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 3);
    let threemformattedDate = currentDate.toISOString().split('T')[0];
    let newCurrentDate = new Date();
    newCurrentDate.setMonth(newCurrentDate.getMonth() - 6);
    let sixmformattedDate = newCurrentDate.toISOString().split('T')[0];

    const records = await sequelize.query(
      `
      SELECT mst_geographical_areas.geographical_area_id, mst_areas.area_name, mst_areas.area_id, 
(select mstadm.first_name, mstadm.admin_id, mst_agencies.agency_name,
(select COUNT(ma.admin_id) from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
where 
ma.created_by = mstadm.admin_id and mst_persona_role.role_name = 'FO' and ma.user_role = 'supervisor') as 'noOfSupervisors', 
(select COUNT(mst_meter_reading_orders.mro_id) from mst_meter_reading_orders where mst_meter_reading_orders.mro_status = 'pending' and mst_meter_reading_orders.fo_supervisor_id IN (select ma.admin_id from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
where ma.created_by = mstadm.admin_id  
and mst_persona_role.role_name = 'FO' and ma.user_role = 'supervisor')) as 'newMRO', 
(select COUNT(mst_meter_reading_orders.mro_id) from mst_meter_reading_orders where mst_meter_reading_orders.mro_status = 'inprogress' and mst_meter_reading_orders.fo_supervisor_id IN (select ma.admin_id from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
where ma.created_by = mstadm.admin_id  
and mst_persona_role.role_name = 'FO' and ma.user_role = 'supervisor')) as 'assignedMRO', 
(select COUNT(mst_meter_reading_orders.mro_id) from mst_meter_reading_orders where mst_meter_reading_orders.mro_status = 'completed' and mst_meter_reading_orders.fo_supervisor_id IN (select ma.admin_id from mst_admin as ma join mst_persona_role 
ON ma.persona_role_id= mst_persona_role.persona_role_id 
where ma.created_by = mstadm.admin_id  
and mst_persona_role.role_name = 'FO' and ma.user_role = 'supervisor')) as 'completedMRO' 
from mst_admin as mstadm join mst_persona_role 
ON mstadm.persona_role_id= mst_persona_role.persona_role_id 
join trx_admin_contractor_other_details ON mstadm.admin_id = trx_admin_contractor_other_details.admin_id 
join mst_agencies ON mst_agencies.agency_id = trx_admin_contractor_other_details.agency_id 
join trx_admin_mapped_areas ON mstadm.admin_id = trx_admin_mapped_areas.admin_id and trx_admin_mapped_areas.area_id = mst_areas.area_id
where trx_admin_mapped_areas.area_id = mst_areas.area_id and mst_persona_role.role_name = 'FO' and mstadm.user_role = 'contractor' FOR JSON PATH) AS lmcContractors 
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id 
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area +
      ` and mst_areas.area_id in (` +
      area_id +
      `) 
ORDER BY 
     mst_areas.created_at DESC
OFFSET ` +
      offset +
      ` ROWS FETCH NEXT ` +
      limit +
      ` ROWS ONLY;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const countResult = await sequelize.query(
      `
      Select COUNT(*) AS totalCount
from mst_geographical_areas LEFT JOIN mst_areas 
ON mst_geographical_areas.geographical_area_id=mst_areas.geographical_area_id  
where mst_areas.is_active = 1 and mst_geographical_areas.geographical_area_id=` +
      geographical_area + ` and mst_areas.area_id in (` +
      area_id +
      `) ;
    `,
      {
        type: Sequelize.QueryTypes.SELECT,
      }
    );

    const count = countResult[0]?.totalCount || 0;

    return {
      totalRecords: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      rows: records,
    };
  } catch (error) {
    console.error('Error fetching TPI Application:', error);
    throw error;
  }
};

module.exports = {
  ongoingList,
  completedList,
  ongoingSummaryCounts,
  completedSummaryCounts,
  getCustomersAreaWiseforRegistaration,
  getCustomersAreaWiseForActive,
  getCustomerStageCountsForRegistration,
  getCustomerStageCountsForActive,
  contractorAreaList,
  lmcAreaList,
  dmaAreaList,
  foAreaList
};
