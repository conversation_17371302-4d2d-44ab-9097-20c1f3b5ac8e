const {dbModels} = require('../database/connection');
const ProofOfOwnership = require('../model/mst_proof_of_ownership');

const getproofOfOwnershipList = async (page = 1, limit = 10) => {
    const offset = (page - 1) * limit; // Calculate the offset for pagination

    try {
        const { count: totalRecords, rows: proofOfOwnership } = await dbModels.mst_proof_of_ownership.findAndCountAll({
            where: {
                is_delete: 1, // Ensure only non-deleted records are returned
            },
            offset: offset, // Apply pagination offset
            limit: limit, // Apply pagination limit
        });

        const totalPages = Math.ceil(totalRecords / limit); // Calculate total pages

        return {
            data: proofOfOwnership,
            meta: {
                totalRecords,
                totalPages,
                currentPage: page,
            },
        };
    } catch (error) {
        throw new Error(`Database error: ${error.message}`);
    }
};


const createProofOfOwnershipList = async (dto) => {
    
    
    return await dbModels.mst_proof_of_ownership.create(dto);
};

const showProofOfOwnershipList = async (id) => {
    
    const data = await dbModels.mst_proof_of_ownership.findByPk(id);
    
    
    return data;
};

const updateProofOfOwnershipList = async (id, dto) => {
    const data = await showProofOfOwnershipList(id);
    return data.update(dto);
};

const deleteProofOfOwnershipList = async (id, dto) => {
    const data = await updateProofOfOwnershipList(id, dto);
    return data;
};

const checkExistingname = async (name) => {
    
    return await dbModels.mst_proof_of_ownership.findOne({
      where: {ownership_name: name },
      raw: true,
    });
  };
  
module.exports = {
    getproofOfOwnershipList,
    updateProofOfOwnershipList,
    showProofOfOwnershipList,
    createProofOfOwnershipList,
    deleteProofOfOwnershipList,
    checkExistingname,
};
