const applicationStatusService = require('../services/application-status-service');
const { sendError, sendSuccess } = require('../helpers/responseHelper');
const message = require('../utils/constants');

// Get list of application statuses

const getApplicationStatusList = async (req, res) => {
  try {
    console.log("Query Parameters:", req.query);

    const { page, limit, search, orderBy, orderName } = req.query;

    const validatedPage = page && page > 0 ? parseInt(page, 10) : 1;
    const validatedLimit = limit && limit > 0 ? parseInt(limit, 10) : 10;

    const filters = {
      page: validatedPage,
      limit: validatedLimit,
      search: search || "", // Default to an empty string if undefined
      orderBy: orderBy || "asc", // Default to "asc" if not provided
      orderName: orderName || "application_status_id", // Default column to order by
    };
     
     
    const data = await applicationStatusService.getApplicationStatusList(filters);

    sendSuccess(req, res, data, message.FETCHED);
  } catch (error) {
    console.error("Error in getApplicationStatusList:", error);
    sendError(req, res, error, message.BAD_REQUEST);
  }
};



// Create new application status
const createApplicationStatus = async (req, res) => {
  try {
    const data = await applicationStatusService.createApplicationStatus(req.body);
    if (data && data.message === 'This Name is already exist') {
      return sendError(req, res, data, message.BAD_REQUEST, 400);
    }
    sendSuccess(req, res, data, message.CREATED);
  } catch (error) {
    sendError(req, res, error, message.BAD_REQUEST);
  }
};

// Update existing application status
const updateApplicationStatus = async (req, res) => {
  try {
    const data = await applicationStatusService.updateApplicationStatus(req.body);
    sendSuccess(req, res, data, message.UPDATED);
  } catch (error) {
    sendError(req, res, error, message.BAD_REQUEST);
  }
};

// Soft delete application status
const deleteApplicationStatus = async (req, res) => {
  try {
    const { application_status_id } = req.body;
    const data = await applicationStatusService.deleteApplicationStatus({ application_status_id });
    if (data && data.message === 'This record is already deleted') {
      return sendError(req, res, data, message.BAD_REQUEST, 400);
    }
    sendSuccess(req, res, data, message.DELETED);
  } catch (error) {
    sendError(req, res, error, message.BAD_REQUEST);
  }
};

module.exports = {
  getApplicationStatusList,
  createApplicationStatus,
  updateApplicationStatus,
  deleteApplicationStatus,
};
