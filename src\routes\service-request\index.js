const {
  verifyPngToken,
} = require("../../middleware/customer-auth");
const express = require('express');
const serviceRequestRoutes = express.Router();
const serviceRequestController = require('../../controllers/service-request-controller');
const validator = require("../../middleware/validator");
const { uploadMultiple } = require("../../middleware/multer");
const { uploadSingleTemp} = require("../../middleware/newMulter");

serviceRequestRoutes.post('/address-change',verifyPngToken("guest"),validator("service-request-validation","updateConnectionAddressValidation"),serviceRequestController.addressChangeRequest);
serviceRequestRoutes.post('/save-permanent-address-change',verifyPngToken("guest"),validator("service-request-validation","saveConnectionAddressValidation"),serviceRequestController.saveAddressChangeDetail);
serviceRequestRoutes.post('/permanent-address-change-document',verifyPngToken("guest"),uploadSingleTemp,validator("service-request-validation","uploadDocumentValidation"),serviceRequestController.uploadDocuments);
serviceRequestRoutes.post('/get-address-change-detail',verifyPngToken("guest"),validator("service-request-validation","getCustomerDetailValidation"),serviceRequestController.getCustomerDetailsById);
serviceRequestRoutes.post('/save-new-address-details',verifyPngToken("guest"),validator("service-request-validation","newAddressValidationAll"),serviceRequestController.saveNewAddressDetail);
serviceRequestRoutes.post('/new-address-document',verifyPngToken("guest"), uploadSingleTemp,validator("service-request-validation","uploadDocumentValidation"),serviceRequestController.uploadNewAddressDocuments);
serviceRequestRoutes.post('/document-delete',verifyPngToken("guest"), validator("service-request-validation","uploadDocumentValidation"),serviceRequestController.documentDelete);
serviceRequestRoutes.post('/get-new-address-detail',verifyPngToken("guest"),validator("service-request-validation","getCustomerDetailValidation"),serviceRequestController.getCustomerNewAddressDetailsById);
serviceRequestRoutes.post('/submit-enable-connection-request',verifyPngToken("guest"),validator("service-request-validation","enableConnectionValidation"),serviceRequestController.submitEnableAddressRequest);
serviceRequestRoutes.post('/extra-points',verifyPngToken("guest"),validator("service-request-validation","validateExtraPointPayload"),serviceRequestController.extraPoints);
serviceRequestRoutes.post('/update-connection',verifyPngToken("guest"),
  validator("service-request-validation", "updateConnectionValidation"),serviceRequestController.updateConnectionRequest);
serviceRequestRoutes.post('/alteration-modification',verifyPngToken("guest"),validator("service-request-validation", "altrationModificationValidation"),serviceRequestController.altrationModification);
serviceRequestRoutes.post('/name-transfer',verifyPngToken("guest"),validator("service-request-validation", "nameTransferValidation"),serviceRequestController.nameTransfer);
serviceRequestRoutes.post('/name-transfer-save-details',verifyPngToken("guest"),validator("service-request-validation", "nameTransferSaveDetailsValidation"),serviceRequestController.nameTransferSaveDetailsInDb);
serviceRequestRoutes.post('/transfer-approval-status-update',verifyPngToken("guest"),validator("service-request-validation", "transferApprovalStatusUpdateValidation"),serviceRequestController.transferApprovalStatusUpdate);
module.exports = {serviceRequestRoutes};
