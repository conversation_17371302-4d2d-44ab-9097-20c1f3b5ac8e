const { sendSuccess, sendError } = require('../helpers/responseHelper');
const serviceRequestService = require('../services/service-request-service');
exports.addressChangeRequest = async (req, res) => {

  try {
    // Call service layer with req.body
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await serviceRequestService.addressChangeRequestService(requestBody);
    
    sendSuccess(req, res, result, `Request submitted successfully`);
  } catch (error) {
    sendError(req, res, error?.details?error.details:error, error.message);
  }
};
exports.submitEnableAddressRequest = async (req, res) => {

  try {
    // Call service layer with req.body
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await serviceRequestService.submitEnableAddressRequestService(requestBody);
    
    //const result = "data fetched successfully";
    sendSuccess(req, res, result, `Request Raised Successfully`);
  } catch (error) {
    sendError(req, res, error?.details?error.details:error, error.message);
  }
};
exports.saveAddressChangeDetail = async (req, res) => {

  try {
    // Call service layer with req.body
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await serviceRequestService.submitPermanentAddressChangeDetailsToCache(requestBody);
    
    sendSuccess(req, res, result, "Details saved successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.uploadDocuments = async (req, res) => {
  console.log("5",req.body);
  
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    // Call service layer with req.body and req.files
    console.log("debug",requestBody);
    
    const result = await serviceRequestService.uploadDocuments(
      requestBody,
      req.files
    );
    sendSuccess(
      req,
      res,
      result,
      "Documents uploaded and Redis cache updated successfully."
    );
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};
exports.getCustomerDetailsById = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await serviceRequestService.CustomerDetailById(requestBody);
    if (result) {
      sendSuccess(req, res, result, "Customer Details Fetched successfully.");
    } else {
      sendError(req, res, null, "Customer Details not found.");
    }
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};
exports.saveNewAddressDetail = async (req, res) => {

  try {
    // Call service layer with req.body
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await serviceRequestService.submitNewAddressDetailsToCache(requestBody);
    
    sendSuccess(req, res, result, "Details saved successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.uploadNewAddressDocuments = async (req, res) => {
  console.log("5",req.body);
  
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    // Call service layer with req.body and req.files
    console.log("debug",requestBody);
    
    const result = await serviceRequestService.uploadNewAddressDocumentsService(
      requestBody,
      req.files
    );
    sendSuccess(
      req,
      res,
      result,
      "Documents uploaded and Redis cache updated successfully."
    );
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};

exports.documentDelete = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result =
      await serviceRequestService.documentDeleteService(requestBody);
    sendSuccess(req, res, result, result.message);
  } catch (error) {
    console.error("Error in deleteDocument:", error);
    sendError(req, res, null, error.message);
  }
};

exports.getCustomerNewAddressDetailsById = async (req, res) => {
  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await serviceRequestService.CustomerNewAddressDetailById(requestBody);
    if (result) {
      sendSuccess(req, res, result, "Customer Details Fetched successfully.");
    } else {
      sendError(req, res, null, "Customer Details not found.");
    }
  } catch (error) {
    sendError(req, res, null, error.message);
  }
};
exports.updateConnectionRequest = async (req, res) => {

  try {
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const requestType=requestBody.update_request_type;
    const requestId = await serviceRequestService.connectionServiceRequest(requestBody);
    sendSuccess(req, res, requestId, `Request raised Successfully`);
  } catch (error) {
    sendError(req, res, error?.details?error.details:error, error.message);
  }
};
  
exports.extraPoints = async (req, res) => {
  try {
    // Call service layer with req.body
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await serviceRequestService.extraPointsService(requestBody);
    //const result = "data fetched successfully";
    sendSuccess(req, res, result, 'Request raised Successfully');
  } catch (error) {
    sendError(req, res, error?.details?error.details:error, error.message);
  }
};
  
exports.altrationModification = async (req, res) => {
  try {
    // Call service layer with req.body
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await serviceRequestService.alterationModification(requestBody);
    //const result = "data fetched successfully";
    sendSuccess(req, res, result, 'Request raised Successfully');
  } catch (error) {
    sendError(req, res, error?.details?error.details:error, error.message);
  }
};
exports.nameTransfer = async (req, res) => {
  try {
    // Call service layer with req.body
    const requestBody = req.body;
    requestBody.bpcl_id = req.bpclId;
    const result = await serviceRequestService.submitDetailsToCache(requestBody);
    //const result = "data fetched successfully";
    sendSuccess(req, res, result, 'Application submitted successfully');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
  
exports.nameTransferSaveDetailsInDb = async (req, res) => {
  try {
    // Call service layer with req.body
    req.body.created_by = req.bpclId;
    const result = await serviceRequestService.nameTrasferSaveToDbService(req.body);
    //const result = "data fetched successfully";
    sendSuccess(req, res, result, 'Request submitted successfully');
  } catch (error) {
    sendError(req, res, error?.details?error.details:error, error.message);
  }
};
  
exports.transferApprovalStatusUpdate = async (req, res) => {
  try {
    // Call service layer with req.body
    const requestBody = req.body;
    // console.log("req.customer=", req.customer);
    // console.log("req.user=", req.user);
    requestBody.updated_by = req.bpclId;

    const result = await serviceRequestService.transferApprovalStatusUpdate(requestBody);
    //const result = "data fetched successfully";
    sendSuccess(req, res, result, 'Name Transfer Status Updated Successfully');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};