const { Op, Sequelize } = require("sequelize");
const { dbModels, sequelize } = require("../database/connection");
const lmcCustomerConnectionModel = dbModels.trx_mapped_lmc_customer_connection;
const lmcPipelineModel = dbModels.trx_lmc_pipeline;

const getPipelineDetails = async (customer_id) => {
  try {
    const results = await lmcCustomerConnectionModel.findOne({
      attributes: [
        "mapped_lmc_customer_connection_id",
        "customer_connection_id",
        "lmc_id",
        "lmc_stage",
        "visit_datetime",
        "reason",
        "visit_status",
        "tpi_id",
        "tpi_status",
        "customer_approval",
      ],
      include: [
        {
          model: lmcPipelineModel,
          as: "trx_lmc_pipelines",
          attributes: [
            "lmc_pipeline_id",
            "mapped_lmc_customer_connection_id",
            "house_type",
            "pipeline_size",
            "extra_charge",
            "customer_consent",
            "extra_charge_approval",
            "pipeline_status",
          ],
        },
      ],
      where: {
        customer_connection_id: customer_id,
        is_active: 1,
      },
      raw: false, // Ensures Sequelize returns model instances
      nest: true, // Ensures nested objects are properly structured
    });

    return results;
  } catch (error) {
    console.error("Error in getPipelineDetails:", error);
    throw error;
  }
};

const updatePipelineDetails = async (params, whereCond) => {
  const results = await lmcPipelineModel.update(params, { where: whereCond });
  console.log("updatePipelineDetails results", results);
  return results;
};

const updateMapCustomerConnectionDetails = async (params, whereCond) => {
  const results = await lmcCustomerConnectionModel.update(params, {
    where: whereCond,
  });
  console.log("updateMapCustomerConnectionDetails results", results);
  return results;
};

const getLmcCasesSummery = async (lmc_id, area_ids) => {
  try {
    if (!lmc_id) {
      throw new Error("Invalid lmc_id provided.");
    }

    const areaWhereCondition = { area_id: { [Op.in]: area_ids } };

    const result = await dbModels.trx_mapped_lmc_connection_stages.findAll({
      attributes: [
        [
          Sequelize.literal(`
            COUNT(DISTINCT CASE 
              WHEN trx_mapped_lmc_connection_stages.tpi_status IN ('no-submission', 'pending','pending-eo') 
              THEN trx_mapped_lmc_connection_stages.lmc_connection_mapping_id 
            END)
          `),
          "total_pending_cases",
        ],
        [
          Sequelize.literal(`
            COUNT(DISTINCT CASE 
              WHEN trx_mapped_lmc_connection_stages.lmc_stage = 'feasibility-check' 
                AND trx_mapped_lmc_connection_stages.tpi_status IN ('no-submission', 'pending','pending-eo') 
              THEN trx_mapped_lmc_connection_stages.lmc_connection_mapping_id 
            END)
          `),
          "pending_feasibility_check",
        ],
        [
          Sequelize.literal(`
            COUNT(DISTINCT CASE 
              WHEN trx_mapped_lmc_connection_stages.lmc_stage = 'installation' 
                AND trx_mapped_lmc_connection_stages.tpi_status IN ('no-submission', 'pending','pending-eo') 
              THEN trx_mapped_lmc_connection_stages.lmc_connection_mapping_id 
            END)
          `),
          "pending_installations",
        ],
        [
          Sequelize.literal(`
            COUNT(DISTINCT CASE 
              WHEN trx_mapped_lmc_connection_stages.lmc_stage = 'commissioning' 
                AND trx_mapped_lmc_connection_stages.tpi_status IN ('no-submission', 'pending','pending-eo') 
              THEN trx_mapped_lmc_connection_stages.lmc_connection_mapping_id 
            END)
          `),
          "pending_commissioning",
        ],
        [
          Sequelize.literal(`
            COUNT(DISTINCT CASE 
              WHEN trx_mapped_lmc_connection_stages.tpi_status IN ('resubmit', 'resubmit-eo') 
              THEN trx_mapped_lmc_connection_stages.lmc_connection_mapping_id 
            END)
          `),
          "current_rework_requests",
        ],
        [
          Sequelize.literal(`
            COUNT(DISTINCT CASE 
              WHEN trx_mapped_lmc_connection_stages.lmc_stage = 'commissioning' 
                AND trx_mapped_lmc_connection_stages.tpi_status IN ('completed', 'completed-eo') 
              THEN trx_mapped_lmc_connection_stages.lmc_connection_mapping_id 
            END)
          `),
          "active_connections",
        ],
      ],
      include: [
        {
          model: dbModels.trx_lmc_connection_mapping,
          as: "lmc_connection_mapping",
          attributes: [],
          required: true,
          where: {
            lmc_id: lmc_id,
            is_active: 1,
          },
          include: [
            {
              model: dbModels.trx_customer_connection,
              as: "customer_connection",
              attributes: [],
              include: [
                {
                  model: dbModels.mst_available_pngareas,
                  as: "available_pngarea",
                  attributes: [], // Important to prevent extra SELECTs
                  required: true,
                  include: [
                    {
                      model: dbModels.mst_pincodes,
                      as: "pincodeAreasAlias",
                      attributes: [], // Prevent SELECT
                      include: [
                        {
                          model: dbModels.mst_areas,
                          as: "area",
                          attributes: [], // Prevent SELECT
                          where: areaWhereCondition,
                          required: true,
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      where: {
        is_latest: 1,
      },
      raw: true,
    });

    return result?.[0] || {};
  } catch (error) {
    console.error("Error in getLmcCasesSummery:", error);
    throw error;
  }
};

const getLmcCases = async (
  whereCondition = {},
  stageCondition = { is_latest: 1 },
  searchCondition = {},
  limit = 1000,
  offset = 0,
  area_ids = []
) => {
  try {
    const areaWhereCondition = { area_id: { [Op.in]: area_ids } };

    const { count, rows } =
      await dbModels.trx_lmc_connection_mapping.findAndCountAll({
        attributes: [
          "lmc_connection_mapping_id",
          "customer_connection_id",
          "lmc_id",
          "updated_at",
        ],
        where: {
          ...whereCondition,
          ...searchCondition,
        },
        include: [
          {
            model: dbModels.mst_admin,
            as: "lmc",
            attributes: ["admin_id"],
            required: true,
            include: [
              {
                model: dbModels.trx_admin_mapped_areas,
                as: "trx_admin_mapped_areas",
                attributes: ["area_id"],
                required: true,
              },
            ],
          },
          {
            model: dbModels.trx_customer_connection,
            as: "customer_connection",
            attributes: [
              "customer_connection_id",
              "first_name",
              "last_name",
              "email",
              "mobile",
              "bp_id",
            ],
            required: false,
            include: [
                {
                  model: dbModels.mst_available_pngareas,
                  as: "available_pngarea",
                  attributes: [], 
                  required: true,
                  include: [
                    {
                      model: dbModels.mst_pincodes,
                      as: "pincodeAreasAlias",
                      attributes: ["area_id"], 
                      include: [
                        {
                          model: dbModels.mst_areas,
                          as: "area",
                          attributes: [], 
                          where: areaWhereCondition,
                          required: true,
                        },
                      ],
                    },
                  ],
                },
              {
                model: dbModels.trx_customer_connection_address,
                as: "trx_customer_connection_addresses",
                attributes: [
                  "permanent_address",
                  "connection_address",
                  "connection_pincode",
                  "connection_city",
                  "connection_state",
                  "connection_district",
                ],
                required: false,
              },
            ],
          },
          {
            model: dbModels.trx_mapped_lmc_connection_stages,
            as: "trx_mapped_lmc_connection_stages",
            attributes: [
              "mapped_lmc_connection_stage_id",
              "lmc_stage",
              "visit_status",
              "visit_datetime",
              "tpi_status",
            ],
            required: Object.keys(stageCondition).length > 0,
            where: stageCondition,
          },
        ],
        limit,
        offset,
        distinct: true,
        col: "lmc_connection_mapping_id", // ✅ fixed here
        order: [["updated_at", "DESC"]],
      });

    return { count, rows };
  } catch (error) {
    console.error("Repository Error - getLmcCases:", error);
    throw error;
  }
};



// Repository function for fetching LMC Feasibility Tickets
const getLmcTickets = async (
  whereCondition,
  searchCondition,
  limit,
  offset,
  securityDepositFilter,
  lmc_id,
  area_ids,
  holdCondition
) => {
  const customerConnectionWhere = {};
  if (securityDepositFilter !== null) {
    customerConnectionWhere.is_security_deposit = securityDepositFilter;
  }

  const areaWhereCondition =
  area_ids.length > 0 ? { area_id: { [Op.in]: area_ids } } : {};

  return await dbModels.trx_mapped_lmc_connection_stages.findAndCountAll({
    attributes: [
      "mapped_lmc_connection_stage_id",
      "lmc_stage",
      "visit_status",
      "tpi_status",
      "visit_datetime",
    ],
    include: [
      {
        model: dbModels.trx_mapped_lmc_feasibility_commission_details,
        as: "trx_mapped_lmc_feasibility_commission_details",
        attributes: ["ready_for_conversion"],
        required: Object.keys(holdCondition).length > 0,
        where: Object.keys(holdCondition).length > 0 ? holdCondition : undefined,
      },
      {
        model: dbModels.trx_lmc_connection_mapping,
        as: "lmc_connection_mapping",
        attributes: ["lmc_connection_mapping_id"],
        required: true,
        where: { lmc_id: lmc_id},
        include: [
          {
            model: dbModels.mst_admin,
            as: "lmc",
            attributes: ["admin_id"],
            required: true,
            include: [
              {
                model: dbModels.trx_admin_mapped_areas,
                as: "trx_admin_mapped_areas",
                attributes: ["area_id"],
                required: area_ids.length > 0,
                where: areaWhereCondition,
              },
            ],
          },
          {
            model: dbModels.trx_customer_connection,
            as: "customer_connection",
            required: true,
            attributes: [
              "customer_connection_id",
              "first_name",
              "last_name",
              "email",
              "mobile",
              "is_security_deposit",
              "bp_id",
            ],
            where: Object.keys(customerConnectionWhere).length ? customerConnectionWhere : undefined,
            include: [
              {
                model: dbModels.trx_customer_connection_address,
                as: "trx_customer_connection_addresses",
                attributes: [
                  "permanent_address",
                  "connection_address",
                  "connection_pincode",
                  "connection_city",
                  "connection_state",
                  "connection_district",
                ],
                required: false,
              },
            ],
          },
        ],
      },
      {
        model: dbModels.trx_lmc_pipeline_documents,
        as: "trx_lmc_pipeline_documents",
        attributes: ["file_path", "file_type", "verification_status"],
        where: { is_active: 1, file_type: "isometric-drawing" },
        required: false,
      },
    ],
    where: {
      ...whereCondition,
      ...searchCondition,
    },
    limit,
    offset,
    raw: false,
    nest: true,
  });
};


const getLmcStageDetails = async (mapped_lmc_connection_stage_id) => {
  try {
    const caseItem = await dbModels.trx_mapped_lmc_connection_stages.findOne({
      attributes: [
        "mapped_lmc_connection_stage_id",
        "lmc_stage",
        "visit_status",
        "tpi_status",
        "created_at",
        "visit_datetime",
        "reason"
      ],
      include: [
        {
          model: dbModels.trx_lmc_connection_mapping,
          as: "lmc_connection_mapping",
          attributes: [],
          include: [
            {
              model: dbModels.trx_customer_connection,
              as: "customer_connection",
              attributes: [
                "customer_connection_id",
                "first_name",
                "last_name",
                "email",
                "mobile",
                "bp_id",
              ],
              include: [
                {
                  model: dbModels.trx_customer_meter,
                  as: "trx_customer_meters",
                  attributes: ["meter_type", "meter_no"],
                  required: false,
                  include: [
                    {
                      model: dbModels.trx_customer_meter_readings,
                      as: "trx_customer_meter_readings",
                      attributes: ["meter_reading_file"],
                      required: false,
                    },
                  ],
                },
                {
                  model: dbModels.trx_customer_connection_address,
                  as: "trx_customer_connection_addresses",
                  attributes: [
                    "connection_address",
                    "connection_city",
                    "connection_state",
                    "connection_district",
                    "connection_pincode",
                  ],
                  required: false, // Optional address data
                },
              ],
            },
          ],
        },
        {
          model: dbModels.trx_lmc_pipeline_documents,
          as: "trx_lmc_pipeline_documents",
          attributes: [
            "lmc_pipeline_document_id",
            "file_type",
            "file_name",
            "file_path",
            "file_status",
            "verification_status",
            "is_active",
          ],
          where: {
            file_type: "isometric-drawing",
            is_active: 1,
            is_latest: 1,
          },
          required: false,
        },
        {
          model: dbModels.trx_mapped_lmc_feasibility_commission_details,
          as: "trx_mapped_lmc_feasibility_commission_details",
          attributes: ["gi_pipeline_length"],
          required: false,
        },
      ],
      where: { mapped_lmc_connection_stage_id, is_active: 1 },
      raw: true,
    });

    if (!caseItem) {
      return { case: null };
    }
    const address =   caseItem[
              "lmc_connection_mapping.customer_connection.trx_customer_connection_addresses.connection_address"
            ] || "N/A";
    return {
      case: {
        mapped_lmc_connection_stage_id: caseItem.mapped_lmc_connection_stage_id,
        customer_connection_id:
          caseItem[
            "lmc_connection_mapping.customer_connection.customer_connection_id"
          ] || "N/A",
        bp_id:
          caseItem[
            "lmc_connection_mapping.customer_connection.bp_id"
          ] || "N/A",
        customer: {
          name: `${caseItem["lmc_connection_mapping.customer_connection.first_name"] || ""} ${caseItem["lmc_connection_mapping.customer_connection.last_name"] || ""}`.trim(),
          email:
            caseItem["lmc_connection_mapping.customer_connection.email"] ||
            "N/A",
          phone:
            caseItem["lmc_connection_mapping.customer_connection.mobile"] ||
            "N/A",
        },
        address: address,
        lmc_stage: caseItem.lmc_stage,
        visit_status: caseItem.visit_status || "Unknown",
        visit_datetime: caseItem.visit_datetime || "N/A",
        tpi_status: caseItem.tpi_status || "Not submitted",
        remark: caseItem.reason || "Not submitted",
        pipeline_document: {
          file_id:
            caseItem["trx_lmc_pipeline_documents.lmc_pipeline_document_id"] ||
            "N/A",
          file_name: caseItem["trx_lmc_pipeline_documents.file_name"] || "N/A",
          file_type: caseItem["trx_lmc_pipeline_documents.file_type"] || "N/A",
          file_path:
            caseItem["trx_lmc_pipeline_documents.file_path"] ||
            caseItem[
              "lmc_connection_mapping.customer_connection.trx_customer_meters.trx_customer_meter_readings.meter_reading_file"
            ] ||
            "N/A",
          file_status:
            caseItem["trx_lmc_pipeline_documents.file_status"] || "N/A",
          verification_status:
            caseItem["trx_lmc_pipeline_documents.verification_status"] || "N/A",
          is_active: caseItem["trx_lmc_pipeline_documents.is_active"] || false,
            pipeline_length:
            caseItem["trx_mapped_lmc_feasibility_commission_details.gi_pipeline_length"] ||
            "N/A",
        },
        meter_details: {
          meter_type:
            caseItem[
              "lmc_connection_mapping.customer_connection.trx_customer_meters.meter_type"
            ] || "N/A",
          meter_no:
            caseItem[
              "lmc_connection_mapping.customer_connection.trx_customer_meters.meter_no"
            ] || "N/A",
          meter_file_path:
            caseItem[
              "lmc_connection_mapping.customer_connection.trx_customer_meters.trx_customer_meter_readings.meter_reading_file"
            ] || "N/A",
        },
        created_at: caseItem.created_at || "N/A",
      },
    };
  } catch (error) {
    console.error("Error fetching installation form data:", error);
    return { error: "Internal server error" };
  }
};

const saveLmcScheduleVisit = async (
  mapped_lmc_connection_stage_id,
  schedule_date
) => {
  try {
    // 1. Find the LMC case stage record
    const caseItem = await dbModels.trx_mapped_lmc_connection_stages.findOne({
      where: { mapped_lmc_connection_stage_id },
      include: [
        {
          model: dbModels.trx_lmc_connection_mapping,
          as: "lmc_connection_mapping",
          attributes: ["lmc_connection_mapping_id", "customer_connection_id"],
        },
      ],
    });

    if (!caseItem) {
      return { success: false, message: "No matching record found." };
    }

    const alreadyScheduled = caseItem.visit_status === "scheduled";

    // 2. Update visit status and schedule date
    caseItem.visit_status = "scheduled";
    caseItem.visit_datetime = schedule_date;

    const customer_connection_id = caseItem.lmc_connection_mapping?.customer_connection_id;

    console.log(customer_connection_id,"customer_connection_id")

    // Save the updated case item
    await caseItem.save();

    // 3. Determine customer status based on LMC stage
    let customerStatus;
    const stage = caseItem.lmc_stage;

    if (["installation", "feasibility-check"].includes(stage)) {
      customerStatus = alreadyScheduled
        ? "onboarding-visit-rescheduled"
        : "onboarding-visit-scheduled";
    }

    if (["commissioning"].includes(stage)) {
      customerStatus = alreadyScheduled
        ? "installation-visit-rescheduled"
        : "installation-visit-scheduled";
    }

    // 4. Update customer connection status if applicable
    if (customerStatus && customer_connection_id) {
      await updateCustomerConnectionStatus(
        customer_connection_id,
        customerStatus,
        "contact-engineering-team-for-further-details",
      );
    }

    // 5. Format the date
    const formattedDate = new Date(schedule_date).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });

    return {
      success: true,
      message: `${stage} scheduled`,
      details: `You have successfully scheduled a visit on ${formattedDate} for customer with BP ID ${customer_connection_id}`,
    };
  } catch (error) {
    console.error("Error saving LMC schedule visit:", error);
    return {
      success: false,
      message: "Failed to schedule visit",
      error: error.message,
    };
  }
};


const savePipelineDrawing = async (payload) => {
  try {
    const {
      customer_connection_id,
      customer_consent_request,
      pipeline_size,
      file_name,
      file_path,
      file_ext,
      file_type,
      lmc_stage,
    } = payload;

    // Validate required fields
    const requiredFields = [
      "customer_connection_id",
      "file_name",
      "file_path",
      "file_type",
    ];
    if (!requiredFields.every((field) => payload[field])) {
      return {
        success: false,
        message: `Missing required fields: ${requiredFields.join(", ")}`,
      };
    }

    // Fetch existing case record
    const caseItem = await dbModels.trx_mapped_lmc_customer_connection.findOne({
      where: { customer_connection_id, lmc_stage },
      raw: false,
      nest: true,
    });

    if (!caseItem) {
      return {
        success: false,
        message:
          "No matching record found for the provided customer_connection_id.",
      };
    }
    // Validate LMC stage and scheduled visit date
    const today = new Date().toLocaleDateString("en-CA", {
      timeZone: "Asia/Kolkata",
    }); // YYYY-MM-DD in IST
    const scheduledDate = new Date(caseItem.visit_datetime).toLocaleDateString(
      "en-CA"
    );

    if (caseItem.lmc_stage !== lmc_stage) {
      return {
        success: false,
        message: `Submission not allowed. Expected LMC stage: ${caseItem.lmc_stage}, but received: ${lmc_stage}.`,
      };
    }

    if (scheduledDate !== today) {
      return {
        success: false,
        message: `Submission only allowed on the scheduled visit date (${scheduledDate}).`,
      };
    }

    // Check if pipeline record exists
    let pipelineRecord = await dbModels.trx_lmc_pipeline.findOne({
      where: {
        mapped_lmc_customer_connection_id:
          caseItem.mapped_lmc_customer_connection_id,
      },
    });

    if (!pipelineRecord) {
      // Create new pipeline record
      pipelineRecord = await dbModels.trx_lmc_pipeline.create({
        mapped_lmc_customer_connection_id:
          caseItem.mapped_lmc_customer_connection_id,
        house_type: null,
        pipeline_size: pipeline_size || 0,
        extra_charge: 0,
        customer_consent_request: Boolean(customer_consent_request) ? 1 : 0,
        extra_charge_approval: 0,
        pipeline_status: "pending",
        is_active: 1,
        created_by: 0,
        updated_by: 0,
      });
    } else {
      // Update existing pipeline record
      await pipelineRecord.update({
        pipeline_size: pipeline_size || pipelineRecord.pipeline_size,
        customer_consent_request: Boolean(customer_consent_request)
          ? 1
          : pipelineRecord.customer_consent_request,
        updated_by: 0,
      });
    }

    // Check if file record exists
    let fileRecord = await dbModels.trx_lmc_pipeline_files.findOne({
      where: {
        mapped_lmc_customer_connection_id:
          caseItem.mapped_lmc_customer_connection_id,
      },
    });

    if (!fileRecord) {
      // Create new file record
      fileRecord = await dbModels.trx_lmc_pipeline_files.create({
        lmc_pipeline_id: pipelineRecord.lmc_pipeline_id,
        mapped_lmc_customer_connection_id:
          caseItem.mapped_lmc_customer_connection_id,
        file_type,
        file_name,
        file_path,
        file_ext,
        file_status: "pending",
        verification_status: "pending",
        is_active: 1,
        created_by: 0,
        updated_by: 0,
      });
    } else {
      // Update existing file record
      await fileRecord.update({
        file_type,
        file_name,
        file_path,
        file_ext,
        updated_by: 0,
      });
    }

    // Update case item visit status
    caseItem.visit_status = "completed";
    caseItem.tpi_status = "pending";
    await caseItem.save();
    if (lmc_stage === "installation") {
      await updateCustomerConnectionStatus(
        customer_connection_id,
        "installation-inprogress"
      );
    }

    return {
      success: true,
      message: `Pipeline details ${pipelineRecord.isNewRecord ? "created" : "updated"} and files saved successfully.`,
      pipeline_id: pipelineRecord.lmc_pipeline_id,
      file_id: fileRecord.lmc_pipeline_file_id,
    };
  } catch (error) {
    console.error("Error in savePipelineDrawing:", {
      error: error.message,
      payload,
    });
    return {
      success: false,
      message: "Internal server error",
      error: error.message,
    };
  }
};

const saveLmcJMR = async (payload, file_path, meter_reading_file, lmc_id) => {
  try {
    const { customer_connection_id, meter_no, meter_type, is_manual, meter_reading, mapped_lmc_connection_stage_id } = payload;

    // Check for duplicate meter number
    const existingMeterNo = await dbModels.trx_customer_meter.findOne({
      where: {
        meter_no,
        customer_connection_id: { [Op.ne]: customer_connection_id },
      },
    });
    if (existingMeterNo) {
      return { error: "Meter number already exists. Please use a unique meter number." };
    }

    // Validate customer connection
    const customerConnection = await dbModels.trx_customer_connection.findByPk(customer_connection_id);
    if (!customerConnection) {
      return { error: "Customer connection ID not found." };
    }

    const accountType = meter_type === "smart" ? "prepaid" : "postpaid";

    await dbModels.trx_customer_connection.update(
      { account_type: accountType },
      { where: { customer_connection_id } }
    );

    // Upsert meter
    let meter = await dbModels.trx_customer_meter.findOne({
      where: { customer_connection_id, meter_no },
    });

    const meterData = {
      meter_type: meter_type || 0,
      meter_installation_datetime: new Date(),
      meter_status: is_manual ? "pending" : "completed",
      installed_by: lmc_id,
      is_active: 1,
      jmr_approval_file: file_path,
    };

    if (!meter) {
      meter = await dbModels.trx_customer_meter.create({
        ...meterData,
        customer_connection_id,
        meter_no,
        created_by: lmc_id || 0,
        created_at: new Date(),
      });
    } else {
      await meter.update({
        ...meterData,
        updated_at: new Date(),
      });
    }

    // Create or update meter reading
    const readingData = {
      customer_meter_id: meter.customer_meter_id,
      previous_reading: 0,
      current_reading: meter_reading || 0,
      reading_units: meter_reading || 0,
      meter_reading_file: meter_reading_file || file_path,
      meter_reading_status: "completed",
      declared_by: "agent",
      submitted_by: lmc_id,
      is_manual,
      updated_at: new Date(),
    };

    const [reading, createdReading] = await dbModels.trx_customer_meter_readings.findOrCreate({
      where: { customer_connection_id, cycle_id: null },
      defaults: {
        ...readingData,
        customer_connection_id,
        created_at: new Date(),
        cycle_id: null,
      },
    });

    if (!createdReading) {
      await dbModels.trx_customer_meter_readings.update(readingData, {
        where: { customer_meter_reading_id: reading.customer_meter_reading_id },
      });
    }

    const currentReadingId = reading.customer_meter_reading_id;

    // Handle manual approval
    if (is_manual) {
      const lastApproval = await dbModels.trx_tpi_manual_meter_approval.findOne({
        attributes: ['customer_meter_reading_id'],
        include: [{
          model: dbModels.trx_customer_meter_readings,
          as: 'customer_meter_reading',
          attributes: [],
          where: { customer_connection_id },
        }],
        order: [['created_at', 'DESC']],
      });

      if (lastApproval?.customer_meter_reading_id) {
        await dbModels.trx_tpi_manual_meter_approval.update(
          {
            is_latest: false,
            is_active: false,
            updated_at: new Date(),
          },
          {
            where: { customer_meter_reading_id: lastApproval.customer_meter_reading_id },
          }
        );
      }

      await dbModels.trx_tpi_manual_meter_approval.create({
        customer_meter_reading_id: currentReadingId,
        meter_reading_file: meter_reading_file || file_path,
        meter_reading,
        tpi_id: lmc_id,
        tpi_status: "pending",
        is_latest: 1,
        is_active: 1,
        created_at: new Date(),
        created_by: lmc_id,
      });

      await dbModels.trx_mapped_lmc_connection_stages.update(
        { visit_status: "completed", tpi_status: "pending" },
        { where: { mapped_lmc_connection_stage_id } }
      );
    } else {
      await dbModels.trx_mapped_lmc_connection_stages.update(
        { visit_status: "completed", tpi_status: "completed" },
        { where: { mapped_lmc_connection_stage_id } }
      );
    }

    const appStatus = is_manual ? "installation-inprogress" : "active-connection";
    const appReason = is_manual ? "activation-in-progress" : null;

    await updateCustomerConnectionStatus(customer_connection_id, appStatus, appReason);

    return { success: true, meter };
  } catch (error) {
    console.error("Error saving LMC JMR:", error);
    return { error: "Internal server error", details: error.message };
  }
};



const getLMCMeterReadingDetails = async (params) => {
  try {
    const whereCondition = {
      customer_connection_id: params.customer_connection_id, // Mandatory condition
    };

    const result = await dbModels.trx_customer_meter_readings.findOne({
      attributes: [
        "customer_connection_id",
        "reading_units",
        "meter_reading_status",
        "declared_by",
        "created_at",
        "meter_reading_file",
      ],
      include: [
        {
          model: dbModels.trx_customer_meter,
          as: "customer_meter",
          attributes: [
            "meter_type",
            "meter_no",
            "meter_status",
            "meter_installation_datetime",
          ],
        },
      ],
      where: whereCondition,
      order: [["created_at", "ASC"]],
    });
    return result;
  } catch (error) {
    console.error("Error fetching meter reading details:", error);
    throw error; // Re-throw error to be handled by the caller
  }
};

const updateCustomerConnectionStatus = async (
  customer_connection_id,
  newStatus,
  masterValue
) => {
  const transaction = await sequelize.transaction();

  try {
    console.log("customer_connection_id:", customer_connection_id);
    console.log("newStatus:", newStatus);

    if (!customer_connection_id || !newStatus) {
      return {
        success: false,
        message: "Missing customer_connection_id or status.",
      };
    }

    let reason = null;

    // 1. Check for reason in mst_masters if masterValue is provided
    if (masterValue) {
      reason = await dbModels.mst_masters.findOne({
        where: { master_value: masterValue },
        transaction,
      });
    }

    const updateData = { status: newStatus };
    if (reason) {
      updateData.reason_id = reason.master_id;
    }

    const [updatedRows] = await dbModels.trx_customer_connection.update(
      updateData,
      {
        where: { customer_connection_id },
        transaction,
      }
    );

    console.log("Updated rows:", updatedRows);

    if (updatedRows === 0) {
      await transaction.rollback();
      return {
        success: false,
        message: "No matching customer found or status unchanged.",
      };
    }

    await transaction.commit();
    return {
      success: true,
      message: "Status updated successfully.",
    };

  } catch (error) {
    await transaction.rollback();
    console.error("Error updating customer connection status:", error);
    return {
      success: false,
      message: "Failed to update status.",
      error: error.message,
    };
  }
};


// Repository function to fetch customer details by ID
const getCustomerDetailsById = async (customer_connection_id) => {
  return await dbModels.trx_customer_connection.findOne({
    where: { customer_connection_id },
    attributes: ["customer_connection_id", "first_name", "last_name", "bp_id"],
    include: [
      {
        model: dbModels.trx_customer_connection_address,
        as: "trx_customer_connection_addresses",
        attributes: [
          "connection_address",
          "connection_city",
          "connection_state",
          "connection_district",
          "connection_pincode",
        ],
        required: false, // Optional address data
      },
      {
        model: dbModels.trx_lmc_connection_mapping,
        as: "trx_lmc_connection_mappings",
        attributes: ["lmc_id"],
        required: false, // Optional LMC connection
        include: [
          {
            model: dbModels.trx_mapped_lmc_connection_stages,
            as: "trx_mapped_lmc_connection_stages",
            attributes: ["form_no", "service_order_no", "service_request_no"],
            required: false, // Optional stages data
          },
        ],
      },
    ],
  });
};

const submitFeasibilityForm = async (
  feasibilityData,
  documentFiles,
  lmc_id
) => {
  try {
    // Validate existence of the LMC stage
    const stageExists = await dbModels.trx_mapped_lmc_connection_stages.findOne(
      {
        where: {
          mapped_lmc_connection_stage_id:
            feasibilityData.mapped_lmc_connection_stage_id,
        },
      }
    );

    if (!stageExists) {
      throw new Error(
        "Mapped LMC Connection Stage ID does not exist. Cannot submit feasibility form."
      );
    }

    // Update the connection stage status
    await dbModels.trx_mapped_lmc_connection_stages.update(
      {
        actual_visit_date: feasibilityData.actual_visit_date,
        visit_status: "completed",
        tpi_status: "pending",
      },
      {
        where: {
          mapped_lmc_connection_stage_id:
            feasibilityData.mapped_lmc_connection_stage_id,
        },
      }
    );

    // Check if feasibility data exists and either update or create
    const existingFeasibility =
      await dbModels.trx_mapped_lmc_feasibility_commission_details.findOne({
        where: {
          mapped_lmc_connection_stage_id:
            feasibilityData.mapped_lmc_connection_stage_id,
        },
      });

    if (existingFeasibility) {
      await dbModels.trx_mapped_lmc_feasibility_commission_details.update(
        {
          ...feasibilityData,
          updated_by: lmc_id,
        },
        {
          where: {
            mapped_lmc_connection_stage_id:
              feasibilityData.mapped_lmc_connection_stage_id,
          },
        }
      );
    } else {
      await dbModels.trx_mapped_lmc_feasibility_commission_details.create({
        ...feasibilityData,
        created_by: lmc_id,
      });
    }

    // Handle document uploads
    for (const doc of documentFiles) {
      try {
        // Check if document already exists with the same mapped_lmc_connection_stage_id and file_type
        const existingDoc = await dbModels.trx_lmc_pipeline_documents.findOne({
          where: {
            mapped_lmc_connection_stage_id:
              feasibilityData.mapped_lmc_connection_stage_id,
            file_type: doc.file_type,
          },
        });

        if (existingDoc) {
          // Update existing document
          await dbModels.trx_lmc_pipeline_documents.update(
            {
              file_name: doc.file_name,
              file_path: doc.file_path,
              file_ext: doc.file_ext,
              file_status: "completed", // or other status based on your logic
              verification_status: "pending", // or other status based on your logic
            },
            {
              where: {
                mapped_lmc_connection_stage_id:
                  feasibilityData.mapped_lmc_connection_stage_id,
                file_type: doc.file_type,
              },
            }
          );
          console.log(`Document ${doc.file_name} updated successfully.`);
        } else {
          // Create new document if not existing
          await dbModels.trx_lmc_pipeline_documents.create({
            mapped_lmc_connection_stage_id:
              feasibilityData.mapped_lmc_connection_stage_id,
            file_type: doc.file_type,
            file_name: doc.file_name,
            file_path: doc.file_path,
            file_ext: doc.file_ext,
            file_status: "completed",
            verification_status: "pending",
            created_by: lmc_id,
          });
          console.log(`Document ${doc.file_name} uploaded successfully.`);
        }
      } catch (fileError) {
        console.error(
          `Error uploading or updating document ${doc.file_name}:`,
          fileError
        );
      }
    }

    await updateCustomerConnectionStatus(
      feasibilityData.customer_connection_id,
      "onboarding-inprogress"
    );

    return {
      success: true,
      message: "Feasibility form submitted successfully",
    };
  } catch (error) {
    console.error("Repository error in submitFeasibilityForm:", error);
    throw new Error(`Error submitting feasibility form: ${error.message}`);
  }
};

const getFeasibilityFormDetails = async (mapped_lmc_connection_stage_id) => {
  try {
    const feasibilityDetails =
      await dbModels.trx_mapped_lmc_feasibility_commission_details.findOne({
        where: { mapped_lmc_connection_stage_id },
        include: [
          {
            model: dbModels.trx_mapped_lmc_connection_stages,
            as: "mapped_lmc_connection_stage",
            attributes: ["visit_status", "actual_visit_date", "visit_datetime"],
            required: false,
            include: [
              {
                model: dbModels.trx_lmc_pipeline_documents,
                as: "trx_lmc_pipeline_documents",
                required: false,
                attributes: ["file_name", "file_path", "file_type"],
              },
            ],
          },
        ],
      });

    return feasibilityDetails;
  } catch (error) {
    console.error("Repository error in getFeasibilityFormDetails:", error);
    throw error;
  }
};

const submitCommissioningForm = async (
  feasibilityData,
  documentFiles,
  lmc_id,
  tpi_status
) => {
  const transaction = await sequelize.transaction();
  try {
    // Step 1: Validate the given stage (should be commissioning stage)
    const commissioningStage = await dbModels.trx_mapped_lmc_connection_stages.findOne({
      where: {
        mapped_lmc_connection_stage_id: feasibilityData.mapped_lmc_connection_stage_id,
      },
      transaction,
    });

    if (!commissioningStage) {
      throw new Error("Mapped LMC Connection Stage ID does not exist. Cannot submit commissioning form.");
    }

    // Step 2: Mark commissioning stage as completed
    await dbModels.trx_mapped_lmc_connection_stages.update(
      {
        actual_visit_date: feasibilityData.actual_visit_date,
        visit_status: "completed",
        tpi_status,
      },
      {
        where: {
          mapped_lmc_connection_stage_id: feasibilityData.mapped_lmc_connection_stage_id,
        },
        transaction,
      }
    );

    const feasibilityStage = await dbModels.trx_mapped_lmc_connection_stages.findOne({
      where: {
        lmc_connection_mapping_id: commissioningStage.lmc_connection_mapping_id,
        lmc_stage: "feasibility-check",
      },
      raw:true,
      transaction,
    });
    if (!feasibilityStage) {
      await transaction.rollback();
      return {
        success: false,
        message: "Associated feasibility stage not found for this connection.",
      };
    }

    const existingfeasibilityData = await dbModels.trx_mapped_lmc_feasibility_commission_details.findOne({
      where: {
        mapped_lmc_connection_stage_id: feasibilityStage.mapped_lmc_connection_stage_id,
      },
      raw:true,
      transaction,
    });

    feasibilityData.house_type = existingfeasibilityData.house_type
    feasibilityData.mdpe_pipeline_length = existingfeasibilityData.mdpe_pipeline_length
    feasibilityData.riser_length = existingfeasibilityData.riser_length
    feasibilityData.gi_pipeline_length = existingfeasibilityData.gi_pipeline_length

    // Step 3: Create or update feasibility/commissioning details
    const existingCommissioning = await dbModels.trx_mapped_lmc_feasibility_commission_details.findOne({
      where: {
        mapped_lmc_connection_stage_id: feasibilityData.mapped_lmc_connection_stage_id,
      },
      transaction,
    });

    if (existingCommissioning) {
      await dbModels.trx_mapped_lmc_feasibility_commission_details.update(
        {
          ...feasibilityData,
          updated_by: lmc_id,
        },
        {
          where: {
            mapped_lmc_connection_stage_id: feasibilityData.mapped_lmc_connection_stage_id,
          },
          transaction,
        }
      );
    } else {
      await dbModels.trx_mapped_lmc_feasibility_commission_details.create(
        {
          ...feasibilityData,
          created_by: lmc_id,
        },
        { transaction }
      );
    }

    // Step 4: Upload commissioning documents
    for (const doc of documentFiles) {
      await dbModels.trx_lmc_pipeline_documents.create(
        {
          mapped_lmc_connection_stage_id: commissioningStage.mapped_lmc_connection_stage_id,
          file_type: doc.file_type,
          file_name: doc.file_name,
          file_path: doc.file_path,
          file_ext: doc.file_ext,
          file_status: "completed",
          verification_status: "pending",
          created_by: lmc_id,
        },
        { transaction }
      );
    }

    // Step 5: Commit transaction
    await transaction.commit();

    return {
      success: true,
      message: "Commissioning form submitted successfully",
    };

  } catch (error) {
    await transaction.rollback();
    console.error("Repository error in submitCommissioningForm:", error);
    throw new Error(`Error submitting commissioning form: ${error.message}`);
  }
};


const getCommissioningFormDetails = async (mapped_lmc_connection_stage_id) => {
  try {
    const feasibilityDetails =
      await dbModels.trx_mapped_lmc_feasibility_commission_details.findOne({
        where: { mapped_lmc_connection_stage_id },
        attributes: [
          "tmlfcd_id",
          "mapped_lmc_connection_stage_id",
          "house_type",
          "mdpe_pipeline_length",
          "riser_length",
          "gi_pipeline_length",
          "meter_type",
          "ready_for_conversion",
          "created_at",
        ],
        include: [
          {
            model: dbModels.trx_mapped_lmc_connection_stages,
            as: "mapped_lmc_connection_stage",
            attributes: ["visit_status", "actual_visit_date", "visit_datetime"],
            required: false,
            include: [
              {
                model: dbModels.trx_lmc_pipeline_documents,
                as: "trx_lmc_pipeline_documents",
                attributes: ["file_name", "file_path", "file_type"],
                required: false,
              },
              {
                model: dbModels.trx_lmc_connection_mapping,
                as: "lmc_connection_mapping",
                attributes: ["customer_connection_id"],
              },
            ],
          },
        ],
      });

    if (!feasibilityDetails) {
      return null;
    }

    const feasibilityData = feasibilityDetails.toJSON();

    console.log(
      feasibilityData.mapped_lmc_connection_stage?.lmc_connection_mapping
        ?.customer_connection_id
    );

    // Now get Meter Reading
    const meterReading = await dbModels.trx_customer_meter_readings.findOne({
      where: {
        customer_connection_id:
          feasibilityData.mapped_lmc_connection_stage?.lmc_connection_mapping
            ?.customer_connection_id,
        declared_by: ["agent"],
      },
      attributes: ["current_reading", "reading_units", "created_at"],
      include: [
        {
          model: dbModels.trx_customer_meter,
          as: "customer_meter",
          attributes: ["meter_no", "meter_type"],
        },
      ],
      order: [["created_at", "DESC"]],
    });

    feasibilityData.meter_reading = meterReading ? meterReading.toJSON() : null;

    return feasibilityData;
  } catch (error) {
    console.error("Repository error in getCommissioningFormDetails:", error);
    throw error;
  }
};

const getCustomerMeterRetails = async (customer_connection_id, meter_no) => {
  try {
    // Find meter details matching connection ID and meter number
    const meterDetails = await dbModels.trx_customer_meter.findOne({
      where: {
        customer_connection_id: customer_connection_id,
        meter_no: meter_no,
        is_active: 1,
      },
      attributes: [
        "customer_meter_id",
        "meter_type",
        "meter_no",
        "meter_status",
        "meter_installation_datetime",
        "is_active",
      ],
      raw: true,
    });

    // If no matching meter found, return null
    if (!meterDetails) {
      return null;
    }

    return meterDetails;
  } catch (error) {
    console.error("Error in getCustomerMeterRetails:", error);
    return null;
  }
};

const submitInstallationForm = async (
  installationData,
  documentFiles,
  lmc_id
) => {
  const transaction = await sequelize.transaction();
  try {
    const stageExists = await dbModels.trx_mapped_lmc_connection_stages.findOne({
      where: {
        mapped_lmc_connection_stage_id: installationData.mapped_lmc_connection_stage_id,
      },
      transaction,
    });

    if (!stageExists) {
      throw new Error("Mapped LMC Connection Stage ID does not exist. Cannot submit installation form.");
    }

    await dbModels.trx_mapped_lmc_connection_stages.update(
      {
        actual_visit_date: installationData.actual_visit_date || null,
        visit_status: "completed",
        tpi_status: "pending",
      },
      {
        where: {
          mapped_lmc_connection_stage_id: installationData.mapped_lmc_connection_stage_id,
        },
        transaction,
      }
    );

    const existingInstallation = await dbModels.trx_mapped_lmc_installation_details.findOne({
      where: {
        mapped_lmc_connection_stage_id: installationData.mapped_lmc_connection_stage_id,
      },
      transaction,
    });

    const installationPayload = {
      mapped_lmc_connection_stage_id: installationData.mapped_lmc_connection_stage_id,
      installation_date: installationData.installation_date,
      testing_date: installationData.testing_date,
      rfc_date: installationData.rfc_date,
      regulator_no: installationData.regulator_no,
      gi_length: installationData.gi_length || 0,
      gi_length_for_tapping: installationData.gi_length_tapping || 0,
      copper_length: installationData.copper_length || 0,
      appliance_valve: installationData.appliance_valve_half_inch || 0,
      isolation_ball_valve: installationData.isolation_ball_valve || 0,
      brass_fitting_set: installationData.brass_fitting_set || 0,
      gi_plug: installationData.gi_plug_half_inch || 0,
      half_two_nipple: installationData.half_inch_2_inch_nipple || 0,
      half_three_nipple: installationData.half_inch_3_inch_nipple || 0,
      half_four_nipple: installationData.half_inch_4_inch_nipple || 0,
      elbow: installationData.elbow || 0,
      equal_tee: installationData.equal_tee || 0,
      socket: installationData.socket || 0,
      union_value: installationData.union || 0,
      anaconda: installationData.anaconda || 0,
      elbow_mf: installationData.elbow_mf || 0,
      checklist: installationData.checklist,
      created_by: lmc_id,
    };

    if (existingInstallation) {
      await dbModels.trx_mapped_lmc_installation_details.update(
        {
          ...installationPayload,
          updated_by: lmc_id,
          updated_at: new Date(),
        },
        {
          where: {
            mapped_lmc_connection_stage_id: installationData.mapped_lmc_connection_stage_id,
          },
          transaction,
        }
      );
    } else {
      await dbModels.trx_mapped_lmc_installation_details.create(installationPayload, { transaction });
    }

    // const feasibilityPayload = {
    //   mapped_lmc_connection_stage_id: installationData.mapped_lmc_connection_stage_id,
    //   house_type: installationData.house_type,
    //   mdpe_pipeline_length: installationData.mdpe_pipeline_length || 0,
    //   riser_length: installationData.riser_length || 0,
    //   gi_pipeline_length: installationData.gi_pipeline_length || 0,
    //   meter_type: installationData.meter_type || "mechanical",
    //   created_by: lmc_id,
    // };

    // await dbModels.trx_mapped_lmc_feasibility_commission_details.update(
    //   { is_latest: 0 },
    //   {
    //     where: {
    //       mapped_lmc_connection_stage_id: installationData.mapped_lmc_connection_stage_id,
    //       is_latest: 1,
    //     },
    //     transaction,
    //   }
    // );

    // await dbModels.trx_mapped_lmc_feasibility_commission_details.create(feasibilityPayload, { transaction });

    for (const doc of documentFiles) {
      const existingDoc = await dbModels.trx_lmc_pipeline_documents.findOne({
        where: {
          mapped_lmc_connection_stage_id: installationData.mapped_lmc_connection_stage_id,
          file_type: doc.file_type,
        },
        transaction,
      });

      const documentPayload = {
        file_name: doc.file_name,
        file_path: doc.file_path,
        file_ext: doc.file_ext,
        file_status: "completed",
        verification_status: "pending",
        created_by: lmc_id,
      };

      if (existingDoc) {
        await dbModels.trx_lmc_pipeline_documents.update(
          {
            ...documentPayload,
            updated_by: lmc_id,
          },
          {
            where: {
              mapped_lmc_connection_stage_id: installationData.mapped_lmc_connection_stage_id,
              file_type: doc.file_type,
            },
            transaction,
          }
        );
      } else {
        await dbModels.trx_lmc_pipeline_documents.create(
          {
            mapped_lmc_connection_stage_id: installationData.mapped_lmc_connection_stage_id,
            file_type: doc.file_type,
            ...documentPayload,
          },
          { transaction }
        );
      }
    }

    const meterWithSameNo = await dbModels.trx_customer_meter.findOne({
      where: { meter_no: installationData.meter_no },
      transaction,
    });

    const customerExistingMeter = await dbModels.trx_customer_meter.findOne({
      where: {
        customer_connection_id: installationData.mapped_lmc_customer_connection_id,
      },
      transaction,
    });

    if (
      meterWithSameNo &&
      meterWithSameNo.customer_connection_id !== installationData.mapped_lmc_customer_connection_id
    ) {
      throw new Error(`Meter number ${installationData.meter_no} is already assigned to another customer.`);
    }

    const meterPayload = {
      customer_connection_id: installationData.mapped_lmc_customer_connection_id,
      meter_no: installationData.meter_no,
      meter_installation_datetime: new Date(),
      installed_by: lmc_id,
      is_active: 0,
      meter_type: "mechanical",
      meter_status: "pending",
    };

    if (customerExistingMeter) {
      await dbModels.trx_customer_meter.update(
        {
          ...meterPayload,
          updated_by: lmc_id,
          updated_at: new Date(),
        },
        {
          where: {
            customer_connection_id: installationData.mapped_lmc_customer_connection_id,
          },
          limit: 1,
          transaction,
        }
      );
    } else {
      await dbModels.trx_customer_meter.create(
        {
          ...meterPayload,
          created_by: lmc_id,
          created_at: new Date(),
        },
        { transaction }
      );
    }

    await transaction.commit();
    await updateCustomerConnectionStatus(
      installationData.mapped_lmc_customer_connection_id,
       "onboarding-inprogress"
    );
    return {
      success: true,
      message: "Installation form submitted successfully",
    };
  } catch (error) {
    await transaction.rollback();
    console.error("Repository error in submitInstallationForm:", error);
    throw new Error(`Error submitting installation form: ${error.message}`);
  }
};

const getPipelineChecklist = async () => {
  try {
    const checklistDetails = await dbModels.mst_masters.findAll({
      where: { master_type: "pipeline-checklist" },
      attributes: ["master_id", "master_type", "master_name", "master_value"],
      raw: true,
    });

    if (!checklistDetails || checklistDetails.length === 0) {
      return null;
    }

    return checklistDetails;
  } catch (error) {
    console.error("Repository error in getPipelineChecklistDetails:", error);
    throw error;
  }
};
const getStageDetails = async (mapped_lmc_connection_stage_id) => {
  return dbModels.trx_mapped_lmc_connection_stages.findOne({
    where: { mapped_lmc_connection_stage_id },
    raw: true,
    include: [
      {
        model: dbModels.trx_lmc_connection_mapping,
        as: "lmc_connection_mapping",
        attributes: ["customer_connection_id"],
      },
    ],
  });
};

const getInstallationDetails = async (mapped_lmc_connection_stage_id) => {
  return dbModels.trx_mapped_lmc_installation_details.findOne({
    where: { mapped_lmc_connection_stage_id },
    raw: true,
  });
};

const getUploadedDocuments = async (mapped_lmc_connection_stage_id) => {
  return dbModels.trx_lmc_pipeline_documents.findAll({
    where: { mapped_lmc_connection_stage_id },
    raw: true,
  });
};

const getLatestFeasibilityCommissioning = async (
  mapped_lmc_connection_stage_id
) => {
  return dbModels.trx_mapped_lmc_feasibility_commission_details.findOne({
    where: {
      mapped_lmc_connection_stage_id,
      is_latest: 1,
    },
    order: [["created_at", "DESC"]],
    raw: true,
  });
};
const getMeterDetails = async (customer_connection_id) => {
  return await dbModels.trx_customer_meter.findOne({
    where: {
      customer_connection_id: customer_connection_id,
    },
  });
};

const getCustomerAddressDetails = async (customer_connection_id) => {
  return dbModels.trx_customer_connection_address.findOne({
    where: {
      customer_connection_id,
      connection_type: "original",
      is_active: true,
    },
    include: [
      {
        model: dbModels.mst_available_pngareas,
        as: "connection_address_pngarea",
        include: [
          {
            model: dbModels.mst_pincodes,
            as: "pincodeAreasAlias",
            include: [
              {
                model: dbModels.mst_areas,
                as: "area",
              },
            ],
          },
        ],
      },
    ],
    raw: true,
    nest: true,
  });
};

const getFeasibilityDeatailsforInstallation = async (customer_connection_id) => {
  try {
    const feasibilityDetails =
      await dbModels.trx_mapped_lmc_feasibility_commission_details.findOne({
        include: [
          {
            model: dbModels.trx_mapped_lmc_connection_stages,
            as: "mapped_lmc_connection_stage",
            attributes: ["visit_status", "actual_visit_date", "visit_datetime"],
            required: true,
            include: [
              {
                model: dbModels.trx_lmc_pipeline_documents,
                as: "trx_lmc_pipeline_documents",
                required: true,
                attributes: ["file_name", "file_path", "file_type"],
              },
              {
                model: dbModels.trx_lmc_connection_mapping,
                as: "lmc_connection_mapping",
                attributes: ["customer_connection_id"],
                where: {
                  customer_connection_id: customer_connection_id,
                },
                required: true,
              },
            ],
          },
        ],
      });
    return feasibilityDetails;
  } catch (error) {
    console.error("Repository error in getFeasibilityDeatailsforInstallation:", error);
    throw error;
  }
};


module.exports = {
  getPipelineDetails,
  updatePipelineDetails,
  updateMapCustomerConnectionDetails,
  getLmcCasesSummery,
  getLmcCases,
  getLmcTickets,
  getLmcStageDetails,
  saveLmcScheduleVisit,
  savePipelineDrawing,
  saveLmcJMR,
  getLMCMeterReadingDetails,
  getCustomerDetailsById,
  submitFeasibilityForm,
  getCommissioningFormDetails,
  submitCommissioningForm,
  getFeasibilityFormDetails,
  getCustomerMeterRetails,
  submitInstallationForm,
  getPipelineChecklist,
  getStageDetails,
  getInstallationDetails,
  getUploadedDocuments,
  getLatestFeasibilityCommissioning,
  getMeterDetails,
  getCustomerAddressDetails,
  getFeasibilityDeatailsforInstallation
};
