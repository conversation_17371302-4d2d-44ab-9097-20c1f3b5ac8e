const Joi = require("joi");
const {
  PINCODE_REGEX,
} = require("../constant/regex");
const {  PINCODE_INVALID } = require("../constant/messages");

const connectionTransferValidation = {
  body: Joi.object({
    existing_connection_id_connection_id: Joi.number().integer().required(),
    connection_transfer_reason: Joi.number().integer().required(),
    connection_transfer_date: Joi.string()
      //.pattern(/^\d{4}-\d{1,2}-\d{1,2}$/)
      .required(),
  }),
};
const extraPointsValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().required(),
    customer_connection_address_id: Joi.number().integer().required(),
    extra_point_type: Joi.alternatives().try(
      Joi.array().items(Joi.string()).required(),
      Joi.string().required()
    ),
  }),
};
// const altrationModificationValidation = {
//   body: Joi.object({
//     customer_connection_id: Joi.number().integer().required(),
//     customer_connection_address_id: Joi.number().integer().required(),
//     area_id: Joi.number().integer().required(),
//     description: Joi.string().allow('', null).optional()
//   }),
// };

const updateConnectionValidation = Joi.object({
  update_request_type: Joi.string()
    .valid("temporary-disconnection", "permanent-disconnection", "permanent-reconnection", "temporary-reconnection")
    .required(),

  // Common Fields
  customer_connection_id: Joi.number().integer().required(),
  customer_connection_address_id: Joi.number().integer().required(),
  request_type: Joi.string() .valid('disconnection','reconnection').required(),
  request_sub_type: Joi.string() .valid('temporary','permanent').required(),
  request_reason_id: Joi.number().integer().allow('', null).optional(),

  estimated_duration_id: Joi.when("update_request_type", {
    is: "temporary-disconnection",
    then: Joi.number().integer().required(),
    otherwise: Joi.forbidden(),
  }),

  disconnection_date: Joi.when("update_request_type", {
    is: "permanent-disconnection",
    then: Joi.date().required(),
    otherwise: Joi.forbidden(),
  }),

  engineer_visit_date: Joi.when("update_request_type", {
    is: "temporary-reconnection",
    then: Joi.date().required(),
    otherwise: Joi.forbidden(),
  }),
});
const permanentAddressSCHEMA = Joi.object({
  same_as_connetion_address: Joi.boolean().required(),
  permanent_pngareas_id: Joi.number().required(),
  permanent_pincode: Joi.string().regex(PINCODE_REGEX).required().messages({
    "string.pattern.base": PINCODE_INVALID,
  }),
  permanent_landmark: Joi.string().allow(null, ""),
  permanent_block_no: Joi.string().allow(null, ""),
  permanent_floor_no: Joi.string().allow(null, ""),
  permanent_building_house_name: Joi.string().required(),
  permanent_house_no: Joi.string().allow(null, ""),
  permanent_state: Joi.string().required(),
  permanent_city: Joi.string().required(),
  permanent_district: Joi.string().required(),
});

const uploadDocumentValidation = Joi.object({
  //bpcl_id: Joi.string().min(1).required(),

  available_pngareas_id: Joi.number().min(1).required(),
  house_flat_no: Joi.string().required(),
  proof_type: Joi.string()
    .required()
    .valid(
      "proof_of_ownership",
      "photo_proof",
      "category_of_security_deposit",
      "proof_of_security_deposit_waiver"
    ),
  proof_master_id: Joi.number().min(1).required(),
  customer_connection_id: Joi.number().min(1).allow("", null),
});

const getCustomerDetailValidation = Joi.object({
  customer_connection_id: Joi.number().min(1).required(),
  available_pngareas_id: Joi.number().required(),
  house_flat_no: Joi.string().required(),
});

const saveConnectionAddressValidation = Joi.object({
  customer_connection_id: Joi.number().integer().required(),
  customer_connection_address_id: Joi.number().integer().required(),
  reason_id: Joi.number().integer().allow('', null).optional(),
  address_request_type: Joi.string().required(),
  permanent_address:permanentAddressSCHEMA
});
const updateConnectionAddressValidation = Joi.object({
  address_request_type: Joi.string()
    .valid("permanent-address", "transfer-connection")
    .required(),

  // Common Fields
  customer_connection_id: Joi.number().integer().required(),
  address_request_type: Joi.string().required(),

  customer_connection_address_id: Joi.when("address_request_type", {
    is: "transfer-connection",
    then: Joi.number().integer().required(),
    otherwise: Joi.forbidden(),
  }),

  reason_id: Joi.when("address_request_type", {
    is: "transfer-connection",
    then: Joi.number().integer().allow('', null).optional(),
    otherwise: Joi.forbidden(),
  }),

  transfer_date: Joi.when("address_request_type", {
    is: "transfer-connection",
    then: Joi.date().required(),
    otherwise: Joi.forbidden(),
  }),

  available_pngareas_id: Joi.when("address_request_type", {
    is: "permanent-address",
    then: Joi.number().integer().required(),
    otherwise: Joi.forbidden(),
  }),

  house_flat_no: Joi.when("address_request_type", {
    is: "permanent-address",
    then: Joi.string().required(),
    otherwise: Joi.forbidden(),
  }),

});
const enableConnectionValidation = Joi.object({
  address_request_type: Joi.string()
    .valid("permanent-address", "transfer-connection")
    .required(),

  // Common Fields
  customer_connection_id: Joi.number().integer().required(),
  address_request_type: Joi.string().required(),
  customer_connection_address_id: Joi.number().integer().required(),
  available_pngareas_id: Joi.number().integer().required(),
  house_flat_no:  Joi.string().required(),

});
const nameTransferValidation = Joi.object({
  customer_connection_id: Joi.number().integer().required(),
  name_transfer_reason_id: Joi.number().integer().required(),
  title: Joi.string().valid('mr', 'mrs', 'ms', 'other').required(),
  first_name: Joi.string().required(),
  middle_name: Joi.string().allow('', null),
  last_name: Joi.string().required(),
  mobile: Joi.string().pattern(/^[0-9]{10}$/).required(),
  alt_mobile: Joi.string().pattern(/^[0-9]{10}$/).allow('', null),
  email: Joi.string().email().allow('', null) 
});

const nameTransferSaveDetailsValidation = Joi.object({
  customer_connection_id: Joi.number().integer().required(),
  connection_service_id: Joi.number().integer().required(),
});


const connectionAddressSCHEMA = Joi.object({
  connection_pngareas_id: Joi.number().required(),
  connection_pincode: Joi.string().regex(PINCODE_REGEX).required().messages({
    "string.pattern.base": PINCODE_INVALID,
  }),
  connection_landmark: Joi.string().allow(null, ""),
  connection_block_no: Joi.string().allow(null, ""),
  connection_floor_no: Joi.string().allow(null, ""),
  connection_house_no: Joi.string().allow(null, ""),
  connection_building_house_name: Joi.string().required(),
  connection_state: Joi.string().required(),
  connection_city: Joi.string().required(),
  connection_district: Joi.string().required(),
  same_as_connection: Joi.boolean().allow("", null),
  permanent_pngareas_id: Joi.number().required(),
  permanent_pincode: Joi.string().regex(PINCODE_REGEX).required().messages({
    "string.pattern.base": PINCODE_INVALID,
  }),
  permanent_landmark: Joi.string().allow(null, ""),
  permanent_block_no: Joi.string().allow(null, ""),
  permanent_floor_no: Joi.string().allow(null, ""),
  permanent_building_house_name: Joi.string().required(),
  permanent_house_no: Joi.string().allow(null, ""),
  permanent_state: Joi.string().required(),
  permanent_city: Joi.string().required(),
  permanent_district: Joi.string().required(),
  connection_type: Joi.string().required().valid("transfer-connection"),
});

const additionalInfoSCHEMA = Joi.object({
  connection_address_type: Joi.string()
    .required()
    .valid("owned", "rented", "leased", "company_owned"),
  extra_connection: Joi.string().required().valid("yes", "no"),
  extra_points: Joi.number().min(0).max(2).required(),
  govt_scheme_id: Joi.number().min(1).required(),
  security_deposit: Joi.string().allow("", null).optional().valid("true", "false"),
  security_deposit_category: Joi.string()
    .allow("", null).optional()
    .when("security_deposit", {
      is: "yes",
      then: Joi.required(),
    }),
  is_lpg: Joi.number().min(0).max(2).required(),
  lpg_consumer_no: Joi.string().when("is_lpg", {
    is: 1,
    then: Joi.string().required(),
    otherwise: Joi.forbidden(),
  }),
  lpg_distributor_name:Joi.string().when("is_lpg", {
    is: 1,
    then: Joi.string().required(),
    otherwise: Joi.forbidden(),
  }),
  lpg_omc_name: Joi.string().when("is_lpg", {
    is: 1,
    then: Joi.string().required(),
    otherwise: Joi.forbidden(),
  }),
});

const newAddressValidationAll = Joi.object({
  customer_connection_id: Joi.number().required(),
  old_available_pngareas_id: Joi.number().allow(null, ""),
  old_house_flat_no: Joi.string().allow(null, ""),
  available_pngareas_id: Joi.number().required(),
  house_flat_no: Joi.string().required(),
  step_no: Joi.number().valid(2, 3).required(),
  transfer_request_id: Joi.number().allow(null, ""),
  connection_address: Joi.alternatives().conditional("step_no", {
    is: 2,
    then: connectionAddressSCHEMA,
    otherwise: Joi.forbidden(),
  }),

  addition_info: Joi.alternatives().conditional("step_no", {
    is: 3,
    then: additionalInfoSCHEMA,
    otherwise: Joi.forbidden(),
  }),
});

const validateExtraPointPayload = Joi.object({
  customer_connection_id: Joi.number().integer().required(),
  customer_connection_address_id: Joi.number().integer().required(),
  extra_point_type: Joi.alternatives().try(
    Joi.array().items(Joi.string()).required(),
    Joi.string().required()
  ),
});


const transferApprovalStatusUpdateValidation = Joi.object({
  name_transfer_request_id: Joi.number().integer().required(),
  approval_status: Joi.number().integer().required(),
});


const altrationModificationValidation = Joi.object({
  customer_connection_id: Joi.number().integer().required(),
  customer_connection_address_id: Joi.number().integer().required(),
  area_id: Joi.number().integer().required(),
  description: Joi.string().allow('', null).optional()
});

module.exports = {
  connectionTransferValidation,
  extraPointsValidation,
  updateConnectionValidation,
  altrationModificationValidation,
  nameTransferValidation,
  saveConnectionAddressValidation,
  updateConnectionAddressValidation,
  uploadDocumentValidation,
  getCustomerDetailValidation,
  newAddressValidationAll,
  enableConnectionValidation,
  nameTransferSaveDetailsValidation,
  newAddressValidationAll,
  validateExtraPointPayload,
  transferApprovalStatusUpdateValidation
};