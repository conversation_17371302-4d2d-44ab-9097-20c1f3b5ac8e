const { Joi } = require('express-validation');
const { PHONE_NO_INVALID, PINCODE_INVALID } = require('../constant/messages');
const {
  MOBILE_REGEX,
  ALPHASPACE_REGEX,
  PINCODE_REGEX,
} = require('../constant/regex');

const currentBillValidation = {
  query: Joi.object({
    bp_id: Joi.string().required(),
    customer_meter_id: Joi.number().optional(),
  }).required(),
};

const paymentHistoryValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().required(),
    page: Joi.number().integer().min(1).allow(null, '').default(null).messages({
      'number.base': 'Page must be a number',
      'number.min': 'Page must be greater than 0',
    }),
    limit: Joi.number()
      .integer()
      .min(1)
      .allow(null, '')
      .default(null)
      .messages({
        'number.base': 'Limit must be a number',
        'number.min': 'Limit must be greater than 0',
      }),
    timeFilter: Joi.number().allow(null, "").optional(),
  }).required(),
};

const checkNumberValidation = {
  body: Joi.object({
    number: Joi.string()
      .regex(MOBILE_REGEX)
      .length(10)
      .messages({
        'string.pattern.base': 'Mobile number must be exactly 10 digits.',
        'string.length': 'Mobile number must be exactly 10 digits.',
      })
      .required(),
    bp_id: Joi.string()
      .alphanum()
      .length(12)
      .messages({
        'string.alphanum': 'BP ID must be alphanumeric.',
        'string.length': 'BP ID must be exactly 12 characters long.',
      })
      .required(),
  }),
};

const verifyOTPValidation = {
  body: Joi.object({
    number: Joi.string()
      .regex(MOBILE_REGEX)
      .length(10)
      .messages({
        'string.pattern.base': 'Mobile number must be exactly 10 digits.',
        'string.length': 'Mobile number must be exactly 10 digits.',
      })
      .required(),
    bp_id: Joi.string().required(),
    OTP: Joi.number().integer().min(100000).max(999999).required(),
  }).required(),
};

module.exports = {
  currentBillValidation,
  paymentHistoryValidation,
  checkNumberValidation,
  verifyOTPValidation,
};
