const express = require("express");
const path = require("path");
const { routes } = require("../routes");
const cors = require("cors");
const swaggerUi = require('swagger-ui-express');
const environment = require("../helpers/environmentHelper");
const swaggerDoc = require('../swagger/swagger.json');
async function createServer() {
  const app = express();
  app.use(express.json({ limit: "1mb" }));
  app.use(express.urlencoded({ extended: true, limit: "1mb" }));
  

  // Enable CORS for all routes
  const corsOptions = {
    origin: "http://localhost:3000",
    allowedHeaders: "*", // Corrected typo
    methods: ["GET", "POST", "PUT", "DELETE"],
    optionsSuccessStatus: 200, // For legacy browser support
  };
  app.use(cors(corsOptions));
  // Handle preflight requests
  app.options("*", cors(corsOptions));
  // app.use(cors());
  app.use('/public', express.static(path.join(__dirname, '../../public')));

  // Set up Swagger
  const { host, basePath } = environment.envSwaggerConfig();
  swaggerDoc["x-host"] = host;
  swaggerDoc["x-basePath"] = basePath;
  swaggerDoc.servers = [{ url: `${host}${basePath}` }];
  app.use("/swagger", swaggerUi.serve, swaggerUi.setup(swaggerDoc, { explorer: true })
  );
  app.use("/swagger", (req, res) => {
    res.json(swaggerDoc);
  });

  app.use("/", routes);

  // Handle invalid JSON error
  app.use((err, req, res, next) => {
    if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
      return res.status(400).json({
        message: 'Invalid JSON payload',
        details: err.message
     });
    }
    next(err);
  });

  return app;
}

module.exports = createServer;
