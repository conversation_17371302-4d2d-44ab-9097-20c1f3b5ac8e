const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_meter_reading_orders",
    {
      mro_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      mro_ticket: {
        type: DataTypes.STRING(10),
        allowNull: false,
        unique: "UK_mstmro_mro_ticket",
      },
      mro_creation_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      total_mru: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      scheduled_mr_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      remarks: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      mro_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      fo_supervisor_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_mstmro_fo_supervisor_id",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "mst_meter_reading_orders",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_mst_mro_id",
          unique: true,
          fields: [{ name: "mro_id" }],
        },
        {
          name: "UK_mstmro_fo_supervisor_id",
          unique: true,
          fields: [{ name: "mro_ticket" }, { name: "fo_supervisor_id" }],
        },
        {
          name: "UK_mstmro_mro_ticket",
          unique: true,
          fields: [{ name: "mro_ticket" }],
        },
      ],
    }
  );
};
