const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_customer_notifications",
    {
      notification_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
      },
      alert_type: {
        type: DataTypes.STRING(30),
        allowNull: true,
      },
      alert_category: {
        type: DataTypes.STRING(30),
        allowNull: true,
      },
      alert_request_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      subject: {
        type: DataTypes.STRING(200),
        allowNull: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_notify: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_customer_notifications",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_notification_id",
          unique: true,
          fields: [{ name: "notification_id" }],
        },
      ],
    }
  );
};
