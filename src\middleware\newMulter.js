const multer = require("multer");
const fs = require("fs");
const path = require("path");
const { sendError } = require("../helpers/responseHelper");
const SASTokenHelper = require("../helpers/azureSASSDKHelper");
const {
  PLEASE_UPLOAD_FILE,
  INVALID_FILE_FRMATE,
} = require("../constant/messages");
const { FILE_OPTIONS } = require("../../config");

const Validators = require("../validation");
const crypto = require("crypto");
const messages = require("../constant/messages");

// Define directory paths
const rootDir = path.resolve(__dirname, "../../"); // Root directory
const publicDir = path.join(rootDir, "public");
const uploadDir = path.join(publicDir, "uploads");
const customersDir = path.join(uploadDir, "customers");

// Ensure the required directories exist
// const ensureDirectoryExists = (dir) => {
//   if (!fs.existsSync(dir)) {
//     fs.mkdirSync(dir, { recursive: true }); // Create directory and parent folders if necessary
//   }
// };

// Ensure the base directories exist at server startup
// ensureDirectoryExists(customersDir);

const storage = multer.memoryStorage({
  destination: (req, file, cb) => {
    console.log(">>>>>insideMIddel", file);
    // const userId = req.bpclId || "defaultUser"; // Get user ID from request or use default
    // const userDir = path.join(customersDir, userId);

    // ensureDirectoryExists(userDir); // Ensure user-specific directory exists

    // cb(null, userDir); // Specify the directory to save files
  },
  // filename: (req, file, cb) => {
  //   const ext = path.extname(file.originalname);
  //   const bpcl_id = req.bpclId;
  //   const {
  //     proof_type,
  //     proof_master_id,
  //     customer_connection_id,
  //     declared_by,
  //     cycle_id,
  //   } = req.body;
  //   let fileName;
  //   const randomString = generateRandomString(6); // Generate a random 6-character string
  //   const timestamp = Date.now();
  //   // Check if additional fields exist
  //   if (customer_connection_id && declared_by && cycle_id) {
  //     fileName = `${bpcl_id}_${customer_connection_id}_${declared_by}_${cycle_id}${ext}`;
  //   } else if (customer_connection_id && proof_type && proof_master_id) {
  //     fileName = `${bpcl_id}_${proof_type}_${proof_master_id}${ext}`;
  //   } else {
  //     fileName = `bpclpng_${randomString}_${timestamp}${ext}`;
  //   }

  //   cb(null, fileName);
  // },
});

// Create multer instance with storage options
const upload = multer({
  storage: storage,
  limits: { fileSize: FILE_OPTIONS.MAX_SIZE * 1024 * 1024 }, // 5 MB limit
  fileFilter: (req, file, cb) => {
    checkJoiValidation(req.validateModule, req.validatorKey, req.body, cb);
    checkFileType(file, cb);
  },
});

function checkJoiValidation(module, validatorKey, requestBody, cb) {
  if (Validators[module] && Validators[module].hasOwnProperty(validatorKey)) {
    const { error, value } =
      Validators[module][validatorKey].validate(requestBody);
    console.log(error);
    //req.body = validated;
    if (error) {
      return cb({
        message:
          error && error.details && error.details[0] && error.details[0].message
            ? error.details[0].message
            : error.toString(),
      });
    }
  }
  return cb(null, true);
}

// Check file type
function checkFileType(file, cb) {
  // Allowed ext
  const filetypes = /pdf|jpg|jpeg|png/;
  // Check ext
  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  // Check mime
  const mimetype = filetypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb({ message: INVALID_FILE_FRMATE });
  }
}

// Middleware to handle single file upload
const generateRandomString = (length = 8) => {
  return crypto.randomBytes(length).toString("hex"); // Generate a random string
};

const uploadSingle = (req, res, next) => {
  upload.single("file")(req, res, async (err) => {
    if (err) {
      sendError(req, res, [], err.message);
      return;
    }
    console.log(">>>", req.file);

    if (!req.file) {
      sendError(req, res, [], PLEASE_UPLOAD_FILE);
      return;
    }
    next();
  });
};

const uploadSingleTemp = (req, res, next) => {
  upload.single("file")(req, res, async (err) => {
    console.log(">>>", req.file);
    if (err) {
      sendError(req, res, [], err.message);
      return;
    }
    console.log(">>>", req.file);

    if (!req.file) {
      sendError(req, res, [], PLEASE_UPLOAD_FILE);
      return;
    }

    const permission = "racwd";
    const sasToken = await SASTokenHelper.generateSASToken(permission);
    const imageUploaded = await SASTokenHelper.azureSingleFileUpload(
      req,
      {},
      sasToken,
      "temp"
    );
    console.log(">>>>>>>inside the muleter", imageUploaded);
    req.files = imageUploaded;
    // return imageUploaded;
    // req.file.path = `/public${req.file.path.replace(publicDir, "").replace(/\\/g, "/")}`;

    next();
  });
};
const uploadSingleTempNew = (module = null, validatorKey = null) => {
  return (req, res, next) => {
    req.validateModule = module;
    req.validatorKey = validatorKey;

    upload.single("file")(req, res, async (err) => {
      console.log("UPLOAD ERR", err);
      if (err) {
        sendError(req, res, [], err.message);
        return;
      }
      console.log(">>>", req.file);

      if (!req.file) {
        sendError(req, res, [], PLEASE_UPLOAD_FILE);
        return;
      }

      const permission = "racwd";
      const sasToken = await SASTokenHelper.generateSASToken(permission);
      const imageUploaded = await SASTokenHelper.azureSingleFileUpload(
        req,
        {},
        sasToken,
        "temp"
      );
      console.log(">>>>>>>inside the muleter", imageUploaded);
      req.files = imageUploaded;
      // return imageUploaded;
      // req.file.path = `/public${req.file.path.replace(publicDir, "").replace(/\\/g, "/")}`;

      next();
    });
  };
};
// Middleware to handle multiple file uploads (at least 2 files required)
// const uploadMultiple = (req, res, next) => {
//   console.log("nnnnnn", req.files);

//   upload.array("files", 5)(req, res, (err) => {
//     if (err) {
//       sendError(req, res, [], err.message);
//       return;
//     }
//     if (!req.files) {
//       sendError(
//         req,
//         res,
//         [],
//         "File upload limit exceeded. Please upload at least 1 files."
//       );
//       return;
//     }

//     req.files.forEach((file) => {
//       file.path = `/public${file.path.replace(publicDir, "").replace(/\\/g, "/")}`;
//     });

//     next();
//   });
// };

const uploadMultiple = (req, res, next) => {
  upload.fields([
    { name: "images", maxCount: 2 },
    { name: "documents", maxCount: 5 },
    { name: "customer-consent", maxCount: 1 },
    { name: "isometric-drawing", maxCount: 1 },
    { name: "natural-gas-consent", maxCount: 1 },
    { name: "jmr-customer-approval", maxCount: 1 },
    { name: "pressure-testing", maxCount: 1 },
    { name: "installed-pipeline", maxCount: 1 },
    { name: "jmr", maxCount: 1 },
    { name: "others", maxCount: 1 },
  ])(req, res, (err) => {
    if (err) {
      sendError(req, res, [], err.message);
      return;
    }
    if (!req.files || Object.keys(req.files).length === 0) {
      sendError(req, res, [], messages.PLEASE_UPLOAD_FILE);
      return;
    }
    next();
  });
};

const uploadMultipleTemp = (req, res, next) => {
  upload.fields([
    { name: "images", maxCount: 2 },
    { name: "documents", maxCount: 5 },
    { name: "customer-consent", maxCount: 1 },
    { name: "isometric-drawing", maxCount: 1 },
    { name: "natural-gas-consent", maxCount: 1 },
    { name: "jmr-customer-approval", maxCount: 1 },
    { name: "pressure-testing", maxCount: 1 },
    { name: "installed-pipeline", maxCount: 1 },
    { name: "jmr", maxCount: 1 },
    { name: "others", maxCount: 1 },
  ])(req, res, async (err) => {
    try {
      if (err) {
        sendError(req, res, [], err.message);
        return;
      }

      if (!req.files || Object.keys(req.files).length === 0) {
        sendError(req, res, [], messages.PLEASE_UPLOAD_FILE);
        return;
      }

      const permission = "racwd";
      const sasToken = await SASTokenHelper.generateSASToken(permission);

      const uploadedFiles = {};

      // Iterate each field (e.g., 'isometric-drawing', 'pressure-testing', etc.)
      for (const fieldName of Object.keys(req.files)) {
        const filesArray = req.files[fieldName];

        uploadedFiles[fieldName] = [];

        for (const file of filesArray) {
          const imageUploaded = await SASTokenHelper.azureMultipleFileUpload(
            req,
            {},
            sasToken,
            "temp"
          );
          console.log(">>>>>>>inside the muleter", imageUploaded);
          uploadedFiles[fieldName].push(imageUploaded);
        }
      }

      req.files = uploadedFiles; // overwrite req.files with uploaded Azure URLs/info
      next();
    } catch (error) {
      console.error("Error in uploadMultipleTemp:", error);
      sendError(req, res, [], error.message);
    }
  });
};

module.exports = {
  uploadSingle,
  uploadMultiple,
  uploadSingleTemp,
  uploadSingleTempNew,
  uploadMultipleTemp,
};
