const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_lmc_connection_mapping",
    {
      lmc_connection_mapping_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      customer_connection_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "trx_customer_connection",
          key: "customer_connection_id",
        },
        unique: "UK_trx_lcm_customer_connection_lmc_id",
      },
      lmc_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_trx_lcm_customer_connection_lmc_id",
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_lmc_connection_mapping",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_lmc_connection_mapping_id",
          unique: true,
          fields: [{ name: "lmc_connection_mapping_id" }],
        },
        {
          name: "UK_trx_lcm_customer_connection_lmc_id",
          unique: true,
          fields: [{ name: "customer_connection_id" }, { name: "lmc_id" }],
        },
      ],
    }
  );
};
