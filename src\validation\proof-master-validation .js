const { Joi } = require('express-validation');

const createProofMasterValidation = {
  body: Joi.object({
    proof_type: Joi.string()
      .min(3)
      .max(30)
      .required()
      .messages({
        'string.min': 'Proof type must have at least 3 characters',
        'string.max': 'Proof type should not exceed 30 characters',
        'any.required': 'Proof type is required',
      }),
    proof_name: Joi.string()
      .min(3)
      .max(200)
      .required()
      .messages({
        'string.min': 'Proof name must have at least 3 characters',
        'string.max': 'Proof name should not exceed 200 characters',
        'any.required': 'Proof name is required',
      }),
    display_order: Joi.number()
      .integer()
      .optional()
      .default(0)
      .messages({
        'number.base': 'Display order must be a number',
        'number.integer': 'Display order must be an integer',
      }),
    is_active: Joi.number()
      .integer()
      .optional()
      .default(1)
      .messages({
        'number.base': 'isActive must be a number',
        'number.integer': 'isActive must be an integer',
      }),
  }),
};

const updateProofMasterValidation = {
  body: Joi.object({
    proof_master_id: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Proof master ID must be a number',
        'any.required': 'Proof master ID is required',
      }),
    proof_type: Joi.string()
      .min(3)
      .max(30)
      .optional()
      .messages({
        'string.min': 'Proof type must have at least 3 characters',
        'string.max': 'Proof type should not exceed 30 characters',
      }),
    proof_name: Joi.string()
      .max(200)
      .optional()
      .messages({
        'string.max': 'Proof name should not exceed 200 characters',
      }),
    display_order: Joi.number()
      .integer()
      .optional()
      .messages({
        'number.base': 'Display order must be a number',
        'number.integer': 'Display order must be an integer',
      }),
    is_active: Joi.number()
      .integer()
      .optional()
      .messages({
        'number.base': 'isActive must be a number',
        'number.integer': 'isActive must be an integer',
      }),
  }),
};

const deleteProofMasterValidation = {
  body: Joi.object({
    proof_master_id: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Proof master ID must be a number',
        'any.required': 'Proof master ID is required',
      }),
      proof_type: Joi.string()
      .min(3)
      .max(30)
      .required()
      .messages({
        'string.min': 'Proof type must have at least 3 characters',
        'string.max': 'Proof type should not exceed 30 characters',
        'any.required': 'Proof type is required',
      }),
  }),
};

const getProofMasterValidation = {
  body: Joi.object({
    page: Joi.number()
      .integer()
      .min(1)
      .allow(null, '')
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.min': 'Page must be greater than 0',
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .allow(null, '')
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.min': 'Limit must be greater than 0',
      }),
    search: Joi.string()
      .max(200)
      .allow(null, '')
      .messages({
        'string.max': 'Search query should not exceed 200 characters',
      }),
    orderBy: Joi.string()
      .valid('asc', 'desc')
      .allow(null, '')
      .messages({
        'string.valid': 'OrderBy must be either "asc" or "desc"',
      }),
    orderName: Joi.string()
      .allow(null, '')
      .messages({
        'string.base': 'OrderName must be a string',
      }),
  }),
};

module.exports = {
  createProofMasterValidation,
  updateProofMasterValidation,
  deleteProofMasterValidation,
  getProofMasterValidation,
};
