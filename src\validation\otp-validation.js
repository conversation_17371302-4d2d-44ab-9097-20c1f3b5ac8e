const { Joi } = require('express-validation');
const {
  MOBILE_REGEX,
} = require('../constant/regex');

const sendOtpValidation = {
  body: Joi.object({
    bp_id: Joi.string().allow('', null).optional(),
    customer_connection_id: Joi.number().allow('', null).optional(),
    email: Joi.string().email().allow('', null).optional(),
    number: Joi.string().allow('', null).optional().regex(MOBILE_REGEX).length(10).messages({
      'string.pattern.base': 'Mobile number must be exactly 10 digits.',
      'string.length': 'Mobile number must be exactly 10 digits.'
    }),
  }).required(),
};

const emailAndSmsOtpVerifyValidation = {
  body: Joi.object({
    bp_id: Joi.string().allow('', null).optional(),
    customer_connection_id: Joi.number().allow('', null).optional(),
    email: Joi.string().email().allow('', null).optional(),
    number: Joi.string().allow('', null).optional().regex(MOBILE_REGEX).length(10).messages({
      'string.pattern.base': 'Mobile number must be exactly 10 digits.',
      'string.length': 'Mobile number must be exactly 10 digits.'
    }),
    OTP: Joi.number().integer().required(),
  }).required(),
};
module.exports = {
  sendOtpValidation,
  emailAndSmsOtpVerifyValidation,
};
