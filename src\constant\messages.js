module.exports = {
  REGISTER_SUCCESSFULLY: 'User Created Successfully',
  EMAIL_ALREADY_EXIST: 'This email is already registered. Please use other',
  MOBILE_ALREADY_EXIST: 'This Phone no is already registered. Please use other',
  PHONE_NO_INVALID: 'This Phone no is invalid',
  PINCODE_INVALID: 'This pincode is invalid',
  OTP_NEW_USER: 'OTP generated and stored for new user.',
  OTP_UPDATE_EXISTING_USER: 'OTP updated and stored for existing user.',
  DATABASE_ERROR: 'Database Error',
  NOT_FOUND: 'Not Found',
  Data_FOUND: 'Data Found',
  FORBIDDEN: 'Forbidden',
  UNAUTHORIZED: 'Unauthorized',
  UNAUTHORIZED_MSG: 'You are not authorized to access this.',
  INTERNAL_SERVER_ERROR: 'Internal Server Error',
  BAD_REQUEST: 'Bad Request',
  OTP_VERIFIED: 'OTP verified successfully!',
  INVALID_OTP: 'Invalid OTP.',
  USER_UPDATED_SUCCESSFULLY: 'User updated successfully.',
  USER_NOT_FOUND: 'User not found.',
  USER_LOGIN_SUCCESSFULLY: 'Login Successfully',
  INVALID_CREDENTIALS: 'Invalid credentials',
  OTP_EXPIRED: 'OTP expired. Please generate a new one.',
  SIGNED_IN: 'You are logged in successfully',
  SOME_ERROR_OCCUR: 'Some error occured during the process. Please try again',
  INVALID_CREDENTIAL: 'Invalid credentials',
  FETCHED: 'Data fetched Successfully',
  CREATED: 'Data created Successfully',
  UPDATED: 'Data updated Successfully',
  DELETED: 'Data deleted Successfully',
  EVENT_SUCCESSFULLY: 'Event Created Successfully',
  EVENT_UPDATED: 'Event Updated Successfully',
  EVENT_NOT_FOUND: 'Event Not Found',
  EVENT_DELETED: 'Event Deleted Successfully',
  ACTION_NOT_SUPPORTED: 'Action Not Supported',
  USER_UPDATED: 'user updated Successfully',
  USER_DELETED: 'user deleted Successfully',
  USER_FETCHED: 'user fetched Successfully',
  INVALID_ROLE_ARRAY: 'Invalid roles data; roles must be an array',
  ERROR_CREATE_ROLE: 'Error creating role: ',
  ERROR_LIST_TILE: 'Error fetching tile: ',
  ERROR_CREATE_TILE: 'Error creating tile: ',
  ERROR_UPDATE_TILE: 'Error updating tile: ',
  ERROR_DELETE_TILE: 'Error deleting tile: ',
  INVALID_TOKEN: 'Invalid Token or expired Please login again',
  TOKEN_REQUIRED: 'A Token is required for authentication',
  NOTIFICATION_MESSAGE: 'This is a test message',
  NOTIFICATION_TITLE: 'This is a test message',
  NOTIFICATION_TOPIC: 'all_user',
  NOTIFICATION_SENT_SUCCESSFULLY: 'Notification sent successfully.',
  USER_ACCOUNT_DEACTIVATED:
    'Your account has been deactivated. Please contact support team',
  USER_ACCOUNT_DELETED:
    'Your account has been deleted. Please contact support for assistance',
  FILE_UPLOAD_FAILD: 'File upload failed.',
  MEDIA_FETCHED: 'Media fetched successfully',
  MEDIA_NOT_EXIST: 'File not exist',
  PLEASE_UPLOAD_FILE: 'Please upload file',
  MEDIA_DELETED: 'Media deleted successfully',
  IS_DELETED: 0,
  MEDIA_MAPPED: 'Media associate with other features',
  INVALID_FILE_FRMATE:
    'Invalid file format. Only JPEG, JPG, PDF and PNG files are allowed.',
  IS_DELETED_YES: 1,
  IS_DELETED_NO: 0,
  ERROR_CREATE_BANNER: 'Error creating banner: ',
  PROVIDE_ENCRYPTED: 'Please provide encrypted payload',
  PROVIDE_DECRYPTED: 'Please provide decrypted payload',
  INVALID_PAYLOAD: 'Invalid payload',
  PASSWORD_UNMATCHED: 'Old password does not match',
  PASSWORD_UPDATE_FAILED:
    'Password update failed. Please ensure your verification otp is correct and try again. If the issue persists, contact support.',
  BANNER_EXIST: 'Banner already exists',
  TITLE_ALREDY_EXIST: 'Title already exists',
  ROLE_EXIST: 'Role already exists',
  TILE_EXIST: 'Tile already exists',
  OFFER_EXIST: 'Offer already exists',
  EVENT_EXIST: 'Event already exists',
  CONTENT_EXIST: 'Content already exists',
  STATUS_CODE: {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500,
    SERVER_ERROR: 500,
  },
  ALTERNATE_PHONE_NO_INVALID: 'Invalid Alternate Mobile Number',
};
