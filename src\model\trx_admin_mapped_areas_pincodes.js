const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_admin_mapped_areas_pincodes",
    {
      mapped_pincode_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      admin_mapped_area_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "trx_admin_mapped_areas",
          key: "admin_mapped_area_id",
        },
        unique: "UK_trxadminmappedareaspincodes_amaid_pincodeid",
      },
      pincode_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_pincodes",
          key: "pincode_id",
        },
        unique: "UK_trxadminmappedareaspincodes_amaid_pincodeid",
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_admin_mapped_areas_pincodes",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_trxadminmappedareaspincodes_mapped_pincode_id",
          unique: true,
          fields: [{ name: "mapped_pincode_id" }],
        },
        {
          name: "UK_trxadminmappedareaspincodes_amaid_pincodeid",
          unique: true,
          fields: [{ name: "admin_mapped_area_id" }, { name: "pincode_id" }],
        },
      ],
    }
  );
};
