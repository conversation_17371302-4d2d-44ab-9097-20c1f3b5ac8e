const { Joi } = require('express-validation');
const getRoleValidation = {
  body: Joi.object({
    page: Joi.number().allow(null, '').default(null),
    limit: Joi.number().allow(null, '').default(null),
    search: Joi.string().allow(null, ''),
    orderBy: Joi.string().valid('asc', 'desc').allow(null, ''),
    orderName: Joi.string().allow(null, ''),
    role_id: Joi.number().optional(),
    all_records: Joi.boolean().optional(),
  }),
};
const createRoleValidation = {
  body: Joi.object({
    role_name: Joi.string().required(),
    slug: Joi.string().optional(),
    default_capability: Joi.string().required(),
    ip_address: Joi.string().required(),
    browser: Joi.string().required(),
  }),
};

const updateRoleValidation = {
  body: Joi.object({
    role_id: Joi.number().integer().required(),
    role_name: Joi.string().required(),
    slug: Joi.string().optional(),
    default_capability: Joi.string().required(),
    ip_address: Joi.string().required(),
    browser: Joi.string().required(),
  }),
};

const deleteRoleValidation = {
  body: Joi.object({
    role_id: Joi.number().integer().min(0).required(),
    ip_address: Joi.string().required(),
    browser: Joi.string().required(),
  }),
};

module.exports = {
  createRoleValidation,
  updateRoleValidation,
  deleteRoleValidation,
  getRoleValidation,
};
