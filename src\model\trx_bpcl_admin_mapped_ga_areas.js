const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_bpcl_admin_mapped_ga_areas",
    {
      bamga_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      bpcl_admin_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
        unique: "UK_trx_bamga_bpcladminid_gaareaid",
      },
      ga_area_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_geographical_areas",
          key: "geographical_area_id",
        },
        unique: "UK_trx_bamga_bpcladminid_gaareaid",
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_bpcl_admin_mapped_ga_areas",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_bamga_id",
          unique: true,
          fields: [{ name: "bamga_id" }],
        },
        {
          name: "UK_trx_bamga_bpcladminid_gaareaid",
          unique: true,
          fields: [{ name: "bpcl_admin_id" }, { name: "ga_area_id" }],
        },
      ],
    }
  );
};
