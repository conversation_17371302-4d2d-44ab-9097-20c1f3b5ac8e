const { QueryTypes } = require("sequelize");
const { sequelize } = require("../model/user.model");
const User = require("../model/user.model");
const UserCapabilitiesMap = require("../model/user-capabilities-map.model");
const Role = require("../model/role.model");
const create = async (userData) => {
  const user = await User.create(userData);
  return user.get();
};

const findById = async (userId) => {
  return await User.findByPk(userId);
};

const findByUsername = async (username) => {
  return await User.findOne({ where: { username } });
};

const updateUser = async (whereCondition, updateData) => {
  const [updatedRowCount] = await User.update(updateData, {
    where: whereCondition,
  });
  return updatedRowCount;
};

const CheckExistingUser = async (email) => {
  return await User.findOne({
    where: { email: email },
    raw: true,
  });
};

const CheckExistingUserWithMobile = async (mobile) => {
  return await User.findOne({
    where: { phone_no: mobile },
    attributes: { exclude: ["password"] },
    raw: true,
  });
};
const CheckExistingUserWithEmail = async (email) => {
  return await User.findOne({
    where: { email: email },
    attributes: { exclude: ["password"] },
    raw: true,
  });
};
const getUserList = async (params) => {
  const totalCount = await User.count({
    where: {
      is_deleted: 0,
    },
  });
  const joinedUsers = await User.findAll(params);
  let userInfo = { count: totalCount };
  userInfo.rows = joinedUsers;
  return userInfo;
};
const getByUserId = async (id, attributes = {}) => {
  const data = await User.findByPk(id, { attributes: attributes });
  if (Object.keys(attributes).length > 0) {
    let userInfo = { count: 1 };
    userInfo.rows = [data];
    return userInfo;
  }
  return data;
};

const getUserDetails = async (id, attributes) => {
  const data = await User.findOne({
    attributes: attributes,
    where: {
      user_id: id,
      is_deleted: 1,
    },
  });
  return data;
};

const updateUserById = async (id, dto) => {
  const data = await getByUserId(id);
  return data.update(dto);
};

const deleteUser = async (id, dto) => {
  const data = await updateUserById(id, dto);
  return data;
};
const mapCapabilitiesWithUser = async (dto) => {
  return await UserCapabilitiesMap.bulkCreate(dto);
};
const mapCapabilitiesUser = async (capability) => {
  return await UserCapabilitiesMap.findOrCreate({
    where: {
      user_id: capability.user_id,
      capabilities: capability.capabilities,
    },
    defaults: capability,
    row: true,
  });
};
const updateUserCapabilities = async (updateInfo, whereCondi) => {
  await UserCapabilitiesMap.update(updateInfo, {
    where: whereCondi,
  });
};

const updateUserDetail = async (updateData) => {
  const { user_id, ...rest } = updateData;
  await User.update(rest, {
    where: { user_id },
  });
  return await User.findOne({ where: { user_id }, raw: true });
};

const getUsersCount = async () => {
  const whereClause = {
    is_deleted: 0,
    is_active: 1,
  };

  const data = await User.count({
    where: whereClause,
  });

  return data;
};

module.exports = {
  create,
  getUserList,
  getByUserId,
  deleteUser,
  updateUserById,
  findById,
  findByUsername,
  updateUser,
  CheckExistingUser,
  CheckExistingUserWithMobile,
  mapCapabilitiesWithUser,
  mapCapabilitiesUser,
  updateUserCapabilities,
  updateUserDetail,
  CheckExistingUserWithEmail,
  getUsersCount,
};
