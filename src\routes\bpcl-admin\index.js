const express = require("express");
const verifyToken = require("../../middleware/auth");
const { restrictToRoles } = require("../../middleware/auth");
const {
  getChannelPartnersCount,
  contractorOnboarding,
  getAreaList,
  getContractorPendingApprovalList,
  contractorAcceptReject,
  getContractorManagementTPI,
  subAdminOnboarding,
  getSubAdminTypeList,
  getGeographicalAreaList,
  getTpiSalesListByAreaWise,
  getTpiEngineerListByAreaWise,
  detailsByPersonaRole,
  getContractorManagementAreaUpdate,
  soStatusUpdate,
  eoStatusUpdate,
  eoJmrUpdate,
  getProfileData,
  customerApprovalList,
  customerPersonalDetails,
  customerApprovalListEo,
  customerApprovalListManualGmr,
  customerlmcInstallationDetails,
  customerManualGmrDetails,
  getSoCommentDetails,
  getEoCommentDetails,
  getEoJmrCommentDetails,
  getCommentDetails,
  getBpclAdminGeographicalAreas
} = require("../../controllers/bpcl-admin-controller");
const { validate } = require("express-validation");
const {
  contractorOnboardingValidation,
  getAreaValidation,
  contractorApprovalValidation,
  contractorAcceptRejectValidation,
  subAdminOnboardingValidation,
  tpiSalesAreaWiseValidation,
  detailsByPersonaRoleValidation,
  soStatusUpdateValidation,
  eoStatusUpdateValidation,
  eoJmrStatusUpdateValidation,
  customerApprovalListValidation,
  customerPersonalDetailsValidation,
  customerLmcInstallationDetailsValidation,
  customerManualGmrDetailsValidation,
  soCommentDetailsValidation,
  eoCommentDetailsValidation,
  eoJmrCommentDetailsValidation,
  commentDetailsValidation
} = require("../../validation/ga-admin-validation");
const {
  uploadSingle,
  uploadSingleTemp,
} = require("../../middleware/newMulter");

const bpclAdminRoutes = express.Router();

bpclAdminRoutes.post(
  "/channel-partners/count",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
  getChannelPartnersCount
);

bpclAdminRoutes.post(
  "/contractor-onboarding",
  verifyToken,
  restrictToRoles("bpcl-sub-admin"),
  uploadSingle,
  validate(contractorOnboardingValidation),
  contractorOnboarding
);

bpclAdminRoutes.post(
  "/area-list",
  // verifyToken,
  // restrictToRoles('bpcl_admin'),
  validate(getAreaValidation),
  getAreaList
);

bpclAdminRoutes.post(
  "/contractor-approval-list",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
  validate(contractorApprovalValidation),
  getContractorPendingApprovalList
);

bpclAdminRoutes.post(
  "/contractor-accept-reject",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
  validate(contractorAcceptRejectValidation),
  contractorAcceptReject
);
bpclAdminRoutes.post(
  "/sub-admin-onboarding",
  verifyToken,
  // restrictToRoles('bpcl_admin'),
  validate(subAdminOnboardingValidation),
  subAdminOnboarding
);

bpclAdminRoutes.get(
  "/sub-admin-type-list",
  //verifyToken,
  // restrictToRoles('bpcl_admin'),
  getSubAdminTypeList
);
bpclAdminRoutes.get(
  "/get-geographical-areas",
  verifyToken,
  //restrictToRoles('bpcl_admin'),
  getGeographicalAreaList
);

bpclAdminRoutes.post(
  "/tpi-sales-areas-wise",
  verifyToken,
  //restrictToRoles('bpcl_admin'),
  validate(tpiSalesAreaWiseValidation),
  getTpiSalesListByAreaWise
);
bpclAdminRoutes.post(
  "/tpi-engineer-areas-wise",
  verifyToken,
  //restrictToRoles('bpcl_admin'),
  validate(tpiSalesAreaWiseValidation),

  getTpiEngineerListByAreaWise
);

bpclAdminRoutes.post(
  "/contractor-details-by-persona-role",
  verifyToken,
  //restrictToRoles('bpcl_admin'),
  validate(detailsByPersonaRoleValidation),
  detailsByPersonaRole
);
bpclAdminRoutes.post(
  "/contractor-management-tpi",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
  getContractorManagementTPI
);

bpclAdminRoutes.post(
  "/contractor-management-area-update",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
  getContractorManagementAreaUpdate
);

bpclAdminRoutes.post(
  "/so-status-update",
  verifyToken,
  validate(soStatusUpdateValidation),
  soStatusUpdate
);

bpclAdminRoutes.post(
  "/eo-status-update",
  verifyToken,
  validate(eoStatusUpdateValidation),
  eoStatusUpdate
);

bpclAdminRoutes.post(
  "/eo-jmr-update",
  verifyToken,
  validate(eoJmrStatusUpdateValidation),
  eoJmrUpdate);

bpclAdminRoutes.post(
  "/admin-profile",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
  getProfileData
);
bpclAdminRoutes.post(
  "/customer-approval-list",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
  validate(customerApprovalListValidation),
  customerApprovalList
);

bpclAdminRoutes.post(
  "/customer-personal-details",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin","engineer"),
  validate(customerPersonalDetailsValidation),
  customerPersonalDetails
);

bpclAdminRoutes.post(
  "/customer-approval-list-eo",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
  validate(customerApprovalListValidation),
  customerApprovalListEo
);

bpclAdminRoutes.post(
  "/customer-approval-list-manual-gmr",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
  validate(customerApprovalListValidation),
  customerApprovalListManualGmr
);

// bpclAdminRoutes.post(
//   "/customer-approval-list-manual-gmr",
//   verifyToken,
//   restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
//   validate(customerApprovalListValidation),
//   customerApprovalListManualGmr
// );

bpclAdminRoutes.post(
  "/lmc-installation-details",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin","engineer"),
  validate(customerLmcInstallationDetailsValidation),
  customerlmcInstallationDetails
);

bpclAdminRoutes.post(
  "/manual-gmr-details",
  verifyToken,
  restrictToRoles("bpcl-sub-admin", "bpcl-admin"),
  validate(customerManualGmrDetailsValidation),
  customerManualGmrDetails
);

bpclAdminRoutes.post(
  "/so-details",
  verifyToken,
  validate(soCommentDetailsValidation),
  getSoCommentDetails
);

bpclAdminRoutes.post(
  "/eo-details",
  verifyToken,
  validate(eoCommentDetailsValidation),
  getEoCommentDetails
);

bpclAdminRoutes.post(
  "/eo-jmr-details",
  verifyToken,
  validate(eoJmrCommentDetailsValidation),
  getEoJmrCommentDetails
);

bpclAdminRoutes.post(
  "/comment-details",
  verifyToken,
  validate(commentDetailsValidation),
  getCommentDetails
);

bpclAdminRoutes.post(
  "/geographical-areas",
  verifyToken,
  // validate(commentDetailsValidation),
  getBpclAdminGeographicalAreas
);

module.exports = bpclAdminRoutes;
