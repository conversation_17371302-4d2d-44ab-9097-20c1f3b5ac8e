const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "mst_govt_scheme",
    {
      govt_scheme_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      isu_scheme_code: {
        type: DataTypes.STRING(5),
        allowNull: true,
      },
      scheme_name: {
        type: DataTypes.STRING(200),
        allowNull: false,
        unique: "UK_mstgovtscheme_scheme_name",
      },
      upfront_amt: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      month_amt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      year: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      rental_amt: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "mst_govt_scheme",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_govt_scheme_id",
          unique: true,
          fields: [{ name: "govt_scheme_id" }],
        },
        {
          name: "UK_mstgovtscheme_scheme_name",
          unique: true,
          fields: [{ name: "scheme_name" }],
        },
      ],
    }
  );
};
