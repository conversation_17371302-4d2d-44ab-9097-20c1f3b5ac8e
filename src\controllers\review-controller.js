const { sendSuccess, sendError } = require('../helpers/responseHelper');
const reviewService = require('../services/review-service');

exports.reviewDetailsUpload = async (req, res) => {
  try {
    // Validate checkbox selection (assuming 'checkboxValues' is sent in body)
    if (!req.body.checkboxValues || !Array.isArray(req.body.checkboxValues) || req.body.checkboxValues.length === 0) {
      return sendError(req, res, {}, 'At least one checkbox must be selected.');
    }

    // Validate file existence
    if (!req.files || req.files.length === 0) {
      return sendError(req, res, {}, 'Please upload at least one file.');
    }

    // Call service to process and store data
    const result = await reviewService.processReviewDetails(req.body, req.files);
    sendSuccess(req, res, result, 'Review details uploaded successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
