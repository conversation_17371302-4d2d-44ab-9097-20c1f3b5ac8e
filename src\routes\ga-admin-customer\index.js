const { validate } = require("express-validation");
const verifyToken = require("../../middleware/auth");
const { restrictToRoles } = require('../../middleware/auth');
const express = require("express");
const gaCustomerRoute = express.Router();
const decrypt = require("../../middleware/encrypt-decrypt");
const gaCustomerController = require("../../controllers/ga-customer-controller");

const {
  areaWiseListValidation
} = require('../../validation/TPI-validation');
const {
  gaCommonValidation,
  gaCommonCountValidation
} = require('../../validation/ga-customer-validation');


gaCustomerRoute.post(
  "/ongoing-areas-list",
  verifyToken,
  validate(gaCommonValidation),
  gaCustomerController.ongoingList
);

gaCustomerRoute.post(
  "/completed-areas-list",
  verifyToken,
  validate(gaCommonValidation),
  gaCustomerController.completedList
);

gaCustomerRoute.post(
  "/ongoing-summary-counts",
  verifyToken,
  validate(gaCommonCountValidation),
  gaCustomerController.ongoingSummaryCounts
);


gaCustomerRoute.post(
  "/completed-summary-counts",
  verifyToken,
  validate(gaCommonCountValidation),
  gaCustomerController.completedSummaryCounts
);

gaCustomerRoute.post(
  "/area-wise-list",
  verifyToken,
  validate(areaWiseListValidation),
  gaCustomerController.areaWiseList
);

gaCustomerRoute.post(
  "/area-count",
  verifyToken,
  validate(areaWiseListValidation),
  gaCustomerController.areaCountData
);

gaCustomerRoute.post(
  "/contractor-area-list",
  verifyToken,
  validate(gaCommonValidation),
  gaCustomerController.contractorAreaList
);

gaCustomerRoute.post(
  "/lmc-area-list",
  verifyToken,
  validate(gaCommonValidation),
  gaCustomerController.lmcAreaList
);

gaCustomerRoute.post(
  "/dma-area-list",
  verifyToken,
  validate(gaCommonValidation),
  gaCustomerController.dmaAreaList
);

gaCustomerRoute.post(
  "/fo-area-list",
  verifyToken,
  validate(gaCommonValidation),
  gaCustomerController.foAreaList
);

module.exports = { gaCustomerRoute };
