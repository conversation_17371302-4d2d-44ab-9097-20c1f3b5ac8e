const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_meter_reading_orders",
    {
      mro_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      mro_ticket: {
        type: DataTypes.STRING(10),
        allowNull: false,
        unique: "UK_mro_mroticket_customermeterid_cycleid",
      },
      bp_id: {
        type: DataTypes.STRING(12),
        allowNull: false,
      },
      cycle_start_date: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      cycle_end_date: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      cycle_start_year_month: {
        type: DataTypes.STRING(6),
        allowNull: false,
      },
      cycle_end_year_month: {
        type: DataTypes.STRING(6),
        allowNull: false,
      },
      scheduled_mr_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      equipment_no: {
        type: DataTypes.STRING(12),
        allowNull: true,
      },
      mro_status: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      fo_supervisor_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      remarks: {
        type: DataTypes.TEXT, // or STRING
        allowNull: true,
      }
      
    },
    {
      sequelize,
      tableName: "trx_meter_reading_orders",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_mro_id",
          unique: true,
          fields: [{ name: "mro_id" }],
        },
        {
          name: "UK_mro_mroticket_bpid_csym_ceym",
          unique: true,
          fields: [
            { name: "mro_ticket" },
            { name: "bp_id" },
            { name: "cycle_start_year_month" },
            { name: "cycle_end_year_month" },
          ],
        },
      ],
    }
  );
};
