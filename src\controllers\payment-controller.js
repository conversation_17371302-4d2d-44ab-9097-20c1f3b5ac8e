const { sendSuccess, sendError } = require('../helpers/responseHelper');
const paymentService = require('../services/payment-service');

exports.currentBill = async (req, res) => {
  try {
    // Call service layer with req.body
    const result = await paymentService.getCurrentBill(req.query);

    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.paymentHistory = async (req, res) => {
  try {
    const result = await paymentService.getPaymentHistory(req.body);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.checkNumber = async (req, res) => {
  console.log('co', req.body.number);
  // const requestBody = req.body;
  // requestBody.bpcl_id = req.bpclId;

  try {
    // Call service layer with req.body
    const result = await paymentService.checkNumberDetails(req.body);

    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.downloadPDF = async (req, res) => {
  try {
    const result = await paymentService.generatePdf(req.body);
    sendSuccess(req, res, result, 'generated.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
