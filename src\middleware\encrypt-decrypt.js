const { decryptCrypto } = require("../utils/encrypt-decrypt");
const { FormatData } = require('../utils');
const message = require('../utils/constants');
const CryptoJS = require("crypto-js");

// Your secret key
const secretKey = process.env.ENCRYPTION_SECRET_KEY;

// Function to check if the payload is encrypted
const isEncrypted = async (payload) => {
    try {
        const bytes = CryptoJS.AES.decrypt(payload, secretKey);
        const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
        JSON.parse(decryptedData);
        return true; 
    } catch (error) {
        return false; 
    }
}

const decrypt = async (req, res, next) => {
  if(!req.headers["encryption"]){
    return next();
  }
  let encryptionNeeded = req.headers["encryption"];
  if (encryptionNeeded === process.env.ENCRYPTION_TRUE) {
    if(req.body.data){
      if(isEncrypted(req.body.data)){
        req.body = decryptCrypto(req.body.data);
        req.body = JSON.parse(req.body);
      }else{
        const output = FormatData('', message.PROVIDE_ENCRYPTED);
        return res.status(403).send(output);
      }
    }else{
      const output = FormatData('', message.INVALID_PAYLOAD);
      return res.status(403).send(output);
    }
    
  }

  if (encryptionNeeded === process.env.ENCRYPTION_FALSE) {
        if(req.body.data){
          if(isEncrypted(req.body.data)){
            const output = FormatData('', message.PROVIDE_DECRYPTED);
            return res.status(403).send(output);
          }
        }
  }
  
  return next();
};

module.exports = decrypt;
