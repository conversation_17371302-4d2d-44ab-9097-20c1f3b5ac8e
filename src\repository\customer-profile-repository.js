const { dbModels } = require('../database/connection');
const { Op, col } = require('sequelize');

const addUpdateCheckEmail = async (payload) => {
  if (payload.type === 'next') {
    // Check if record exists and email matches
    const existingEmail = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id: payload.customer_connection_id,
        email: payload.email,
      },
    });
    console.log('existing email', existingEmail);
    return (existingEmail? 'yes' : 'no');

  } else {
    const updatedEmail = await dbModels.trx_customer_connection.update(
      { email: payload.email }, // Set the new email ID
      { where: { customer_connection_id: payload.customer_connection_id } } // Update for the given connection ID
    );
    return 'updated';
  }
};
const addUpdateCheckNumber = async (payload) => {
  if (payload.type === 'next') {
    const existingEmail = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id: payload.customer_connection_id },
    });
    console.log('existing email', existingEmail);
    const result =
      existingEmail && existingEmail.email === payload.email ? 'yes' : 'no';
    console.log('result', result);

    // Check if record exists and email matches
    return result;
  } else {
    const updatedEmail = await dbModels.trx_customer_connection.update(
      { email: payload.email }, // Set the new email ID
      { where: { customer_connection_id: payload.customer_connection_id } } // Update for the given connection ID
    );
    return 'updated';
  }
};

const addUpdateNumber = async (payload) => {
  console.log(payload);
  const existingNumber = await dbModels.trx_customer.findOne({
    where: { bpcl_id: payload.bpcl_id, mobile: payload.number },
  });
  console.log('exit', existingNumber);

  if (existingNumber !== null && existingNumber !== undefined) {
    // Update the trx_customer_connection table
    await dbModels.trx_customer_connection.update(
      {
        bpcl_id: payload.bpcl_id,
        customer_id: existingNumber.customer_id,
        mobile: payload.number,
        updated_at: new Date(),
      },
      {
        where: { mobile: payload.old_number }, // Update based on customer_id
      }
    );

    return 'Updated successfully';
  } else {
    return 'Not Existing';
  }
};

const addUpdateCheckAltNumber = async (payload) => {
  if (payload.type === 'next') {
    const existingAltNumber = await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id: payload.customer_connection_id },
    });
    console.log('existingAltNumber', existingAltNumber);
    const result =
      existingAltNumber &&
      existingAltNumber.alternate_mobile === payload.alt_number
        ? 'yes'
        : 'no';
    console.log('result', result);

    // Check if record exists and email matches
    return result;
  } else {
    const updatedEmail = await dbModels.trx_customer_connection.update(
      { alternate_mobile: payload.alt_number }, // Set the new email ID
      { where: { customer_connection_id: payload.customer_connection_id } } // Update for the given connection ID
    );
    return 'updated';
  }
};

module.exports = {
  addUpdateCheckEmail,
  addUpdateCheckNumber,
  addUpdateNumber,
  addUpdateCheckAltNumber,
};
