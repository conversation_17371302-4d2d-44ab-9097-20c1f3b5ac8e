const { validate } = require("express-validation");
const verifyToken = require("../../middleware/auth");
const express = require("express");
const lmcContractor = express.Router();
const decrypt = require("../../middleware/encrypt-decrypt");
const lmcContractorController = require("../../controllers/lmc-contractor-controller");
const { restrictToRoles } = require('../../middleware/auth');

const {
  getLmcFeasibilityTicketsValidation,
} = require("../../validation/lmc-validation");
const { uploadSingle } = require("../../middleware/multer");
const {
  getLmcCustomerAssignmentTicketsValidation,
  supervisorOnboardingValidation,
  createSupervisorValidation,
  revokeSupervisorValidation,
  mySupervisorValidation,
  geographicalAreasValidation,
  dashboardMetricsValidation,
  supervisorAssignValidation,
  supervisorTicketAssignValidation,
} = require("../../validation/lmc-contractor-validation");
const {
  lmcCustomerAssignments,
} = require("../../controllers/lmc-contractor-controller");

lmcContractor.get(
  "/customer-assignments",
  verifyToken,
  restrictToRoles("contractor"),
  validate(getLmcCustomerAssignmentTicketsValidation),
  lmcContractorController.lmcCustomerAssignments
);
lmcContractor.get(
  "/supervisor-onboarding",
  verifyToken,
  restrictToRoles("contractor"),
  validate(supervisorOnboardingValidation),
  lmcContractorController.getAllLMCSupervisors
);
lmcContractor.post(
  "/create-supervisor",
  verifyToken,
  restrictToRoles("contractor"),
  uploadSingle,
  validate(createSupervisorValidation),
  lmcContractorController.createLMCSupervisor
);
lmcContractor.get(
  "/my-supervisor",
  verifyToken,
  restrictToRoles("contractor"),
  validate(mySupervisorValidation),
  lmcContractorController.getMyLMCSupervisors
);
lmcContractor.post(
  "/revoke-supervisor",
  verifyToken,
  restrictToRoles("contractor"),
  validate(revokeSupervisorValidation),
  lmcContractorController.revokeLMCSupervisor
);
lmcContractor.get(
  "/geographical-areas",
  verifyToken,
  // restrictToRoles("contractor"),
  validate(geographicalAreasValidation),
  lmcContractorController.getGeographicalAreas
);
lmcContractor.get(
  "/supervisor-tracking",
  verifyToken,
  restrictToRoles("contractor"),
  validate(geographicalAreasValidation),
  lmcContractorController.getSupervisorsTracking
);
lmcContractor.get(
  "/dashboard-metrics",
  verifyToken,
  restrictToRoles("contractor"),
  validate(dashboardMetricsValidation),
  lmcContractorController.getDashboardMetrics
);
lmcContractor.post(
  "/customer-assignments",
  verifyToken,
  restrictToRoles("contractor"),
  validate(supervisorAssignValidation),
  lmcContractorController.lmcContractorCustomerAssignments
);
lmcContractor.get(
  "/customer-assignment-ticket",
  verifyToken,
  restrictToRoles("contractor"),
  validate(supervisorTicketAssignValidation),
  lmcContractorController.getLmcCustomerAssignmentsTicket
);
lmcContractor.post(
  "/contractor-geographical-areas",
  verifyToken,
  // restrictToRoles("contractor"),
  validate(geographicalAreasValidation),
  lmcContractorController.getGeoAreaByUser
);

module.exports = { lmcContractor };
