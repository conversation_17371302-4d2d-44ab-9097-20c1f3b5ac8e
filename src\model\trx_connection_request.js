const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('trx_connection_request', {
    connection_request_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    customer_connection_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer_connection',
        key: 'customer_connection_id'
      }
    },
    customer_connection_address_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'trx_customer_connection_address',
        key: 'customer_connection_address_id'
      }
    },
    request_type: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    disconnection_type: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    engineer_visit_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    request_reason_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_masters',
        key: 'master_id'
      }
    },
    estimated_duration_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mst_masters',
        key: 'master_id'
      }
    },
    disconnection_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    request_status: {
      type: DataTypes.STRING(10),
      allowNull: false
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'trx_connection_request',
    schema: 'dbo',
    timestamps: true,
underscored: true,
    indexes: [
      {
        name: "PK_connection_request_id",
        unique: true,
        fields: [
          { name: "connection_request_id" },
        ]
      },
    ]
  });
};
