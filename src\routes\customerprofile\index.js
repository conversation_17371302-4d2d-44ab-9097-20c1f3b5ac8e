const { validate } = require('express-validation');
const express = require('express');
const customerProfileRoutes = express.Router();
const customerProfileController = require('../../controllers/customer-profile-controller');
const validator = require("../../middleware/validator");
const {
    verifyBpclToken,
    verifyPngToken,
  } = require("../../middleware/customer-auth");

 const {
     addUpdateEmailValidation,
  emailOtpVerifyValidation,
  sendOtpValidation,
  emailAndSmsOtpVerifyValidation,
  addUpdateNumberValidation,
  addUpdateAltNumberValidation,
  altNumberOtpVerifyValidation,
 } = require('../../validation/customer-profile-validation');

customerProfileRoutes.post('/add-update-email',verifyPngToken("guest"),validate(addUpdateEmailValidation),customerProfileController.addUpdateEmail);
customerProfileRoutes.post('/add-update-number',verifyPngToken("guest"),validate(addUpdateNumberValidation),customerProfileController.addUpdateNumber);
customerProfileRoutes.post('/add-update-alt-number',verifyPngToken("guest"),validate(addUpdateAltNumberValidation),customerProfileController.addUpdateAltNumber);


module.exports = { customerProfileRoutes };
