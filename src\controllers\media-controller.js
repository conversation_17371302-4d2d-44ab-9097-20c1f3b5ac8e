const { sendSuccess, sendError } = require("../helpers/responseHelper");
const mediaService = require("../services/media-service");

exports.mediaUpload = async (req, res) => {
  try {
    const result = await mediaService.mediaUpload(req);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getAzureImage = async (req, res) => {
  try {
    const result = await mediaService.getAzureImage(req);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getAzureDownload = async (req, res) => {
  try {
    const result = await mediaService.getAzureDownload(req);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.deleteAzure = async (req, res) => {
  try {
    const result = await mediaService.deleteAzure(req);
    sendSuccess(req, res, result, "Data Deleted successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.OCRUpload = async (req, res) => {
  try {
    const result = await mediaService.OCRUpload(req);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.imageRead = async (req, res) => {
  try {
    const result = await mediaService.imageRead(req);
    sendSuccess(req, res, result, "Fetch Data successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

