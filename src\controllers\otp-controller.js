const { sendSuccess, sendError } = require('../helpers/responseHelper');
const otpService = require('../services/otp-service');


exports.sendOtp = async (req, res) => {
  try {
    const result = await otpService.sendOtp(req.body);
    sendSuccess(req, res, null,result);
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.emailAndSmsOtpVerify = async (req, res) => {
  try {
    const result = await otpService.otpVerifyEmailAndSms(req.body);
    sendSuccess(req, res, null, result);
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

