const Sequelize = require("sequelize");
module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    "trx_admin_status_history",
    {
      admin_status_history_id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      remarks: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: "mst_admin",
          key: "admin_id",
        },
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: "trx_admin_status_history",
      schema: "dbo",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          name: "PK_ash_id",
          unique: true,
          fields: [{ name: "admin_status_history_id" }],
        },
      ],
    }
  );
};
