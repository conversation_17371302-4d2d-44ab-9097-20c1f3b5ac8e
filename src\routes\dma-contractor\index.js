const express = require('express');
const { validate } = require('express-validation');
const verifyToken = require('../../middleware/auth');
const { restrictToRoles } = require('../../middleware/auth');
const dmaContractorController = require('../../controllers/dma-contractor-controller');
const { assignCustomersValidation, reassignCustomerValidation, getSupervisorPerformanceValidation } = require('../../validation/dma-validation');

const dmaContractorRoutes = express.Router();

dmaContractorRoutes.post(
    '/dashboard-summary',
    verifyToken,
    restrictToRoles('contractor'),
    dmaContractorController.getDashboardSummary
);

dmaContractorRoutes.post(
    '/supervisors-performance',
    verifyToken,
    restrictToRoles('contractor'),
    validate(getSupervisorPerformanceValidation),
    dmaContractorController.getSupervisorsPerformance
);

dmaContractorRoutes.post(
    '/assign-customers',
    verifyToken,
    restrictToRoles('contractor'),
    validate(assignCustomersValidation),
    dmaContractorController.assignCustomersToSupervisor
);

dmaContractorRoutes.put(
    '/reassign-customer',
    verifyToken,
    restrictToRoles('contractor'),
    validate(reassignCustomerValidation),
    dmaContractorController.reassignCustomer
);

module.exports = dmaContractorRoutes;
