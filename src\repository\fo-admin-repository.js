const { Op, Sequelize } = require("sequelize");
const { dbModels, sequelize } = require("../database/connection");

const createFOSupervisor = async (supervisorData, contractorId, filePath,filename) => {
  const {
    first_name,
    last_name,
    email,
    mobile,
    password,
    persona_role_id,
    bpclAdminId,
  } = supervisorData;

  const transaction = await sequelize.transaction();

  try {
    // Create supervisor
    const supervisor = await dbModels.mst_admin.create(
      {
        first_name,
        last_name,
        email,
        mobile,
        password,
        user_role: "supervisor",
        persona_role_id,
        bpcl_admin_id: bpclAdminId,
        is_active: false,
        created_by: contractorId,
      },
      { transaction }
    );

    // Upload supporting document if file is provided
    if (filePath && filename) {
      await dbModels.mst_admin_documents.create(
        {
          admin_id: supervisor.admin_id,
          document_type: "supporting",
          file_name: filename,
          file_path: filePath,
          file_ext: filename?.split(".").pop(),
          created_by: contractorId,
        },
        { transaction }
      );
    }

    // Insert into trx_tpi_onboard_approve
    await dbModels.trx_tpi_onboard_approve.create(
      {
        supervisor_id: supervisor.admin_id,
        admin_type: "fo",
        approve_status: "pending",
        is_active: true,
        created_by: contractorId,
        created_at: new Date(),
      },
      { transaction }
    );

    if (Array.isArray(supervisorData.area_ids) && supervisorData.area_ids.length > 0) {
      const areaMappings = supervisorData.area_ids.map(area_id => ({
        admin_id: supervisor.admin_id,
        area_id,
        is_active: true,
        created_by: contractorId
      }));

      await dbModels.trx_admin_mapped_areas.bulkCreate(areaMappings, { transaction });
    }

    await transaction.commit();
    return supervisor;
  } catch (error) {
    await transaction.rollback();
    console.error("Error in createFOSupervisor:", error.message, error.stack);
    throw new Error("Failed to create FO Supervisor", error);
  }
};

const revokeFOSupervisor = async (payload) => {
  try {
    const { supervisorId, remark, assign_mro = [] } = payload;

    // Step 1: Find the supervisor by admin_id
    let supervisor = await dbModels.mst_admin.findOne({
      where: { admin_id: supervisorId },
      attributes: ["admin_id", "first_name", "last_name"],
    });

    if (!supervisor) {
      return { success: false, message: "Supervisor not found." };
    }

    // Step 2: Revoke supervisor (set inactive)
    await dbModels.mst_admin.update(
      {
        is_active: false,
        updated_at: new Date(),
      },
      { where: { admin_id: supervisorId } }
    );

    // Step 3: Assign MROs from array
    const reassignmentResults = [];
    for (const assignment of assign_mro) {
      const { fo_supervisor_id, mro_id } = assignment;

      if (!fo_supervisor_id || !mro_id) {
        reassignmentResults.push({
          mro_id,
          success: false,
          message: "Missing fo_supervisor_id or mro_id",
        });
        continue;
      }

      const [updatedCount] = await dbModels.mst_meter_reading_orders.update(
        { fo_supervisor_id },
        { where: { mro_id: mro_id } }
      );

      reassignmentResults.push({
        mro_id,
        success: updatedCount > 0,
        message:
          updatedCount > 0
            ? `Assigned to supervisor ${fo_supervisor_id}`
            : "No matching MRO found or already assigned.",
      });
    }

    return {
      success: true,
      message: `Supervisor ${supervisor.first_name} ${supervisor.last_name} (LMC${supervisor.admin_id}) revoked and reassignment completed.`,
      supervisor,
      reassignmentResults,
    };
  } catch (error) {
    console.error("Error in revokeFOSupervisor:", error);
    return {
      success: false,
      message: "Failed to revoke supervisor access and reassign MROs.",
      error: error.message,
    };
  }
};

const getMyLMCSupervisors = async (contractorId, is_active) => {
  try {
    let whereCondition = { approve_status: "approved" };

    // Convert `is_active` from query params to a proper boolean
    if (typeof is_active === "string") {
      is_active = is_active === "true" || is_active === "1"; // Convert "true"/"1" to `true`, everything else to `false`
    }

    if (typeof is_active === "boolean") {
      whereCondition.is_active = is_active;
    }

    const supervisors = await dbModels.mst_admin.findAll({
      where: {
        created_by: contractorId,
        user_role: "supervisor",
        ...(typeof is_active === "boolean" ? { is_active } : {}), // Apply filter only if it's a valid boolean
      },
      attributes: [
        "admin_id",
        "first_name",
        "last_name",
        "email",
        "mobile",
        "alternate_mobile",
        "persona_role_id",
        "is_active",
        "created_at", // Onboarding date
        "updated_at", // Inactive from date
      ],
      include: [
        {
          model: dbModels.mst_persona_role,
          as: "persona_role",
          attributes: ["persona_role_id", "role_name"],
        },
        // {
        //   model: dbModels.mst_agencies,
        //   as: 'agency',
        //   attributes: ['agency_id', 'agency_name']
        // },
        {
          model: dbModels.trx_tpi_onboard_approve,
          as: "trx_tpi_onboard_approves",
          attributes: [
            "tpi_onboard_approve_id",
            "approve_status",
            "is_active",
            "updated_at",
          ],
          where: Object.keys(whereCondition).length > 0 ? whereCondition : {},
          required: false,
        },
      ],
      raw: true,
      order: [["created_at", "DESC"]],
    });

    // Format response
    return supervisors;
  } catch (error) {
    console.error("Error in getMyLMCSupervisors:", error);
    throw error;
  }
};

const getGeographicalAreasMetrics = async (admin_id) => {
  try {
    // Total assigned GAs for this admin
    const totalAssignedGAs = await dbModels.trx_admin_mapped_ga_areas.count({
      where: {
        admin_id,
        is_active: true,
      },
    });

    // Total assigned areas via mapped GAs
    const totalAssignedAreas = await dbModels.trx_admin_mapped_areas.count({
      include: [
        {
          model: dbModels.trx_admin_mapped_ga_areas,
          as: "amga",
          where: {
            admin_id,
            is_active: true,
          },
        },
      ],
      where: {
        is_active: true,
      },
    });

    // Total FOs under this supervisor (if FOs are admins with user_role = 'sales' and report to this admin)
    const totalFOsUnderSupervision = await dbModels.mst_admin.count({
      where: {
        user_role: "supervisor",
      },
    });

    return {
      total_assigned_gas: totalAssignedGAs,
      total_assigned_areas: totalAssignedAreas,
      total_fo_under_supervision: totalFOsUnderSupervision,
    };
  } catch (error) {
    console.error("Error in getAdminKPIStats:", error);
    return {
      total_assigned_gas: 0,
      total_assigned_areas: 0,
      total_fo_under_supervision: 0,
      error: "Internal server error",
    };
  }
};

const getDashboardMetrics = async (geographical_area_id) => {
  try {
    const supervisors = await dbModels.mst_geographical_areas.findAll({
      attributes: ["region_name", "ga_area_name", "geographical_area_id"],
      where: {
        geographical_area_id: geographical_area_id,
      },
      include: [
        {
          model: dbModels.mst_areas,
          as: "mst_areas",
          attributes: ["area_name"],
          include: [
            {
              model: dbModels.trx_admin_mapped_areas,
              as: "trx_admin_mapped_areas",
              attributes: ["admin_id", "area_id"],
              include: [
                {
                  model: dbModels.mst_admin,
                  as: "admin",
                  attributes: [
                    "admin_id",
                    "first_name",
                    "last_name",
                    "email",
                    "mobile",
                    "alternate_mobile",
                    "persona_role_id",
                    "is_active",
                  ],
                  include: [
                    {
                      model: dbModels.trx_mapped_lmc_customer_connection,
                      as: "trx_mapped_lmc_customer_connections",
                      attributes: [
                        "mapped_lmc_customer_connection_id",
                        "lmc_stage",
                        "visit_status",
                        "tpi_status",
                        "created_at",
                        "visit_datetime",
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      order: [["created_at", "DESC"]],
    });

    return supervisors;
  } catch (error) {
    console.error("Error in getDashboardMetrics:", error);
    throw error;
  }
};

const getMROTicketDetails = async (mro_ticket) => {
  try {
    const mroRecords = await dbModels.mst_meter_reading_orders.findAll({
      attributes: [
        "mro_id",
        "scheduled_mr_date",
        "created_at",
        "mro_status"
      ],
      where: { mro_ticket },
      include: [
        {
          model: dbModels.trx_meter_reading_units,
          as: "trx_meter_reading_units",
          attributes: [
            "mru_id",
            "scheduled_mr_date",
            "created_at",
            "mru_status",
            "equipment_no",
            "bp_id"
          ],
          required: false,
          include: [
            {
              model: dbModels.trx_customer_meter_readings,
              as: "trx_customer_meter_readings",
              attributes: [
                "meter_reading_file",
                "created_at",
                "current_reading",
                "declared_by",
              ],
              required: false,
            },
            {
              model: dbModels.trx_customer_connection,
              as: "customer_connection",
              attributes: [
                "customer_connection_id",
                "first_name",
                "last_name",
                "email",
                "mobile",
                "bp_id",
                "ca_no"
              ],
              required: false,
              include: [
                {
                  model: dbModels.trx_customer_connection_address,
                  as: "trx_customer_connection_addresses",
                  attributes: [
                    "permanent_address",
                    "connection_address",
                    "connection_pincode",
                    "connection_city",
                    "connection_state",
                    "connection_district",
                  ],
                  required: false,
                },
                {
                  model: dbModels.trx_customer_meter,
                  as: "trx_customer_meters",
                  attributes: [
                    "meter_no",
                    "meter_status",
                    "meter_type",
                    "customer_meter_id",
                  ],
                  required: false,
                }
              ]
            }
          ],
        },
      ],
      raw : true
    });
    return mroRecords;
  } catch (error) {
    console.error("Error in getMROTicketDetails:", error);
    return { error: "Internal server error" };
  }
};

const getMROListing = async (
  status = "new",
  search,
  limit = 10,
  offset = 0,
  area_ids
) => {
  try {
    let whereCondition = {};

    if (search) {
      whereCondition.mro_ticket = {
        [Op.iLike]: `%${search}%`,
      };
    }

    if (status === "new") {
      whereCondition.fo_supervisor_id = null;
    } else if (status === "assigned") {
      whereCondition.fo_supervisor_id = { [Op.ne]: null };
    } else if (status === "completed") {
      whereCondition.mro_status = "completed";
    } else if (status === "rejected") {
      whereCondition.mro_status = "rejected";
    } else if (status === "pending") {
      whereCondition.mro_status = "pending";
    }

    const areaWhereCondition = area_ids.length > 0 ? { area_id: { [Op.in]: area_ids, } } : {};
    const allData = await dbModels.mst_meter_reading_orders.findAll({
      where: whereCondition,
      attributes: [
        "mro_ticket",
        [sequelize.fn("MIN", sequelize.col("mst_meter_reading_orders.total_mru")), "total_customers"],
        [sequelize.fn("MIN", sequelize.col("mst_meter_reading_orders.created_at")), "created_at"],
        [sequelize.fn("MIN", sequelize.col("mst_meter_reading_orders.scheduled_mr_date")), "scheduled_mr_date"],
        [sequelize.fn("MIN", sequelize.col("mst_meter_reading_orders.updated_at")), "assigned_date"],
      ],
      include: [
        {
          model: dbModels.mst_admin,
          as: "fo_supervisor",
          attributes: [], 
          required: true,
          include: [
            {
              model: dbModels.trx_admin_mapped_areas,
              as: "trx_admin_mapped_areas", 
              attributes: [], 
              required: area_ids.length > 0,
              where: areaWhereCondition,
            },
          ],
        },
      ],
      group: ["mro_ticket"],
      raw: true,
    });

    const paginatedRows = allData.slice(offset, offset + limit);

    return {
      count: allData.length,
      rows: paginatedRows,
    };
  } catch (error) {
    console.error("Repo error in getMROListing:", error);
    throw new Error("Failed to fetch MRO listing");
  }
};

const getAllFOSupervisors = async (params) => {
  try {
    const {
      approve_status,
      persona_role_id,
      page = 1,
      limit = 10,
      offset = 0,
      search = "",
      fromDate,
      toDate,
      area_ids
    } = params;

    const whereCondition = {};
    const mainWhere = {
      user_role: "supervisor",
      persona_role_id,
    };

    // Approval Status Filter
    switch (approve_status) {
      case "rejected":
        whereCondition.approve_status = "rejected";
        break;
      case "approved":
        whereCondition.approve_status = "approved";
        break;
      case "active":
        whereCondition.approve_status = "approved";
        mainWhere.is_active = 1;
        break;
      case "inactive":
        whereCondition.approve_status = "approved";
        mainWhere.is_active = 0;
        break;
      case "pending":
        whereCondition.approve_status = "pending";
        break;
    }

    // Search Filter
    if (search) {
      const lowerSearch = search.toLowerCase();
      mainWhere[Op.or] = [
        where(fn("LOWER", col("first_name")), {
          [Op.like]: `%${lowerSearch}%`,
        }),
        where(fn("LOWER", col("last_name")), { [Op.like]: `%${lowerSearch}%` }),
        where(fn("LOWER", col("email")), { [Op.like]: `%${lowerSearch}%` }),
        where(fn("LOWER", col("mobile")), { [Op.like]: `%${lowerSearch}%` }),
      ];
    }

    // Date Filter
    if (fromDate || toDate) {
      mainWhere.created_at = {};
      if (fromDate) mainWhere.created_at[Op.gte] = new Date(fromDate);
      if (toDate) mainWhere.created_at[Op.lte] = new Date(toDate);
    }

    // Area Filter
    const areaWhereCondition = area_ids.length > 0 ? { area_id: { [Op.in]: area_ids, } } : {};

    // Fetch paginated supervisors
    const supervisors = await dbModels.mst_admin.findAndCountAll({
      where: mainWhere,
      attributes: [
        "admin_id",
        "first_name",
        "last_name",
        "email",
        "mobile",
        "alternate_mobile",
        "persona_role_id",
        "is_active",
        "created_at",
        "updated_at",
      ],
      include: [
        {
          model: dbModels.trx_tpi_onboard_approve,
          as: "trx_tpi_onboard_approves",
          attributes: [
            "tpi_onboard_approve_id",
            "approve_status",
            "is_active",
            "created_at",
            "updated_at",
            "remark",
          ],
          where: whereCondition,
          required: true,
        },
        {
          model: dbModels.trx_admin_mapped_areas,
          as: "trx_admin_mapped_areas", 
          attributes: [], 
          required: area_ids.length > 0,
          where: areaWhereCondition,
        },
      ],
      order: [["created_at", "DESC"]],
      offset,
      limit: parseInt(limit),
      distinct: true,
      raw: true,
    });

    return {
      data: supervisors.rows,
      total: supervisors.count,
      page: parseInt(page),
      pages: Math.ceil(supervisors.count / limit),
    };
  } catch (error) {
    console.error("Error in getAllFOSupervisors:", error);
    throw error;
  }
};

const getDetailedMROCustomerInfo = async (customerConnectionId) => {
  try {
    return await dbModels.trx_customer_connection.findOne({
      where: { customer_connection_id: customerConnectionId },
      attributes: [
        "customer_connection_id",
        "first_name",
        "last_name",
        "email",
        "mobile",
        "bp_id",
        "ca_no",
      ],
      include: [
        {
          model: dbModels.trx_customer_connection_address,
          as: "trx_customer_connection_addresses",
          attributes: [
            "permanent_address",
            "connection_address",
            "connection_pincode",
            "connection_city",
            "connection_state",
            "connection_district",
          ],
          required: false,
        },
        {
          model: dbModels.trx_meter_reading_orders,
          as: "related_meter_reading_orders",
          attributes: [
            "mro_ticket",
            "scheduled_mr_date",
            "equipment_no",
            "mro_id",
            "remarks",
          ],
          required: false,
          include: [
            {
              model: dbModels.trx_customer_meter_readings,
              as: "trx_customer_meter_readings",
              attributes: [
                "customer_meter_reading_id",
                "meter_reading_file",
                "created_at",
                "current_reading",
                "declared_by",
              ],
              required: false,
            },
          ],
        },
        {
          model: dbModels.trx_customer_meter,
          as: "trx_customer_meters",
          attributes: ["meter_no", "meter_status", "customer_meter_id"],
          required: false,
        },
      ],
      raw: true,
    });
  } catch (error) {
    console.error("Error in getDetailedMROCustomerInfo Repository:", error);
    throw error;
  }
};

const assignSupervisorToMROOrders = async (payload) => {
  try {
    const { fo_supervisor_id, mro_ticket } = payload;

    if (!mro_ticket || !fo_supervisor_id) {
      throw new Error(
        "Missing required parameters: mro_ticket or fo_supervisor_id"
      );
    }

    // Build dynamic where condition
    const whereCondition = {};
    if (mro_ticket) {
      whereCondition.mro_ticket = mro_ticket;
    }

    const [updatedRowsCount] = await dbModels.trx_meter_reading_orders.update(
      { fo_supervisor_id },
      { where: whereCondition }
    );

    if (updatedRowsCount === 0) {
      return {
        success: false,
        message:
          "No MRO orders found for the given ticket and mro_id (if provided).",
      };
    }

    return {
      success: true,
      message: `Supervisor assigned to ${updatedRowsCount} MRO order(s).`,
    };
  } catch (error) {
    console.error("Error assigning FO Supervisor to MRO:", error);
    return {
      success: false,
      message: error.message,
    };
  }
};

const getMROKPIs = async ({ start_date, end_date }) => {
  try {
    const now = new Date();
    const defaultStart = new Date(now.getFullYear(), now.getMonth(), 1); // start of current month
    const defaultEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0); // end of current month

    // Fallback to current month if not provided
    const dateFilter = {
      created_at: {
        [Op.between]: [
          new Date(start_date || defaultStart),
          new Date(end_date || defaultEnd),
        ],
      },
    };

    // Total MROs
    const totalMROs = await dbModels.mst_meter_reading_orders.count({
      where: dateFilter,
    });

    // New MROs (status: pending)
    const newMROs = await dbModels.mst_meter_reading_orders.count({
      where: {
        fo_supervisor_id: null,
        mro_status: { [Op.ne]: "completed" },
        ...dateFilter,
      },
    });

    // Assigned MROs (with supervisor assigned)
    const assignedMROs = await dbModels.mst_meter_reading_orders.count({
      where: {
        fo_supervisor_id: { [Op.ne]: null },
        ...dateFilter,
      },
    });

    // Completed MROs
    const completedMROs = await dbModels.mst_meter_reading_orders.count({
      where: {
        mro_status: "completed",
        ...dateFilter,
      },
    });

    return {
      total_mros: totalMROs,
      new_mros: newMROs,
      assigned_mros: assignedMROs,
      completed_mros: completedMROs,
    };
  } catch (error) {
    console.error("Error fetching MRO KPIs:", error);
    return {
      total_mros: 0,
      new_mros: 0,
      assigned_mros: 0,
      completed_mros: 0,
      error: "Internal server error",
    };
  }
};
const getFoSupervisorById = async (email, mobile) => {
  try {
    return await dbModels.mst_admin.findOne({
      where: {
        [Op.or]: [{ email: email }, { mobile: mobile }],
        user_role: "supervisor",
      },
      include: [
        {
          model: dbModels.mst_persona_role,
          as: "persona_role",
          attributes: ["persona_role_id", "role_name"],
        },
      ],
    });
  } catch (error) {
    console.error("Error in getDmaSupervisorById:", error);
    throw error;
  }
};


const getSupervisorMROListing = async (supervisorId, area_ids) => {
  try {
    const areaWhereCondition =
      area_ids.length > 0 ? { area_id: { [Op.in]: area_ids } } : {};

    const data = await dbModels.mst_meter_reading_orders.findAll({
      attributes: ["mro_ticket", "mro_id"],
      where: {
        mro_status: { [Op.ne]: "completed" }, // ✅ Filter out completed MROs
      },
      include: [
        {
          model: dbModels.mst_admin,
          as: "fo_supervisor",
          attributes: [],
          required: true,
          where: { admin_id: supervisorId }, // ✅ Supervisor filter
          include: [
            {
              model: dbModels.trx_admin_mapped_areas,
              as: "trx_admin_mapped_areas",
              attributes: [],
              required: area_ids.length > 0,
              where: areaWhereCondition, // ✅ Area filter
            },
          ],
        },
      ],
      raw: true,
    });

    return data;
  } catch (error) {
    console.error("Repository Error - getSupervisorMROListing:", error);
    throw error;
  }
};
const getFOSupervisor = async (payload) => {
  try {
    const { supervisorId } = payload;

    // Step 1: Find the supervisor by admin_id
    let supervisor = await dbModels.mst_admin.findOne({
      where: { admin_id: supervisorId },
      attributes: ["admin_id", "first_name", "last_name",'email'],
    });

    return supervisor
  } catch (error) {
    console.error("Error in revokeFOSupervisor:", error);
    return {
      success: false,
      message: "Failed to revoke supervisor access and reassign MROs.",
      error: error.message,
    };
  }
};


module.exports = {
  createFOSupervisor,
  getAllFOSupervisors,
  revokeFOSupervisor,
  getMyLMCSupervisors,
  getGeographicalAreasMetrics,
  getDashboardMetrics,
  getMROTicketDetails,
  getMROListing,
  getDetailedMROCustomerInfo,
  assignSupervisorToMROOrders,
  getMROKPIs,
  getFoSupervisorById,
  getSupervisorMROListing,
  getFOSupervisor
};
