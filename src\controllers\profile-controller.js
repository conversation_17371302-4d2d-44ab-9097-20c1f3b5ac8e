const { sendSuccess, sendError } = require('../helpers/responseHelper');
const authRepository = require('../repository/auth-repository');

exports.getProfile = async (req, res) => {
    try {
        const { admin_id } = req.user;
        const user = await authRepository.findUserByIdAndRole(admin_id);

        if (!user) {
            return sendError(req, res, null, 'User not found or unauthorized role');
        }

        const { password: _, ...userData } = user.toJSON();
        sendSuccess(req, res, userData, 'Profile retrieved successfully');
    } catch (error) {
        console.error('Profile error:', error);
        sendError(req, res, null, error.message || 'Failed to retrieve profile');
    }
};

module.exports = { getProfile: exports.getProfile };