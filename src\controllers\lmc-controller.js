const { sendSuccess, sendError } = require('../helpers/responseHelper');
const lmcService = require('../services/lmc-service');
const { paginateQuery } = require('../utils/commonFn');

exports.lmcList = async (req, res) => {
  try {
    const result = await lmcService.getLmcConnectionDetails(req.body,req?.user?.admin_id);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.lmcCasesSummery = async (req, res) => {
  try {
    const result = await lmcService.getLmcCasesSummery(req);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.lmcCases = async (req, res) => {
  try {
    const result = await lmcService.getLmcCases(req.query,req?.user?.admin_id);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.lmcFeasibilityTickets = async (req, res) => {
  try {
    const lmc_stage = req.query.lmc_stage ? req.query.lmc_stage  : 'feasibility-check';
    const result = await lmcService.getLmcTickets(req,lmc_stage);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getLmcStageDetails = async (req, res) => {
  try {
    const result = await lmcService.getLmcStageDetails(req.query,req?.user?.admin_id);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.lmcScheduleVisit = async (req, res) => {
  try {
    const result = await lmcService.saveLmcScheduleVisit(req.body,req?.user?.admin_id);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.lmcDrawingUpload = async (req, res) => {
  try {
    const result = await lmcService.uploadPipelineDrawing(req.body,req.file,req?.user?.admin_id);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.lmcPipelineDrawing = async (req, res) => {
  try {
    const result = await lmcService.submitPipelineDrawing(req.body,req.file,req?.user?.admin_id);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.lmcInstallationTickets = async (req, res) => {
  try {
    const lmc_stage = req.query.lmc_stage ? req.query.lmc_stage  : 'installation';
    const result = await lmcService.getLmcTickets(req,lmc_stage);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.lmcCommissioningTickets = async (req, res) => {
  try {
    const lmc_stage = req.query.lmc_stage ? req.query.lmc_stage  : 'commissioning';
    const result = await lmcService.getLmcTickets(req,lmc_stage);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.lmcJMRSubmit = async (req, res) => {
  try {
    const result = await lmcService.submitLmcJMR(req.body,req.file,req?.user?.admin_id);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.lmcJMRSubmissionDetails = async (req, res) => {
  try {
    const result = await lmcService.getLmcJMRSubmissionDetails(req.query,req?.user?.admin_id);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.lmcMeterReadingUploads = async (req, res) => {
  try {

    // Ensure files are uploaded
    if (!req.files) {
      return sendError(req, res, {}, 'Please upload meter reading Image.');
    }
    // Call service layer with req.body and req.files
    const result = await lmcService.processLmcMeterReadingImage(req.body, req.files,req?.user?.admin_id);

    sendSuccess(req, res, result, 'Documents uploaded and Redis cache updated successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getFormDetails = async (req, res) => {
  try {
    const result = await lmcService.getFormDetails(req);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};


exports.submitFeasibilityFrom = async (req, res) => {
  try {
    const result = await lmcService.submitFeasibilityFrom(req);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.submitCustomerConsent = async (req, res) => {
  try {
    const result = await lmcService.submitCustomerConsent(req.body,req.file,req?.user?.admin_id);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getFeasibilityDetails = async (req, res) => {
  try {
    const result = await lmcService.getFeasibilityFormDetails(req);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.submitCommissioningForm = async (req, res) => {
  try {
    const result = await lmcService.submitCommissioningForm(req);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getCommissioningFormDetails = async (req, res) => {
  try {
    const result = await lmcService.getCommissioningFormDetails(req);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.saveInstallationDetailsToCache = async (req, res) => {
  try {
    const { step, ...payload } = req.body;

    if (!step) {
      throw new Error("Step is required in payload.");
    }

    const result = await lmcService.saveInstallationDetailsToCache(step, payload);

    sendSuccess(req, res, result, "Details saved successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.InstallationDocumentsUpload = async (req, res) => {
  try {
    const payload = req.body;
    const files = req.files; // Assuming you are using multer or similar middleware
    const result = await lmcService.InstallationDocumentsUpload(payload, files);

    sendSuccess(req, res, result, "Documents uploaded successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.saveInstallationDetails = async (req, res) => {
  try {
    const payload = req.body;

    const lmc_id = req?.user?.admin_id;
    const result = await lmcService.saveInstallationFromRedis(payload,lmc_id);

    sendSuccess(req, res, result, "Details saved successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.getInstallationDetails = async (req, res) => {
  try {
    const payload = req.body;

    const lmc_id = req?.user?.admin_id;
    const result = await lmcService.getInstallationDetailsFromCache(payload,lmc_id);

    sendSuccess(req, res, result, "Details saved successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getPipelineChecklist = async (req, res) => {
  try {
    const payload = req.body;

    const lmc_id = req?.user?.admin_id;
    const result = await lmcService.getPipelineChecklist(payload,lmc_id);

    sendSuccess(req, res, result, "Details saved successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
exports.getInstallationFormData = async (req, res) => {
  try {
    const payload = req.body;

    const result = await lmcService.getInstallationFormData(payload?.mapped_lmc_connection_stage_id);

    sendSuccess(req, res, result, "Details saved successfully.");
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};

exports.getInstallationPrefill = async (req, res) => {
  try {
    const result = await lmcService.getInstallationPrefill(req?.body?.customer_connection_id);
    sendSuccess(req, res, result, 'Fetch Data successfully.');
  } catch (error) {
    sendError(req, res, error, error.message);
  }
};
