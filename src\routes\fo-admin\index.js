const { validate } = require("express-validation");
const {restrictToRoles} = require("../../middleware/auth");
const express = require("express");
const foAdmin = express.Router();
const decrypt = require("../../middleware/encrypt-decrypt");
const foAdminController = require("../../controllers/fo-admin-controller");
const {
  getfoFeasibilityTicketsValidation,
  mySupervisorMROListingValidation,
} = require("../../validation/fo-admin-validation");
const { uploadSingle } = require("../../middleware/newMulter");
const {
  getfoCustomerAssignmentTicketsValidation,
  supervisorOnboardingValidation,
  createSupervisorValidation,
  revokeSupervisorValidation,
  mySupervisorValidation,
  geographicalAreasValidation,
  dashboardMetricsValidation,
  supervisorAssignValidation,
  supervisorTicketAssignValidation,
} = require("../../validation/fo-admin-validation");
const {
  foCustomerAssignments,
} = require("../../controllers/fo-admin-controller");
const verifyToken = require('../../middleware/auth');

foAdmin.post(
  "/supervisor-onboarding",
  verifyToken,
  restrictToRoles("contractor"),
  validate(supervisorOnboardingValidation),
  foAdminController.getAllFOSupervisors
);
foAdmin.post(
  "/create-supervisor",
  verifyToken,
  restrictToRoles("contractor"),
  uploadSingle,
  validate(createSupervisorValidation),
  foAdminController.createFoSupervisor
);
foAdmin.post(
  "/mro-listing",
  verifyToken,
  // restrictToRoles("contractor"),
  validate(mySupervisorValidation),
  foAdminController.getMROListing
);

foAdmin.post(
  "/supervisor-mro-listing",
  verifyToken,
  restrictToRoles("contractor"),
  validate(mySupervisorMROListingValidation),
  foAdminController.getSupervisorMROListing
);
foAdmin.post(
  "/mro-details",
  // verifyToken,
  // restrictToRoles("contractor"),
  // validate(mySupervisorValidation),
  foAdminController.getMROTicketDetails
);
foAdmin.post(
  "/mro-customer-details",
  // verifyToken,
  // restrictToRoles("contractor"),
  // validate(mySupervisorValidation),
  foAdminController.getDetailedMROCustomerInfo
);

foAdmin.post(
  "/supervisor-list",
  verifyToken,
  restrictToRoles("contractor"),
  validate(mySupervisorValidation),
  foAdminController.getAllFOSupervisors
);

foAdmin.post(
  "/assign-supervisor",
  verifyToken,
  restrictToRoles("contractor"),
  // validate(mySupervisorValidation),
  foAdminController.assignOSupervisors
);


foAdmin.post(
  "/revoke-supervisor",
  verifyToken,
  restrictToRoles("contractor"),
  // validate(mySupervisorValidation),
  foAdminController.revokeFOSupervisor

);
foAdmin.post(
  "/mro-metrics",
  verifyToken,
  restrictToRoles("contractor"),
  // validate(mySupervisorValidation),
  foAdminController.getMROKPIs
);
foAdmin.post(
  "/meter-reading-upload",
  verifyToken,
  restrictToRoles("contractor"),
  uploadSingle,
  // validate(mySupervisorValidation),
  foAdminController.meterReadingUploads
);

foAdmin.post(
  "/area-metrics",
  verifyToken,
  restrictToRoles("contractor"),
  // validate(mySupervisorValidation),
  foAdminController.getAreaMetrics
);


module.exports = { foAdmin };
