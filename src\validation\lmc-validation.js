const { Joi } = require('express-validation');
const moment = require('moment');

const getLmcConnectionValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).allow(null, '').default(null),
  area_ids: Joi.string()
  .pattern(/^\d+(,\d+)*$/)
  .optional()
  .messages({
    "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
  })
  }),
};

const getLmcCasesSummaryValidation = {
  query: Joi.object({
    area_ids: Joi.string()
    .pattern(/^\d+(,\d+)*$/)
    .optional()
    .messages({
      "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
    })
  }),

};

const getLmcCasesValidation = {
  query: Joi.object({
    caseType: Joi.string().valid('active', 'closed').default('active'),
    search: Joi.string().optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).default(1000),
    lmc_stage: Joi.string().valid('feasibility-check', 'installation', 'commissioning').optional(),
    visit_status: Joi.string().valid('schedule', 'rescheduled', 'to-be-rescheduled', 'pending', 'inprogress', 'completed', 'failed','not-scheduled').optional(),
    tpi_status: Joi.string().valid('pending', 'no-submission', 'approved', 'rework', 'rejected').optional(),
    area_ids: Joi.string()
    .pattern(/^\d+(,\d+)*$/)
    .optional()
    .messages({
      "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
    })
  }),

};

const getLmcFeasibilityTicketsValidation = {
  query: Joi.object({
    ticketType: Joi.string().valid('rejected', 'open','pending').default('open'),
    search: Joi.string().optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).default(1000),
    lmc_stage: Joi.string().valid('feasibility-check', 'installation', 'commissioning').optional(),
    visit_status: Joi.string().valid('schedule', 'rescheduled', 'to-be-rescheduled', 'pending', 'inprogress', 'completed', 'failed','not-scheduled').optional(),
    tpi_status: Joi.string().valid('pending', 'no-submission', 'approved', 'rework', 'rejected').optional(),
    
  area_ids: Joi.string()
  .pattern(/^\d+(,\d+)*$/)
  .optional()
  .messages({
    "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
  })
  }),
};

const getLmcCommissioningTicketsValidation = {
  query: Joi.object({
    ticketType: Joi.string().valid('rejected', 'open','activated','customer-approval','hold').default('active'),
    search: Joi.string().optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).default(1000),
    lmc_stage: Joi.string().valid('feasibility-check', 'installation', 'commissioning').optional(),
    visit_status: Joi.string().valid('schedule', 'rescheduled', 'to-be-rescheduled', 'pending', 'inprogress', 'completed', 'failed','not-scheduled').optional(),
    tpi_status: Joi.string().valid('pending', 'no-submission', 'approved', 'rework', 'rejected').optional(),
    area_ids: Joi.string()
    .pattern(/^\d+(,\d+)*$/)
    .optional()
    .messages({
      "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
    })
  }),

};

const getLmcFeasibilityVisitValidation = {
  query: Joi.object({
    mapped_lmc_connection_stage_id: Joi.number().integer().min(1).required(),
    area_ids: Joi.string()
    .pattern(/^\d+(,\d+)*$/)
    .optional()
    .messages({
      "string.pattern.base": "area_ids must be a comma-separated list of positive integers."
    })
  }),

};

const getLmcScheduleVisitValidation = {
  body: Joi.object({
    mapped_lmc_connection_stage_id: Joi.number().integer().min(1).required(),
    schedule_date: Joi.string()
      .custom((value, helpers) => {
        const allowedFormats = [
          moment.ISO_8601,
          'YYYY-MM-DD',
          'YYYY-MM-DD HH:mm:ss',
        ];
        const parsedDate = moment(value, allowedFormats, true);
        if (!parsedDate.isValid()) {
          return helpers.error("any.invalid", { message: "Invalid or non-existent date." });
        }
        if (parsedDate.isBefore(moment(), 'day')) {
          return helpers.error("date.greater", { limit: 'today' });
        }
        return value;
      })
      .required()
  }),
};

const installationSubmitSchemaValidation = {
  body: Joi.object({
    mapped_lmc_connection_stage_id: Joi.number().integer().min(1).required()
      .messages({
        "number.base": "mapped_lmc_connection_stage_id must be a number",
        "any.required": "mapped_lmc_connection_stage_id is required",
      }),
    mapped_lmc_customer_connection_id: Joi.number().integer().min(1).required()
      .messages({
        "number.base": "mapped_lmc_customer_connection_id must be a number",
        "any.required": "mapped_lmc_customer_connection_id is required",
      })
  })
};

const getLmcMeterReadingUploadValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required()
  })
};

const formDetailsValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required()
  })
};

const submitDrawingValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required()
      .messages({
        "number.base": "customer_connection_id must be a number",
        "number.integer": "customer_connection_id must be an integer",
        "number.min": "customer_connection_id must be greater than 0",
        "any.required": "customer_connection_id is required"
      }),
    customer_consent_request: Joi.boolean().required()
      .messages({
        "boolean.base": "customer_consent_request must be true or false",
        "any.required": "customer_consent_request is required"
      }),
    file_type: Joi.string().valid("isometric-drawing", "installed-pipeline", "pressure-testing", "others").required()
      .messages({
        "string.base": "file_type must be a string",
        "any.only": "file_type must be one of 'isometric-drawing', 'installed-pipeline', 'pressure-testing', 'others'",
        "any.required": "file_type is required"
      }),
    pipeline_size: Joi.number().integer().min(1).required()
      .messages({
        "number.base": "pipeline_size must be a number",
        "number.integer": "pipeline_size must be an integer",
        "number.min": "pipeline_size must be greater than 0",
        "any.required": "pipeline_size is required"
      }),
    lmc_stage: Joi.string().valid('feasibility-check', 'installation', 'commissioning').required(),
  })
};

const getLmcUploadValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required(),
    file_type: Joi.string().valid("isometric-drawing", "installed-pipeline", "pressure-testing", "others").required()
      .messages({
        "string.base": "file_type must be a string",
        "any.only": "file_type must be one of 'isometric-drawing', 'installed-pipeline', 'pressure-testing', 'others'",
        "any.required": "file_type is required"
      }),
    lmc_stage: Joi.string().valid('feasibility-check', 'installation', 'commissioning').required(),
  })
};

const submitLmcJMRValidation = {
  body: Joi.object({
    meter_no: Joi.string()
      .alphanum()
      .length(20)
      .message("Meter serial number should be exactly 20 alphanumeric characters")
      .required(),
    meter_reading: Joi.number().precision(3).positive().required(),
    customer_connection_id: Joi.number().integer().min(1).required(),
    is_manual: Joi.number().integer().valid(0, 1).required(),
    meter_type: Joi.string().valid('mechanical', 'smart').required(),
  })
};

const feasibilitySubmitSchemaValidation = {
  body: Joi.object({
    mapped_lmc_connection_stage_id: Joi.number().integer().min(1).required(),
    house_type: Joi.string().required(),
    mdpe_pipeline_length: Joi.number().required(),
    riser_length: Joi.number().required(),
    gi_pipeline_length: Joi.number().required(),
    actual_visit_date: Joi.string().required(),
    customer_connection_id: Joi.number().integer().min(1).required(),
  })
};

const commissioningSubmitSchemaValidation = {
  body: Joi.object({
    mapped_lmc_connection_stage_id: Joi.number().integer().min(1).required(),
    meter_type: Joi.string().required(),
    actual_visit_date: Joi.string().required(),
    ready_for_conversion: Joi.number().valid(0, 1).required(),
    meter_reading: Joi.number().required(),
    customer_connection_id: Joi.number().integer().min(1).required(),
    meter_no: Joi.string().required(),
    is_manual: Joi.number().integer().valid(0, 1).optional(),
  })
};

const pipelineSubmitValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required(),
    pipeline_size: Joi.number().integer().min(1).required(),
    file_type: Joi.string().valid(
      'isometric-drawing',
      'natural-gas-consent',
      'jmr',
      'jmr-customer-approval',
      'customer-consent'
    ).required(),
    lmc_stage: Joi.string().valid(
      'feasibility-check',
      'installation',
      'commissioning'
    ).required(),
    customer_consent_request: Joi.boolean().required(),
  })
};

const uploadReadingValidation = {
  body: Joi.object({
    customer_connection_id: Joi.number().integer().min(1).required()
  })
};

const getStageSubmissionDetailsValidation = {
  body: Joi.object({
    mapped_lmc_connection_stage_id: Joi.number().integer().min(1).required()
  })
};

const getInstallationSubmissionDetailsValidation = {
  body: Joi.object({
    mapped_lmc_connection_stage_id: Joi.number().integer().min(1).required(),
    mapped_lmc_customer_connection_id: Joi.number().integer().min(1).required()
  })
};

module.exports = {
  getLmcConnectionValidation,
  getLmcCasesSummaryValidation,
  getLmcCasesValidation,
  getLmcFeasibilityTicketsValidation,
  getLmcFeasibilityVisitValidation,
  getLmcScheduleVisitValidation,
  submitDrawingValidation,
  getLmcUploadValidation,
  submitLmcJMRValidation,
  getLmcCommissioningTicketsValidation,
  getLmcMeterReadingUploadValidation,
  formDetailsValidation,
  installationSubmitSchemaValidation,
  feasibilitySubmitSchemaValidation,
  commissioningSubmitSchemaValidation,
  uploadReadingValidation,
  getStageSubmissionDetailsValidation,
  getInstallationSubmissionDetailsValidation
};
